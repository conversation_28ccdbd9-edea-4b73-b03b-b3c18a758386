
## Introduction 🚀

If you’re a developer looking for the most Powerful & comprehensive [**Free Bootstrap 5 HTML Laravel Admin Template**](https://themeselection.com/item/sneat-free-bootstrap-html-laravel-admin-template/) built for developers, rich with features, and highly customizable look no further than <PERSON>neat. We’ve followed the highest industry standards to bring you the very best admin template that is not only fast and easy to use but highly scalable. Offering ultimate convenience and flexibility, you’ll be able to build whatever application you want with very little hassle.

Build premium quality applications with ease. Use our innovative **[Laravel admin template](https://themeselection.com/item/category/laravel-admin-templates/)** to create eye-catching, high-quality WebApps. Your apps will be completely responsive, ensuring they’ll look stunning and function flawlessly on desktops, tablets, and mobile devices.

[View Demo](https://demos.themeselection.com/sneat-bootstrap-html-laravel-admin-template-free/demo/)

## Installation ⚒️

Installing and running Sneat is super easy, please Follow below steps and you will be ready to rock 🤘

1. Open the terminal in your root directory of Sneat Laravel.
2. Use the following command to install the composer

```bash
composer install
```

3. Run the following command to generate the key

```bash
php artisan key:generate
```

4. By running the following command, you will be able to get all the dependencies in your **node_modules** folder:

```bash
yarn
```

5. To run the project, you need to run the following command in the project directory. It will compile JavaScript and Styles.

```bash
yarn dev
```

6. To serve the application, you need to run the following command in the project directory

```bash
php artisan serve
```

7. Now navigate to the given address, and you will see your application is running.🥳

## Available Tasks 🧑‍💻

**Building for Production:** If you want to run the project and make the build in the production mode then run the following command in the root directory, by default The project will continue to run in the development mode:

```bash
yarn prod
```

## What's Included 📦

- Dashboard
- Layouts
  - Without menu
  - Without Navbar
  - Container
  - Fluid
  - Blank
- Pages
  - Account Settings
  - Login
  - Register
  - Forgot Password
  - Error
  - Under Maintenance
- Cards
- User Interface
  - **All Bootstrap Components**
- Extended UI
  - Perfect Scrollbar
  - Text Divider
- Boxicon
- Form Elements
  - Basic Inputs
  - Input Groups
- Form Layout
  - Vertical Form
  - Horizontal Form
- Tables

## Documentation 📜

<!-- If you have docs in wiki then use below line -->

Check GitHub [Wiki](https://github.com/themeselection/sneat-bootstrap-html-laravel-admin-template-free/wiki) of this repo

<!-- If you have live docs then use below line -->

Check out our live [Documentation](https://demos.themeselection.com/sneat-bootstrap-html-admin-template/documentation/laravel-introduction.html)
