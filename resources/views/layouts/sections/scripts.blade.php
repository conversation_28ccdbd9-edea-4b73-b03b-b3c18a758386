<!-- BEGIN: Vendor JS-->
<script src="{{ asset(mix('assets/vendor/libs/jquery/jquery.js')) }}"></script>
<script src="{{ asset(mix('assets/vendor/libs/popper/popper.js')) }}"></script>
<script src="{{ asset(mix('assets/vendor/js/bootstrap.js')) }}"></script>
<script src="{{ asset(mix('assets/vendor/libs/perfect-scrollbar/perfect-scrollbar.js')) }}"></script>
<script src="{{ asset(mix('assets/vendor/js/menu.js')) }}"></script>

<script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.6-rc.0/js/select2.min.js"></script>
<script src="https://cdn.ckeditor.com/ckeditor5/41.3.0/classic/ckeditor.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.10.0/js/bootstrap-datepicker.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
<script src="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote-lite.min.js"></script>
<script>
  let url  = window.location.href;
  $('.menu-link').each(function(){
    const href = $(this).attr('href');
    if (url .split('?')[0] === href) {
      $(this).closest('.menu-item').addClass('active');
      $(this).parents('.menu-item').addClass('active');
      $(this).parents('.menu-item').addClass('open');
    }
  });
  function disablePastTime(id){
    var input = document.getElementById(id);
    input.min = new Date().toISOString().split("T")[0];
  }
  function disableFeatureTime(id){
    var input = document.getElementById(id);
    input.max = new Date().toISOString().split("T")[0];
  }
  function sort(title, type) {
    var currentUrl = new URL(window.location.href);

    currentUrl.searchParams.forEach(function(value, key) {
      if (value === 'asc' || value === 'desc') {
        currentUrl.searchParams.delete(key);
      }
    });
    currentUrl.searchParams.set(title, type);
    window.location.href = currentUrl.href;
  }
  function showConfirmationModal(id) {
    const deleteForm = document.getElementById('delete-form');
    deleteForm.action = deleteForm.action.replace('user_id',id);
  }
  $(document).ready(function (){
    $(".date").datepicker({
      format: "yyyy/mm/dd",
      autoclose: true,
      todayHighlight: true,
    });
  });

</script>
@yield('vendor-script')
<!-- END: Page Vendor JS-->
<!-- BEGIN: Theme JS-->
<script src="{{ asset(mix('assets/js/main.js')) }}"></script>

<!-- END: Theme JS-->
<!-- Pricing Modal JS-->
@stack('pricing-script')
<!-- END: Pricing Modal JS-->
<!-- BEGIN: Page JS-->
@yield('page-script')
<!-- END: Page JS-->
