<aside id="layout-menu" class="layout-menu menu-vertical menu bg-menu-theme">

  <!-- ! Hide app brand if navbar-full -->
  <div class="app-brand demo">
    <a href="{{url('/')}}" class="app-brand-link">
      <img src="{{asset('./assets/img/logo.png')}}">
    </a>

    <a href="javascript:void(0);" class="layout-menu-toggle menu-link text-large ms-auto d-block d-xl-none">
      <i class="bx bx-chevron-left bx-sm align-middle"></i>
    </a>
  </div>

  <div class="menu-inner-shadow"></div>

  <ul class="menu-inner py-1">
    @if(auth('admin')->user() !== null && auth('admin')->user()->role  == 'admin' )
      <li class="menu-item">
        <a href="{{route('admin.index')}}" class="menu-link">
          <i class="menu-icon tf-icons bx bx-home-circle"></i>Home
        </a>
      </li>
      <li class="menu-item">
        <a href="{{route('admin.classes.index')}}" class="menu-link">
          <i class=' menu-icon tf-icons bx bx bx-buildings'></i>
          <div class="text-truncate">Class</div>
        </a>
      </li>
      <li class="menu-item">
        <a href="{{route('admin.terms.index')}}" class="menu-link">
          <i class='menu-icon tf-icons bx bx bx-buildings'></i>
          <div class="text-truncate">Term</div>
        </a>
      </li>
      <li class="menu-item {{Request::is('admin/students/*') ? 'active' : ''}}">
        <a href="{{route('admin.students.index')}}" class="menu-link">
          <i class=' menu-icon tf-icons bx bxs-graduation'></i>
          <div class="text-truncate">Students</div>
        </a>
      </li>
      <li class="menu-item {{Request::is('admin/users/*') ? 'active' : ''}}">
        <a href="{{route('admin.users.index')}}" class="menu-link">
          <i class='menu-icon tf-icons bx bxs-user'></i>
          <div class="text-truncate">Admin</div>
        </a>
      </li>
      <li class="menu-item {{Request::is('admin/accounts/*') ? 'active' : ''}}">
        <a href="{{route('admin.accounts.index')}}" class="menu-link">
          <i class='menu-icon tf-icons bx bxs-user-account'></i>
          <div class="text-truncate">Account</div>
        </a>
      </li>
      <li class="menu-item {{Request::is('admin/schedules/*') ? 'active' : ''}}">
        <a href="{{route('admin.schedules.index')}}" class="menu-link">
          <i class='menu-icon tf-icons bx bxs-calendar'></i>
          <div class="text-truncate">Schedule</div>
        </a>
      </li>
      <li class="menu-item">
        <a href="{{route('admin.reports.index')}}" class="menu-link">
          <i class='menu-icon tf-icons bx bxs-report'></i>
          <div class="text-truncate">Report</div>
        </a>
      </li>
      <li class="menu-item {{Request::is('admin/grades/*')? 'active open' : ''}}">
        <a href="javascript:void(0)" class="menu-link menu-toggle">
          <i class='menu-icon tf-icons bx bx-layer-plus'></i>
          <div class="text-truncate">Grades</div>
        </a>
        <ul class="menu-sub">
          <li class="menu-item {{Request::is('admin/grades/*') ? 'active' : ''}}">
            <a href="{{route('admin.grades.index')}}" class="menu-link">Grades</a>
          </li>
          <li class="menu-item">
            <a href="{{route('admin.grades.template')}}" class="menu-link">Template</a>
          </li>
        </ul>
      </li>
      <li class="menu-item {{Request::is('admin/grades-lc/*') ? 'active' : ''}}">
          <a href="{{route('admin.grades-lc')}}" class="menu-link">
              <i class='menu-icon tf-icons bx bx-layer-plus'></i>
              <div class="text-truncate">Grades LC</div>
          </a>
      </li>
      @include('layouts.sections.menu.learningCenterMenu')
      <li class="menu-item">
        <a href="javascript:void(0)" class="menu-link menu-toggle">
          <i class='menu-icon tf-icons bx bxs-user-check'></i>
          <div class="text-truncate">Attendance</div>
        </a>
        <ul class="menu-sub">
          <li class="menu-item">
            <a href="{{route('admin.attendance.regular')}}" class="menu-link">Regular Class</a>
          </li>
{{--          <li class="menu-item">--}}
{{--            <a href="{{route('admin.attendance.special')}}" class="menu-link">Special Class</a>--}}
{{--          </li>--}}
          <li class="menu-item">
            <a href="{{route('admin.attendance.private')}}" class="menu-link">Private tutor</a>
          </li>
        </ul>
      </li>
      <li class="menu-item">
        <a href="{{route('admin.messages.index')}}" class="menu-link">
          <i class='menu-icon tf-icons bx bx-chat'></i>
          <div class="text-truncate">Message</div>
        </a>
      </li>
          <li class="menu-item {{ Request::is('admin/invoices/*')
            || Request::is('admin/refunds/*')
            || Request::is('admin/invoices-batch')
            || Request::is('admin/invoice-products/*')
             ? 'active open' : ''}}">
              <a href="javascript:void(0)" class="menu-link menu-toggle">
                  <i class='menu-icon tf-icons bx bx-layer-plus'></i>
                  <div class="text-truncate">Invoices</div>
              </a>
              <ul class="menu-sub">
                  <li class="menu-item {{(Request::is('admin/invoices/*') || Request::is('admin/invoices-batch')) ? 'active' : ''}}">
                      <a href="{{route('admin.invoices.index')}}" class="menu-link">Invoices</a>
                  </li>
                  <li class="menu-item {{Request::is('admin/invoice-products/*') ? 'active' : ''}}">
                      <a href="{{route('admin.invoice-products.index')}}" class="menu-link">Products</a>
                  </li>
                  <li class="menu-item {{Request::is('admin/refunds/*') ? 'active' : ''}}">
                      <a href="{{route('admin.invoices.refunds')}}" class="menu-link">Refunds</a>
                  </li>
              </ul>
          </li>
      <li class="menu-item {{ Request::is('kakao') ? 'active' : '' }}">
          <a href="{{ route('admin.kakao') }}" class="menu-link">
              <i class='menu-icon tf-icons bx bx-message-square-dots'></i>
              <div class="text-truncate">Kakao Management</div>
          </a>
      </li>

      @endif
      @if(auth('admin')->user() !== null && auth('admin')->user()->role  == 'teacher' )
        <li class="menu-item">
          <a href="javascript:void(0)" class="menu-link menu-toggle">
            <i class='menu-icon tf-icons bx bx-layer-plus'></i>
            <div class="text-truncate">Grades</div>
          </a>
          <ul class="menu-sub">
            <li class="menu-item">
              <a href="{{route('admin.grades.index')}}" class="menu-link">Grades</a>
            </li>
          </ul>
        </li>
            <li class="menu-item {{Request::is('admin/grades-lc/*') ? 'active' : ''}}">
                <a href="{{route('admin.grades-lc')}}" class="menu-link">
                    <i class='menu-icon tf-icons bx bx-layer-plus'></i>
                    <div class="text-truncate">Grades LC</div>
                </a>
            </li>
        @include('layouts.sections.menu.learningCenterMenu')
        @endif

  </ul>

</aside>
