<li class="menu-item {{ Request::is('admin/tasks/*')
            || Request::is('admin/main-homeworks/*')
            || Request::is('admin/homeworks/*')
            || Request::is('admin/vocabularies/*')
            || Request::is('admin/v2/grades/*')
            || Request::is('admin/tag-vocabularies/*')
             ? 'active open' : ''}}">
    <a href="javascript:void(0)" class="menu-link menu-toggle">
        <i class='menu-icon tf-icons bx bx-layer-plus'></i>
        <div class="text-truncate">Learning Center</div>
    </a>
    <ul class="menu-sub">
        <li class="menu-item {{Request::is('admin/main-homeworks/*') || Request::is('admin/homeworks/*') ? 'active' : ''}}">
            <a href="{{route('admin.main-homeworks.index')}}" class="menu-link">Main Homework</a>
        </li>
        <li class="menu-item {{Request::is('admin/request-unlocks/*') ? 'active' : ''}}">
            <a href="{{route('admin.request-unlocks.index')}}" class="menu-link">Request Unlock</a>
        </li>
        <li class="menu-item {{Request::is('admin/tasks/*') ? 'active' : ''}}">
            <a href="{{route('admin.tasks.index')}}" class="menu-link">Tasks</a>
        </li>
        <li class="menu-item {{Request::is('admin/vocabularies/*') ? 'active' : ''}}">
            <a href="{{route('admin.vocabularies.index')}}" class="menu-link">Vocabularies</a>
        </li>
        <li class="menu-item {{Request::is('admin/tag-vocabularies/*') ? 'active' : ''}}">
            <a href="{{route('admin.tag-vocabularies.index')}}" class="menu-link">Tag Vocabularies</a>
        </li>
        <li class="menu-item {{Request::is('admin/v2/grades/*') ? 'active' : ''}}">
            <a href="{{route('admin.v2.grades.index')}}" class="menu-link">Homework Result</a>
        </li>
    </ul>
</li>
