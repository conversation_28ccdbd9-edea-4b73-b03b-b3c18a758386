@extends('web.layouts.commonMaster')
@section('title', 'Messages')

@section('vendor-style')
@endsection

@section('vendor-script')
@endsection

@push('after-scripts')
  <script>
    function redirectToMessage() {
      var selectedValue = document.getElementById("listMessage").value;
      window.location.href = "{{ route('web.messages.show', ':id') }}".replace(':id', selectedValue);
    }
  </script>
@endpush

@section('content')
  @include('web.layouts.navbar')
  <div class="box__content--main transition page-student">
    @include('web.layouts.header',['title'=> 'Message'])
    <main class="pt-[57px] min-h-screen bg-neutral-50 page-message">
      <div class="flex flex-col lg:flex-row min-h-[calc(100vh-57px)]">
        @if(count($messages))
          <div class="hidden lg:block border-r border-r-neutral-100 bg-white menu__tabs" style="max-height: 850px; overflow-y: auto; scrollbar-width: thin;">
            <ul>
              @foreach($messages as $message)
                <li @if(request()->is('messages/' . $message->message->id)) style="font-weight: 400;background-color: #E8F9F3;" @endif>
                  <a href="{{route('web.messages.show',$message->message->id)}}">
                    <div class="p-4 border-b-neutral-100 border-b">
                      <p class="font-semibold text-14-20 line-clamp-1 w-[208px]">{{$message->message->name}}</p>
                      <div class="text-12-16 text-[#494F5B] mb-2 line-clamp-1 w-[208px]">
                        {!! $message->message->content !!}
                      </div>
                      <div class="flex justify-between items-center">
                        <div class="text-12-16 text-[#494F5B]">
                          {{ $message->message->updated_at->format('Y/m/d H:i A') }}
                        </div>
                        <div class="text-12-16 text-white bg-green rounded-full px-1.5">{{$message->is_read == 0 ? 1 : ''}}</div>
                      </div>
                    </div>
                  </a>
                </li>
              @endforeach
            </ul>
          </div>
          <div class="bg-neutral-200 py-2 px-4 lg:hidden">
            <div class="bg-white text-16-24 rounded-lg">
              <div class="relative">
                <select name="session" class="box__select focus:outline-none cursor-pointer" single="single" id="listMessage" onchange="redirectToMessage()">
                  <option value="">Select message</option>
                  @foreach($messages as $message)
                    <option value="{{$message->message->id}}" {{$message->message->id == request()->route('id') ? 'selected' : ''}}>
                      {{$message->message->name}}
                    </option>
                  @endforeach
                </select>
              </div>
            </div>
          </div>
          <div class="w-full message__tab-content bg-neutral-100 flex-1">
            <div class="content h-full" id="listMessageItem">
              <div class="font-semibold p-4 text-16-24 bg-white text-dark hidden lg:block">{{isset($me) ? $me->message->name : ''}}</div>
              <div class="p-4 lg:py-8 xl:max-w-[780px] mx-auto">
                <div class="flex gap-y-4 flex-col lg:gap-y-8 xl:w-full">
                  @if(isset($me))
                    <div>
                      <div class="flex px-4 py-2 bg-neutral-50 rounded-t-lg items-center gap-x-2">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" viewBox="0 0 16 16" fill="none" style="width: 16px; height: 16px;">
                          <g clip-path="url(#clip0_78_14843)">
                            <path d="M9.3334 0.666504H6.66673C6.30007 0.666504 6.00007 0.966504 6.00007 1.33317C6.00007 1.69984 6.30007 1.99984 6.66673 1.99984H9.3334C9.70007 1.99984 10.0001 1.69984 10.0001 1.33317C10.0001 0.966504 9.70007 0.666504 9.3334 0.666504ZM8.00007 9.33317C8.36673 9.33317 8.66673 9.03317 8.66673 8.6665V5.99984C8.66673 5.63317 8.36673 5.33317 8.00007 5.33317C7.6334 5.33317 7.3334 5.63317 7.3334 5.99984V8.6665C7.3334 9.03317 7.6334 9.33317 8.00007 9.33317ZM12.6867 4.9265L13.1867 4.4265C13.4401 4.17317 13.4467 3.75317 13.1867 3.49317L13.1801 3.4865C12.9201 3.2265 12.5067 3.23317 12.2467 3.4865L11.7467 3.9865C10.7134 3.15984 9.4134 2.6665 8.00007 2.6665C4.80007 2.6665 2.08007 5.3065 2.00007 8.5065C1.9134 11.8932 4.62673 14.6665 8.00007 14.6665C11.3201 14.6665 14.0001 11.9798 14.0001 8.6665C14.0001 7.25317 13.5067 5.95317 12.6867 4.9265ZM8.00007 13.3332C5.42007 13.3332 3.3334 11.2465 3.3334 8.6665C3.3334 6.0865 5.42007 3.99984 8.00007 3.99984C10.5801 3.99984 12.6667 6.0865 12.6667 8.6665C12.6667 11.2465 10.5801 13.3332 8.00007 13.3332Z" fill="#575F6B"/>
                          </g>
                          <defs>
                            <clipPath id="clip0_78_14843">
                              <rect width="16" height="16" fill="white"/>
                            </clipPath>
                          </defs>
                        </svg>
                        <div class="font-medium text-12-16 text-neutral-600">{{isset($me->message) ? $me->message->updated_at->format('Y년 m월 d일') : '' }}</div>
                      </div>
                      <div class="p-4 bg-white rounded-b-lg">
                        <div class="font-medium text-14-20 text-neutral-800 mb-5">
                          {{$me->message->name}}
                        </div>
                        <div style="word-wrap: break-word;" class="text-edited">
                          {!! $me->message->content !!}
                        </div>
                      </div>
                    </div>
                  @endif
                </div>
              </div>
            </div>
          </div>
        @else
          <div class="w-full max-w-[1320px] mx-auto p-4 lg:py-8 lg:px-[60px]" style="border-radius: 6px">
            <div class="bg-white px-4 py-[48px] flex items-center justify-center flex-col">
              <div class="max-w-full w-[275px] m-auto mb-6">
                <svg xmlns="http://www.w3.org/2000/svg" width="276" height="200" viewBox="0 0 276 200" fill="none">
                  <g clip-path="url(#clip0_1557_18604)">
                    <path d="M272.188 175.76H3.81055V175.894H272.188V175.76Z" fill="#EBEDF2"/>
                    <path d="M245.295 184.397H227.518V184.531H245.295V184.397Z" fill="#EBEDF2"/>
                    <path d="M220.762 184.397H182.755V184.531H220.762V184.397Z" fill="#EBEDF2"/>
                    <path d="M161.792 179.415H151.492V179.55H161.792V179.415Z" fill="#EBEDF2"/>
                    <path d="M50.3741 180.317H31.9688V180.451H50.3741V180.317Z" fill="#EBEDF2"/>
                    <path d="M88.619 180.317H55.1523V180.451H88.619V180.317Z" fill="#EBEDF2"/>
                    <path d="M124.656 182.582H104.898V182.717H124.656V182.582Z" fill="#EBEDF2"/>
                    <path d="M131.027 151.821H27.3793C25.6885 151.821 24.3145 150.447 24.3145 148.756V3.06487C24.3145 1.37409 25.6885 0 27.3793 0H131.027C132.717 0 134.091 1.37409 134.091 3.06487V148.756C134.091 150.447 132.717 151.821 131.027 151.821ZM27.3793 0.134189C25.7637 0.134189 24.4486 1.44924 24.4486 3.06487V148.756C24.4486 150.372 25.7637 151.687 27.3793 151.687H131.027C132.642 151.687 133.957 150.372 133.957 148.756V3.06487C133.957 1.44924 132.642 0.134189 131.027 0.134189H27.3793Z" fill="#EBEDF2"/>
                    <path d="M247.125 151.821H143.478C141.787 151.821 140.413 150.447 140.413 148.756V3.06487C140.413 1.37409 141.787 0 143.478 0H247.125C248.816 0 250.19 1.37409 250.19 3.06487V148.756C250.19 150.447 248.816 151.821 247.125 151.821ZM143.478 0.134189C141.862 0.134189 140.547 1.44924 140.547 3.06487V148.756C140.547 150.372 141.862 151.687 143.478 151.687H247.125C248.741 151.687 250.056 150.372 250.056 148.756V3.06487C250.056 1.44924 248.741 0.134189 247.125 0.134189H143.478Z" fill="#EBEDF2"/>
                    <path d="M44.5218 74.217L117.359 74.217L117.359 11.1108L44.5218 11.1108L44.5218 74.217Z" fill="#E2E4EB"/>
                    <path d="M42.8939 74.2171L115.731 74.2171L115.731 11.1109L42.8939 11.1109L42.8939 74.2171Z" fill="#F7F7FA"/>
                    <path d="M112.827 71.9627V13.3653L45.8026 13.3653V71.9627H112.827Z" fill="#F7F7FA"/>
                    <path d="M85.9466 71.9627L98.0933 13.3653H72.6834L60.542 71.9627H85.9466Z" fill="white"/>
                    <path d="M47.0264 71.9627L47.0264 13.3653H45.8026V71.9627H47.0264Z" fill="#E2E4EB"/>
                    <path d="M44.0974 61.4584L114.53 61.4584V58.4311L44.0974 58.4311V61.4584Z" fill="#F7F7FA"/>
                    <path d="M113.975 172.561H125.606V122.122H113.975V172.561Z" fill="#EBEDF2"/>
                    <path d="M38.0199 175.76H123.38V172.567H38.0199V175.76Z" fill="#EBEDF2"/>
                    <path d="M113.976 122.122H35.7812V172.561H113.976V122.122Z" fill="#F7F7FA"/>
                    <path d="M109.409 128.333H40.3555V144.875H109.409V128.333Z" fill="#EBEDF2"/>
                    <path d="M109.409 149.808H40.3555V166.351H109.409V149.808Z" fill="#EBEDF2"/>
                    <path d="M56.2693 137.737H93.4878C94.1104 137.737 94.6204 137.227 94.6204 136.604C94.6204 135.981 94.1104 135.471 93.4878 135.471H56.2693C55.6466 135.471 55.1367 135.981 55.1367 136.604C55.1367 137.227 55.6466 137.737 56.2693 137.737Z" fill="#F7F7FA"/>
                    <path d="M56.2693 159.217H93.4878C94.1104 159.217 94.6204 158.708 94.6204 158.085C94.6204 157.462 94.1104 156.952 93.4878 156.952H56.2693C55.6466 156.952 55.1367 157.462 55.1367 158.085C55.1367 158.708 55.6466 159.217 56.2693 159.217Z" fill="#F7F7FA"/>
                    <path d="M235.487 44.0944C242.111 42.2694 244.376 30.2515 227.64 25.3456C231.333 37.4279 223.941 47.2774 235.487 44.0944Z" fill="#EBEDF2"/>
                    <path d="M236.951 49.3117C242.168 43.6704 237.236 30.9064 218.428 36.1236C229.066 45.4309 227.853 59.1558 236.951 49.3117Z" fill="#D4D9DF"/>
                    <path d="M231.799 50.7555C227.43 46.4293 233.688 31.3733 248.116 38.3511C239.942 46.1663 239.421 58.2969 231.799 50.7555Z" fill="#F7F7FA"/>
                    <path d="M227.44 59.1879H240.156L241.116 50.5838H226.484L227.44 59.1879Z" fill="#E2E4EB"/>
                    <path d="M226.34 52.1242H241.261C241.787 52.1242 242.212 51.7002 242.212 51.1742C242.212 50.6482 241.787 50.2241 241.261 50.2241H226.34C225.814 50.2241 225.39 50.6482 225.39 51.1742C225.39 51.7002 225.814 52.1242 226.34 52.1242Z" fill="#E2E4EB"/>
                    <path d="M120.457 161.187V146.614H132.593V132.042H144.734V117.474H156.87V102.901H169.012V88.3283H181.153V73.7608H193.289V59.1879H205.43H272.192V175.76H120.457H108.315V161.187H120.457Z" fill="#F7F7FA"/>
                    <path d="M187.215 161.187V146.614H199.357V132.042H211.493V117.474H223.634V102.901H235.77V88.3283H247.911V73.7608H260.047V59.1879H272.189V73.7608V175.76H187.215H175.079V161.187H187.215Z" fill="#D4D9DF"/>
                    <path d="M175.079 161.187H107.599V165.557H175.079V161.187Z" fill="#EBEDF2"/>
                    <path d="M187.222 146.615H119.741V150.984H187.222V146.615Z" fill="#EBEDF2"/>
                    <path d="M199.357 132.042H131.876V136.411H199.357V132.042Z" fill="#EBEDF2"/>
                    <path d="M211.499 117.474H144.019V121.843H211.499V117.474Z" fill="#EBEDF2"/>
                    <path d="M223.634 102.901H156.153V107.27H223.634V102.901Z" fill="#EBEDF2"/>
                    <path d="M235.777 88.3283H168.296V92.6975H235.777V88.3283Z" fill="#EBEDF2"/>
                    <path d="M247.911 73.7609H180.431V78.13H247.911V73.7609Z" fill="#EBEDF2"/>
                    <path d="M260.054 59.1879H192.573V63.5571H260.054V59.1879Z" fill="#EBEDF2"/>
                    <path d="M137.999 200C195.476 200 242.07 197.28 242.07 193.924C242.07 190.568 195.476 187.848 137.999 187.848C80.522 187.848 33.9277 190.568 33.9277 193.924C33.9277 197.28 80.522 200 137.999 200Z" fill="#F7F7FA"/>
                    <path d="M183.459 55.6453C183.368 57.2556 183.18 58.7317 182.971 60.2614C182.762 61.7804 182.477 63.2887 182.15 64.7916C181.516 67.7975 180.641 70.7818 179.439 73.6856L178.956 74.7699L178.833 75.0383L178.774 75.1725C178.752 75.2154 178.736 75.253 178.693 75.3442C178.543 75.6448 178.35 75.9669 178.076 76.2728C177.802 76.5788 177.453 76.8793 177.051 77.0833C176.653 77.2926 176.213 77.4268 175.827 77.4698C175.027 77.5664 174.404 77.4215 173.878 77.2658C172.842 76.9276 172.096 76.4607 171.388 75.983C170.695 75.4945 170.073 74.9792 169.482 74.4532C167.153 72.3062 165.21 70.0089 163.417 67.5076L165.199 66.0047C166.246 66.9977 167.335 67.9961 168.425 68.9515C169.509 69.9123 170.636 70.8194 171.764 71.6353C172.327 72.0432 172.896 72.4189 173.46 72.7302C174.007 73.0415 174.581 73.2992 174.973 73.3743C175.161 73.4119 175.29 73.3904 175.231 73.3743C175.177 73.3475 174.941 73.4602 174.887 73.5246C174.855 73.5515 174.855 73.5515 174.866 73.5193L175.038 73.0684L175.413 72.0915C176.342 69.4453 177.045 66.6703 177.587 63.8469C177.866 62.4406 178.07 61.0075 178.28 59.5851C178.484 58.1735 178.645 56.6974 178.768 55.3394L183.465 55.6346L183.459 55.6453Z" fill="#FFC3BD"/>
                    <path d="M180.208 49.741C178.206 50.2509 176.811 58.4847 176.811 58.4847L182.323 62.2313C182.323 62.2313 187.61 53.9277 186.07 51.727C184.465 49.4351 183.128 49.0003 180.208 49.741Z" fill="#10C285"/>
                    <path d="M180.206 49.741C178.204 50.2509 176.809 58.4847 176.809 58.4847L182.321 62.2313C182.321 62.2313 187.608 53.9277 186.068 51.727C184.463 49.4351 183.126 49.0003 180.206 49.741Z" fill="#10C285"/>
                    <path opacity="0.4" d="M180.206 49.741C178.204 50.2509 176.809 58.4847 176.809 58.4847L182.321 62.2313C182.321 62.2313 187.608 53.9277 186.068 51.727C184.463 49.4351 183.126 49.0003 180.206 49.741Z" fill="black"/>
                    <path opacity="0.3" d="M180.387 51.5767L178.852 59.8749L181.949 61.979L180.387 51.5767Z" fill="black"/>
                    <path d="M165.289 65.6612L162.665 61.5175L161.178 66.2248C161.178 66.2248 163.33 68.4792 165.273 67.9424L165.289 65.6612Z" fill="#FFC3BD"/>
                    <path d="M158.823 60.1487L157.792 64.1583L161.179 66.2302L162.666 61.5228L158.823 60.1487Z" fill="#FFC3BD"/>
                    <path d="M191.028 188.293H186.176L185.854 177.054H190.706L191.028 188.293Z" fill="#FFC3BD"/>
                    <path d="M233.856 165.75L230.158 168.89L221.693 161.338L225.392 158.192L233.856 165.75Z" fill="#FFC3BD"/>
                    <path d="M229.141 168.514L232.727 164.408C232.856 164.258 233.065 164.225 233.231 164.333L236.887 166.705C237.268 166.952 237.332 167.521 237.026 167.859C235.749 169.271 235.062 169.888 233.484 171.692C232.512 172.803 231.208 174.526 229.871 176.061C228.562 177.564 226.941 176.351 227.365 175.594C229.249 172.191 228.798 170.994 228.83 169.324C228.835 169.024 228.948 168.734 229.141 168.519V168.514Z" fill="#263238"/>
                    <path d="M186.01 187.735H191.464C191.662 187.735 191.823 187.875 191.85 188.068L192.473 192.384C192.537 192.829 192.151 193.253 191.694 193.248C189.794 193.215 188.877 193.103 186.477 193.103C185.001 193.103 181.952 193.258 179.913 193.258C177.873 193.258 177.766 191.246 178.614 191.063C182.419 190.247 183.917 189.115 185.194 188.041C185.425 187.848 185.715 187.741 186.005 187.741L186.01 187.735Z" fill="#263238"/>
                    <path opacity="0.2" d="M185.852 177.059L186.018 182.851H190.87L190.704 177.059H185.852Z" fill="black"/>
                    <path opacity="0.2" d="M225.394 158.198L221.69 161.338L226.06 165.234L229.758 162.089L225.394 158.198Z" fill="black"/>
                    <path d="M180.207 49.741C180.207 49.741 177.893 50.5569 182.525 79.0371H202.235C201.902 71.0126 201.897 66.0691 205.729 49.6015C205.729 49.6015 201.602 48.7051 197.361 48.5011C194.044 48.3401 191.328 48.2327 188.408 48.5011C184.565 48.85 180.207 49.7464 180.207 49.7464V49.741Z" fill="#10C285"/>
                    <g opacity="0.4">
                      <path d="M180.207 49.741C180.207 49.741 177.893 50.5569 182.525 79.0371H202.235C201.902 71.0126 201.897 66.0691 205.729 49.6015C205.729 49.6015 201.602 48.7051 197.361 48.5011C194.044 48.3401 191.328 48.2327 188.408 48.5011C184.565 48.85 180.207 49.7464 180.207 49.7464V49.741Z" fill="black"/>
                      <path d="M180.207 49.741C180.207 49.741 177.893 50.5569 182.525 79.0371H202.235C201.902 71.0126 201.897 66.0691 205.729 49.6015C205.729 49.6015 201.602 48.7051 197.361 48.5011C194.044 48.3401 191.328 48.2327 188.408 48.5011C184.565 48.85 180.207 49.7464 180.207 49.7464V49.741Z" fill="url(#paint0_linear_1557_18604)"/>
                    </g>
                    <path d="M197.36 48.4958C194.043 48.3401 191.327 48.2328 188.407 48.5011C187.924 48.5441 187.43 48.5978 186.942 48.6568C186.614 49.2204 186.368 49.9772 186.674 50.7502C187.302 52.3121 189.567 52.4946 191.22 52.4946C197.323 52.4946 198.353 48.9306 198.396 48.7803L198.455 48.5656C198.09 48.5387 197.725 48.5119 197.36 48.4958Z" fill="white"/>
                    <path d="M196.476 38.4155C195.912 41.2979 195.348 46.5742 197.361 48.4958C197.361 48.4958 196.572 51.4157 191.221 51.4157C185.338 51.4157 188.408 48.4958 188.408 48.4958C191.618 47.7282 191.537 45.345 190.979 43.1068L196.476 38.4155Z" fill="#FFC3BD"/>
                    <path opacity="0.2" d="M194.234 40.3317L190.981 43.1067C191.116 43.6381 191.218 44.1749 191.255 44.6955C192.484 44.5184 194.17 43.1712 194.299 41.8883C194.363 41.2496 194.336 40.6323 194.234 40.3317Z" fill="black"/>
                    <path d="M193.128 22.753C192.629 21.0676 192.2 18.4 195.211 17.2245C194.035 15.786 193.751 13.0807 195.195 11.4758C196.639 9.87095 198.845 10.4614 198.845 10.4614C198.845 10.4614 199.489 6.5377 204.261 7.22475C209.032 7.9118 206.907 11.0679 206.907 11.0679C206.907 11.0679 213.418 9.7797 213.852 15.9148C214.287 22.0499 210.551 21.2931 210.551 21.2931C210.551 21.2931 213.455 23.7299 211.85 26.4083C210.245 29.0868 206.52 28.1635 206.52 28.1635C206.52 28.1635 204.77 32.8977 200.648 30.2032C196.526 27.5087 193.134 22.7584 193.134 22.7584L193.128 22.753Z" fill="#263238"/>
                    <path d="M183.459 26.3171C181.843 28.3943 182.557 30.756 183.979 31.6631C187.441 33.8746 192.449 24.6048 191.145 23.4937C189.841 22.3827 185.069 24.2398 183.453 26.3171H183.459Z" fill="#263238"/>
                    <path d="M198 21.3682C195.724 20.4504 193.416 21.4111 192.697 22.796C190.941 26.1722 198.912 32.2536 201.22 28.0347C202.589 25.528 200.351 22.3183 198 21.3682Z" fill="#10C285"/>
                    <path d="M197.03 31.6793C197.196 36.4939 197.368 38.5282 195.183 41.2174C191.904 45.2591 185.951 44.4111 184.352 39.7413C182.913 35.5385 182.886 28.3729 187.384 26.0219C191.818 23.7031 196.858 26.8699 197.025 31.6846L197.03 31.6793Z" fill="#FFC3BD"/>
                    <path d="M196.316 34.2181C194.185 33.4183 193.627 30.9117 193.982 29.9455C192.919 29.9026 190.557 29.5161 189.677 28.185C188.002 29.237 184.465 30.3266 184.078 28.55C183.692 26.768 189.038 22.1626 193.069 21.8137C197.868 21.395 202.602 26.1131 201.378 30.6219C200.154 35.1306 196.311 34.2181 196.311 34.2181H196.316Z" fill="#263238"/>
                    <path d="M182.527 79.0317C182.527 79.0317 178.7 116.422 179.017 131.22C179.349 146.614 184.991 181.729 184.991 181.729H191.555C191.555 181.729 190.514 147.516 191.099 132.396C191.732 115.912 196.966 79.0317 196.966 79.0317H182.532H182.527Z" fill="#263238"/>
                    <path d="M192.118 181.965H184.421L183.852 179.507L192.477 179.206L192.118 181.965Z" fill="#10C285"/>
                    <path opacity="0.2" d="M191.027 88.3336C186.185 98.0757 190.055 115.842 191.767 122.45C192.648 112.52 194.124 100.389 195.278 91.4736C194.542 86.4335 193.26 83.841 191.027 88.3336Z" fill="black"/>
                    <path d="M187.733 79.0317C187.733 79.0317 192.199 117.903 195.57 130.378C199.574 145.192 224.442 165.132 224.442 165.132L229.488 160.849C229.488 160.849 211.463 139.191 208.404 131.387C201.872 114.731 207.464 89.9064 202.242 79.0317H187.733Z" fill="#263238"/>
                    <path d="M230.292 160.618L224.034 165.938L221.693 164.537L228.773 157.951L230.292 160.618Z" fill="#10C285"/>
                    <path d="M198.167 35.0286C198.049 36.0216 197.49 36.8643 196.835 37.3635C195.853 38.115 194.908 37.3474 194.796 36.1827C194.694 35.136 195.086 33.4828 196.256 33.1876C197.404 32.8977 198.295 33.8907 198.167 35.0233V35.0286Z" fill="#FFC3BD"/>
                    <path d="M207.741 54.8348C208.858 57.5508 209.77 60.2346 210.57 63.0257C211.354 65.8114 212.035 68.6401 212.352 71.6352C212.443 72.3759 212.47 73.1489 212.508 73.9164V73.9862V74.1043V74.3512C212.491 74.5069 212.475 74.6625 212.454 74.8074C212.411 75.0973 212.347 75.3603 212.271 75.6018C212.126 76.0903 211.939 76.5036 211.745 76.8954C210.951 78.4198 210.006 79.5792 209.024 80.701C207.033 82.9017 204.918 84.8072 202.599 86.5624L201.026 84.8394C202.814 82.7568 204.623 80.6097 206.147 78.4198C206.898 77.3355 207.618 76.1976 208.026 75.1778C208.122 74.9255 208.197 74.6893 208.235 74.4961C208.251 74.3995 208.267 74.319 208.267 74.2599C208.267 74.2277 208.267 74.2116 208.267 74.1902C208.267 74.1794 208.267 74.1794 208.262 74.1741V74.1043C208.181 73.4655 208.133 72.8375 208.015 72.1881C207.612 69.6009 206.877 66.9655 206.088 64.3568C205.267 61.7589 204.349 59.1235 203.383 56.6115L207.741 54.8294V54.8348Z" fill="#FFC3BD"/>
                    <path d="M205.729 49.6014C207.704 50.2026 210.076 57.0033 210.076 57.0033L202.417 62.4835C202.417 62.4835 199.621 56.8047 200.452 54.2551C201.317 51.5981 203.48 48.9197 205.734 49.6068L205.729 49.6014Z" fill="#10C285"/>
                    <path opacity="0.4" d="M205.729 49.6014C207.704 50.2026 210.076 57.0033 210.076 57.0033L202.417 62.4835C202.417 62.4835 199.621 56.8047 200.452 54.2551C201.317 51.5981 203.48 48.9197 205.734 49.6068L205.729 49.6014Z" fill="black"/>
                    <path d="M227.817 170.86C227.629 170.86 227.458 170.774 227.302 170.597C227.173 170.446 227.179 170.307 227.211 170.21C227.388 169.62 229.079 169.131 229.267 169.078C229.309 169.067 229.358 169.078 229.385 169.115C229.411 169.153 229.417 169.201 229.395 169.239C229.095 169.808 228.499 170.779 227.882 170.849C227.855 170.849 227.833 170.849 227.812 170.849L227.817 170.86ZM229.063 169.384C228.392 169.598 227.527 169.969 227.431 170.28C227.42 170.318 227.415 170.377 227.479 170.446C227.597 170.586 227.726 170.64 227.866 170.629C228.198 170.591 228.639 170.13 229.063 169.384Z" fill="#10C285"/>
                    <path d="M228.53 169.4C227.971 169.4 227.343 169.287 227.123 169.008C227.059 168.922 226.973 168.745 227.134 168.503C227.236 168.353 227.386 168.262 227.569 168.24C228.267 168.149 229.324 169.067 229.367 169.104C229.399 169.131 229.41 169.174 229.405 169.212C229.399 169.249 229.367 169.287 229.329 169.298C229.147 169.362 228.846 169.4 228.524 169.4H228.53ZM227.676 168.466C227.676 168.466 227.628 168.466 227.601 168.466C227.483 168.482 227.392 168.535 227.327 168.632C227.247 168.756 227.274 168.82 227.306 168.863C227.515 169.131 228.465 169.233 229.05 169.131C228.701 168.863 228.095 168.46 227.676 168.46V168.466Z" fill="#10C285"/>
                    <path d="M184.186 188.583C183.703 188.583 183.258 188.508 183.011 188.283C182.855 188.138 182.78 187.95 182.801 187.724C182.812 187.585 182.882 187.483 182.995 187.418C183.59 187.091 185.523 188.079 185.743 188.197C185.786 188.218 185.813 188.267 185.802 188.315C185.797 188.363 185.759 188.401 185.711 188.411C185.297 188.492 184.718 188.583 184.181 188.583H184.186ZM183.322 187.58C183.231 187.58 183.161 187.59 183.107 187.622C183.059 187.649 183.038 187.687 183.032 187.746C183.016 187.902 183.064 188.02 183.166 188.116C183.462 188.379 184.261 188.428 185.335 188.25C184.669 187.928 183.784 187.585 183.322 187.585V187.58Z" fill="#10C285"/>
                    <path d="M185.693 188.411C185.693 188.411 185.661 188.412 185.645 188.401C185.065 188.138 183.922 187.091 184.013 186.544C184.034 186.415 184.126 186.254 184.442 186.222C184.679 186.2 184.893 186.264 185.081 186.42C185.693 186.93 185.806 188.234 185.806 188.288C185.806 188.331 185.79 188.369 185.757 188.39C185.736 188.406 185.714 188.411 185.693 188.411ZM184.534 186.442C184.534 186.442 184.485 186.442 184.464 186.442C184.255 186.463 184.238 186.549 184.233 186.576C184.179 186.909 184.958 187.73 185.543 188.084C185.489 187.703 185.323 186.919 184.925 186.587C184.807 186.485 184.673 186.436 184.528 186.436L184.534 186.442Z" fill="#10C285"/>
                    <path d="M202.531 77.8776L203.035 79.6436C203.105 79.7831 202.944 79.9227 202.713 79.9227H182.424C182.247 79.9227 182.097 79.8368 182.086 79.7241L181.909 77.9582C181.898 77.8347 182.048 77.7327 182.247 77.7327H202.209C202.354 77.7327 202.488 77.7918 202.531 77.883V77.8776Z" fill="#10C285"/>
                    <path opacity="0.3" d="M202.531 77.8776L203.035 79.6436C203.105 79.7831 202.944 79.9227 202.713 79.9227H182.424C182.247 79.9227 182.097 79.8368 182.086 79.7241L181.909 77.9582C181.898 77.8347 182.048 77.7327 182.247 77.7327H202.209C202.354 77.7327 202.488 77.7918 202.531 77.883V77.8776Z" fill="white"/>
                    <path d="M199.852 80.1106H200.388C200.496 80.1106 200.576 80.0569 200.571 79.9872L200.318 77.6952C200.313 77.6308 200.217 77.5718 200.109 77.5718H199.572C199.465 77.5718 199.385 77.6255 199.39 77.6952L199.642 79.9872C199.648 80.0516 199.744 80.1106 199.852 80.1106Z" fill="#263238"/>
                    <path d="M183.933 80.1106H184.469C184.577 80.1106 184.657 80.0569 184.652 79.9871L184.4 77.6952C184.394 77.6308 184.298 77.5717 184.19 77.5717H183.653C183.546 77.5717 183.466 77.6254 183.471 77.6952L183.723 79.9871C183.729 80.0516 183.825 80.1106 183.933 80.1106Z" fill="#263238"/>
                    <path d="M191.894 80.1106H192.43C192.538 80.1106 192.618 80.0569 192.613 79.9871L192.36 77.6952C192.355 77.6308 192.259 77.5717 192.151 77.5717H191.614C191.507 77.5717 191.427 77.6254 191.432 77.6952L191.684 79.9871C191.69 80.0516 191.786 80.1106 191.894 80.1106Z" fill="#263238"/>
                    <path opacity="0.2" d="M171.717 101.414C146.328 111.28 117.741 98.6984 107.87 73.3045C97.9987 47.9107 110.591 19.3232 135.979 9.45762C161.373 -0.413295 189.961 12.1682 199.832 37.5674C209.703 62.9613 197.11 91.5488 171.717 101.414Z" fill="url(#paint1_linear_1557_18604)"/>
                    <path opacity="0.1" d="M162.788 6.91876L124.099 94.7908C123.503 94.34 122.913 93.873 122.333 93.3953C118.645 90.3465 115.382 86.7341 112.677 82.6172L146.09 6.7148C151.71 5.80768 157.356 5.90967 162.788 6.91876Z" fill="white"/>
                    <path opacity="0.1" d="M190.292 22.168L153.916 104.774C146.294 104.785 138.839 103.019 132.124 99.7182L171.844 9.51132C178.816 12.238 185.15 16.5428 190.292 22.168Z" fill="white"/>
                    <path d="M113.241 22.2055C131.593 -0.220053 164.657 -3.52646 187.083 14.8252C209.508 33.1768 212.809 66.2355 194.458 88.6611C176.101 111.092 143.042 114.393 120.616 96.0415C98.1907 77.6898 94.8897 44.6311 113.247 22.2002L113.241 22.2055ZM191.135 85.9505C207.984 65.3606 204.956 35.0018 184.361 18.1477C163.766 1.29359 133.413 4.33162 116.558 24.9215C99.7043 45.5168 102.732 75.8756 123.327 92.7297C143.922 109.584 174.276 106.546 191.135 85.9505Z" fill="#10C285"/>
                    <path d="M195.768 85.768C200.937 88.8543 205.854 92.2466 210.685 95.7463C215.516 99.2459 220.223 102.896 224.861 106.632C229.498 110.367 234.039 114.221 238.424 118.263C240.62 120.281 242.772 122.353 244.881 124.479C246.986 126.61 249.057 128.778 251 131.102C252.074 132.38 251.902 134.291 250.625 135.359C249.648 136.18 248.295 136.271 247.238 135.697C244.576 134.248 242.042 132.648 239.535 131.006C237.034 129.358 234.576 127.662 232.166 125.906C227.335 122.407 222.66 118.719 218.081 114.914C213.503 111.108 208.994 107.217 204.609 103.175C200.224 99.1332 195.924 94.9841 191.877 90.529C190.723 89.2569 190.814 87.2924 192.086 86.133C193.122 85.1883 194.625 85.0809 195.774 85.7626L195.768 85.768Z" fill="#10C285"/>
                    <path d="M201.542 84.3026L196.357 85.0916L200.072 88.3444C200.072 88.3444 203.002 87.2816 203.292 85.2849L201.548 84.3026H201.542Z" fill="#FFC3BD"/>
                    <path d="M193.556 88.06L196.814 90.6203L200.791 88.0224L196.358 85.0917L193.556 88.06Z" fill="#FFC3BD"/>
                    <path d="M169.041 93.7173C169.969 91.1033 172.578 89.2461 175.326 89.2461H218.293C221.551 89.2461 223.886 91.8279 223.51 95.0055L212.416 188.159C212.035 191.342 209.088 193.919 205.824 193.919H69.4727C66.2146 193.919 63.8798 191.337 64.2555 188.159L73.5735 109.911C73.9546 106.728 76.9014 104.152 80.1649 104.152H154.36C160.785 104.152 166.872 99.8201 169.041 93.7119V93.7173Z" fill="#10C285"/>
                    <path d="M169.041 93.7173C169.969 91.1033 172.578 89.2461 175.326 89.2461H218.293C221.551 89.2461 223.886 91.8279 223.51 95.0055L212.416 188.159C212.035 191.342 209.088 193.919 205.824 193.919H69.4727C66.2146 193.919 63.8798 191.337 64.2555 188.159L73.5735 109.911C73.9546 106.728 76.9014 104.152 80.1649 104.152H154.36C160.785 104.152 166.872 99.8201 169.041 93.7119V93.7173Z" fill="url(#paint2_linear_1557_18604)"/>
                    <path d="M66.7354 189.635H209.19L217.306 93.7763H74.8511L66.7354 189.635Z" fill="#EBEDF2"/>
                    <path d="M66.7354 189.635H209.19L215.062 93.7763H74.8511L66.7354 189.635Z" fill="#D4D9DF"/>
                    <path d="M66.7354 189.635H209.19L214.085 90.7383H71.6306L66.7354 189.635Z" fill="#EBEDF2"/>
                    <path d="M66.7354 189.635H209.19L211.128 90.7383H71.6306L66.7354 189.635Z" fill="#D4D9DF"/>
                    <path d="M66.7354 189.635H209.19L210.038 88.0545H67.5834L66.7354 189.635Z" fill="#EBEDF2"/>
                    <path d="M66.736 189.635H209.191L206.265 89.9761H64.7285L66.736 189.635Z" fill="#D4D9DF"/>
                    <path d="M66.7365 189.635H209.191L204.961 90.7383H62.5068L66.7365 189.635Z" fill="#EBEDF2"/>
                    <path d="M66.7372 189.635H209.192L202.456 92.2304H60.001L66.7372 189.635Z" fill="#D4D9DF"/>
                    <path d="M66.7365 189.635H209.191L201.381 93.0624H58.9268L66.7365 189.635Z" fill="#EBEDF2"/>
                    <path d="M138.07 99.7665C138.162 97.0881 140.18 95.188 142.928 95.188H185.895C189.153 95.188 192.315 97.8288 192.948 101.092L209.92 188.025C210.559 191.283 208.428 193.929 205.17 193.929H68.8292C65.5711 193.929 62.4096 191.288 61.7762 188.025L47.7831 116.368C47.1443 113.11 49.2752 110.464 52.5333 110.464H126.729C133.154 110.464 137.856 106.03 138.076 99.7665H138.07Z" fill="#10C285"/>
                    <path opacity="0.2" d="M138.07 99.7665C138.162 97.0881 140.18 95.188 142.928 95.188H185.895C189.153 95.188 192.315 97.8288 192.948 101.092L209.92 188.025C210.559 191.283 208.428 193.929 205.17 193.929H68.8292C65.5711 193.929 62.4096 191.288 61.7762 188.025L47.7831 116.368C47.1443 113.11 49.2752 110.464 52.5333 110.464H126.729C133.154 110.464 137.856 106.03 138.076 99.7665H138.07Z" fill="url(#paint3_linear_1557_18604)"/>
                  </g>
                  <defs>
                    <linearGradient id="paint0_linear_1557_18604" x1="189.511" y1="55.3493" x2="203.028" y2="65.2208" gradientUnits="userSpaceOnUse">
                      <stop stop-color="#125D58"/>
                      <stop offset="1" stop-color="#131F3B"/>
                    </linearGradient>
                    <linearGradient id="paint1_linear_1557_18604" x1="153.851" y1="6.09256" x2="153.851" y2="104.777" gradientUnits="userSpaceOnUse">
                      <stop stop-color="#10C285"/>
                      <stop offset="1" stop-color="#085C3F"/>
                    </linearGradient>
                    <linearGradient id="paint2_linear_1557_18604" x1="124.543" y1="113.153" x2="171.236" y2="174.207" gradientUnits="userSpaceOnUse">
                      <stop stop-color="#125D58"/>
                      <stop offset="1" stop-color="#131F3B"/>
                    </linearGradient>
                    <linearGradient id="paint3_linear_1557_18604" x1="128.852" y1="95.188" x2="128.852" y2="193.929" gradientUnits="userSpaceOnUse">
                      <stop stop-color="#00D595"/>
                      <stop offset="1" stop-color="#006F4E"/>
                    </linearGradient>
                    <clipPath id="clip0_1557_18604">
                      <rect width="275" height="200" fill="white" transform="translate(0.5)"/>
                    </clipPath>
                  </defs>
                </svg>
              </div>
              <div class="text-16-24 font-semibold text-dark">There no data yet</div>
            </div>
          </div>
        @endif
      </div>
    </main>
  </div>
  <div class="backdrop fixed inset-0 bg-black/[0.7] z-[51] transition opacity-0 invisible"></div>
@endsection
