@extends('web.layouts.commonMaster')
@section('title', 'Grade')

@section('vendor-style')

@endsection

@section('vendor-script')
@endsection

@push('after-scripts')
  <script>
    $(document).ready(function() {
      $('.session, .term').change(function() {
        var sessionValue = $('.session').val();
        var termValue = $('.term').val();
        var yearValue = $('.year').val();
        var studentValue = $('.student').val();
        var newUrl = '?year=' + yearValue + '&session=' + sessionValue + '&term=' + termValue;
        @if(auth('web')->check() && auth('web')->user()->type == 'parent' && count($students))
          newUrl += '&student_id=' + studentValue;
        @endif
        if(!termValue) return;
        window.location.href = newUrl;
      });
      $('.year').change(function() {
        var yearValue = $('.year').val();
        var studentValue = $('.student').val();
        if(!studentValue){
          studentValue = '';
        }
        $.ajax({
          url: window.location.origin + '/ajax-get-term/' + yearValue + '?student_id='+ studentValue,
          type: 'GET',
          success: function(response) {
            if(response.status){
              var options = '<option value="">Please select</option>';
              $.each(response.data, function(index, term) {
                options += '<option value="' + term.id + '">' + term.type + '-' + term.year + '</option>';
              });
              $('.term').html(options);
            }
          },
          error: function(xhr, status, error) {
            console.log(error);
          }
        });
      });

      $('.student').change(function() {
        window.location.href = '?student_id='+ $('.student').val();
      });
    });
  </script>
@endpush()

@section('content')
  @include('web.layouts.navbar')
  <div class="box__content--main transition page-student {{auth('web')->user()->type == 'parent' ? 'page-parent' : ''}}">
    @include('web.layouts.header',['title'=> 'Grade'])
    <main class="pt-[57px] min-h-screen bg-neutral-50 page-grade">
      <div class="bg-neutral-100">
        <div class="container py-2">
          <div class="lg:gap-[8px] lg:flex lg:justify-between">
            <div class="pb-2 lg:pb-0">
              @if(auth('web')->user()->type == 'parent' && count($students))
                <select name="student_id" class="box__select px-4 py-2 rounded-lg bg-white focus:outline-none cursor-pointer student" single="single">
                  @foreach($students as $student)
                    <option {{ $student->id == request()->student_id ? 'selected' : '' }}  value="{{$student->id}}">{{$student->name_english}}</option>
                  @endforeach
                </select>
              @endif
            </div>
            <div class="flex gap-x-2">
              <select name="session" class="box__select px-4 py-2 rounded-lg bg-white focus:outline-none cursor-pointer session" single="single">
                @for($i = 1; $i <= 16; $i++)
                  <option value="{{ $i }}" {{ $i == request()->session ? 'selected' : '' }}>Session {{ $i }}</option>
                @endfor
              </select>
              @if(count($terms))
                <select name="term" class="box__select px-4 py-2 rounded-lg bg-white focus:outline-none cursor-pointer term" single="single">
                  @foreach($terms as $term)
                    <option {{ $term->id == (request()->term ?: $termSlected) ? 'selected' : '' }} value="{{$term->id}}">{{$term->type}}-{{$term->year}}</option>
                  @endforeach
                </select>
              @endif
              @if(count($years))
                <select name="year" class="box__select px-4 py-2 rounded-lg bg-white focus:outline-none cursor-pointer year" single="single">
                  @foreach($years as $year)
                    <option {{ (request()->has('year') && $year->year == request()->year) || (!request()->has('year') && $year->year == now()->year) ? 'selected' : '' }} value="{{$year->year}}">{{$year->year}}</option>
                  @endforeach
                </select>
              @endif
            </div>
          </div>
        </div>
      </div>
      <div class="w-full max-w-[1320px] mx-auto p-4 lg:py-8 lg:px-[60px]">
        <div class="grid-cols-1 grid-rows-1 lg:grid-cols-2 gap-4 lg:gap-[34px] grid">
          @foreach($combinedGrades as $grade)
            <div class="bg-white p-4 rounded-lg">
              <div class="flex items-center justify-between border-l-4 border-green px-4 py-2 mb-6">
                <div class="text-14-20 font-semibold text-[0F1A30] mobile-strim250">
                  {{$grade[0]->classes->name}}
                </div>
                <div class="text-green font-semibold text-14-20">{{$grade[0]->classes->subject}}</div>
              </div>
              <div class="mb-4 rounded-lg border border-[#EEF0F6]">
                <div>
                  <div class="text-neutral-200 font-semibold rounded-t-lg text-12-16 bg-dark py-3 text-center">Class Participation</div>
                  <div class="flex max-w-full overflow-auto w-full flex-nowrap">
                    <div class="flex w-full items-center flex-shrink-0 xl:flex-1 xl:w-full">
                      <div class="px-2 py-2.5 w-1/3 text-14-20 text-neutral-500 text-center border-l border-neutral-100">{{$grade[0]->attendance}}</div>
                      <div class="px-2 py-2.5 w-2/3 text-14-20 font-semibold text-green text-center border-l border-neutral-100">{{$grade[0]->participation}}</div>
                    </div>
                  </div>
                  <div class="text-neutral-200 font-semibold text-12-16 bg-dark py-3 text-center">Class Work</div>
                  <div class="flex max-w-full overflow-auto w-full">
                    <div class="flex flex-col w-1/3 flex-shrink-0 xl:flex-1 xl:w-full">
                      <div class="px-2 py-3 flex-1 text-12-16 font-semibold text-neutral-500 text-center bg-neutral-100">Score</div>
                      <div class="p-2 flex-1 text-14-20 text-neutral-700 text-center border-r border-neutral-100">
                        @if ($grade[0]->score == 'notFM')
                          {{ $grade[0]->score1 . '/' . $grade[0]->score2 }}
                        @elseif($grade[0]->score == null)
                          N/A
                        @else
                          FM
                        @endif
                      </div>
                    </div>
                    <div class="flex flex-col w-1/3 flex-shrink-0 xl:flex-1 xl:w-full">
                      <div class="px-2 py-3 flex-1 text-12-16 font-semibold text-neutral-500 text-center bg-neutral-100">%</div>
                      <div class="p-2 flex-1 text-14-20 text-neutral-700 text-center border-r border-neutral-100">
                        @if ($grade[0]->score == 'notFM')
                          @if ($grade[0]->score2 != 0 && ($grade[0]->score1 * 100) % $grade[0]->score2 == 0)
                            {{ $grade[0]->score1 * 100 / $grade[0]->score2 }}%
                          @else
                            {{ number_format($grade[0]->score1 * 100 / $grade[0]->score2, 2) }}%
                          @endif
                        @else
                          N/A
                        @endif
                      </div>
                    </div>
                    <div class="flex flex-col w-1/3 flex-shrink-0 xl:flex-1 xl:w-full">
                      <div class="px-2 py-3 flex-1 text-12-16 font-semibold text-neutral-500 text-center bg-neutral-100">Grade</div>
                      <div class="p-2 flex-1 text-14-20 font-semibold text-green text-center border-r border-neutral-100">
                        @if ($grade[0]->score == 'notFM')
                          {{gradeFormat($grade[0]->score1 * 100 / $grade[0]->score2)}}
                        @else
                          {{gradeFormat(0)}}
                        @endif
                      </div>
                    </div>
                  </div>
                  <div class="text-neutral-200 font-semibold text-12-16 bg-dark py-3 text-center">Home Work</div>
                  @if($grade[0]->classes->level == 'QR5')
                    <div class="rounded-b-lg relative flex flex-col max-w-full overflow-auto w-full bg-neutral-50">
                      <div class="flex">
                        <div class="px-2 py-3 text-12-16 font-semibold text-neutral-500 text-center w-1/3 flex-shrink-0 bg-neutral-100 xl:flex-1 xl:w-full">HW1</div>
                        <div class="px-2 py-3 text-12-16 font-semibold text-neutral-500 text-center w-1/3 flex-shrink-0 bg-neutral-100 xl:flex-1 xl:w-full">%</div>
                        <div class="px-2 py-3 text-12-16 font-semibold text-neutral-500 text-center w-1/3 flex-shrink-0 bg-neutral-100 xl:flex-1 xl:w-full">Grade</div>
                      </div>
                      <div class="text-neutral-500 font-semibold sticky bottom-0 left-0 text-12-16 py-3 lg:px-[92px] text-center flex-1">Vocab HW</div>
                      <div class="flex">
                        <div class="px-2 py-2.5 text-14-20 text-neutral-700 text-center w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full border-y border-neutral-100">
                          @if(isset($grade['HW1']))
                            @if($grade['HW1']->vocab_hw == null)
                              N/A
                            @else
                            {{$grade['HW1']->vocab_hw == 'notFM' ? $grade['total_vocab_hw1'] . '/' . $grade['total_vocab_hw2'] : 'FM'}}
                            @endif
                          @endif
                        </div>
                        <div class="px-2 py-2.5 text-14-20 text-neutral-700 text-center border-l border-y border-neutral-100 w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full">
                          {{$grade['total_vocab_hw2'] > 0 ? number_format($grade['total_vocab_hw1'] / $grade['total_vocab_hw2'] * 100,2) . '%' : 'N/A' }}
                        </div>
                        <div class="px-2 py-2.5 text-14-20 font-semibold text-green text-center border-l border-y border-neutral-100 w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full">{{$grade['total_vocab_hw2'] > 0 ? gradeFormat($grade['total_vocab_hw1'] / $grade['total_vocab_hw2'] * 100) : 'N/A' }}</div>
                      </div>
                      <div class="text-neutral-500 font-semibold sticky bottom-0 left-0 text-12-16 py-3 lg:px-[92px] text-center flex-1 border-b border-neutral-100">Short Response</div>
                      <div class="text-green sticky bottom-0 left-0 font-semibold text-14-20 py-2.5 lg:px-[92px] xl:px-2 text-center bg-white">
                        @if(isset($grade['HW1']))
                          {{$grade['HW1']->short_response}}
                        @endif
                      </div>
                    </div>
                  @else
                    <div class="rounded-b-lg relative flex flex-col max-w-full overflow-auto w-full bg-neutral-50">
                      <div class="flex">
                        <div class="px-2 py-3 text-12-16 font-semibold text-neutral-500 text-center w-1/3 flex-shrink-0 bg-neutral-100 xl:flex-1 xl:w-full">HW1</div>
                        <div class="px-2 py-3 text-12-16 font-semibold text-neutral-500 text-center w-1/3 flex-shrink-0 bg-neutral-100 xl:flex-1 xl:w-full">HW2</div>
                        <div class="px-2 py-3 text-12-16 font-semibold text-neutral-500 text-center w-1/3 flex-shrink-0 bg-neutral-100 xl:flex-1 xl:w-full">HW3</div>
                        <div class="px-2 py-3 text-12-16 font-semibold text-neutral-500 text-center w-1/3 flex-shrink-0 bg-neutral-100 xl:flex-1 xl:w-full">Score</div>
                        <div class="px-2 py-3 text-12-16 font-semibold text-neutral-500 text-center w-1/3 flex-shrink-0 bg-neutral-100 xl:flex-1 xl:w-full">%</div>
                        <div class="px-2 py-3 text-12-16 font-semibold text-neutral-500 text-center w-1/3 flex-shrink-0 bg-neutral-100 xl:flex-1 xl:w-full">Grade</div>
                      </div>
                      @if($grade[0]->classes->level == 'QR1')
                        <div class="text-neutral-500 font-semibold sticky bottom-0 left-0 text-12-16 py-3 lg:px-[92px] text-center flex-1">Sentence With Context Clue</div>
                        <div class="flex">
                          <div class="px-2 py-2.5 text-14-20 text-neutral-700 text-center w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full border-y border-neutral-100">
                            @if (isset($grade['HW1']) )
                              @if ($grade['HW1']['sentence'] == 'notFM')
                                {{ $grade['HW1']['sentence1'] }} / {{ $grade['HW1']['sentence2'] }}
                              @elseif($grade['HW1']['sentence'] == null)
                                N/A
                              @else
                                FM
                              @endif
                            @else
                              0
                            @endif
                          </div>
                          <div class="px-2 py-2.5 text-14-20 text-neutral-700 text-center border-l border-y border-neutral-100 w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full">
                            @if (isset($grade['HW2']) )
                              @if ($grade['HW2']['sentence'] == 'notFM')
                                {{ $grade['HW2']['sentence1'] }} / {{ $grade['HW2']['sentence2'] }}
                              @elseif($grade['HW2']['sentence'] == null)
                                N/A
                              @else
                                FM
                              @endif
                            @else
                              0
                            @endif
                          </div>
                          <div class="px-2 py-2.5 text-14-20 text-neutral-700 text-center border-l border-y border-neutral-100 w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full">
                            @if (isset($grade['HW3']) )
                              @if ($grade['HW3']['sentence'] == 'notFM')
                                {{ $grade['HW3']['sentence1'] }} / {{ $grade['HW3']['sentence2'] }}
                              @elseif($grade['HW3']['sentence'] == null)
                                N/A
                              @else
                                FM
                              @endif
                            @else
                              0
                            @endif
                          </div>
                          <div class="px-2 py-2.5 text-14-20 text-neutral-700 text-center w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full border-y border-neutral-100">{{$grade['sentence_total1']}}/{{$grade['sentence_total2']}}</div>
                          <div class="px-2 py-2.5 text-14-20 font-semibold text-green text-center border-l border-y border-neutral-100 w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full">
                            {{ $grade['sentence_total2'] ? intval($grade['sentence_total1']*100/$grade['sentence_total2']) : 0 }}%
                          </div>
                          <div class="px-2 py-2.5 text-14-20 font-semibold text-green text-center border-l border-y border-neutral-100 w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full">{{$grade['sentence_total2'] ? gradeFormat($grade['sentence_total1']*100/$grade['sentence_total2']) : 'N/A'}}</div>
                        </div>
                        <div class="text-neutral-500 font-semibold sticky bottom-0 left-0 text-12-16 py-3 lg:px-[92px] text-center flex-1">Vocabulary Matrix 1</div>
                        <div class="flex">
                          <div class="px-2 py-2.5 text-14-20 text-neutral-700 text-center w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full border-y border-neutral-100">
                            @if (isset($grade['HW1']) )
                              @if ($grade['HW1']['vocabulary_maxtrix_1'] == 'notFM')
                                {{ $grade['HW1']['vocabulary_maxtrix_11'] }} / {{ $grade['HW1']['vocabulary_maxtrix_12'] }}
                              @elseif($grade['HW1']['vocabulary_maxtrix_1'] == null)
                                N/A
                              @else
                                FM
                              @endif
                            @else
                              0
                            @endif
                          </div>
                          <div class="px-2 py-2.5 text-14-20 text-neutral-700 text-center border-l border-y border-neutral-100 w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full">
                            @if (isset($grade['HW2']) )
                              @if ($grade['HW2']['vocabulary_maxtrix_1'] == 'notFM')
                                {{ $grade['HW2']['vocabulary_maxtrix_11'] }} / {{ $grade['HW2']['vocabulary_maxtrix_12'] }}
                              @elseif($grade['HW2']['vocabulary_maxtrix_1'] == null)
                                N/A
                              @else
                                FM
                              @endif
                            @else
                              0
                            @endif
                          </div>
                          <div class="px-2 py-2.5 text-14-20 text-neutral-700 text-center border-l border-y border-neutral-100 w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full">
                            @if (isset($grade['HW3']) )
                              @if ($grade['HW3']['vocabulary_maxtrix_1'] == 'notFM')
                                {{ $grade['HW3']['vocabulary_maxtrix_11'] }} / {{ $grade['HW3']['vocabulary_maxtrix_12'] }}
                              @elseif($grade['HW3']['vocabulary_maxtrix_1'] == null)
                                N/A
                              @else
                                FM
                              @endif
                            @else
                              0
                            @endif
                          </div>
                          <div class="px-2 py-2.5 text-14-20 text-neutral-700 text-center w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full border-y border-neutral-100">{{$grade['total_vocabulary_maxtrix_11']}}/{{$grade['total_vocabulary_maxtrix_12']}}</div>
                          <div class="px-2 py-2.5 text-14-20 font-semibold text-green text-center border-l border-y border-neutral-100 w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full">
                            {{ $grade['total_vocabulary_maxtrix_12'] ? intval($grade['total_vocabulary_maxtrix_11'] * 100 / $grade['total_vocabulary_maxtrix_12']) : 0 }}%
                          </div>
                          <div class="px-2 py-2.5 text-14-20 font-semibold text-green text-center border-l border-y border-neutral-100 w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full">{{$grade['total_vocabulary_maxtrix_12']? gradeFormat($grade['total_vocabulary_maxtrix_11']*100/$grade['total_vocabulary_maxtrix_12']) : 'N/A'}}</div>
                        </div>
                        <div class="text-neutral-500 font-semibold sticky bottom-0 left-0 text-12-16 py-3 lg:px-[92px] text-center flex-1">Vocabulary Matrix 2</div>
                        <div class="flex">
                          <div class="px-2 py-2.5 text-14-20 text-neutral-700 text-center w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full border-y border-neutral-100">
                            @if (isset($grade['HW1']) )
                              @if ($grade['HW1']['vocabulary_maxtrix_2'] == 'notFM')
                                {{ $grade['HW1']['vocabulary_maxtrix_21'] }} / {{ $grade['HW1']['vocabulary_maxtrix_22'] }}
                              @elseif($grade['HW1']['vocabulary_maxtrix_2'] == null)
                                N/A
                              @else
                                FM
                              @endif
                            @else
                              0
                            @endif
                          </div>
                          <div class="px-2 py-2.5 text-14-20 text-neutral-700 text-center border-l border-y border-neutral-100 w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full">
                            @if (isset($grade['HW2']) )
                              @if ($grade['HW2']['vocabulary_maxtrix_2'] == 'notFM')
                                {{ $grade['HW2']['vocabulary_maxtrix_21'] }} / {{ $grade['HW2']['vocabulary_maxtrix_22'] }}
                              @elseif($grade['HW2']['vocabulary_maxtrix_2'] == null)
                                N/A
                              @else
                                FM
                              @endif
                            @else
                              0
                            @endif
                          </div>
                          <div class="px-2 py-2.5 text-14-20 text-neutral-700 text-center border-l border-y border-neutral-100 w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full">
                            @if (isset($grade['HW3']) )
                              @if ($grade['HW3']['vocabulary_maxtrix_2'] == 'notFM')
                                {{ $grade['HW3']['vocabulary_maxtrix_21'] }} / {{ $grade['HW3']['vocabulary_maxtrix_22'] }}
                              @elseif($grade['HW3']['vocabulary_maxtrix_2'] == null)
                                N/A
                              @else
                                FM
                              @endif
                            @else
                              0
                            @endif
                          </div>
                          <div class="px-2 py-2.5 text-14-20 text-neutral-700 text-center w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full border-y border-neutral-100">{{$grade['total_vocabulary_maxtrix_21']}}/{{$grade['total_vocabulary_maxtrix_22']}}</div>
                          <div class="px-2 py-2.5 text-14-20 font-semibold text-green text-center border-l border-y border-neutral-100 w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full">
                            {{ $grade['total_vocabulary_maxtrix_22'] ? intval($grade['total_vocabulary_maxtrix_21'] * 100 / $grade['total_vocabulary_maxtrix_22']) : 0 }}%
                          </div>
                          <div class="px-2 py-2.5 text-14-20 font-semibold text-green text-center border-l border-y border-neutral-100 w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full">{{$grade['total_vocabulary_maxtrix_22'] ? gradeFormat($grade['total_vocabulary_maxtrix_21']*100/$grade['total_vocabulary_maxtrix_22']) : 'N/A'}}</div>
                        </div>
                      @else
                        <div class="text-neutral-500 font-semibold sticky bottom-0 left-0 text-12-16 py-3 lg:px-[92px] text-center flex-1">Vocabulary Matrix</div>
                        <div class="flex">
                          <div class="px-2 py-2.5 text-14-20 text-neutral-700 text-center w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full border-y border-neutral-100">
                            @if (isset($grade['HW1']) )
                              @if ($grade['HW1']['vocab'] == 'notFM')
                                {{ $grade['HW1']['vocab1'] }} / {{ $grade['HW1']['vocab2'] }}
                              @elseif($grade['HW1']['vocab'] == null)
                                N/A
                              @else
                                FM
                              @endif
                            @else
                              0
                            @endif
                          </div>
                          <div class="px-2 py-2.5 text-14-20 text-neutral-700 text-center border-l border-y border-neutral-100 w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full">
                            @if (isset($grade['HW2']) )
                              @if ($grade['HW2']['vocab'] == 'notFM')
                                {{ $grade['HW2']['vocab1'] }} / {{ $grade['HW2']['vocab2'] }}
                              @elseif($grade['HW2']['vocab'] == null)
                                N/A
                              @else
                                FM
                              @endif
                            @else
                              0
                            @endif
                          </div>
                          <div class="px-2 py-2.5 text-14-20 text-neutral-700 text-center border-l border-y border-neutral-100 w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full">
                            @if (isset($grade['HW3']) )
                              @if ($grade['HW3']['vocab'] == 'notFM')
                                {{ $grade['HW3']['vocab1'] }} / {{ $grade['HW3']['vocab2'] }}
                              @elseif($grade['HW3']['vocab'] == null)
                                N/A
                              @else
                                FM
                              @endif
                            @else
                              0
                            @endif
                          </div>
                          <div class="px-2 py-2.5 text-14-20 text-neutral-700 text-center w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full border-y border-neutral-100">{{$grade['total_vocab1']}}/{{$grade['total_vocab2']}}</div>
                          <div class="px-2 py-2.5 text-14-20 font-semibold text-green text-center border-l border-y border-neutral-100 w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full">
                            {{ $grade['total_vocab2'] ? intval($grade['total_vocab1'] * 100 / $grade['total_vocab2']) : 0 }}%
                          </div>
                          <div class="px-2 py-2.5 text-14-20 font-semibold text-green text-center border-l border-y border-neutral-100 w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full">{{gradeFormat($grade['total_vocab2'] ? intval($grade['total_vocab1'] * 100 / $grade['total_vocab2']) : 0)}}</div>
                        </div>
                        <div class="text-neutral-500 font-semibold sticky bottom-0 left-0 text-12-16 py-3 lg:px-[92px] text-center flex-1">Concept Questions</div>
                        <div class="flex">
                          <div class="px-2 py-2.5 text-14-20 text-neutral-700 text-center w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full border-y border-neutral-100">
                            @if (isset($grade['HW1']) )
                              @if ($grade['HW1']['concept'] == 'notFM')
                                {{ $grade['HW1']['concept1'] }} / {{ $grade['HW1']['concept2'] }}
                              @elseif($grade['HW1']['concept'] == null)
                                N/A
                              @else
                                FM
                              @endif
                            @else
                              0
                            @endif
                          </div>
                          <div class="px-2 py-2.5 text-14-20 text-neutral-700 text-center border-l border-y border-neutral-100 w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full">
                            @if (isset($grade['HW2']) )
                              @if ($grade['HW2']['concept'] == 'notFM')
                                {{ $grade['HW2']['concept1'] }} / {{ $grade['HW2']['concept2'] }}
                              @elseif($grade['HW2']['concept'] == null)
                                N/A
                              @else
                                FM
                              @endif
                            @else
                              0
                            @endif
                          </div>
                          <div class="px-2 py-2.5 text-14-20 text-neutral-700 text-center border-l border-y border-neutral-100 w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full">
                            @if (isset($grade['HW3']) )
                              @if ($grade['HW3']['concept'] == 'notFM')
                                {{ $grade['HW3']['concept1'] }} / {{ $grade['HW3']['concept2'] }}
                              @elseif($grade['HW3']['concept'] == null)
                                N/A
                              @else
                                FM
                              @endif
                            @else
                              0
                            @endif
                          </div>
                          <div class="px-2 py-2.5 text-14-20 text-neutral-700 text-center w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full border-y border-neutral-100">{{$grade['total_concept1']}}/{{$grade['total_concept2']}}</div>
                          <div class="px-2 py-2.5 text-14-20 font-semibold text-green text-center border-l border-y border-neutral-100 w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full">
                            {{ $grade['total_concept2'] ? intval($grade['total_concept1'] * 100 / $grade['total_concept2']) : 0 }}%
                          </div>
                          <div class="px-2 py-2.5 text-14-20 font-semibold text-green text-center border-l border-y border-neutral-100 w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full">{{gradeFormat($grade['total_concept2'] ? $grade['total_concept1']*100/$grade['total_concept2'] : 0)}}</div>
                        </div>
                      @endif
                      <div class="text-neutral-500 font-semibold sticky bottom-0 left-0 text-12-16 py-3 lg:px-[92px] text-center flex-1">Skill Application</div>
                      <div class="flex">
                        <div class="px-2 py-2.5 text-14-20 text-neutral-700 text-center w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full border-y border-neutral-100">
                          @if (isset($grade['HW1']) )
                            @if ($grade['HW1']['skill'] == 'notFM')
                              {{ $grade['HW1']['skill1'] }} / {{ $grade['HW1']['skill2'] }}
                            @elseif($grade['HW1']['skill'] == null)
                              N/A
                            @else
                              FM
                            @endif
                          @else
                            0
                          @endif
                        </div>
                        <div class="px-2 py-2.5 text-14-20 text-neutral-700 text-center border-l border-y border-neutral-100 w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full">
                          @if (isset($grade['HW2']) )
                            @if ($grade['HW2']['skill'] == 'notFM')
                              {{ $grade['HW2']['skill1'] }} / {{ $grade['HW2']['skill2'] }}
                            @elseif($grade['HW2']['skill'] == null)
                              N/A
                            @else
                              FM
                            @endif
                          @else
                            0
                          @endif
                        </div>
                        <div class="px-2 py-2.5 text-14-20 text-neutral-700 text-center border-l border-y border-neutral-100 w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full">
                          @if (isset($grade['HW3']) )
                            @if ($grade['HW3']['skill'] == 'notFM')
                              {{ $grade['HW3']['skill1'] }} / {{ $grade['HW3']['skill2'] }}
                            @elseif($grade['HW3']['skill'] == null)
                              N/A
                            @else
                              FM
                            @endif
                          @else
                            0
                          @endif
                        </div>
                        <div class="px-2 py-2.5 text-14-20 text-neutral-700 text-center w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full border-y border-neutral-100">{{$grade['total_skill1']}}/{{$grade['total_skill2']}}</div>
                        <div class="px-2 py-2.5 text-14-20 font-semibold text-green text-center border-l border-y border-neutral-100 w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full">
                          {{ $grade['total_skill2'] ? intval($grade['total_skill1'] * 100 / $grade['total_skill2']) : 0 }}%
                        </div>
                        <div class="px-2 py-2.5 text-14-20 font-semibold text-green text-center border-l border-y border-neutral-100 w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full">{{gradeFormat($grade['total_skill2'] ? $grade['total_skill1']*100/$grade['total_skill2'] : 0)}}</div>
                      </div>
                    </div>
                  @endif
                </div>
              </div>
              @if(!empty($grade[0]->description))
                <div class="bg-neutral-50 rounded-lg border border-greenLight px-4 py-6 relative">
                <div class="text-14-20 text-green font-medium mb-4">
                  Messages and Consultations
                </div>
                <div class="text-14-20 text-neutral-700 z-[2] relative">
                  <div class="text-14-20 text-neutral-700 {{$grade[0]->description  ? 'text-dot' : ''}}  relative ml-5">
                    {{$grade[0]->description}}
                  </div>
                </div>
                <div class="absolute top-0 right-0">
                  <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="60" height="59" viewBox="0 0 60 59" fill="none">
                    <g opacity="0.2">
                      <mask id="mask0_283_11502" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="60" height="59">
                        <rect x="15.9707" width="45.9643" height="45.9643" transform="rotate(19.8763 15.9707 0)" fill="url(#pattern0)"/>
                      </mask>
                      <g mask="url(#mask0_283_11502)">
                        <rect x="15.9707" width="45.9643" height="45.9643" transform="rotate(19.8763 15.9707 0)" fill="#10C285"/>
                      </g>
                    </g>
                    <defs>
                      <pattern id="pattern0" patternContentUnits="objectBoundingBox" width="1" height="1">
                        <use xlink:href="#image0_283_11502" transform="scale(0.00195312)"/>
                      </pattern>
                      <image id="image0_283_11502" width="512" height="512" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAgAAAAIACAYAAAD0eNT6AAAAAXNSR0IArs4c6QAAIABJREFUeF7snXl8XNV593/PGcmWNDO22QIYQ4ytzWZpGjcLEBL2xdiYJWYJYEggaZo0fdM2XdK3fbsnTZs2TZukKZCAwTgY2xgvmM0sSYGkSRxSSGVrJJvNAcxqa2YkL5rzvNyRjGVjWzN37jr3d//h88H3PMv3OaP5zb3nPEfAiwRIgARIgARIIHEEJHEZM2ESIAESIAESIAFQAHASkAAJkAAJkEACCVAAJLDoTJkESIAESIAEKAA4B0iABEiABEgggQQoABJYdKZMAiRAAiRAAhQAnAMkQAIkQAIkkEACFAAJLDpTJgESIAESIAEKAM4BEiABEiABEkggAQqABBadKZMACZAACZAABQDnAAmQAAmQAAkkkAAFQAKLzpRJgARIgARIgAKAc4AESIAESIAEEkiAAiCBRWfKJEACJEACJEABwDlAAiRAAiRAAgkkQAGQwKIzZRIgARIgARKgAOAcIAESIAESIIEEEqAASGDRmTIJkAAJkAAJUABwDpAACZAACZBAAglQACSw6EyZBEiABEiABCgAOAdIgARIgARIIIEEKAASWHSmTAIkQAIkQAIUAJwDJEACJEACJJBAAhQACSw6UyYBEiABEiABCgDOARIgARIgARJIIAEKgAQWnSmTAAmQAAmQAAUA5wAJkAAJkAAJJJAABUACi86USYAESIAESIACgHOABEiABEiABBJIgAIggUVnyiRAAiRAAiRAAcA5QAIkQAIkQAIJJEABkMCiM2USIAESIAESoADgHCABEiABEiCBBBKgAEhg0ZkyCZAACZAACVAAcA6QAAmQAAmQQAIJUAAksOhMmQRIgARIgAQoADgHSIAESIAESCCBBCgAElh0pkwCJEACJEACFACcAyRAAiRAAiSQQAIUAAksOlMmARIgARIgAQoAzgESIAESIAESSCABCoAEFp0pkwAJkAAJkAAFAOcACZAACZAACSSQAAVAAovOlEmABEiABEiAAoBzgARIgARIgAQSSIACIIFFZ8okQAIkQAIkQAHAOUACJEACJEACCSRAAZDAojNlEiABEiABEqAA4BwgARIgARIggQQSoABIYNGZMgmQAAmQAAlQAHAOkAAJkAAJkEACCVAAJLDoTJkESIAESIAEKAA4B0iABEiABEgggQQoABJYdKZMAiRAAiRAAhQAnAMkQAIkQAIkkEACFAAJLDpTJgESIAESIAEKAM4BEiABEiABEkggAQqABBadKZMACZAACZAABQDnAAmQAAmQAAkkkAAFQAKLzpRJgARIgARIgAKAc4AESIAESIAEEkiAAiCBRWfKJEACJEACJEABwDlAAiRAAiRAAgkkQAGQwKIzZRIgARIgARKgAOAcIAESIAESIIEEEqAASGDRmTIJkAAJkAAJUABwDpAACZAACZBAAglQACSw6Ew5oQQOm55BsTQOjbYJpYYWWB1bJmEGtyElA9hptiGd6sNrXYWEEmLaJJAoAhQAiSo3k617As1Tj4akjkdKTjBWp6ngaAATgfJ/MxXm7wiAFwG8JIoXrZF1sHgadvB/MbDB+f+8SIAE6oAABUAdFJEpJJXAaQ0Y9+sZUPMxgZ4GxUkAJvhM4y0IfqKQxyD2h+g7ai3w2KDPPmmeBEjABwIUAD5ApUkS8I1Ac+skpGSmABcAcjqArG++KjOcB/RRBVajZFfzCUFl0HgXCUSBAAVAFKrAGEhg/wQMWjp/Eyk9S1RnAzgZQJQ/txsFssqqXYniuB8Ca3eyuCRAAtEkEOU/JNEkxqhIwG8Ch5+YRn//GUbNLAWcL/0j/Xbpk/03BXjYQtdAG1eg2PWKT35olgRIwAUBCgAX0DiEBDwnML5tCgbNbBGdBeCjAMZ47iNcgyVAf1l+OmDNSvSv/wUADTckeieBZBOgAEh2/Zl9aAROa0D2pQ8bq7NUZA6AztBCCcfxZhE8aFVWYkzpAbzZ2xdOGPRKAsklQAGQ3Noz86AJZFoPg5rzjcB5tH8ugHFBhxBRf9sAPK4iawBdjnxufUTjZFgkUFcEKADqqpxMJmIE4raALyr4RiwkbPwR0LUjKoExDhKoJwIUAPVUTeYSPoE9F/A57/OdJjy83BJQFAE8qqIrYc0q9He/5NYUx5EACexJgAKAM4IEaiWQ7eiA1QtEnL35OBVAY60mOX6fBCwUP1fBvbDmXi4k5CwhgdoIUADUxo+jE0lgjwV8zja96YnEEH7Sr4ngfgu7GHk8CPRuDz8kRkAC8SFAARCfWjHSMAk4C/iQOs2IzlbFhQDGhxkOfe9NQPqh+kj5VQF7DnB6kEBFBCgAKsLEmxJJIN15AqQ0R5xteooZEe/Al8gS7SfpEoAnVLECqdQK9K3rIRwSIIF3E6AA4Kwggd0EUsi0nWJE5qjiIgBT6hDOZsBZWKf9AIYfmUsTgGYAaQCH12HO60R1hTWpFciv/wkAW4c5MiUSqJoABUDVyDigvghMbEE2e7axmKNS7rV/aPzzk35Afy7Aryz0aYh0IWVexJbBl0d/T946FhMajsRg6RhnbYOBnKDACYDMALQl/mzwqqiussasQD7/EPCSI4R4kUAiCVAAJLLsCU86234ooLNFnQ58cnYdfLE5j7yfVMhDEH0U+Yafer93fvoYZAc/CJXTBXr28KFEqZjPpAGorFHjbDFsWMmzCmJeTYZfNQEKgKqRcUAsCUzonIydOme41/7H6mCr3oiDdgaXo/is82g/uGvc9INhS2ca6FkxP7BoJLMuARYPn1WwNjiY9EQC4RCgAAiHO70GQSDTepyBmatwDtiR99fBIr6oHrVbfx0PFc+VzyoQuwr5MQ94/0QliA8AfZDAgQlQAHCG1BGBGY1I951WXsSH8la9o2Oe3AAEj6rKKgya1di27vlY5NPcfhQaMFMUF0BxFqS8uDDO11sCXW1hlqM5dR9e6yrEORnGTgK7CFAAcC7EnICziK/lzPIvfYWziG9CzBPafUpeE+7H6935eOezR9MkR5RNi3c+GDq4yBFlSC3iuoGYVzPh4VMAJHwCxDJ9ZxGfxUwB5kLgLEgbG8s8hoK2gD4lkFXD755/AUBjnM+BQx/fNgUlnCUqs+ugds7iy5+oymLY0lIM9G6q27oxsbokQAFQl2Wtw6ScL45BM1tE5wI4CYCJcZZvCbDGQtcku2vd8NMbNc7xyM45CkfFuKZO6EOLCKGLUOhZF/NcGH4CCFAAJKDIMU2xvLDMGDtbnV/68e+3H9UFfNGZHpnW4yCpWaLlfgxxF3lD9RZdjHzuibp+qhOdGcRIqiRAAVAlMN7uJ4HpY9AyeJYxuGh4EV+cu9Jtg+AxVaxCg7kXW9Y/5ye5urOdPvZwmIbzjcpMBc6J9dkLzo4Cg2UWWDYsBtiJsO4mbDwTogCIZ93qKOrJTciOPddAL62DQ3Y2CbDaityLfH4Nu8x5NU1nNCJTONXAXqgqF0JwrFeWQ7CzWYDlVmUZiqlHuL0whArQ5TsEKAA4GUIg4HzpN5w9vHJ/DoBxIQThhcuhBXyKNdbIKj7q9QJpBTbqZj3I8AmGwGI0y7L47/iooHa8JVIEKAAiVY46Duaw6RkMDF4g0I8DZmaM2+/mBbjfKlZC7P0o9L5Wx1WLfmot045EqjRLLJwnA2cOH2oU/bjfHeHA2+seHlDFMqQaVqGv6804JsGY40WAAiBe9YpXtAdNGY8djc7K/UsBnBvjP84vC2SFtboc/faR0Q/UiVeZ6idaZ1dB5hxj5UIVp/sjDotpboMAHlPI3dDUMvYaiGkVYxA2BUAMihSrEMefcBAGt8+ugz36I1dxP8kjZGM1C51gR+wiKbeCnhG7DIYCdhYM/rjca0CxGP3dL8U0D4YdQQIUABEsSuxCcg6GKQ3OGv7Sd1Zsj4ldDiMb8sDehUJvVwxzYMj7IzC+41gM4sKYHwZFMcAZ7ikBCgBPcSbIWLbzEFh7wfCXvvN4vzGG2e9u68pfVzEsn8uQd51kKDpbFXFdhLpbDFhdgoHcr13S4LAEE6AASHDxq059zxa85wFoqNpG+AOGuvA5+/PH2nvwZm9f+CExgvAItI5FWk41YmYr9BIAk8KLxbVnigHX6JI9kAIg2fUfPfumKccg1XCpCJyFfDHtziYvCHS5VSxHceIPgcecRVa8SGBvAoKWjhnG2AsV4nQjfF8METli4ElVWQI+GYhh+YINmQIgWN7x8NY07b1I2YuG++6fDCCO86RLVFdyf348plwko9z9OXB2FJwW0ydeQ+cTpOzt2Nq7IZKcGVRoBOL4hz00WHXteELnZOzUOTH+0h86mU1k5du/3O5Bvru7ruvF5IIlsOeal7gudKUYCHbWRN4bBUDkS+RjgOOmtcEOXi4Q57CdE3305JNp6QfUaZ6yHMasQn79Gz45olkS2E1gwuQJ2Dn2AhFcDOD8GDa1UkB+oqrOMcZLMLDhRZY3mQQoAJJWd+eXfkkvE8XlgL4/hum/NvRo3yxHvvgQsMnpoMaLBEIiMKkZ2ZZzDXCxKpx1AweFFIhbt/q2iPlvdXbBlAaXYNvGF9wa4rj4EaAAiF/Nqo+4uf0oGJkropcD+FAM3+n3isg9Froc+dyPATiP+3mRQMQIzGhEuu80I7hYIRcBODJiAY4WjiMGnlTgTujOxSg+u3m0Afz3eBOgAIh3/fYf/Z7NeeK4ZW9kJ77H67VMzKuOCWRajysfeAXnaRs6Y5bp7q2FUlrIMy9iVr0Kw6UAqBBULG7bsw1vHJvzDC1SsmYl+tevjQVzBkkClRDItB4HSc0SVec1Qdx21jhP3B5Vxe08tbCSYsfnHgqA+NRqP5FObEEme4GozoMgjquTh770oYtQ6FkX+3IwARIYjcDuHTdx3F64DYo16hxhXCwsAV7qHy1d/nt0CVAARLc2B4hsUjMy6dkCvfLtrW/O4/2mGKWhUPxMjSyFsUuwtWdjjGJnqCTgLQHnOGMzOEcgThdCp9dAnFpqbxHVu62zZqDY8wjX5ng7NYKwRgEQBGVvfKSQbT/JKK5R4AoA47wxG5iVoV/6prQAfRt6A/NKRyQQFwJ7vsKL29O8N0Vwr7V6G4o9DwNwFhTyijgBCoCIFwgtnTOM0XkKvQzAEVEPd0R87E8eo2Ix1IgRiLcY2CSQu63oYuRzXMAbsak1MhwKgCgWZ/fq4asAtEYxxP3ENNSNj2eXx6hkDDXyBMqNh8ZcGMvjthXPCXSRNXIr8rn1kWedsAApAKJS8OapR8M0XCJirwFkRlTCqiCO3V/6SC1CseuVCsbwFhIgATcE4iwGALYidlNzH8dQAPgId1TTmanvAVKXCeAs5nNO2otLPbaXVwIbXQppXI6+rjdHzZU3kAAJeEtg6DXBricDZwMY460D36ztaji0EGIWsYW3b5xHNRyXL5xRE4nRDYJs2wUC+TwUZ8XohLFtb4sUp+/+UowZXIG3Nm6NEXOGSgL1TWDoycAcAT4OgSMGxsYk4R1vdye9TyELUNi+CnjO+TvDKyACFAABgS67aZl2pJjSHQBOD9Kte1/OYTvOh1OXoEnuxevdefe2OJIESCAQAnu+JoiTGNgiwBILuwCF3v8C4Cwk5uUjAQoAH+HuYToz9T0C8zggbUG5dOdH+gW60kKWoJBfzUYf7ihyFAlEgsBBU8ZjR8OF5RM/RZ2thTF5MiAviOodVspioCsSLOswCAqAgIoq6Y4VkHIb0Chezjv9h8rdvZplGX/pR7FEjIkEaiUwsQXZljPL5xNYXAJBulaLAY3vUpHbUDK3oX/dywH5TIQbCoAgypztPFnUPhGEqyp87Bj+0r8LY+09eLO3r4qxvJUESCDOBA5uHYft5iIxuAJaXjPQEIN0ShA8pFbuQGbsMmx+uhiDmCMdIgVAAOUxmfZvK/C5AFyN5sLZsveIiiyCpJZx9f5ouPjvJJAAAtn2Q6H6cYE4u5E+AsDEIOuCCJZZa+5Acf0atiF2VzEKAHfcqholmXbnDPsPVzXIu5t3d+TjPn3vqNISCdQjgeb2o2Dk4yI6N0anFr4hwFIruJ2dB6ublBQA1fFydbdk2p0OWB2uBrsbpID8RBWLYHUJBnK/dmeGo0iABBJLYNzUVlhzhUCcs0eOiwmH/1XBAuwcXIhtG1+IScyhhUkBEAB6Sbf/BIIPBeDqlyq6EDsb7sK2dc8H4I8uSIAEkkAg3XmCEXuFKq6E4NgYpGwB/ZGKWYCGnUvYt2TfFaMACGAmm0zbvyrk//jkqlegP7DAD1DoWeeTD5olARIggSEC49o+ZCyuVGdrITAxBli2CbDMqpmP4vqH2F9gd8UoABwW6c4TYOxsUf0QIC0CvGEVP4O1izDQu6nmCd7S8Vti9Gc129lt4GWB3mWNLERf7qce2qUpEiABEqiUgEGm42MGeoUClwI4pNKBId63SYDbrJj5yK/PhRhHJFwnVwCMm34w7OAnBHrdAQ7f2SGKf7HF7P8D1u6spWKSaV8K4JIabGwR1bvLv/SLPY9y1WsNJDmUBEjAYwIzGjGu7xyj4rwmmAMg67EDP8w9oYL5aLSLkroNOmkCIIWW9nOMwXWK8iStsCuWPKiF0oVA73bXs3D8CQdJabvTC2BaFTYGAL1XRRYib1fX5L8Kp7yVBEiABNwTmNSMTHqWQJ3FgzMBNLm3FcjIAREstVbno9jzSJJeESRDAGQ7OozV61RwDYCj3EwpAb5lC7kvuBn7zphs5yECexu0/KHY3zUI6MOqspANemqizcEkQAJhExhqOHSxiHwC0DMBpMIO6cD+5QWBnW+NvQ19G3qjHWvt0dWvAHAm3k5zmSg+ObyftVZa9u1DcY73ZKFduu1sY2Seark3wKEAnFa8P1PBA0DpLhQ2vFprsBxPAiRAApEi0DLtSEjpShn6Ifa+SMX27mCcI4sfV9FbMNYsqdf26PUmAJxFKacZ0etU5VJAW7ycZCL4F5vP/aGXNmmLBEiABBJHINN6nFG5RstPBnB0pPNXFMVgqVVzCwrrfwjAEQd1cdWHABjfcawp6bWquBaCyb5VRvBzzec+4Jt9GiYBEiCBZBEwyHR+1GjJEQPOToLxkU5f8awIbrMpmY+t3c9GOtYKgouxAJjchMzY2QL9DADn3VIQubyohdwxFXDlLSRAAiRAAlURaB2LLM4xaq4ZXqQ9pqrhgd+saxW4Ec2NC/FaVyFw9x44DOJL04MwR5jIdpxirH5SBZcFv9VEe7TQ0+5tQrRGAiRAAiSwB4HyAUW4QoCrQjxHpdKi5EVliTV6y/BZBLF5RRAPAeAcUNGAa8V5xA+E+QW8TAu5WvbyVzqheB8JkAAJkIBDYNy0NmNLV+uQGJgacSg5Ffke7I75KD67OeKxBvLY3C2DFLKtM8WmPg1RZ9tc6NtHVOUaFLsXuE2I40iABEiABFwTEGQ7TzLqiAG5HMDBri35P3AnICtUcTOK3Q9GtbdA9J4ANLdOQkPqKoH+DhTv9b9OFXtYr4XsibV2BKzYG28kARIgARLYH4EU0m2nD22n9n7Hl8fYfy2qC2yD+c+oLRyMiACYPgaZ0kUCvWF4QZ/xuAC1mVMUVXE6+nNe9vOvLSaOJgESIAESACZMnoDSmLmiuBrAqQEtCHdDvgTBAwrcjHx2VRR+TIYrALLtnQa4QRXzABzmhmgAYzYr9DIUen4UgC+6IAESIAEScEtgQudkDNp5gvJ6sSluzQQw7hVRnW9TDd9D37qeAPzt00UIAmBSM9Itc0Xg/Np31FpUr7dE9UZr5OvI516PapCMiwRIgARI4F0EBJnWjxo1Tgv4jwPIRJSRAvpDVXMzituXAs9tCzLO4ARApnW6UZmnIs4Xf1SPjbQAHlHF7SgWlgAv9QdZDPoiARIgARLwmsA7hxMF2TPGTRJbBVhkrf0u+nufcmOg2jH+CoBDO7IY0IuHez+fVW1wwd0vL4jaH9jG1HexZf1zwfmlJxIgARIggcAINE89Gg0NnxDVT0d7S+Fwk6Em8wM/zyHwRwC0dM4wxn5GAafPc1QfvQwIsMqq3ohiz8P11N85sA8THZEACZBAPAkYZNtPNgqn62BcvqfWeI3aOwEw/oSDMLhjnoi9AZDjvQ7UI3sKyI9U9RZkmpZg89NFj+zSDAmQAAmQQBwJOE+qt2OuqDonx54S4V0ETyv0ZjTsvB1bntviBeraBUC5S5P9A1W9BoK0F0F5bkPwvCjm25TOx9aejZ7bp0ESIAESIIH4Exg3tdXYhmsV6uwiiOYphc7phIJbbcp+A1t7N9QC3b0AaOmYaIz+lQKOamqoJQifxg6IYKm1uBXF3KNR7cTkU+40SwIkQAIk4J6AQbrtTCO4ViFO+/dm96Z8G7lTIN+z1vwN+te97MaLGwEgyLR9WiD/5HRpduPU3zG6VtXcDiMLkF//hr++aJ0ESIAESKCuCRzcOg7bzUXDi9mDOnm2GqR9qvL/UOz+FoBSNQOrEwDN7UdJSu4A9GPVOAng3pdE9XZr5Fbkc+sD8EcXJEACJEACSSOQ7egwVq9VKTevOypi6T+qVq5Gf/dLlcZVuQDIdJwu0EUR6ti3A9AVanAr+nrur1b5VAqI95EACZAACZDAXgRSyLadL1rua3NBhF6Dv6awc1Ho/WElFatMAKTb54ngJgBjKjHq8z3dqnozxN6GwoZXffZF8yRAAiRAAiSwfwIt045EqnSdKK6PSG+B7apyPYrdd4xWttEFQLbjeqclLoAwD+jZLsAK7tkfrZz8dxIgARIggdAIvNMDR64GtCW0OABVyOdR6P6PA8VwYAGQbr9WBLeEuC/yaQVuQsOOBV7tewyxIHRNAiRAAiSQBALZzkNg9RqR8gm3x4WUslWVeQd6ErB/AZBp+6hAHgQwNuDgR3bo87zzUcC50B0JkAAJkECSCYTbGXenQs5DofuRfZVg3wKgvNofvwRwaHB1C6b3cXD50BMJkAAJkAAJDBMI72yc19TK+/a1O2BfAsBIpt355X16AIV7S4AFVuVmFLufDsAfXZAACZAACZBAuATSbe8zIjcocBWACQEE87AWcufs3RDv3QIg0/E7Av2OvwEN/9ovFBfwyF1/SdM6CZAACZBAVAm0jkUmdaFAfT+qWKG/jUKPs6D/nWtPAZA+9nCRxm4A433A9aqozLdGbkZ+fc4H+zRJAiRAAiRAAvEk4DQZAm5QVafJ0Ht8SOIthe1Aofe1Xbb3EAAm0/5vCnzBY8cPKeRGFFIrgK4dHtumORIgARIgARKoIwLTxyBT2vVU4GwvExPov9pCz++/WwA0Tz1aUqkej1b9K1RWqeJv0N/9cy8ToC0SIAESIAESSASBlo7fEtG/hJS7DY7et2d0KNvUytRdCwLfMWjSbV9TkT8effyod7yuYj+FfO/KUe/kDSRAAiRAAiRAAgcmkO24UFS/D+CQWlEJ5O9sofsvHDvDAqB1rGTMrz0w/rSW7AUY6N1Ua5AcTwIkQAIkQAIkMEyg/JTerAbk+BqZvKqFhqOdV/JDAiDbfpEoltVotFu14TQUu16p0Q6HkwAJkAAJkAAJ7E0g03qYwDxaa3dBNToLfT33lgWAyXTcodBP1EB7qw4OnohtG1+owQaHkgAJkAAJkAAJHIjAhM7JMmj/B8A4t6BEcLvN5+Y5AsBp/LO5lq5/qnINit0L3AbDcSRAAiRAAiRAAhUSGDqn59YK797XbZu1kDtSkG57n4g8VYOhn2ghd1IN4zmUBEggKALjph8MLd0gqucCmAyFgWCjKlZB7IKRe4SDCol+SIAEqicg2fafQvGB6kcOjVCkThBk2z8tij26A1VjUC1moj93XzVjeC8JkEAIBLJt14nKvzmrfvbtXfoF+j07OPh1vs4LoT50SQLVEMi2zRIV17vtVOR6MZn2byvwuWr8jrj311rIHbN3f2GXtjiMBEjALwLZ9i+L4isVmt8pgjut6ldR6FlX4RjeRgIkECwB5/X9iwAmunErkH8TyXQ8AKhzSEDVlwDfsoWc150Dq46DA0iABA5AINN5msA6x4FW20jECrDUWvtV9PfW8pqQ5SEBEvCBQE0/4AX3iWTa/xfAdDexKXAlCrk73YzlGBIggWAISKZjLaDvr8mb4D4Fvop87r9qssPBJEAC3hFIt31CRO5wafAZRwC87rYBkAqmIZ9b79I5h5EACfhNIN15goj18qjtx9XiK+jP3V9eR8SLBEggPAKZ1ukC4/yId3O96giAfgDNbkZr4+AEvLVxq5uxHEMCJBAAgWzH9aJ6sw+enlbFP6OYc359lHywT5MkQAKjERg3/WCxg2+Mdtt+/r3gCADnw2vcGNDCxEbgsUE3YzmGBEggAALZ9j8Txd/76KlbRb+G/LgFwNqdPvqhaRIggXcROK1BMi+5/dyVHAEwAKDJDVkdY8fjzd4+N2M5hgRIIAACmfbfFeDf/fckLyjs11Eofg94yXmqyIsESMBvAgdNGS87G7a4dDPgCIA3ARzkxoCmbCu29m5wM5ZjSIAEAiAwrvXDYs2PA/C0y8WrKvgmGga/zdeDAVKnq2QSGDe1VWyqx2Xyb4hk2nKAtLkxoIKLkc/d42Ysx5AACQRCICWZ9hyAKYF42+1kqwDftih9E4UNrwbsm+5IIBkEMm2XCGSpy2TXOwLgUUBOc2NAFP9gi7kvuxnLMSRAAgERqL1veA2BsrtgDfA4lAQOSMCkO/5RRf/IJaaHxaTbb1bB9S4NdGsh1+lyLIeRAAkERMBk2+erYl5A7vblZqeoLLAGX0O+uzvEOOiaBOqGgGTancf/rW4SEuBGQabtC05LQDcGnDFq8CH05X7qdjzHkQAJBEIgZbLt/6iKL7rd9eNRlOwu6BFImkk4gWzrSaLmSbcUFPi8INtxiqg+7tYIVFZqsftC1+M5kARIIDgCLZ0zROxfQzDTRWtgL+NUCO5X4CvI59z//fEyItoigRgRkHT7KggucBuyGv2wADMaJZN3dgJkXBpShTn9PJ0KAAAgAElEQVQDhfWPuRzPYSRAAkETSLf/hhF8WYGPA0gF7X4vf/+lFl/lqaIhV4Hu40Mg03G6QB+uQcT3aWHiIeXDQSTdsQKis11nL3heGwZ/g9t+XBPkQBIIh8C4aW2mVPoTFVwDYEw4Qbzj9SmFfBWFbmdVsw05FrongWgSmDB5ggyOcdp7H11DgMu1kLto6HSwdPs8EcyvwZizGuABLegcoHd7bXY4mgRIIHACza2TTEq+pCo3QJAO3P+eDtldMOQC0H1UCUxuksyYFQDOriVCVb0KxZ6FQwLg4NZxsiP1MqAttRiFYpUW+y8DNjndBXmRAAnEjUCm9TCD1O8p9HcBTAg3/F3dBQdu5t+UcCtB71EgMKlZ0i2La3nvP5xFQZsbjsRrXYV3zgc3mfYbFfi0B2muU5UrUOz28gQyD8KiCRIggYoJHDY9g/7S9SL6xwAmVjzOnxtfE+A7NjX2m9j6zFv+uKBVEogwgWx7pygWATix1ijLn6VC7vOOnXcEADLTjheU/sebLUJO8w98w4p8A/n1bk8qqjVPjicBEqiZwKRmZFo+JYI/guK9NZurzcBwd0H7ryj0vlabKY4mgRgQyHYeYtT+gQK/7/bU3r2ytAo9HoWedXsKAOebP9N+pwKXe4glLyrftUZuRn69046UFwmQQCwJzGhEOv8JEfwpgJCbf5V/YNxsS4Nfx8CGF2OJk0GTwIEIlBfn2k+r6GcBZL2CJZCFttB91S57u58AOP8n29kuap/xYTWwAvIjVdyM4vYlwHPbvEqIdkiABAIlYJBpu1hg/gzQ9wfq+d3OdojqAmtSX+MPjJArQfceEJjchPTYS0XsDYB8rIYtfvuLZbua0vHo29C7bwFQfgrQ8XcK/b8eZLM/E85jvEXW2u+iv/cpH/3QNAmQgJ8Esu0fEYs/gWCWn24qsG2hWP12Q5K/QX/uZxXcz1tIIDoEMm3TjOJaFXFa8h/qV2AC/I0t5P5ypP09nwCU/2VSs2Ra1gKY5lcg79hV/FQNbsZYuROvd+d990cHJEAC3hNwuola/XJEugvep8BX2V3Q+zLToocEDu3IYrteIRbOttsPemh5f6a6tLBjxt5P3/chAJyegOUFgU5//+YAAnNcbHu7JeFKq3ojij1rAvJJNyRAAl4SSHeeYIz9I1VcCaDBS9MubD2hYr+GfO8qp0mJi/EcQgLeE2jpnGGM/YwCn6ih+261cW1TxYdRzDmL/Pe49i0AnFtCO0JUf6VqbkYqdTv6upwWxbxIgATiRCBS3QXlF+UnAoXuu9ldME6TqI5iHTf9YJRK1wy/2z8+4MxUVa5BsfuOffndvwAYWg/wtwr984AD3uXOeSqwzEJuQqHbOWeAKj6kQtAtCbgi0Dz1aJMyf6gwn665yZirAPYYtF5Fv4b8uDuAtTtrN0cLJHBAAoJMx+kGeoMCFwNoCoPX21+af4VC7q/35/uAAsAZZNJt/6AifxJG8CN8viiqC22p9B1s2/hCyLHQPQmQQDUEsu2HGsXvKvB7AA6qZqj398oLqvgXFPM3AS/1e2+fFhNNID39CJjStaJ6A4DWMFmI4Bs2n/uDA8UwqgBwtiKYdPtXdGj/b9jXIIBVavRm9PXcD6AUdkD0TwIkUCGBg1vHYaf5nCi+CODwCkf5ddurKvgGGu138GZvn19OaDcRBFLItp0vzjkaKB/PG/b6Fwjk722he9Sn95UIgKEKZto+I5BvAWiMSEk3CfQW25D6Prasfy4iMTEMEiCBUQmUuwteL4IvRaC74BaBfNui9E12Fxy1cLxhJIEJnZPNYOlTCvmks30uInB2qODzyOduriSeygWAY21c24ekJD+A4NhKjAd0jwVkjaq9FcWdy9hkKCDqdEMCNROY0Yhs31Wi5VeMEeguaG+yJfvP7C5Yc2Hr2MDkJmTGXCTlL309y5vW+Z7h2qgWV1TTC6M6AeDEedCU8WZn4z8o9DMRS96JzlHzd1pTmo++3p94hpWGSIAE/CRgkOm4RIAvs7ugn5hp2zWBca0fNjZ1rUKvCP+UzHdlYQX4rh1jv1zt66zqBcAu39nWk0TN1wGc7BqqvwPXqcitKGEB+rtf8tcVrZMACXhCIN1xnoh+GcBHPbHn3ojzR3WJVf0qij2/dG+GI2NLoKVjIlK4WlSvC6QxnjtQT6ixX3L7g9e9ANgtBGaLGufI0I+4i9/3USUIHlCVW1EorQB6t/vukQ5IgARqI+C0GXaeCCjO96EnejWxKQT3KeQryHc/Uc1A3htHAq1jkUnNEdFroTgXQCqiWTw+tK21x2l05fqqXQDsct3S/gEj+lmFXA5B2nVE/g58U4AfWCu3or/75/66onUSIIGaCaTb3mdEvqzAxyPwyvFHqvJVFLudHUi86omA8/1lcJ2i3MUy5K2q+wGrKArkLqv6H9W85z9QmbwTALu8OD2Ot9krBTIPwCkRniPOK4L5sKn5KHa9EuE4GRoJkMD4timmZP7P8NqjUJqqjCjC/wz1Esg53dW4FTmus9PZs4/S5SLlR/zvi3AaXSpyGyR1k9fdcb0XACMpZlqnG5V5KuX9kYdEFLDzAX5UFbej2L8Y2DQQ0TgZFgmQQHr6EQY7v6hivhCB7oIbVOXfUSx9l68W4zI1p49Bdse5Rs01ClwUoW3tewPsE+BOa+1/or/3F37R9VcAvBO1s3Vi7GwZ2jlwZsjv9A7EcosAd1nB7TxNzK8pR7sk4AEBdhf0AGKCTGRajzMq16jIpwAcFt3Mda0CNyLdfAc2P130O86ABMCINLIdHcbaT0a/EBh67GJ33Iris5v9LgTtkwAJuCDgvHIcwKdE1FmIPNGFBS+HvCbAd2zDjn/Flue2eGmYtlwQcA7hsTs/LpDPAvhNFxaCGjL0w1PxnX2d2OdnEMELgHeymT4GmdKcGDwVGHpFALkRhdRyoGuHnwWhbRIgATcEWscibS4XwV+E3YMdQF4gt1hr/gH96152kw3HuCaQQrrtdCPiHLkb5Uf8zvl2Q7/2C8UFYZ1LEaIAGFHg8a1TzaC5XgXOYowjXZfe/4GOwr/TGixAX+6n/rujBxIggeoIvNNd0Dm7pKO6sV7fLf0Cp7ugfh0DvZu8tk57IwiMa/uQsXKVAk6jngg/4scrorjVpkrfQ9+G3rBrGA0BsJvCSPXmHKEY+qEK+y2Q4HmxeqdNNXwPfet6wi4k/ZMACexBwCDbeoFY8+cQfDBkNjtFcKcFvoJ8bn3IsdSP+6Ypx6Cx8UpRdd7rt0c4Mft2Y6tHhp4iZ+6J0nHUURMAu2vYPPVopFKfFMV1ETt7YO959vaRy3hSoQtgGu/yeptGhCc1QyOBeBBoaT9fjNNmGKeGHHBpqLsgvhr0u96Q8/bOfbbzEKheJtCrAZwU4QXlTs4bFZiPUumWqJ4vEV0BMHLKtHTOMMY673Q+4ZxL6N1s8tzSiC2FhSVhvdfxPCsaJIF6IJDtOEWs/imkfGRr2H/7nlDFX6KYe7ge0PqbQ+tYZHHO8Na9OQDG+OuvJuvbBFhpVW9EsceprfMDMbJX2B+C6sA454lvNxeJ4JqIbyd08toqghXW6m1xmAjVFYJ3k0CMCaQ7TjRGv6Ra/kERdqvXJ1Ts15DvdVq6RvrLIuCKG2TbTzYqcxXq1OnQgP1X6U7XqprbkUrdHqenwPESACNL4mwnVL1y6CxmPabKagV9+yaB3G3V3sKDRYJGT38ksB8C7C4YvamRaZtmIJcryj/ypkQvwD0iemW4Ne/34/pKJ74CYHcdDNJtZwxv+4j64yEn6uH+AmxBHPEPN8NLCgF2Fwy30i3TjoTYy0R0bsTbxzucRmwLj9aCPjdFrAcBsDvv8ScchNK2uTFo/ODE7KwM/bFCb0Nz40K81lVwU0COIQES8IhAlLoLCp5XK99AMX9Tfa4lmtSMTHqWqM6D4LxI7/gaml7DZ8fUV2O4+hIAI/8O7G79eH303x9hQIBVVuztyE+6D3hs0KM/aTRDAiRQLQF2F6yWWKX3D7/Xh9OHP+oLup2ctgqwqJ5bw9evAHhnSjrnEIy5SIbWCpwVgSNFR/uwvCyCH9iSWYj+9WtHu5n/TgIk4BeBiS3IpK8XmC9FYJ3RFoF8y4p+E/nc635l7Ivdlo7fMsZerRCnSc/hvvjwzqjzZPZhVb0FxZ3LgOe2eWc6epYSIABGQC/3FjDXCjAPkLboleNdEeXKnQehd6LQsy4G8TJEEqhDAuXugleLyp+E3l3QORNe1Oku+M+R7i7onAQLuWKoM18s/tZ2K/R2DJZux7aNL9ThJN5nSskSACMRlHsL6DyFXhnx1pG7ou4SYLE1pQVRaCGZlA8I8ySBEQSGugvC/AUUHwiZTPS6Czo/sEzDJTFZzOeUb4sIViZ5q3ZyBcA7n95YNZl4RwyUTyosmdt42EjIf4bpPpkEsu0fEYs/gWBWyAAsFKtV5a/R3/3zwGMpd+YrXSqQeQBOjkCDpdEQsFnbCEIUACOny+5dBM5kPmW0mRSBfx/aSaCyGFJaiELvaxGIiSGQQHII7BYCyekuOGHyBOwcc6EAc2Oygt+ZjzzefR+fSgqA/f2p2tWQQuFsUzk2Bn/RdivbZlmG17vzMYiZIZJAfRCo++6Ck5qRbTorJu14d82plwSyhA3Y9v8RowAY/c9P3LauOBltg2KNAotR5JkEo5eYd5CARwTqqrvg8OtRmLmqcE5njfI5LLsKONSLn1uqK5rQFAAVYdp1U+yaVziBD51JALsY+fH3R+koyqrQ82YSiBOBpinHmIbGP1TgBkBbQg59g0L/EYVxt1Tw+U8h235SfHrwl8nubqrWZH7Ap5+VzzYKgMpZ7Xmns+K1IXW1OK8IgE63ZgIet7m8k0DkTuS7n+ThIwHTp7vkEchMfY/R1BdV8DkA40MGsEFVfhfF7vv3ikOQbT/FKJxte0473veEHGel7hO5da9SOJXcRwFQCaXR7hnX/kFjcbUCl8fnwyMviGKR1dIP0N/71Ggp8t9JgARqIHDQlPEYbPi8KL4Y9rZjFfwZ8rmvoqX1/SaVulJVnb9bR9eQXZBDNwv0TmtkIfpyPw3ScT36ogDwtKqnNaDlpbNNCp9QxUUxeWfmEHAaDi2xqot5WqGnE4LGSGAvAuXugjcI5EvhfunKCxHobljp7CiIYJm1shDF7oeGD+SpdCzvOwABCgDfpsfkJmQbzh5eNeuIgUbfXHlpWPGciKywoouRzz3B1wRewqUtEthFYEYj0vkrRfDlGL1CDLJ8u3c1tTTczcPS/EFPAeAP1z2tOv0FBrfPFimfcX1mDJplDMUveF5UllMMBDFJ6COhBKLUXTACJdC1quZ2YMedKD67OQIB1XUIFABBl7dpyjFINV4sotcBeF/Q7l37oxhwjY4DSaAiAk5TIcVfDf9IqGhIXdzkPHWELrKphu+hb11PXeQUkyQoAMIslHNk8dAe22shmBxmKFX5phioChdvJoGqCESru2BVoVdx8xsCLB0+apevGqsA5+WtFABe0nRva7jZkMxV6FUADnFvKuiR8oIA9/A1QdDc6a/uCUSru6AXuAcEWDXUpIc9SbwAWqsNCoBaCXo+frjZ0JAQOB/AGM9d+GWw/ChPFpd3E/TnnINJ1C9XtEsCiSGQbe8UxS0APhzLnBVFFdyAQv9yYNNALHOo06ApAKJc2HHTDxY7uC4+vQVGwHTEgJEl5ScDfbmfUQxEeaIxtggSEIxr/4Ap6aUqcimAqRGMsdKQNmshd0SlN/O+4AhQAATH2pUnyXQ8H6P9uvvL8UWBLONrAldTgIOSQ2DoVaDVWcNf+q11kjoFQEQLSQEQ0cLsCqtOBMAIys6aAXu3BZah0OMs/nH2+/IigYQScPoB9J1mxFyqUKdfyOF1CIICIKJFpQCIaGHqVwDsAfwNEaweOqgIDwK92yNeDoZHAh4QmNyEdONHjJjZCr0ilq/4qqNAAVAdr8DupgAIDLU7R/X3BGC/HLYKHDEgdyM99j5sfrrojhhHkUAECRx+YhrF7TMN9FIFZgLIRjDKA4X0ag1ChQIgosWmAIhoYbx4AiCQhQo9A0DcFuAMAPqQqtyNVMNK9HW9GfEyMTwSeDeBCZMnYOcYpwPoJQDOBdAcM0yvlM8IESxCKbVBTOkll/FTALgE5/cwCgC/Cddov5YnADo4+F5s27ipvLBIyz0GnKM+j6wxpKCHO2sEfqIqi2F1CQZyvw46APojgYoJjJt+MEqDs8Q5VldwTqy28Q4l+ZYIVg29lpt0H/DYYPn/po89XKTxlYo57HkjBYBLcH4PowDwm3CN9j0QAC+MCCGFTMdHDexchTi/SuK24MjpK/DfKrIMMng3+jb01oiXw0mgdgITOidjp84RsRcBciqAVO1GA7XwlqjcYyGLUDzi4Xe+9EeGQAEQaEGCckYBEBRpl348FgAjoxgWA7hMoY4YeI/LEMMc9owAy6zqMh5jHGYZEug73fY+iMwRlI/9js+ZHnuW6i0VnYd844NA144DVpECoC4nOQVAxMvqowAYmfnIVsSXxXDNwO6TC9WuRPGox/b5KybitWZ4kSYwco++86XfEeloKwlO8awWc1MquZWvACqiFLubKAAiXrKABEB9iQHgdRHcN/Qec8wDo/66ifgcYHhhEdhju148hfGB0FEAhDWxIuOXAiAypdh3ICEIgD1fE6TbTjcilylwMYBDI45rX+G9JdDVFmYFxpTux5u9fTHMgSEHRWDXIj5xHu3LuYC2BOU6cD8UAIEjj5pDCoCoVWSveEIWAHuKgWz7ScO7CS6P4QJCJ5ehHQUiKyHmbp49HvHJH1R4TVOOQUPqPFGZDSlv12sMynWofigAQsUfBecUAFGowgFiiJAAGBHlaQ1IbzrDiJk7/GQgRscX7wH7KQFWWmtWoH/9L3hgUcQ/DF6Gl+44EaIXCWQOoO/30nSAtrZA8TgEs1z5pABwha2eBlEARLya0RQAI6E5YuCVM8tbC6X8muDgiCPdX3ibBLrSqlmBYulRtiWOaRX3G/ZpDci8fKoRna2KOQAqW/wWPQyvCbDcqixFMfUIxpeOkpJudBUmBYArbPU0iAIg4tWMvgAYCdA52GTr6UZSl8T8YJM8oA+oygoYsxr59W9EfJowvH0RcDrxDY45zwAXKnAegINiCuql8nZXyFIUun+0xwFa4zuOpQCIaVUjEDYFQASKcKAQ4iUA9sjEoKXzN42xzoEnnwCkLeKo9xdeCdBfCmSVhS5CoWddTPNIRtjjO45FyZ49/D4/jp34huvknJqJe4aP0H4SgN1nASkAkjGvfcqSAsAnsF6ZjbEA2BNBpvU4A2fNgM4CZIZXfEKws7EsBthvIAT0+3Q5UmjGe24pnhWRlcNf+s5R2U7nywNfFACjEeK/H4AABUDEp0fdCICRnMe3TcGgmS1SPpvgZDhtfOJ5DR1nrLISTbgfr3fn45lG3KKe2IJsy5lGzSwFZsfwfIuRwLsEWGytWYn+9WurrgQFQNXIOGA3gbj+4U1MDetSAIysnrMFK9V4sYjzZACnAWiIaXG3AXhcVVbx0CIfKpg+9nCg8dzhQ3bOBjDWBy9BmRz60hfciXxufU1OKQBqwpf0wRQAEZ8BdS8ARvLPdh4Cay+I8Ulqe/6yU11pjaxCPrf/d7gRn3+hhpdpPQ6SmiWqzq/8OD8pct7f/3io/8TgUk8PsaIACHWKxt05BUDEK5goATCyFuWz1MfOEnEOKop9R7bNorLaClZjTOlBdiPcz4fu8BPT6N9+plF1Hu07T4TidnT1yMS2Q/CIqt4DbVyBYpfbo3QP/BeKAiDif8GjHR4FQLTrg8QKgD3qMqkZ2aazyosItfzOd0LEy3ag8HZ3IyzJGlfvfWOc/LtC33PV/lkAmuKbnvRD9REFFmPM4HK8tXGr77lQAPiOuJ4dUABEvLoUAO8qUAq7WxI7iwjj/CvRWef9rAgesmJXIT/4EPCcs5agnq+h+lmdpYKzYr4jxKnTayK4f+jgKTwYeAMpCoB6/qz4nhsFgO+Ia3NAAXBAfilkWj9iIBeryhwIJtdGO+TRiiIEa1RwLwaxGgO5X4cckTfu09OPgCnNFMVMQJ29+VlvDIdmpVtU77EpLENfz8/2u0c/iPAoAIKgXLc+KAAiXloKgCoKVD+LxnYl3SXOQkJgDYpHPQY8NlgFjTBvLe/NR0rPGl7AdxIAE2ZAHviubbueBwHs0wQFgF9kE2GXAiDiZaYAcFmgTOthUHN+nWwbcyC8IcAjVrEKDWNXYuszb7kk48+w8gK+/jOG9+Y7C/gm+uMoMKtDazVUFsOWlmKgd1NgnqtxRAFQDS3euxcBCoCITwkKAC8KtEfjGOcgmMO9sBqijd3tiYcayIRzkuHuhk7OF/5HAYwJkYkHrkcs4mvcsQJbntvigVF/TVAA+Mu3zq1TAES8wBQAnhcohUznqUbshaq4EMBUzz0EbVDwvKiutiIPQhp+hL6uN30JwenTAHuqUT1HRWZC8V5f/ARr9OWhI6GxHP324cAX8dWaKwVArQQTPZ4CIOLlpwDwuUDldQPmQrFwFhF+oA7eVTtNZ54R6JMWeBoiv4I0dFUtCsZNPxg6eBxUjjfAbyjUacRzXB3wcSbUL8tHP1uzAv3dTvvd0Xvu+zwNXZunAHCNjgPj24M9MbWjAAiw1OXV6oOzxeJCCM4E0Bygd59dST+gznvsV6CyVUS3wdl14FyCtKo0QXQ8gCMAmQRoi88BBWl+O6CPKWQFBgdXYdvGF4J07qsvCgBf8da7cT4BiHiFKQDCKtBw86H6OHAmLIhh+t29aHKsvaduuy9SAIQ5x2LvmwIg4iWkAIhEgUYcOQun+dD0SETFIPYmkLyjmikA+CmogQAFQA3wghhKARAE5Sp9ZDs6AMwRVWcR4YcBpKq0wNu9IbATwH+pcxxzQ2kltvZu8MZsjKxQAMSoWNELlQIgejXZIyIKgIgXyFkZr3qGEZ2tWj7A5qCIRxzv8IbWLTxa7rcfl616fhKnAPCTbt3bpgCIeIkpACJeoD3CO60BmV+fbBQXqDhtb+X4OEUf4Vh7RbDSOr/0C0f+V4w6IvqPlALAf8Z17IECIOLFpQCIeIEOFF7TtPeiYfBcAzlLgfMBZGKcTZCh88TESmlTAFRKivftgwAFQMSnBQVAxAtUcXiTmpFuPsWIma3ARYAeU/HQZNw4dKqe8yu/ceeDgRylWw9cKQDqoYqh5UABEBr6yhxTAFTGKXZ37dlG92MAGmOXQ20BW0CfEsUaa2QV8rknYt2QpzYW7kdTALhnx5GgAIj4JKAAiHiBvAjP6bpnS2caqPOqwNlZcIQXZiNnY9cCPtGVsA0r0b/u5cjFGLeAKADiVrFIxUsBEKlyvDsYCoCIF8j78Eb0HNBZgLy/3KsvvteIvfmNPwK6dsQ3lQhGTgEQwaLEJ6Q4/2GJD+UaIqUAqAFePQxtnno0UmamqLlgqD1x1Fv0Dp+oJ7Iag2Y1tq17vh7KENkcghIAB00ZLzsb3J2OKHhe87nJkWWY4MAoACJefAqAiBco0PAmNyE99jQjOksVMyE4NlD3+3OmeFYEq63FvejvfwzYNBCJuJIQRFACwHkMlWnPu9zJ8oQWch9JQjniliMFQMQrRgEQ8QKFGV5Lx0QYnFJeO6A4O0BB4Byh+7iFrgH0CRR6/zdMDIn2HaQASHesgOjsankL8De2kPvLasfxfv8JUAD4z7gmDxQANeFL1uDm9qNgzPEwpRMN5AQFpkExCcDhLtYROEfkboZgkwBdFvor2NTTUH0G/d0vJQtshLMNUAAg2zpb1KyoksYOTdnpiWzTXCWoMG6nAAiDehU+KQCqgMVb90NgRiOatxyBhsajYUstMJKBSiME2fIARR6CHbC2CJPqx+DOFzEw4RVgrdNrn1eUCQQpABwVmW2/F87rpwovAb5iC7n/W+HtvC1gAhQAAQOv1h0FQLXEeD8JJIhAwAIAEyZPkMHGNYDMGI2yAIttIXclAKezI68IEqAAiGBRRoZEARDxAjE8EgiTQNACoJzrxBaTyXxNgd/eTwOrPhX9O+R7vs7mTmFOjtF9UwCMzijUOygAQsVP5yQQbQKhCIBhJE1TjkFD6iKj8psqmCDAa1b0SZim5dj6zFvRBsfoHAIUABGfBxQAES8QwyOBMAmEKQDCzJu+PSFAAeAJRv+MUAD4x5aWSSD2BCgAYl/CMBOgAAiTfgW+KQAqgMRbSCCpBCgAklp5T/KmAPAEo39GKAD8Y0vLJBB7AhQAsS9hmAlQAIRJvwLfFAAVQOItJJBUAhQASa28J3lTAHiC0T8jFAD+saVlEog9AQqA2JcwzAQoAMKkX4FvCoAKIPEWEkgqAQqApFbek7wpADzB6J8RCgD/2NIyCcSeAAVA7EsYZgIUAGHSr8A3BUAFkHgLCSSVAAVAUivvSd4UAJ5g9M8IBYB/bGmZBGJPgAIg9iUMMwEKgDDpV+CbAqACSLyFBJJKgAIgqZX3JG8KAE8w+meEAsA/trRMArEnQAEQ+xKGmQAFQJj0K/BNAVABJN5CAkklQAGQ1Mp7kjcFgCcY/TNCAeAfW1omgdgToACIfQnDTIACIEz6FfimAKgAEm8hgaQSCFsAHH5iGvn+Dog5CJLajEJLN7B2Z1LLEbe8KQAiXjEKgIgXiOGRQJgEwhIA6c4TROxfAZgJoGkEgi0CudNa/C36u18KEw19j06AAmB0RqHeQQEQKn46J4FoEwhDAGTaPyfANwE0HADOFlVzBYrrH4g2wGRHRwEQ8fpTAES8QAyPBMIkELQASLdfK4JbK0x5h0LPRqHnRxXez9sCJkABEDDwat1RAFRLjPeTQIIIBCkAmtuPkpTkAG2pmLDiWS3aaUDv9orH8MbACFAABIbanSMKAHfcOIoEEkEgQAFgsu3/oorfr5arKq5DMTe/2nG8338CFAD+M67JA7l2QJwAAB4SSURBVAVATfg4mATqm0CAAkDS7RshONYF0GVayF3iYhyH+EyAAsBnwLWapwColSDHk0AdEwhMAEwfI5nBbQDcfGd0aSF3XB1XIbapuSlmbJONY+AUAHGsGmMmgYAIBCUAMlPfI0htdpnVK1rIHelyLIf5SIACwEe4XpimAPCCIm2QQJ0SCEoApI89XKTxFZcUN2shd4TLsRzmIwEKAB/hemGaAsALirRBAnVKgAKgTgsbTFoUAMFwdu2FAsA1Og4kgfonQAFQ/zX2MUMKAB/hemGaAsALirRBAnVKgAKgTgsbTFoUAMFwdu2FAsA1Og4kgfonQAFQ/zX2MUMKAB/hemGaAsALirRBAnVKgAKgTgsbTFoUAMFwdu2FAsA1Og4kgfonQAFQ/zX2MUMKAB/hemGaAsALirRBAnVKgAKgTgsbTFoUAMFwdu2FAsA1Og4kgfonQAFQ/zX2MUMKAB/hemGaAsALirRBAnVKgAKgTgsbTFoUAMFwdu2FAsA1Og4kgfonQAFQ/zX2MUMKAB/hemFaMu3dANrd2FKTakffuh43YzmGBEggBgSCEgBN094rDaXnXBERPK/53GRXYznIVwIUAL7ird24ZNp/DODDbiyp6tko9qxxM5ZjSIAEYkAgKAGQaf2YwDzmjoj8QgvdM9yN5Sg/CVAA+EnXA9uSbl8FwQVuTInq12yx50/djOUYEiCBGBAISACYTMffKvTPXRJ5SAu5c1yO5TAfCVAA+AjXC9Mm036TAje4tLVZm6QNr3fnXY7nMBIggSgTCEIAHH5iWorbnFeJro70FcFtNp+7NsoYkxobBUDUK59t/7QobnQbpgDfsYXc592O5zgSIIEIEwhAAJhM+78p8AW3FBT4XRRy33Y7nuP8I0AB4B9bbyxnph0vKD1Ti7Hyo7tCz9/XYoNjSYAEIkjAbwGQbf+yKL5SS+Zq7fvR3/tULTY41h8CFAD+cPXSqpFM+2sADq7JqOJeFf0jFHrW1WSHg0mABKJDwC8BkG3vFIt/gmBWjclu1cLEQ4HHBmu0w+E+EKAA8AGq1yZNpv3bCnzOA7sK4GGF3IjCkcv4ofSAKE2QQJgEvBUABum2M4zIZxS4BECq1tQEuNEWcr9dqx2O94cABYA/XL212tI5Q4z9ubdG8bKo3mat/TYGNrzosW2aIwESCIKAFwLgoCnjsaPxOhH9PQBTvAxbjX4YfT3/7aVN2vKOAAWAdyx9tSSZjrWAvt8HJzsEuMfCfgeF3h/6YJ8mSYAE/CJQiwAAXheVlSp6JYAmH0J8Rgu5E32wS5MeEaAA8Aik72bS7WeKwO+mPjkV+T4kdRP6ut70PSc6IAESqI1AbQKgNt+jjFbR2cj3rPLVCY3XRIACoCZ8wQ6WbPt9UJwXgNdtIlhsrf4Lij2/DMAfXZAACbghEF0B8KgWcme4SYljgiNAARAc69o9ZdqmCWQtgObajVVs4UlV+Q8US4uB3u0Vj+KNJEAC/hOIpgDYpmo+iOL6mrYv+w+PHigA4jYHsm3XicotIYT9ughut2pvRqG3KwT/dEkCJLA3gQgKABV8BvncTSxW9AlQAES/Ru+KsMb2wB5krGsVuBHNjQvxWlfBA4M0QQIk4IZAxASACBbYfO4aN6lwTPAEKACCZ+6Bx9axkjGLAMzxwFgtJvoEstBauRn9651XE7xIgASCJJBp+6hAorF7x9lRUCzN5avCICdAbb4oAGrjF+bolMm236qKq8MMYoTvLhW5DZCbkV//RkRiYhgkUIcEJjUjk54l0M8AOBNA+H/H+eUfy3kW/sSJJbbIBO2IgK+p4g8i8UdgCMtAeQcBcBPyuccjQ4qBkEDcCWQ7TjHWfkpFLgOQiUg6KoJ/tfmJf8zOohGpSBVhUABUASuytw71CJgP4KiIxTjUV0AHb0Fhw6sRi43hkED0CaSnHwGULhfRTwGIWlOdV9Xop9DXc2/0QTLCfRGgAKiXeZHtPMRo6c8V8lmfunrVQmoHgFUqMh/5zH3A2p21GONYEqhvAqc1ILvpfFFzPYCZABojlu82gf6nhf49Cr3OQWW8YkqAAiCmhdtv2M3tR5kU/kyBTwbcL6BSkq8KdKFVuRXF3P9UOoj3kUDdE8i0TTOKa1VkHoAjI5jvgEDm21Lp7zHQuymC8TGkKglQAFQJLDa3Owd87ExdLsAXADk+onEPLRy0O25F8dnNEY2RYZGAfwQmTJ6AwcbLBOUv/ZMjtJZnZM4bVOQmQL+HfO51/2DQctAEKACCJh6Gv2z7R4zi9xS4KIKPEx0iJQCPDh1TnFoOdDmvDHiRQL0SSCHddroxMk9VLgW0JYKJWgCPDH0mu+8e/oxGMEyGVAsBCoBa6MVtrLOgyJSuFcXnAD0mouG/JcBiK7iduwgiWiGG5Y5ApvU4o3KNilwH4HB3RnwftVlUb7WNqe9iy/rnfPdGB6ESoAAIFX9ozlPIts4UNc7539HYR7xvFOvKCwdtaj6KXa+ERouOScAtgZZpR0LsZSKY59Nx3m4j22vccHfPwsDtwKYBj4zSTMQJUABEvEC+h5dt7zSqn1XItQAm+O7PnYNBCB5SKwvRkrqH7YfdQeSooAhMbEE6fZEYuQqKcwGkgvJcpZ8dAvm+VfkOD+6pklyd3E4BUCeFrD2NyU3IjJ0t0C8OL0aq3aQ/FrZBsaa8eJDrBfwhTKtuCKSQbT/JKK5R4EoAWTdGAh2jeFaLuSmB+qSzSBGgAIhUOSISzO53lZ8GcHBEotpXGG+JYJW1ehuKPQ8D0AjHytDqkcDuz4rzBO2IWKVIARCrcvkRLAWAH1TrxuY7TwWi03N8/2xfFMgya0u3or/3qbopAROJHoGmae9Fo71CtNydrz16AVYYEQVAhaDq9zYKgPqtrbeZZTs6jLWfVBHnj95h3hr33FpXeSdBSuZja/eznlunweQRyHYeAi1dGvH9+tXVhQKgOl51eDcFQB0W1d+UWsciYy4W4AYAZ0S0cckuBM5e5sdU5A6ktt+NLc9t8ZcNrdcVgXHTD4aWLhbVKwCcHuHFfO6wUwC441ZHoygA6qiYgacyvm2KKcn1Cjj7micG7r86h9uh8qACd2FsaQXe7O2rbjjvTgSBg1vHYbuZI8DlEJwNYEzE83bm8ThXMVIAuMJWT4MoAOqpmuHlYpBuO8OIfCbC3QZH0tkOxUMKLEazLMPr3fnw0NFz+AQmNyHbcLaBmasWl0CQDj+mA0awu3Om4Om3n1CsdxUvBYArbPU0iAKgnqoZhVxaOiYihWtE1VkVPS0KIY0SwwCg9yrMXSjk7wVe6o9BzAyxZgKtY5E15xvF5aqYHYMvfSfjLlW9BWhc8E5jrPEdx0pJN7rCQQHgCls9DaIAqKdqRi2XcW0fMlbmDe+LPihq4b0rHkVRBCut4C7kd9wHPLct8jEzwCoIzGhES/4sk8IVqpgDYHwVg8O6dYtA7rRGb0Ff7qfvCoICIKy61IVfCoC6KGPUk3B+beEco8ZpkhLVA4n2hjgAxcPl1wTFwhI+GYj6HNtffK1jkZZTjZjZivJivvfEIBNn8eqPFXob0s13YPPTxf3GTAEQg3JGN0QKgOjWpj4jcw4kwuBVInpdhI8p3pv9VhHcY2GXIj/4EJ8MRH1qTmpGtuU8o3qpQma7XiQXfJo5hd6OwdJt2LbxhYrcUwBUhIk37ZsABQBnRngEdndR+2RMfpk5ux77ofpI+cnAWHsPdxOEN3329Ox86TedVV7IN/R4393K+ODT2VJ+7eS2myUFQPAVqyOPFAB1VMwYpzJ0Pnp8dhHsQj18LoGuBOw9KGx4NcY1iGHoE1uQbTlz+Ev/YgCZmCSxA4oHPTnPggIgJiWPZpgUANGsS3Kjykx9D2AuF8gnAHw4RiB2AvKowt4NHbwHxWc3xyj2+ITqdORDabYoLgbkHABN8QkeTypkAUxqEfq63vQkbgoATzAm1QgFQFIrH4e8m6Ycg8bGK0Wd9QLojEPIwzEOLeISWQkxd6NvXU+MYo9eqM48aEidJyrOdj3neN3G6AW534heFNWF1qS+j/z6nOdxUwB4jjRJBikAklTtOOfa0vFbxtirFHI5gCNjlIpC8HMF7gbM3b58CcQIRsWhZqYdDwxeJOVdIzKj4nHRuPE1Ae6yIj9AvvtJX0+ppACIRsVjGgUFQEwLl+CwDbLtJw+fu+5s64rLYq9dJdsokFVW7UoUj3oMeGwwwbUcmbpBS+dvGmNnK3BZTJpIjYx/QIBVVuztyI+/H1i7M5C6UgAEgrlenVAA1GtlE5HXpGZk0rMF9ipAzotB3/a9q/KqKFZaIyuRzz+UvF4D08cgbU83ohcr9MKYPdlxarkDwH0K/ACF/hXApoHAP3YUAIEjryeHFAD1VM0k5+Kc3GZ3zhXIVQA+EvFTCvdVKacl8UPldQN258q6XUSYbT8UFjNFdDYgzvv8bMymrbO+44cqWAgzdim2PvNWqPFTAISKP+7OKQDiXkHG/24CzVOPhknNFedRsuCDMRQDFpD/VtEVULsChd6uWJc50zYNYmaLOl/6OCmWx+oKfqYWd8JiEQZyv45MPSgAIlOKOAZCARDHqjHmygmUxUDDJSI6F8DJMRQDgOJZEVk5tG5g3A8De79cOeW970wh236SsTpLRZxH+3E4FGpf2XYJsNiKWRjZxZsUAO5nKUeCAoCTIDkExrdOhTWXiZYXmb0vpom/IYL7rOJemIYHPdtPXiuMCZMnYHDMeQa4UAFnPUb0D3/aZ876KwUWQVJ3RfZLf2TcFAC1ztxEj6cASHT5E5x8trMdqpcL7GUxOpNg74I558L/WAX3wuI+FHP/E2hFM63TIXKBqJwP4FQADYH6987ZuvK2Pdi7Yve6hQLAu1mQQEsUAAksOlPei0CmdbqBuSym289GJvOqCB6wKisxpvSA9+cUTGpGuvkUA5ylIs6pjh2xnUuC50VluRVdjHzu8djmQQEQ29JFIXAKgChUgTFEh0C640QjuEyhH4/1FxywHZAfqmI1UoP3om9DryvI49umoCTni+ACKE4D0OzKTjQGrRfIUmtLS9Hf+1Q0QqoxCgqAGgEmezgFQLLrz+wPRMD58hs0s2O9gHB3fhsFWGPFrkIeDwK92/eT+ogFfDgrhl349k5raCGfNSvRv35t3U14CoC6K2mQCVEABEmbvuJLoCwGcImIuQRQ55CiOH92CgAeUsHPodgIVYFgikDeD+DsGO7NHzmvhlsvy1LI4FLXTz7iMlMpAOJSqUjGGec/YpEEyqASQCDTehjUnC/A3BgeTlOPBbKAPlVusWxSdyTq8CUKgHqcz4HlRAEQGGo6qksCTmc7yByBXgLFmQDG1mWe0UvK6bX/QwWWwco96O9+KXohBhARBUAAkOvXBQVA/daWmQVN4KAp47EjdcHQawKcD2hL0CHUub/C269f7lc196Bx+73Y8tyWOs/3QOmlkOn4mFF7lYp8yhUHRVEF/wSklqKw7leubHBQrAlQAMS6fAw+ugQmtiCbOddYma2iswAcFt1YIx3ZqwKssKLLkd+5BnhuW6Sj9Tu4lmlHGlP6bQU+4/HhSU+p4DvI998RyqFGfnOj/X0SoADgxCAB/wkMr6yXC1V0DoB2/13G2sMGEdxjgeXI554E4DQ8SvaVPvZwIw1fVshvA2jyEcbLCnwFhYYbgS7ntENedUyAAqCOi8vUIkog2+E00JkjWj4C19lRkIpopEGF5azcX6sqK6ByD4rrnwnKcQz8GGTaPyvOlzIwPsB41yvM76Cw/rEAfdJVwAQoAAIGTncksAcBZ0eByCxR59AcOSdB6wYGoHhYRVfCmlWJXcR3oI9Dc+skSaUWAPqxkD41KsC3bMH+0QH6RoQUGt16QYACwAuKtEECnhCY1Ixs85lGxTlQxzk69whPzEbHyMsCrLIiq5DPrwFe6o9OaBGLJN1+pgjuBHBo+JHJL7RUmoOB3k3hx8IIvCRAAeAlTdoiAe8IGIxr+4ApyRx12vACJ3pnOlBLTwl0lbWyEv25n8M53JjXgQmk268VwY0AxkQI1a9VcUHgB05FCEA9hkIBUI9VZU71R6B56tFImZkCzITKmRCkI5rkNggeVedAotLgKgxseDGicUYzrEzbZwTy3Yh2mnxLrT2zbs5RiOYMCDQqCoBAcdMZCXhBYHIT0k0fM6IzFToTQKsXVl3bKJ+sp6utYDXyxUf4aN8lyXTH1SI6H4BxaSGIYW8o9FQUetYF4Yw+/CVAAeAvX1onAf8JlE/sw1miMhtS7uXvdzfCEqC/LLfeHTpk5xd8tF9jmbOdJ4vaRwKoXY2Bliv9rIr9EAq9r9VujBbCJEABECZ9+iYBrwkcNj2DbYNnG/3/7d1fqKR1GQfw5/euq+vOuO0KJljBartzQEOF1IKykIIygqhWxC66EbpICbSboiSxP14ESpBB0HUXpjeRSoohGYEgaF6IZ46sIRSa0aJnDml7nF/N9scCO8y8M/ue931/n7me3/t7ns/zoF8958zEp/KpTyOMd63oipf++V/51UOx9+QjceL4qyt6rscMLjw/pb2/i4jzO4TxSJ6MPyH4dWhib1OqANDt+amewE4CKfYfuTz2VNem2S9wRVwZEXvnJDsZkZ7MMX0wpvnBf/3c1y/wzYm3wNtSOmf0QORTYa1Tr5zj1tga392pohX7PwICgIUgUIzA4X1x4IzL//F391dUKS6MaZybUxyKyCnl9Jeo4sQ05xci5Sdjc/up4j92t4m9GIy+mFLMfu7fxddf8578vnh143gXi1dzt7/T3PwIECDQXYFzjxxIf6vWu/15D/nnebIx+3hrrw4K+D8AHRyakgkQ6L5ANRzdniO+teJO/pRyemAa02cjqpdiz/RE5DgvcvWeFNMrItLsl0TPXuWdOcXVsTn+zSqf6VnNCAgAzTi7hQABAm8JHLroHenkGS9EzH4Es5LXozmm347J87N/Ef//L086/9JBTN74bEr59oh470pujvRwnqzPfiHQq2MCAkDHBqZcAgR6IDA8+pUU6Qcr6OQPOVc3xtZzv1zsWRefGYPtm1OKO1fxiYM5ppfE5PlnF6vBu3dbQADY7Qm4nwCB4gTScPR0RFy2ZONP5+3tz8Trx1+s/Zxz1j6Ucr5/2T9BTCnunm6Ob61dh4O7IiAA7Aq7SwkQKFZgsHZpSnn2d//LvJ7Kg31Xx8vPbC3zkFNnh0cuSVH9NiIOLPGsl/JkPPvMiekSz3C0YQEBoGFw1xEgULjAcO22FPmOJRRezm++eeVKv2dhsPbJlPIvImJP3bpylT8Yr208Ufe8c80LCADNm7uRAIGCBdJw9HhEfLguQY64ISbj2VcFr/RVDUY/ySlurPvQU3/RMBkvE2zqXu1cTQEBoCacYwQIEFhc4P1703Bz9jHK9f4UL8WTeXN81Wn5CN79axekKjYi8v7F+zr1/YUP5c3x7MupvDoiIAB0ZFDKJECgBwJL/vw/R7o+Juv3ni6Jajj6cY74Us3nv5In43fWPOvYLggIALuA7koCBAoVGB79XIo0+637Oq838r50Xvx5fbPO4bnO7B9dm6p4cK73vs2b8r504LTWV7cw595WQACwGAQIEGhKYDC6JaW4q951TXzgzpGz0rA6UfdHFDmny2Jr/Zl6/TnVtIAA0LS4+wgQKFagGq59J0f+Rh2AFHHPdDK+uc7ZRc6k4Wj2/QSjRc78+705qmti8txjdc4607yAANC8uRsJEChUoDpndFfOcUud9nPkb8Zk47t1zi5yJg1Hv4qIaxY5858AUOVPx2sbD9Q560zzAgJA8+ZuJECgUIFqOLonR3y5Tvs54qaYjH9U5+wiZ9Jw7b6I/PlFzrz1fwDysZhs1P0dhzpXOrOEgACwBJ6jBAgQWESgGwHg6M8i0rFF+norAKTrYrJ+X52zzjQvIAA0b+5GAgQKFRAACh18S9sWAFo6GGURINA/AQGgfzPtckcCQJenp3YCBDolIAB0aly9L1YA6P2INUiAQFsEBIC2TEIdMwEBwB4QIECgIQEBoCFo18wlIADMxeRNBAgQWF5AAFje0BNWJyAArM7SkwgQILCjgABgQdokIAC0aRpqIUCg1wICQK/H27nmBIDOjUzBBAh0VUAA6Ork+lm3ANDPueqKAIEWCggALRxKwSUJAAUPX+sECDQrIAA06+22nQUEABtCgACBhgQEgIagXTOXgAAwF5M3ESBAYHkBAWB5Q09YnYAAsDpLTyJAgMCOAgKABWmTgADQpmmohQCBXgsIAL0eb+eaEwA6NzIFEyDQVQEBoKuT62fdAkA/56orAgRaKCAAtHAoBZckABQ8fK0TINCsgADQrLfbdhYQAGwIAQIEGhIQABqCds1cAgLAXEzeRIAAgeUFBIDlDT1hdQICwOosPYkAAQI7CggAFqRNAgJAm6ahFgIEei0gAPR6vJ1rTgDo3MgUTIBAVwUEgK5Orp91CwD9nKuuCBBooYAA0MKhFFySAFDw8LVOgECzAgJAs95u21lAALAhBAgQaEhAAGgI2jVzCQgAczF5EwECBJYXEACWN/SE1QkIAKuz9CQCBAjsKCAAWJA2CQgAbZqGWggQ6LWAANDr8XauOQGgcyNTMAECXRUQALo6uX7WLQD0c666IkCghQLLBICI9GLE9JXT31a6KCIO1bknR7ouJuv31TnrTPMCAkDz5m4kQKBQgeUCQPvRBID2z+i/KxQAujUv1RIg0GEBAaDDw+th6QJAD4eqJQIE2ikgALRzLqVWJQCUOnl9EyDQuIAA0Di5C3cQEACsBwECBBoSEAAagnbNXAICwFxM3kSAAIHlBQSA5Q09YXUCAsDqLD2JAAECOwpUw9EPc8RNfWXKkY/FZOP+vvbXt74EgL5NVD8ECLRWoBoevSNHuq21BS5ZWI780Zhs/HrJxzjekIAA0BC0awgQIBCDozeklH7aU4lpzicviK0XXu5pf71rSwDo3Ug1RIBAawUOHj6Yts/8Y0Sc3doa6xf2eJ6MP1L/uJNNCwgATYu7jwCBogWqwejOnOJrfUPIOT4eW+NH+9ZXn/sRAPo8Xb0RINBCgcP70uDMxyLFB1pYXK2SUoq7ppvjr9Y67NCuCQgAu0bvYgIEihU4cPG5abp9b0R8rOMGOeX8/enWxtcjYtrxXoorXwAobuQaJkCgJQJVDNa+kCLfHCmuiogu/fP49Yj8cK7ie/HaxhMt8VTGggJdWrgFW/N2AgQIdETg4OGDsX32u2Oaz2p9xXvyJDbf/H3E82+0vlYF7iggAFgQAgQIECBQoIAAUODQtUyAAAECBAQAO0CAAAECBAoUEAAKHLqWCRAgQICAAGAHCBAgQIBAgQICQIFD1zIBAgQIEBAA7AABAgQIEChQQAAocOhaJkCAAAECAoAdIECAAAECBQoIAAUOXcsECBAgQEAAsAMECBAgQKBAAQGgwKFrmQABAgQICAB2gAABAgQIFCggABQ4dC0TIECAAAEBwA4QIECAAIECBQSAAoeuZQIECBAgIADYAQIECBAgUKCAAFDg0LVMgAABAgQEADtAgAABAgQKFBAAChy6lgkQIECAgABgBwgQIECAQIECAkCBQ9cyAQIECBAQAOwAAQIECBAoUEAAKHDoWiZAgAABAgKAHSBAgAABAgUKCAAFDl3LBAgQIEBAALADBAgQIECgQAEBoMCha5kAAQIECAgAdoAAAQIECBQoIAAUOHQtEyBAgAABAcAOECBAgACBAgUEgAKHrmUCBAgQICAA2AECBAgQIFCggABQ4NC1TIAAAQIEBAA7QIAAAQIEChQQAAocupYJECBAgIAAYAcIECBAgECBAgJAgUPXMgECBAgQEADsAAECBAgQKFBAAChw6FomQIAAAQICgB0gQIAAAQIFCggABQ5dywQIECBAQACwAwQIECBAoEABAaDAoWuZAAECBAgIAHaAAAECBAgUKCAAFDh0LRMgQIAAAQHADhAgQIAAgQIFBIACh65lAgQIECAgANgBAgQIECBQoIAAUODQtUyAAAECBAQAO0CAAAECBAoUEAAKHLqWCRAgQICAAGAHCBAgQIBAgQICQIFD1zIBAgQIEBAA7AABAgQIEChQQAAocOhaJkCAAAECAoAdIECAAAECBQoIAAUOXcsECBAgQEAAsAMECBAgQKBAAQGgwKFrmQABAgQICAB2gAABAgQIFCggABQ4dC0TIECAAAEBwA4QIECAAIECBQSAAoeuZQIECBAgIADYAQIECBAgUKCAAFDg0LVMgAABAgQEADtAgAABAgQKFPg7MOXyVzLFoKEAAAAASUVORK5CYII="/>
                    </defs>
                  </svg>
                </div>
                <div class="absolute bottom-[-19px] left-0">
                  <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="83" height="83" viewBox="0 0 83 83" fill="none">
                    <g opacity="0.2">
                      <mask id="mask0_283_11505" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="11" y="11" width="61" height="61">
                        <rect x="11.1016" y="30.6682" width="45.3518" height="45.3518" transform="rotate(-25.1237 11.1016 30.6682)" fill="url(#pattern0)"/>
                      </mask>
                      <g mask="url(#mask0_283_11505)">
                        <rect x="16.1992" y="18.8953" width="47.7371" height="45.3518" fill="#10C285"/>
                      </g>
                    </g>
                    <defs>
                      <pattern id="pattern0" patternContentUnits="objectBoundingBox" width="1" height="1">
                        <use xlink:href="#image0_283_11505" transform="scale(0.00195312)"/>
                      </pattern>
                      <image id="image0_283_11505" width="512" height="512" xlink:href="data:image/png;base64,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"/>
                    </defs>
                  </svg>
                </div>
              </div>
              @endif
            </div>
          @endforeach
          @foreach($combinedGradeW as $gradeW)
              <div class="bg-white p-4 rounded-lg">
                <div class="flex items-center justify-between border-l-4 border-green px-4 py-2 mb-6">
                  <div class="text-14-20 font-semibold text-[0F1A30] mobile-strim250">{{$gradeW[0]->classes->name}}</div>
                  <div class="text-green font-semibold text-14-20">{{$gradeW[0]->classes->subject}}</div>
                </div>
                <div class="mb-4 rounded-lg border border-[#EEF0F6]">
                  <div>
                    <div class="text-neutral-200 font-semibold rounded-t-lg text-12-16 bg-dark py-3 text-center">Class Participation</div>
                    <div class="flex max-w-full overflow-auto w-full flex-nowrap">
                      <div class="flex w-full items-center flex-shrink-0 xl:flex-1 xl:w-full">
                        <div class="px-2 py-2.5 w-1/3 text-14-20 text-neutral-500 text-center border-l border-neutral-100">{{$gradeW[0]->attendance}}</div>
                        <div class="px-2 py-2.5 w-2/3 text-14-20 font-semibold text-green text-center border-l border-neutral-100">{{$gradeW[0]->participation}}</div>
                      </div>
                    </div>
                    <div class="text-neutral-200 font-semibold text-12-16 bg-dark py-3 text-center">Home Work</div>
                    <div class="rounded-b-lg flex flex-col max-w-full overflow-auto w-full bg-neutral-50">
                      <div class="flex">
                        <div class="px-2 py-3 text-12-16 font-semibold text-neutral-500 text-center w-1/3 flex-shrink-0 bg-neutral-100 xl:flex-1 xl:w-full">HW1</div>
                        <div class="px-2 py-3 text-12-16 font-semibold text-neutral-500 text-center w-1/3 flex-shrink-0 bg-neutral-100 xl:flex-1 xl:w-full">HW2</div>
                        <div class="px-2 py-3 text-12-16 font-semibold text-neutral-500 text-center w-1/3 flex-shrink-0 bg-neutral-100 xl:flex-1 xl:w-full">HW3</div>
                      </div>
                      @if(in_array($gradeW[0]->classes->level,['QW1','QW2','QW3']))
                        <div class="text-neutral-500 sticky bottom-0 left-0 font-semibold text-12-16 py-3 xl:px-[92px] text-center flex-1">Transcription</div>
                        <div class="flex">
                          <div class="px-2 py-2.5 text-14-20 text-neutral-700 text-center border-y border-neutral-100 w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full">{{isset($gradeW['HW1']) ? $gradeW['HW1']->transcription :'N/A'}}</div>
                          <div class="px-2 py-2.5 text-14-20 text-neutral-700 text-center border-l border-y border-neutral-100 w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full">{{isset($gradeW['HW2']) ? $gradeW['HW2']->transcription :'N/A'}}</div>
                          <div class="px-2 py-2.5 text-14-20 text-neutral-700 text-center border-l border-y border-neutral-100 w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full">{{isset($gradeW['HW3']) ? $gradeW['HW3']->transcription :'N/A'}}</div>
                        </div>
                      @endif
                      <div class="text-neutral-500 font-semibold text-12-16 py-3 xl:px-[92px] text-center flex-1 sticky bottom-0 left-0">Technical Writing Skill</div>
                      <div class="flex">
                        <div class="px-2 py-2.5 text-14-20 {{isset($gradeW['HW1']) && $gradeW['HW1']->skill == 'notFM' ? 'text-neutral-700' : 'font-semibold text-green'}} text-center border-l border-y border-neutral-100 w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full">
                          @if(isset($gradeW['HW1']))
                            @if($gradeW['HW1']->skill == 'notFM')
                              {{ $gradeW['HW1']->skill1 }} / {{ $gradeW['HW1']->skill2 }}
                            @elseif($gradeW['HW1']->skill == null)
                              N/A
                            @else
                              {{ $gradeW['HW1']->skill }}
                            @endif
                          @else
                            N/A
                          @endif
                        </div>
                        <div class="px-2 py-2.5 text-14-20 {{isset($gradeW['HW2']) && $gradeW['HW2']->skill == 'notFM' ? 'text-neutral-700' : 'font-semibold text-green'}} text-center border-l border-y border-neutral-100 w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full">
                          @if(isset($gradeW['HW2']))
                            @if($gradeW['HW2']->skill == 'notFM')
                              {{ $gradeW['HW2']->skill1 }} / {{ $gradeW['HW2']->skill2 }}
                            @elseif($gradeW['HW2']->skill == null)
                              N/A
                            @else
                              {{ $gradeW['HW2']->skill }}
                            @endif
                          @else
                            N/A
                          @endif
                        </div>
                        <div class="px-2 py-2.5 text-14-20 {{isset($gradeW['HW3']) && $gradeW['HW3']->skill == 'notFM' ? 'text-neutral-700' : 'font-semibold text-green'}} text-center border-l border-y border-neutral-100 w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full">
                          @if(isset($gradeW['HW3']))
                            @if($gradeW['HW3']->skill == 'notFM')
                              {{ $gradeW['HW3']->skill1 }} / {{ $gradeW['HW3']->skill2 }}
                            @elseif($gradeW['HW3']->skill == null)
                              N/A
                            @else
                              {{ $gradeW['HW3']->skill }}
                            @endif
                          @else
                            N/A
                          @endif
                        </div>
                      </div>
                      @if(in_array($gradeW[0]->classes->level,['QW1']) || ($gradeW[0]->classes->level == 'QW2' && $gradeW[0]->session <=8) )
                        <div class="text-neutral-500 font-semibold text-12-16 py-3 xl:px-[92px] text-center flex-1 sticky bottom-0 left-0">Prewriting</div>
                        <div class="flex">
                          <div class="px-2 py-2.5 text-14-20 font-semibold text-green text-center border-y border-neutral-100 w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full">{{isset($gradeW['HW1']) ? $gradeW['HW1']->prewriting : 'N/A'}}</div>
                          <div class="px-2 py-2.5 text-14-20 font-semibold text-green text-center border-l border-y border-neutral-100 w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full">{{isset($gradeW['HW2']) ? $gradeW['HW2']->prewriting : 'N/A'}}</div>
                          <div class="px-2 py-2.5 text-14-20 font-semibold text-green text-center border-l border-y border-neutral-100 w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full">{{isset($gradeW['HW3']) ? $gradeW['HW3']->prewriting : 'N/A'}}</div>
                        </div>
                      @endif
                      @if($gradeW[0]->classes->level == 'QW2' && $gradeW[0]->session >8 )
                        <div class="text-neutral-500 font-semibold text-12-16 py-3 xl:px-[92px] text-center flex-1 sticky bottom-0 left-0">Prewriting/Writing/Completed Essay</div>
                        <div class="flex">
                          <div class="px-2 py-2.5 text-14-20 font-semibold text-green text-center border-y border-neutral-100 w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full">{{isset($gradeW['HW1']) ? $gradeW['HW1']->prewriting : 'N/A'}}</div>
                          <div class="px-2 py-2.5 text-14-20 font-semibold text-green text-center border-l border-y border-neutral-100 w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full">{{isset($gradeW['HW2']) ? $gradeW['HW2']->writing : 'N/A'}}</div>
                          <div class="px-2 py-2.5 text-14-20 font-semibold text-green text-center border-l border-y border-neutral-100 w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full">{{isset($gradeW['HW3']) ? $gradeW['HW3']->completed_essay : 'N/A'}}</div>
                        </div>
                      @endif
                      @if($gradeW[0]->classes->level == 'QW3' || $gradeW[0]->classes->level == 'QW4')
                        <div class="text-neutral-500 font-semibold text-12-16 py-3 xl:px-[92px] text-center flex-1 sticky bottom-0 left-0">Prewriting/Writing/Revision</div>
                        <div class="flex">
                          <div class="px-2 py-2.5 text-14-20 font-semibold text-green text-center border-y border-neutral-100 w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full">{{isset($gradeW['HW1']) ? $gradeW['HW1']->prewriting : 'N/A'}}</div>
                          <div class="px-2 py-2.5 text-14-20 font-semibold text-green text-center border-l border-y border-neutral-100 w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full">{{isset($gradeW['HW2']) ? $gradeW['HW2']->writing : 'N/A'}}</div>
                          <div class="px-2 py-2.5 text-14-20 font-semibold text-green text-center border-l border-y border-neutral-100 w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full">{{isset($gradeW['HW3']) ? $gradeW['HW3']->revision : 'N/A'}}</div>
                        </div>
                      @endif
                      @if($gradeW[0]->classes->level == 'QW1' || ($gradeW[0]->classes->level == 'QW2' && $gradeW[0]->session <=8))
                        <div class="text-neutral-500 font-semibold text-12-16 py-3 xl:px-[92px] text-center flex-1 sticky bottom-0 left-0">Writing</div>
                        <div class="flex">
                          <div class="px-2 py-2.5 text-14-20 font-semibold text-green text-center border-y border-neutral-100 w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full">{{isset($gradeW['HW1']) ? $gradeW['HW1']->writing : 'N/A'}}</div>
                          <div class="px-2 py-2.5 text-14-20 font-semibold text-green text-center border-l border-y border-neutral-100 w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full">{{isset($gradeW['HW2']) ? $gradeW['HW2']->writing : 'N/A'}}</div>
                          <div class="px-2 py-2.5 text-14-20 font-semibold text-green text-center border-l border-y border-neutral-100 w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full">{{isset($gradeW['HW3']) ? $gradeW['HW3']->writing : 'N/A'}}</div>
                        </div>
                      @endif
                    </div>
                  </div>
                </div>
                @if(!empty($gradeW[0]->description))
                  <div class="bg-neutral-50 rounded-lg border border-greenLight px-4 py-6 relative">
                  <div class="text-14-20 text-green font-medium mb-4">
                    Messages and Consultations
                  </div>
                  <div class="text-14-20 text-neutral-700  z-[2] relative">
                    <div class="text-14-20 text-neutral-700 text-dot relative ml-5">
                      {{$gradeW[0]->description}}
                    </div>
                  </div>
                  <div class="absolute top-0 right-0">
                    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="60" height="59" viewBox="0 0 60 59" fill="none">
                      <g opacity="0.2">
                        <mask id="mask0_283_11502" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="60" height="59">
                          <rect x="15.9707" width="45.9643" height="45.9643" transform="rotate(19.8763 15.9707 0)" fill="url(#pattern0)"/>
                        </mask>
                        <g mask="url(#mask0_283_11502)">
                          <rect x="15.9707" width="45.9643" height="45.9643" transform="rotate(19.8763 15.9707 0)" fill="#10C285"/>
                        </g>
                      </g>
                      <defs>
                        <pattern id="pattern0" patternContentUnits="objectBoundingBox" width="1" height="1">
                          <use xlink:href="#image0_283_11502" transform="scale(0.00195312)"/>
                        </pattern>
                        <image id="image0_283_11502" width="512" height="512" xlink:href="data:image/png;base64,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"/>
                      </defs>
                    </svg>
                  </div>
                  <div class="absolute bottom-[-19px] left-0">
                    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="83" height="83" viewBox="0 0 83 83" fill="none">
                      <g opacity="0.2">
                        <mask id="mask0_283_11505" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="11" y="11" width="61" height="61">
                          <rect x="11.1016" y="30.6682" width="45.3518" height="45.3518" transform="rotate(-25.1237 11.1016 30.6682)" fill="url(#pattern0)"/>
                        </mask>
                        <g mask="url(#mask0_283_11505)">
                          <rect x="16.1992" y="18.8953" width="47.7371" height="45.3518" fill="#10C285"/>
                        </g>
                      </g>
                      <defs>
                        <pattern id="pattern0" patternContentUnits="objectBoundingBox" width="1" height="1">
                          <use xlink:href="#image0_283_11505" transform="scale(0.00195312)"/>
                        </pattern>
                        <image id="image0_283_11505" width="512" height="512" xlink:href="data:image/png;base64,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"/>
                      </defs>
                    </svg>
                  </div>
                </div>
                @endif
              </div>
          @endforeach
          @foreach($classesNotGrades as $classeNotGrade)
              <div class="bg-white p-4 rounded-lg">
                <div class="flex items-center justify-between border-l-4 border-green px-4 py-2 mb-6">
                  <div class="text-14-20 font-semibold text-[0F1A30] mobile-strim250">{{$classeNotGrade->name}}</div>
                  <div class="text-green font-semibold text-14-20">{{$classeNotGrade->subject}}</div>
                </div>
                <div class="text-center">
                  No Data
                </div>
              </div>
          @endforeach
        </div>
        @if(!count($combinedGrades) && !count($combinedGradeW) && !count($classesNotGrades))
          <div class="w-full max-w-[1320px] mx-auto p-4 lg:py-8 lg:px-[60px]" style="border-radius: 6px">
            <div class="bg-white px-4 py-[48px] flex items-center justify-center flex-col">
              <div class="max-w-full w-[275px] m-auto mb-6">
                <svg xmlns="http://www.w3.org/2000/svg" width="276" height="200" viewBox="0 0 276 200" fill="none">
                  <g clip-path="url(#clip0_1557_18604)">
                    <path d="M272.188 175.76H3.81055V175.894H272.188V175.76Z" fill="#EBEDF2"/>
                    <path d="M245.295 184.397H227.518V184.531H245.295V184.397Z" fill="#EBEDF2"/>
                    <path d="M220.762 184.397H182.755V184.531H220.762V184.397Z" fill="#EBEDF2"/>
                    <path d="M161.792 179.415H151.492V179.55H161.792V179.415Z" fill="#EBEDF2"/>
                    <path d="M50.3741 180.317H31.9688V180.451H50.3741V180.317Z" fill="#EBEDF2"/>
                    <path d="M88.619 180.317H55.1523V180.451H88.619V180.317Z" fill="#EBEDF2"/>
                    <path d="M124.656 182.582H104.898V182.717H124.656V182.582Z" fill="#EBEDF2"/>
                    <path d="M131.027 151.821H27.3793C25.6885 151.821 24.3145 150.447 24.3145 148.756V3.06487C24.3145 1.37409 25.6885 0 27.3793 0H131.027C132.717 0 134.091 1.37409 134.091 3.06487V148.756C134.091 150.447 132.717 151.821 131.027 151.821ZM27.3793 0.134189C25.7637 0.134189 24.4486 1.44924 24.4486 3.06487V148.756C24.4486 150.372 25.7637 151.687 27.3793 151.687H131.027C132.642 151.687 133.957 150.372 133.957 148.756V3.06487C133.957 1.44924 132.642 0.134189 131.027 0.134189H27.3793Z" fill="#EBEDF2"/>
                    <path d="M247.125 151.821H143.478C141.787 151.821 140.413 150.447 140.413 148.756V3.06487C140.413 1.37409 141.787 0 143.478 0H247.125C248.816 0 250.19 1.37409 250.19 3.06487V148.756C250.19 150.447 248.816 151.821 247.125 151.821ZM143.478 0.134189C141.862 0.134189 140.547 1.44924 140.547 3.06487V148.756C140.547 150.372 141.862 151.687 143.478 151.687H247.125C248.741 151.687 250.056 150.372 250.056 148.756V3.06487C250.056 1.44924 248.741 0.134189 247.125 0.134189H143.478Z" fill="#EBEDF2"/>
                    <path d="M44.5218 74.217L117.359 74.217L117.359 11.1108L44.5218 11.1108L44.5218 74.217Z" fill="#E2E4EB"/>
                    <path d="M42.8939 74.2171L115.731 74.2171L115.731 11.1109L42.8939 11.1109L42.8939 74.2171Z" fill="#F7F7FA"/>
                    <path d="M112.827 71.9627V13.3653L45.8026 13.3653V71.9627H112.827Z" fill="#F7F7FA"/>
                    <path d="M85.9466 71.9627L98.0933 13.3653H72.6834L60.542 71.9627H85.9466Z" fill="white"/>
                    <path d="M47.0264 71.9627L47.0264 13.3653H45.8026V71.9627H47.0264Z" fill="#E2E4EB"/>
                    <path d="M44.0974 61.4584L114.53 61.4584V58.4311L44.0974 58.4311V61.4584Z" fill="#F7F7FA"/>
                    <path d="M113.975 172.561H125.606V122.122H113.975V172.561Z" fill="#EBEDF2"/>
                    <path d="M38.0199 175.76H123.38V172.567H38.0199V175.76Z" fill="#EBEDF2"/>
                    <path d="M113.976 122.122H35.7812V172.561H113.976V122.122Z" fill="#F7F7FA"/>
                    <path d="M109.409 128.333H40.3555V144.875H109.409V128.333Z" fill="#EBEDF2"/>
                    <path d="M109.409 149.808H40.3555V166.351H109.409V149.808Z" fill="#EBEDF2"/>
                    <path d="M56.2693 137.737H93.4878C94.1104 137.737 94.6204 137.227 94.6204 136.604C94.6204 135.981 94.1104 135.471 93.4878 135.471H56.2693C55.6466 135.471 55.1367 135.981 55.1367 136.604C55.1367 137.227 55.6466 137.737 56.2693 137.737Z" fill="#F7F7FA"/>
                    <path d="M56.2693 159.217H93.4878C94.1104 159.217 94.6204 158.708 94.6204 158.085C94.6204 157.462 94.1104 156.952 93.4878 156.952H56.2693C55.6466 156.952 55.1367 157.462 55.1367 158.085C55.1367 158.708 55.6466 159.217 56.2693 159.217Z" fill="#F7F7FA"/>
                    <path d="M235.487 44.0944C242.111 42.2694 244.376 30.2515 227.64 25.3456C231.333 37.4279 223.941 47.2774 235.487 44.0944Z" fill="#EBEDF2"/>
                    <path d="M236.951 49.3117C242.168 43.6704 237.236 30.9064 218.428 36.1236C229.066 45.4309 227.853 59.1558 236.951 49.3117Z" fill="#D4D9DF"/>
                    <path d="M231.799 50.7555C227.43 46.4293 233.688 31.3733 248.116 38.3511C239.942 46.1663 239.421 58.2969 231.799 50.7555Z" fill="#F7F7FA"/>
                    <path d="M227.44 59.1879H240.156L241.116 50.5838H226.484L227.44 59.1879Z" fill="#E2E4EB"/>
                    <path d="M226.34 52.1242H241.261C241.787 52.1242 242.212 51.7002 242.212 51.1742C242.212 50.6482 241.787 50.2241 241.261 50.2241H226.34C225.814 50.2241 225.39 50.6482 225.39 51.1742C225.39 51.7002 225.814 52.1242 226.34 52.1242Z" fill="#E2E4EB"/>
                    <path d="M120.457 161.187V146.614H132.593V132.042H144.734V117.474H156.87V102.901H169.012V88.3283H181.153V73.7608H193.289V59.1879H205.43H272.192V175.76H120.457H108.315V161.187H120.457Z" fill="#F7F7FA"/>
                    <path d="M187.215 161.187V146.614H199.357V132.042H211.493V117.474H223.634V102.901H235.77V88.3283H247.911V73.7608H260.047V59.1879H272.189V73.7608V175.76H187.215H175.079V161.187H187.215Z" fill="#D4D9DF"/>
                    <path d="M175.079 161.187H107.599V165.557H175.079V161.187Z" fill="#EBEDF2"/>
                    <path d="M187.222 146.615H119.741V150.984H187.222V146.615Z" fill="#EBEDF2"/>
                    <path d="M199.357 132.042H131.876V136.411H199.357V132.042Z" fill="#EBEDF2"/>
                    <path d="M211.499 117.474H144.019V121.843H211.499V117.474Z" fill="#EBEDF2"/>
                    <path d="M223.634 102.901H156.153V107.27H223.634V102.901Z" fill="#EBEDF2"/>
                    <path d="M235.777 88.3283H168.296V92.6975H235.777V88.3283Z" fill="#EBEDF2"/>
                    <path d="M247.911 73.7609H180.431V78.13H247.911V73.7609Z" fill="#EBEDF2"/>
                    <path d="M260.054 59.1879H192.573V63.5571H260.054V59.1879Z" fill="#EBEDF2"/>
                    <path d="M137.999 200C195.476 200 242.07 197.28 242.07 193.924C242.07 190.568 195.476 187.848 137.999 187.848C80.522 187.848 33.9277 190.568 33.9277 193.924C33.9277 197.28 80.522 200 137.999 200Z" fill="#F7F7FA"/>
                    <path d="M183.459 55.6453C183.368 57.2556 183.18 58.7317 182.971 60.2614C182.762 61.7804 182.477 63.2887 182.15 64.7916C181.516 67.7975 180.641 70.7818 179.439 73.6856L178.956 74.7699L178.833 75.0383L178.774 75.1725C178.752 75.2154 178.736 75.253 178.693 75.3442C178.543 75.6448 178.35 75.9669 178.076 76.2728C177.802 76.5788 177.453 76.8793 177.051 77.0833C176.653 77.2926 176.213 77.4268 175.827 77.4698C175.027 77.5664 174.404 77.4215 173.878 77.2658C172.842 76.9276 172.096 76.4607 171.388 75.983C170.695 75.4945 170.073 74.9792 169.482 74.4532C167.153 72.3062 165.21 70.0089 163.417 67.5076L165.199 66.0047C166.246 66.9977 167.335 67.9961 168.425 68.9515C169.509 69.9123 170.636 70.8194 171.764 71.6353C172.327 72.0432 172.896 72.4189 173.46 72.7302C174.007 73.0415 174.581 73.2992 174.973 73.3743C175.161 73.4119 175.29 73.3904 175.231 73.3743C175.177 73.3475 174.941 73.4602 174.887 73.5246C174.855 73.5515 174.855 73.5515 174.866 73.5193L175.038 73.0684L175.413 72.0915C176.342 69.4453 177.045 66.6703 177.587 63.8469C177.866 62.4406 178.07 61.0075 178.28 59.5851C178.484 58.1735 178.645 56.6974 178.768 55.3394L183.465 55.6346L183.459 55.6453Z" fill="#FFC3BD"/>
                    <path d="M180.208 49.741C178.206 50.2509 176.811 58.4847 176.811 58.4847L182.323 62.2313C182.323 62.2313 187.61 53.9277 186.07 51.727C184.465 49.4351 183.128 49.0003 180.208 49.741Z" fill="#10C285"/>
                    <path d="M180.206 49.741C178.204 50.2509 176.809 58.4847 176.809 58.4847L182.321 62.2313C182.321 62.2313 187.608 53.9277 186.068 51.727C184.463 49.4351 183.126 49.0003 180.206 49.741Z" fill="#10C285"/>
                    <path opacity="0.4" d="M180.206 49.741C178.204 50.2509 176.809 58.4847 176.809 58.4847L182.321 62.2313C182.321 62.2313 187.608 53.9277 186.068 51.727C184.463 49.4351 183.126 49.0003 180.206 49.741Z" fill="black"/>
                    <path opacity="0.3" d="M180.387 51.5767L178.852 59.8749L181.949 61.979L180.387 51.5767Z" fill="black"/>
                    <path d="M165.289 65.6612L162.665 61.5175L161.178 66.2248C161.178 66.2248 163.33 68.4792 165.273 67.9424L165.289 65.6612Z" fill="#FFC3BD"/>
                    <path d="M158.823 60.1487L157.792 64.1583L161.179 66.2302L162.666 61.5228L158.823 60.1487Z" fill="#FFC3BD"/>
                    <path d="M191.028 188.293H186.176L185.854 177.054H190.706L191.028 188.293Z" fill="#FFC3BD"/>
                    <path d="M233.856 165.75L230.158 168.89L221.693 161.338L225.392 158.192L233.856 165.75Z" fill="#FFC3BD"/>
                    <path d="M229.141 168.514L232.727 164.408C232.856 164.258 233.065 164.225 233.231 164.333L236.887 166.705C237.268 166.952 237.332 167.521 237.026 167.859C235.749 169.271 235.062 169.888 233.484 171.692C232.512 172.803 231.208 174.526 229.871 176.061C228.562 177.564 226.941 176.351 227.365 175.594C229.249 172.191 228.798 170.994 228.83 169.324C228.835 169.024 228.948 168.734 229.141 168.519V168.514Z" fill="#263238"/>
                    <path d="M186.01 187.735H191.464C191.662 187.735 191.823 187.875 191.85 188.068L192.473 192.384C192.537 192.829 192.151 193.253 191.694 193.248C189.794 193.215 188.877 193.103 186.477 193.103C185.001 193.103 181.952 193.258 179.913 193.258C177.873 193.258 177.766 191.246 178.614 191.063C182.419 190.247 183.917 189.115 185.194 188.041C185.425 187.848 185.715 187.741 186.005 187.741L186.01 187.735Z" fill="#263238"/>
                    <path opacity="0.2" d="M185.852 177.059L186.018 182.851H190.87L190.704 177.059H185.852Z" fill="black"/>
                    <path opacity="0.2" d="M225.394 158.198L221.69 161.338L226.06 165.234L229.758 162.089L225.394 158.198Z" fill="black"/>
                    <path d="M180.207 49.741C180.207 49.741 177.893 50.5569 182.525 79.0371H202.235C201.902 71.0126 201.897 66.0691 205.729 49.6015C205.729 49.6015 201.602 48.7051 197.361 48.5011C194.044 48.3401 191.328 48.2327 188.408 48.5011C184.565 48.85 180.207 49.7464 180.207 49.7464V49.741Z" fill="#10C285"/>
                    <g opacity="0.4">
                      <path d="M180.207 49.741C180.207 49.741 177.893 50.5569 182.525 79.0371H202.235C201.902 71.0126 201.897 66.0691 205.729 49.6015C205.729 49.6015 201.602 48.7051 197.361 48.5011C194.044 48.3401 191.328 48.2327 188.408 48.5011C184.565 48.85 180.207 49.7464 180.207 49.7464V49.741Z" fill="black"/>
                      <path d="M180.207 49.741C180.207 49.741 177.893 50.5569 182.525 79.0371H202.235C201.902 71.0126 201.897 66.0691 205.729 49.6015C205.729 49.6015 201.602 48.7051 197.361 48.5011C194.044 48.3401 191.328 48.2327 188.408 48.5011C184.565 48.85 180.207 49.7464 180.207 49.7464V49.741Z" fill="url(#paint0_linear_1557_18604)"/>
                    </g>
                    <path d="M197.36 48.4958C194.043 48.3401 191.327 48.2328 188.407 48.5011C187.924 48.5441 187.43 48.5978 186.942 48.6568C186.614 49.2204 186.368 49.9772 186.674 50.7502C187.302 52.3121 189.567 52.4946 191.22 52.4946C197.323 52.4946 198.353 48.9306 198.396 48.7803L198.455 48.5656C198.09 48.5387 197.725 48.5119 197.36 48.4958Z" fill="white"/>
                    <path d="M196.476 38.4155C195.912 41.2979 195.348 46.5742 197.361 48.4958C197.361 48.4958 196.572 51.4157 191.221 51.4157C185.338 51.4157 188.408 48.4958 188.408 48.4958C191.618 47.7282 191.537 45.345 190.979 43.1068L196.476 38.4155Z" fill="#FFC3BD"/>
                    <path opacity="0.2" d="M194.234 40.3317L190.981 43.1067C191.116 43.6381 191.218 44.1749 191.255 44.6955C192.484 44.5184 194.17 43.1712 194.299 41.8883C194.363 41.2496 194.336 40.6323 194.234 40.3317Z" fill="black"/>
                    <path d="M193.128 22.753C192.629 21.0676 192.2 18.4 195.211 17.2245C194.035 15.786 193.751 13.0807 195.195 11.4758C196.639 9.87095 198.845 10.4614 198.845 10.4614C198.845 10.4614 199.489 6.5377 204.261 7.22475C209.032 7.9118 206.907 11.0679 206.907 11.0679C206.907 11.0679 213.418 9.7797 213.852 15.9148C214.287 22.0499 210.551 21.2931 210.551 21.2931C210.551 21.2931 213.455 23.7299 211.85 26.4083C210.245 29.0868 206.52 28.1635 206.52 28.1635C206.52 28.1635 204.77 32.8977 200.648 30.2032C196.526 27.5087 193.134 22.7584 193.134 22.7584L193.128 22.753Z" fill="#263238"/>
                    <path d="M183.459 26.3171C181.843 28.3943 182.557 30.756 183.979 31.6631C187.441 33.8746 192.449 24.6048 191.145 23.4937C189.841 22.3827 185.069 24.2398 183.453 26.3171H183.459Z" fill="#263238"/>
                    <path d="M198 21.3682C195.724 20.4504 193.416 21.4111 192.697 22.796C190.941 26.1722 198.912 32.2536 201.22 28.0347C202.589 25.528 200.351 22.3183 198 21.3682Z" fill="#10C285"/>
                    <path d="M197.03 31.6793C197.196 36.4939 197.368 38.5282 195.183 41.2174C191.904 45.2591 185.951 44.4111 184.352 39.7413C182.913 35.5385 182.886 28.3729 187.384 26.0219C191.818 23.7031 196.858 26.8699 197.025 31.6846L197.03 31.6793Z" fill="#FFC3BD"/>
                    <path d="M196.316 34.2181C194.185 33.4183 193.627 30.9117 193.982 29.9455C192.919 29.9026 190.557 29.5161 189.677 28.185C188.002 29.237 184.465 30.3266 184.078 28.55C183.692 26.768 189.038 22.1626 193.069 21.8137C197.868 21.395 202.602 26.1131 201.378 30.6219C200.154 35.1306 196.311 34.2181 196.311 34.2181H196.316Z" fill="#263238"/>
                    <path d="M182.527 79.0317C182.527 79.0317 178.7 116.422 179.017 131.22C179.349 146.614 184.991 181.729 184.991 181.729H191.555C191.555 181.729 190.514 147.516 191.099 132.396C191.732 115.912 196.966 79.0317 196.966 79.0317H182.532H182.527Z" fill="#263238"/>
                    <path d="M192.118 181.965H184.421L183.852 179.507L192.477 179.206L192.118 181.965Z" fill="#10C285"/>
                    <path opacity="0.2" d="M191.027 88.3336C186.185 98.0757 190.055 115.842 191.767 122.45C192.648 112.52 194.124 100.389 195.278 91.4736C194.542 86.4335 193.26 83.841 191.027 88.3336Z" fill="black"/>
                    <path d="M187.733 79.0317C187.733 79.0317 192.199 117.903 195.57 130.378C199.574 145.192 224.442 165.132 224.442 165.132L229.488 160.849C229.488 160.849 211.463 139.191 208.404 131.387C201.872 114.731 207.464 89.9064 202.242 79.0317H187.733Z" fill="#263238"/>
                    <path d="M230.292 160.618L224.034 165.938L221.693 164.537L228.773 157.951L230.292 160.618Z" fill="#10C285"/>
                    <path d="M198.167 35.0286C198.049 36.0216 197.49 36.8643 196.835 37.3635C195.853 38.115 194.908 37.3474 194.796 36.1827C194.694 35.136 195.086 33.4828 196.256 33.1876C197.404 32.8977 198.295 33.8907 198.167 35.0233V35.0286Z" fill="#FFC3BD"/>
                    <path d="M207.741 54.8348C208.858 57.5508 209.77 60.2346 210.57 63.0257C211.354 65.8114 212.035 68.6401 212.352 71.6352C212.443 72.3759 212.47 73.1489 212.508 73.9164V73.9862V74.1043V74.3512C212.491 74.5069 212.475 74.6625 212.454 74.8074C212.411 75.0973 212.347 75.3603 212.271 75.6018C212.126 76.0903 211.939 76.5036 211.745 76.8954C210.951 78.4198 210.006 79.5792 209.024 80.701C207.033 82.9017 204.918 84.8072 202.599 86.5624L201.026 84.8394C202.814 82.7568 204.623 80.6097 206.147 78.4198C206.898 77.3355 207.618 76.1976 208.026 75.1778C208.122 74.9255 208.197 74.6893 208.235 74.4961C208.251 74.3995 208.267 74.319 208.267 74.2599C208.267 74.2277 208.267 74.2116 208.267 74.1902C208.267 74.1794 208.267 74.1794 208.262 74.1741V74.1043C208.181 73.4655 208.133 72.8375 208.015 72.1881C207.612 69.6009 206.877 66.9655 206.088 64.3568C205.267 61.7589 204.349 59.1235 203.383 56.6115L207.741 54.8294V54.8348Z" fill="#FFC3BD"/>
                    <path d="M205.729 49.6014C207.704 50.2026 210.076 57.0033 210.076 57.0033L202.417 62.4835C202.417 62.4835 199.621 56.8047 200.452 54.2551C201.317 51.5981 203.48 48.9197 205.734 49.6068L205.729 49.6014Z" fill="#10C285"/>
                    <path opacity="0.4" d="M205.729 49.6014C207.704 50.2026 210.076 57.0033 210.076 57.0033L202.417 62.4835C202.417 62.4835 199.621 56.8047 200.452 54.2551C201.317 51.5981 203.48 48.9197 205.734 49.6068L205.729 49.6014Z" fill="black"/>
                    <path d="M227.817 170.86C227.629 170.86 227.458 170.774 227.302 170.597C227.173 170.446 227.179 170.307 227.211 170.21C227.388 169.62 229.079 169.131 229.267 169.078C229.309 169.067 229.358 169.078 229.385 169.115C229.411 169.153 229.417 169.201 229.395 169.239C229.095 169.808 228.499 170.779 227.882 170.849C227.855 170.849 227.833 170.849 227.812 170.849L227.817 170.86ZM229.063 169.384C228.392 169.598 227.527 169.969 227.431 170.28C227.42 170.318 227.415 170.377 227.479 170.446C227.597 170.586 227.726 170.64 227.866 170.629C228.198 170.591 228.639 170.13 229.063 169.384Z" fill="#10C285"/>
                    <path d="M228.53 169.4C227.971 169.4 227.343 169.287 227.123 169.008C227.059 168.922 226.973 168.745 227.134 168.503C227.236 168.353 227.386 168.262 227.569 168.24C228.267 168.149 229.324 169.067 229.367 169.104C229.399 169.131 229.41 169.174 229.405 169.212C229.399 169.249 229.367 169.287 229.329 169.298C229.147 169.362 228.846 169.4 228.524 169.4H228.53ZM227.676 168.466C227.676 168.466 227.628 168.466 227.601 168.466C227.483 168.482 227.392 168.535 227.327 168.632C227.247 168.756 227.274 168.82 227.306 168.863C227.515 169.131 228.465 169.233 229.05 169.131C228.701 168.863 228.095 168.46 227.676 168.46V168.466Z" fill="#10C285"/>
                    <path d="M184.186 188.583C183.703 188.583 183.258 188.508 183.011 188.283C182.855 188.138 182.78 187.95 182.801 187.724C182.812 187.585 182.882 187.483 182.995 187.418C183.59 187.091 185.523 188.079 185.743 188.197C185.786 188.218 185.813 188.267 185.802 188.315C185.797 188.363 185.759 188.401 185.711 188.411C185.297 188.492 184.718 188.583 184.181 188.583H184.186ZM183.322 187.58C183.231 187.58 183.161 187.59 183.107 187.622C183.059 187.649 183.038 187.687 183.032 187.746C183.016 187.902 183.064 188.02 183.166 188.116C183.462 188.379 184.261 188.428 185.335 188.25C184.669 187.928 183.784 187.585 183.322 187.585V187.58Z" fill="#10C285"/>
                    <path d="M185.693 188.411C185.693 188.411 185.661 188.412 185.645 188.401C185.065 188.138 183.922 187.091 184.013 186.544C184.034 186.415 184.126 186.254 184.442 186.222C184.679 186.2 184.893 186.264 185.081 186.42C185.693 186.93 185.806 188.234 185.806 188.288C185.806 188.331 185.79 188.369 185.757 188.39C185.736 188.406 185.714 188.411 185.693 188.411ZM184.534 186.442C184.534 186.442 184.485 186.442 184.464 186.442C184.255 186.463 184.238 186.549 184.233 186.576C184.179 186.909 184.958 187.73 185.543 188.084C185.489 187.703 185.323 186.919 184.925 186.587C184.807 186.485 184.673 186.436 184.528 186.436L184.534 186.442Z" fill="#10C285"/>
                    <path d="M202.531 77.8776L203.035 79.6436C203.105 79.7831 202.944 79.9227 202.713 79.9227H182.424C182.247 79.9227 182.097 79.8368 182.086 79.7241L181.909 77.9582C181.898 77.8347 182.048 77.7327 182.247 77.7327H202.209C202.354 77.7327 202.488 77.7918 202.531 77.883V77.8776Z" fill="#10C285"/>
                    <path opacity="0.3" d="M202.531 77.8776L203.035 79.6436C203.105 79.7831 202.944 79.9227 202.713 79.9227H182.424C182.247 79.9227 182.097 79.8368 182.086 79.7241L181.909 77.9582C181.898 77.8347 182.048 77.7327 182.247 77.7327H202.209C202.354 77.7327 202.488 77.7918 202.531 77.883V77.8776Z" fill="white"/>
                    <path d="M199.852 80.1106H200.388C200.496 80.1106 200.576 80.0569 200.571 79.9872L200.318 77.6952C200.313 77.6308 200.217 77.5718 200.109 77.5718H199.572C199.465 77.5718 199.385 77.6255 199.39 77.6952L199.642 79.9872C199.648 80.0516 199.744 80.1106 199.852 80.1106Z" fill="#263238"/>
                    <path d="M183.933 80.1106H184.469C184.577 80.1106 184.657 80.0569 184.652 79.9871L184.4 77.6952C184.394 77.6308 184.298 77.5717 184.19 77.5717H183.653C183.546 77.5717 183.466 77.6254 183.471 77.6952L183.723 79.9871C183.729 80.0516 183.825 80.1106 183.933 80.1106Z" fill="#263238"/>
                    <path d="M191.894 80.1106H192.43C192.538 80.1106 192.618 80.0569 192.613 79.9871L192.36 77.6952C192.355 77.6308 192.259 77.5717 192.151 77.5717H191.614C191.507 77.5717 191.427 77.6254 191.432 77.6952L191.684 79.9871C191.69 80.0516 191.786 80.1106 191.894 80.1106Z" fill="#263238"/>
                    <path opacity="0.2" d="M171.717 101.414C146.328 111.28 117.741 98.6984 107.87 73.3045C97.9987 47.9107 110.591 19.3232 135.979 9.45762C161.373 -0.413295 189.961 12.1682 199.832 37.5674C209.703 62.9613 197.11 91.5488 171.717 101.414Z" fill="url(#paint1_linear_1557_18604)"/>
                    <path opacity="0.1" d="M162.788 6.91876L124.099 94.7908C123.503 94.34 122.913 93.873 122.333 93.3953C118.645 90.3465 115.382 86.7341 112.677 82.6172L146.09 6.7148C151.71 5.80768 157.356 5.90967 162.788 6.91876Z" fill="white"/>
                    <path opacity="0.1" d="M190.292 22.168L153.916 104.774C146.294 104.785 138.839 103.019 132.124 99.7182L171.844 9.51132C178.816 12.238 185.15 16.5428 190.292 22.168Z" fill="white"/>
                    <path d="M113.241 22.2055C131.593 -0.220053 164.657 -3.52646 187.083 14.8252C209.508 33.1768 212.809 66.2355 194.458 88.6611C176.101 111.092 143.042 114.393 120.616 96.0415C98.1907 77.6898 94.8897 44.6311 113.247 22.2002L113.241 22.2055ZM191.135 85.9505C207.984 65.3606 204.956 35.0018 184.361 18.1477C163.766 1.29359 133.413 4.33162 116.558 24.9215C99.7043 45.5168 102.732 75.8756 123.327 92.7297C143.922 109.584 174.276 106.546 191.135 85.9505Z" fill="#10C285"/>
                    <path d="M195.768 85.768C200.937 88.8543 205.854 92.2466 210.685 95.7463C215.516 99.2459 220.223 102.896 224.861 106.632C229.498 110.367 234.039 114.221 238.424 118.263C240.62 120.281 242.772 122.353 244.881 124.479C246.986 126.61 249.057 128.778 251 131.102C252.074 132.38 251.902 134.291 250.625 135.359C249.648 136.18 248.295 136.271 247.238 135.697C244.576 134.248 242.042 132.648 239.535 131.006C237.034 129.358 234.576 127.662 232.166 125.906C227.335 122.407 222.66 118.719 218.081 114.914C213.503 111.108 208.994 107.217 204.609 103.175C200.224 99.1332 195.924 94.9841 191.877 90.529C190.723 89.2569 190.814 87.2924 192.086 86.133C193.122 85.1883 194.625 85.0809 195.774 85.7626L195.768 85.768Z" fill="#10C285"/>
                    <path d="M201.542 84.3026L196.357 85.0916L200.072 88.3444C200.072 88.3444 203.002 87.2816 203.292 85.2849L201.548 84.3026H201.542Z" fill="#FFC3BD"/>
                    <path d="M193.556 88.06L196.814 90.6203L200.791 88.0224L196.358 85.0917L193.556 88.06Z" fill="#FFC3BD"/>
                    <path d="M169.041 93.7173C169.969 91.1033 172.578 89.2461 175.326 89.2461H218.293C221.551 89.2461 223.886 91.8279 223.51 95.0055L212.416 188.159C212.035 191.342 209.088 193.919 205.824 193.919H69.4727C66.2146 193.919 63.8798 191.337 64.2555 188.159L73.5735 109.911C73.9546 106.728 76.9014 104.152 80.1649 104.152H154.36C160.785 104.152 166.872 99.8201 169.041 93.7119V93.7173Z" fill="#10C285"/>
                    <path d="M169.041 93.7173C169.969 91.1033 172.578 89.2461 175.326 89.2461H218.293C221.551 89.2461 223.886 91.8279 223.51 95.0055L212.416 188.159C212.035 191.342 209.088 193.919 205.824 193.919H69.4727C66.2146 193.919 63.8798 191.337 64.2555 188.159L73.5735 109.911C73.9546 106.728 76.9014 104.152 80.1649 104.152H154.36C160.785 104.152 166.872 99.8201 169.041 93.7119V93.7173Z" fill="url(#paint2_linear_1557_18604)"/>
                    <path d="M66.7354 189.635H209.19L217.306 93.7763H74.8511L66.7354 189.635Z" fill="#EBEDF2"/>
                    <path d="M66.7354 189.635H209.19L215.062 93.7763H74.8511L66.7354 189.635Z" fill="#D4D9DF"/>
                    <path d="M66.7354 189.635H209.19L214.085 90.7383H71.6306L66.7354 189.635Z" fill="#EBEDF2"/>
                    <path d="M66.7354 189.635H209.19L211.128 90.7383H71.6306L66.7354 189.635Z" fill="#D4D9DF"/>
                    <path d="M66.7354 189.635H209.19L210.038 88.0545H67.5834L66.7354 189.635Z" fill="#EBEDF2"/>
                    <path d="M66.736 189.635H209.191L206.265 89.9761H64.7285L66.736 189.635Z" fill="#D4D9DF"/>
                    <path d="M66.7365 189.635H209.191L204.961 90.7383H62.5068L66.7365 189.635Z" fill="#EBEDF2"/>
                    <path d="M66.7372 189.635H209.192L202.456 92.2304H60.001L66.7372 189.635Z" fill="#D4D9DF"/>
                    <path d="M66.7365 189.635H209.191L201.381 93.0624H58.9268L66.7365 189.635Z" fill="#EBEDF2"/>
                    <path d="M138.07 99.7665C138.162 97.0881 140.18 95.188 142.928 95.188H185.895C189.153 95.188 192.315 97.8288 192.948 101.092L209.92 188.025C210.559 191.283 208.428 193.929 205.17 193.929H68.8292C65.5711 193.929 62.4096 191.288 61.7762 188.025L47.7831 116.368C47.1443 113.11 49.2752 110.464 52.5333 110.464H126.729C133.154 110.464 137.856 106.03 138.076 99.7665H138.07Z" fill="#10C285"/>
                    <path opacity="0.2" d="M138.07 99.7665C138.162 97.0881 140.18 95.188 142.928 95.188H185.895C189.153 95.188 192.315 97.8288 192.948 101.092L209.92 188.025C210.559 191.283 208.428 193.929 205.17 193.929H68.8292C65.5711 193.929 62.4096 191.288 61.7762 188.025L47.7831 116.368C47.1443 113.11 49.2752 110.464 52.5333 110.464H126.729C133.154 110.464 137.856 106.03 138.076 99.7665H138.07Z" fill="url(#paint3_linear_1557_18604)"/>
                  </g>
                  <defs>
                    <linearGradient id="paint0_linear_1557_18604" x1="189.511" y1="55.3493" x2="203.028" y2="65.2208" gradientUnits="userSpaceOnUse">
                      <stop stop-color="#125D58"/>
                      <stop offset="1" stop-color="#131F3B"/>
                    </linearGradient>
                    <linearGradient id="paint1_linear_1557_18604" x1="153.851" y1="6.09256" x2="153.851" y2="104.777" gradientUnits="userSpaceOnUse">
                      <stop stop-color="#10C285"/>
                      <stop offset="1" stop-color="#085C3F"/>
                    </linearGradient>
                    <linearGradient id="paint2_linear_1557_18604" x1="124.543" y1="113.153" x2="171.236" y2="174.207" gradientUnits="userSpaceOnUse">
                      <stop stop-color="#125D58"/>
                      <stop offset="1" stop-color="#131F3B"/>
                    </linearGradient>
                    <linearGradient id="paint3_linear_1557_18604" x1="128.852" y1="95.188" x2="128.852" y2="193.929" gradientUnits="userSpaceOnUse">
                      <stop stop-color="#00D595"/>
                      <stop offset="1" stop-color="#006F4E"/>
                    </linearGradient>
                    <clipPath id="clip0_1557_18604">
                      <rect width="275" height="200" fill="white" transform="translate(0.5)"/>
                    </clipPath>
                  </defs>
                </svg>
              </div>
              <div class="text-16-24 font-semibold text-dark">There no data yet</div>
            </div>
          </div>
        @endif
      </div>
    </main>
  </div>
  <div class="backdrop fixed inset-0 bg-black/[0.7] z-[51] transition opacity-0 invisible"></div>
@endsection
