@extends('web.layouts.commonMaster')
@section('title', 'Homework Results')
@section('vendor-style')
@endsection
@section('vendor-script')
@endsection
@push('after-scripts')
    <script src="{{ mix('js/pages/user/homeworksResult.js') }}"></script>
@endpush()

@section('content')
    @include('web.layouts.navbar')
    <div class="box__content--main transition page-student {{ auth('web')->user()->type == 'parent' ? 'page-parent' : '' }}">
        @include('web.layouts.header', ['title' => 'Homework results'])
        <main class="min-h-screen bg-neutral-50 page-grade">
            <div id="homeworks-result-root"></div>
        </main>
    </div>
    <div class="backdrop fixed inset-0 bg-black/[0.7] z-[51] transition opacity-0 invisible"></div>
@endsection
