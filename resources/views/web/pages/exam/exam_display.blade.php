@extends('web.layouts.commonMaster')
@section('title', 'Homework')
@section('vendor-style')
@endsection
@section('vendor-script')
@endsection
@push('after-scripts')
    <script>
        window.tasksData = @json($tasksData);
        window.homeworkDetail = @json($homework);
        window.account_id = @json($accountId);
    </script>
    <script src="{{ mix('js/pages/exam.js') }}"></script>
@endpush()

@section('content')
    @include('web.layouts.navbar')

    <div class="box__content--main transition page-student {{ auth('web')->user()->type == 'parent' ? 'page-parent' : '' }}">

        @include('web.layouts.header', ['title' => request()->query('classwork') == 'true' ? 'Classwork' : 'Homework'])
        <main class="min-h-screen bg-neutral-50 page-grade">
            <div id="exam-root"></div>
        </main>
    </div>
    <div class="backdrop fixed inset-0 bg-black/[0.7] z-[51] transition opacity-0 invisible"></div>
@endsection
