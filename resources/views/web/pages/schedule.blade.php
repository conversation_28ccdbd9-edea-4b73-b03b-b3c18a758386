@extends('web.layouts.commonMaster')
@section('title'){{ __('Schedule') }}
@endsection
@push('after-styles')
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css"
        integrity="sha512-1ycn6IcaQQ40/MKBW2W4Rhis/DbILU74C1vSrLJxCq57o941Ym01SwNsOMqvEBFlcgUa6xLiPY/NS5R+E6ztJQ=="
        crossorigin="anonymous" referrerpolicy="no-referrer"/>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fullcalendar/core@4.4.0/main.min.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fullcalendar/daygrid@4.4.0/main.min.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fullcalendar/timegrid@4.4.0/main.min.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fullcalendar/list@4.4.0/main.min.css">
  <style>
    .before\:bg-private::before {
      content: var(--tw-content);
      --tw-bg-opacity: 1;
      background-color: #ffc145;
    }
    .calendar__schedule .icon__status.stn{
      color: blue;
    }
    .calendar__schedule .icon__status.sto{
      color: green;
    }
    .calendar__schedule .icon__status.stl{
      color: #ffc145;
    }
    .calendar__schedule .icon__status.stx{
      color: red;
    }
    .calendar__schedule .fc-view .fc-day-header {
      text-align: left;
      padding: 4px 8px 16px 8px;
      font-size: 12px;
      font-weight: 700;
      line-height: 12px;
      color: #71717A;
      text-transform: uppercase;
    }
  </style>
@endpush
@section('content')
  @include('web.layouts.navbar')
  <div class="box__content--main transition page-student {{auth('web')->user()->type == 'parent' ? 'page-parent' : ''}}">
    @include('web.layouts.header',['title'=> 'Schedule'])
    <main class="pt-[57px] min-h-screen bg-neutral-50 flex page-schedule">
      <div class="flex-1 flex flex-col">
        <div class=" bg-neutral-100">
          <div class="container py-2">
            <div class="lg:gap-[8px] box lg:flex lg:justify-between">
              <input hidden id="student-id" value="{{$student_id}}">
              <div class="pb-2 lg:pb-0">
                @if($childs)
                  <select id="students" class="box__select px-4 py-2 rounded-lg bg-white focus:outline-none cursor-pointer" single="single">
                    @foreach($childs as $child)
                      <option value="{{$child->id}}">{{$child->name_english}}</option>
                    @endforeach
                  </select>
                @endif
              </div>
              <div class="flex gap-x-2">
                <select id="months"
                        class="box__select px-4 py-2 rounded-lg bg-white focus:outline-none cursor-pointer flex-1">
                  <option value="01">January</option>
                  <option value="02">February</option>
                  <option value="03">March</option>
                  <option value="04">April</option>
                  <option value="05">May</option>
                  <option value="06">June</option>
                  <option value="07">July</option>
                  <option value="08">August</option>
                  <option value="09">September</option>
                  <option value="10">October</option>
                  <option value="11">November</option>
                  <option value="12">December</option>
                </select>
                <select id="years"
                        class="box__select px-4 py-2 rounded-lg bg-white focus:outline-none cursor-pointer flex-1"
                        single="single">
                  <option selected value="2024">2024</option>
                  <option value="2025">2025</option>
                  <option value="2026">2026</option>
                  <option value="2027">2027</option>
                  <option value="2028">2028</option>
                  <option value="2029">2029</option>
                  <option value="2030">2030</option>
                  <option value="2031">2031</option>
                  <option value="2032">2032</option>
                  <option value="2033">2033</option>
                  <option value="2034">2034</option>
                  <option value="2035">2035</option>
                </select>
              </div>
            </div>
          </div>
        </div>
        <div class="flex flex-1 ">
          <div class="flex-1">
            <div class="p-4 lg:py-8 lg:px-[60px]">
              <div id='calendar' class="calendar__schedule"></div>
            </div>
          </div>
          <div class="w-[240px] flex-shrink-0 bg-white p-4 hidden lg:block">
            <h2 class="text-20-26 lg:text-24-32 font-semibold mb-4 lg:mb-8">Class</h2>
            <div class="mb-4 lg:mb-8 flex flex-col gap-4">
              <div class="flex items-center">
                <div class="mr-2 w-2 h-2 rounded-full bg-blue"></div>
                <div class="text-12-16 font-medium text-neutral-700">Reading Class</div>
              </div>
              <div class="flex items-center">
                <div class="mr-2 w-2 h-2 rounded-full bg-green"></div>
                <div class="text-12-16 font-medium text-neutral-700">Writing Class</div>
              </div>
              <div class="flex items-center">
                <div class="mr-2 w-2 h-2 rounded-full" style="background: #ffc145"></div>
                <div class="text-12-16 font-medium text-neutral-700">Private Class</div>
              </div>
            </div>
            <div class="flex flex-col gap-8 list-schedule">

            </div>
          </div>
        </div>
      </div>
    </main>

  </div>
  <div class="backdrop fixed inset-0 bg-black/[0.7] z-[51] transition opacity-0 invisible"></div>

@endsection
@push('after-scripts')
  <script src="https://cdn.jsdelivr.net/npm/@fullcalendar/core@4.4.0/main.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/@fullcalendar/interaction@4.4.0/main.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/@fullcalendar/daygrid@4.4.0/main.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/@fullcalendar/timegrid@4.4.0/main.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/@fullcalendar/list@4.4.0/main.min.js"></script>
  <script src="https://unpkg.com/popper.js/dist/umd/popper.min.js"></script>
  <script src="https://unpkg.com/tooltip.js/dist/umd/tooltip.min.js"></script>
  <script src="{{ mix('/assets/web/js/calendar.js') }}"></script>
@endpush

