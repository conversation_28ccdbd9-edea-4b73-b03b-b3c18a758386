@extends('web/layouts/contentNavbarLayout')

@section('title', 'Dashboard - Analytics')

@section('vendor-style')
  <link rel="stylesheet" href="{{asset('assets/vendor/libs/apex-charts/apex-charts.css')}}">
  <style>
    .detail-qty {
      max-width: 80px;
      padding: 9px 20px;
      position: relative;
      width: 100%;
      border-radius: 4px;
    }

    .detail-qty > a {
      font-size: 16px;
      position: absolute;
      right: 8px;
      color: #707070;
    }


    .detail-qty > a.qty-up {
      top: 0;
    }

    .detail-qty > a.qty-down {
      bottom: 0;
    }
  </style>
@endsection

@section('vendor-script')
  <script src="{{asset('assets/vendor/libs/apex-charts/apexcharts.js')}}"></script>
@endsection

@section('page-script')
  <script src="{{asset('assets/js/dashboards-analytics.js')}}"></script>
@endsection

@section('content')
  <div class="table-responsive text-nowrap" style="padding:  0 20px;">
    <table class="table table-hover">
      <thead>
      <tr>
        <th>Product title</th>
        <th>Quantity</th>
        <th>Price</th>
        <th>Action</th>
      </tr>
      </thead>
      <tbody class="table-border-bottom-0">
      @if(count($data))
        @foreach($data as $item)
          <tr>
            <td>{{$item->name}}</td>
            <td class="text-center" data-title="Stock">
              <div class="detail-qty border radius  m-auto d-flex align-items-center">
                <form method="post" action="{{route('web.carts.update',$item->rowId)}}">
                  @csrf
                  <input type="hidden" name="type" value="subtraction">
                  <button class="qty-down" style="font-size: 20px;background: none;border: none">-</button>
                </form>
                <span class="qty-val">{{$item->qty}}</span>
                <form method="post" action="{{route('web.carts.update',$item->rowId)}}">
                  @csrf
                  <input type="hidden" name="type" value="addition">
                  <button class="qty-down" style="font-size: 20px;background: none;border: none">+</button>
                </form>
              </div>
            </td>
            <td>{{$item->price * $item->qty}}</td>
            <td>
              <form method="post" action="{{route('web.carts.update',$item->rowId)}}">
                @csrf
                <input type="hidden" name="type" value="delete_item">
                <button style="border: none;background: none">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-trash" viewBox="0 0 16 16">
                  <path d="M5.5 5.5A.5.5 0 0 1 6 6v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5m2.5 0a.5.5 0 0 1 .5.5v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5m3 .5a.5.5 0 0 0-1 0v6a.5.5 0 0 0 1 0z"/>
                  <path d="M14.5 3a1 1 0 0 1-1 1H13v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4h-.5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1H6a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1h3.5a1 1 0 0 1 1 1zM4.118 4 4 4.059V13a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V4.059L11.882 4zM2.5 3h11V2h-11z"/></svg>
                </button>
              </form>
            </td>
          </tr>
        @endforeach
      @else
        <tr>
          <td colspan="4" class="text-center">No data</td>
        </tr>
      @endif
      </tbody>

    </table>
      <div class="d-flex justify-content-sm-between">
        <form action="{{route('web.carts.checkout')}}" method="post">
          @csrf
          <button class="btn btn-secondary">Check out</button>
          <span class="btn btn-success">Total price: {{$total}}</span>
        </form>
        <form method="post" action="{{route('web.carts.destroy',)}}">
          @method('delete')
          @csrf
          <input type="hidden" name="type" value="delete_cart">
          <button class="btn btn-danger">Delete cart</button>
        </form>
      </div>

  </div>
@endsection
