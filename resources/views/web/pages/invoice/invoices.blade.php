@extends('web.layouts.commonMaster')
@section('title'){{ __('Invoice') }}
@endsection
@push('after-styles')
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css">
  <style>
    .refund .select2-container--default .select2-selection--single{
      width: 100%;
      min-height: 45px;
    }
  </style>
@endpush
@section('content')
  @include('web.layouts.navbar')
  <div class="box__content--main transition page-student {{auth('web')->user()->type == 'parent' ? 'page-parent' : ''}}">
    @include('web.layouts.header',['title'=> 'Invoice'])
    <main class="pt-[57px] min-h-screen bg-neutral-50 lg:flex page-invoice">
      <div class="bg-neutral-200 py-2 px-4 lg:hidden invoice-select">
        <div class="bg-white text-16-24 rounded-lg">
          <div class="relative lg:hidden">
            <select name="session" class="box__select focus:outline-none cursor-pointer" single="single"
                    id="menuInvoice">
              <option value="">Subscriptions</option>
              <option value="1">Invoices</option>
              <option value="2">Refund</option>
            </select>
            <div class="absolute top-[18px] right-[22px]">
              <svg xmlns="http://www.w3.org/2000/svg" width="10" height="5" viewBox="0 0 10 5" fill="none">
                <path d="M7.96021 0.833374L5.00065 3.79293L2.04109 0.833374H7.96021Z" fill="#ABB3BF" stroke="#ABB3BF"/>
              </svg>
            </div>
          </div>
          <div class="font-semibold text-16-24 text-dark hidden lg:block">Class WR2 Fri1800</div>
        </div>
      </div>
      <div class="w-full max-w-[1320px] mx-auto p-4 lg:px-[60px] lg:py-8">
        <div class="tabs lg:flex lg:justify-between w-full lg:gap-8 xl:gap-16">
          <div class="menu__tabs">
            <ul class="py-4 lg:w-[200px] hidden lg:block">
              <li class="text-16-24 mb-6 text-neutral-500 relative w-fit hover:text-dark hover:font-semibold">
                <a href="#menuInvoiceItem">Waiting Payment</a>
              </li>
              <li class="text-16-24 mb-6 text-neutral-500 relative w-fit hover:text-dark hover:font-semibold">
                <a href="#menuInvoiceItem1">Invoices</a>
              </li>
              <li class="text-16-24 mb-6 text-neutral-500 relative w-fit hover:text-dark hover:font-semibold choosen">
                <a href="#menuInvoiceItem2">Refund</a>
              </li>
            </ul>
          </div>
          <div class="flex-1 invoice__tab--content">
            <div id="menuInvoiceItem" class="rounded-lg bg-white py-8 px-4 lg:px-10 content">
              @if(count($subs) >0)
                @foreach($subs as $key => $sub)
                  <div>
                    <div class="flex justify-between items-center mb-8">
                      <div class="text-dark font-semibold text-20-26">Invoice</div>
                      <div class="text-red text-10-16 rounded-lg font-semibold bg-[#FFF0F6] py-0.5 px-2"
                           style="text-transform: capitalize;">{{$sub->status}}</div>
                    </div>
                    <div class="lg:flex lg:justify-between lg:gap-10 lg:items-center py-3 px-4 bg-neutral-50 mb-4">
                      <div class="mb-10 lg:mb-0">
                        <div class="text-neutral-800 font-semibold text-14-20 mb-2">{{$sub->parent->name_english}}</div>
                        <div class="text-[#494F5B] text-12-16 mb-2">{{date('Y-m-d', strtotime($sub->created_at))}}</div>
                        <div class="text-neutral-500 text-12-16 break-all">
                          @foreach($sub->product as $key => $product)
                            {{$key > 0 ? ', ':''}}{{$product->prod_name}}
                          @endforeach
                        </div>
                      </div>
                      <div>
                        <div class="flex justify-between items-center mb-2">
                          <div class="text-neutral-500 text-12-16">Amount</div>
                          <div class="text-dark text-14-20 font-semibold">${{number_format($sub->price,2)}} USD</div>
                        </div>
                        <div data-href="{{$sub->url_invoice}}"
                          class="ahref bg-neutral-100 cursor-pointer rounded-lg py-2.5 px-[60px] flex justify-center items-center hover:bg-neutral-200">
                          <div
                             class="font-semibold text-neutral-700 text-14-20">Invoice</div>
                          <svg xmlns="http://www.w3.org/2000/svg" width="18" height="19" viewBox="0 0 18 19" fill="none"
                               style="width: 18px; height: 18px;" class="ml-2.5">
                            <g clip-path="url(#clip0_502_11080)">
                              <path
                                d="M12.4425 7.25H11.25V3.5C11.25 3.0875 10.9125 2.75 10.5 2.75H7.5C7.0875 2.75 6.75 3.0875 6.75 3.5V7.25H5.5575C4.89 7.25 4.5525 8.06 5.025 8.5325L8.4675 11.975C8.76 12.2675 9.2325 12.2675 9.525 11.975L12.9675 8.5325C13.44 8.06 13.11 7.25 12.4425 7.25ZM3.75 14.75C3.75 15.1625 4.0875 15.5 4.5 15.5H13.5C13.9125 15.5 14.25 15.1625 14.25 14.75C14.25 14.3375 13.9125 14 13.5 14H4.5C4.0875 14 3.75 14.3375 3.75 14.75Z"
                                fill="#373E49"/>
                            </g>
                            <defs>
                              <clipPath id="clip0_502_11080">
                                <rect width="18" height="18" fill="white" transform="translate(0 0.5)"/>
                              </clipPath>
                            </defs>
                          </svg>
                        </div>
                      </div>
                    </div>
                    <div class="flex border-t border-neutral-100 py-4 justify-between items-center">
                      <div class="font-semibold text-14-20 text-neutral-700">Total</div>
                      <div class="text-green text-20-26 font-semibold">${{number_format($sub->price,2)}} USD</div>
                    </div>
                    <div data-href="{{ config('app.wphost') }}/checkout/order-pay/{{$sub->order_id}}/?pay_for_order=true&key={{$sub->order_key}}"
                      class="ahref bg-green leading-[20px] relative w-fit py-2.5 px-[90px] mx-auto rounded-lg text-center hover:bg-[#0d9b6a] lg:px-[90px] cursor-pointer">
                      <span class="text-14-20 font-semibold text-white z-10 relative">Pay Now</span>
                    </div>
                  </div>
                @endforeach
              @else
                <div>
                  <div class="mb-6">
                    <img src="{{asset('assets/web/images/dashboard-pay.png')}}" alt="dashboard-pay" class="max-w-[205px] mx-auto">
                  </div>
                  <div class="mb-6">
                    <p class="text-14-20 text-neutral-400 font-medium text-center">
                      No outstanding payments.<br/>
                      Your account is up-to-date
                    </p>
                  </div>
                </div>
              @endif
            </div>
            <div id="menuInvoiceItem1" class="rounded-lg w-full bg-white py-8 px-4 lg:px-10 content">
              <div class="flex justify-between items-center mb-8">
                <div class="text-dark font-semibold text-20-26">Invoices<span class="text-red" style="font-size: 10px"> (Order status will be updated 1 hour after payment is completed)</span></div>
{{--                <div class="underline text-dark font-semibold text-14-20 cursor-pointer">Edit Billing Address</div>--}}
              </div>
{{--              <div class="rounded-lg p-4 bg-neutral-50 flex justify-between items-center mb-6 lg:mb-8">--}}
{{--                <div class="text-neutral-800 text-14-20">Next Payment Due</div>--}}
{{--                <div class="text-neutral-800 text-14-20">Sep 27, 2024</div>--}}
{{--              </div>--}}
              <div class="hidden lg:block">
                <table class="w-full">
                  <thead>
                  <tr class="border-b border-neutral-100">
                    <td>
                      <div class="font-semibold text-12-16 text-neutral-500 py-2 px-4">Date</div>
                    </td>
                    <td>
                      <div class="font-semibold text-12-16 text-neutral-500 py-2 px-4">Status</div>
                    </td>
                    <td>
                      <div class="font-semibold text-12-16 text-neutral-500 py-2 px-4">Product name</div>
                    </td>
                    <td>
                      <div class="font-semibold text-12-16 text-neutral-500 py-2 px-4">Amount</div>
                    </td>
                    <td>
                      <div class="flex gap-2 items-center px-4">
                        <div class="font-semibold text-12-16 text-neutral-500">Invoice</div>
                      </div>
                    </td>
                  </tr>
                  </thead>
                  <tbody>
                  @if(count($invoices) > 0)
                    <input hidden class="numInv" value="{{count($invoices)}}">
                    @foreach($invoices as $key => $invoice)
                      <input hidden class="inv{{$key}}id" value="{{$invoice->id}}">
                      <input hidden class="inv{{$key}}date" value="{{$invoice->payment_date}}">
                      <input hidden class="inv{{$key}}price" value="{{$invoice->price}}">
                      <input hidden class="inv{{$key}}name" value="@foreach($invoice->product as $key => $product){{$key > 0 ? ', ':''}}{{$product->prod_name}}@endforeach">
                    @endforeach
                  @endif
                  @if(count($allInvoices) > 0)
                    @foreach($allInvoices as $key => $invoice)
                      <tr class="border-b border-neutral-100">
                        <td style="min-width: 130px">
                          <div class="text-14-20 text-neutral-700 py-6 px-4">{{date('Y-m-d', strtotime($invoice->created_at))}}</div>
                        </td>
                        <td style="text-transform: capitalize;">
                          <div class="text-14-20 text-neutral-700 py-6 px-4">{{$invoice->status == 'request_refund' ? "Request Refund" : $invoice->status}}</div>
                        </td>
                        <td>
                          <div class="text-14-20 text-neutral-700 py-6 px-4 break-all">
                            @foreach($invoice->product as $key => $product)
                              {{$key > 0 ? ', ':''}}{{$product->prod_name}}
                            @endforeach
                          </div>
                        </td>
                        <td>
                          <div class="text-14-20 text-neutral-700 py-6 px-4">${{number_format($invoice->price,2)}} USD</div>
                        </td>
                        <td>
                          <a target="_blank" href="{{$invoice->url_invoice}}" class="text-14-20 text-green font-semibold underline py-6 px-4 cursor-pointer">Download
                          </a>
                        </td>
                      </tr>

                    @endforeach
                  @endif
                  </tbody>
                </table>
              </div>
              <div class="max-h-[424px] overflow-y-scroll lg:max-h-[136px] lg:hidden">
                @if(count($invoices) > 0)
                  @foreach($invoices as $key => $invoice)
                    <div class="px-4 py-6 border-t border-neutral-100">
                      <div class="mb-2">
                        <div class="text-12-16 text-neutral-800 font-bold mb-1">Date</div>
                        <div class="text-14-20 text-neutral-700">{{date('Y-m-d', strtotime($invoice->created_at))}}</div>
                      </div>
                      <div class="mb-2">
                        <div class="text-12-16 text-neutral-800 font-bold mb-1">Product name</div>
                        <div class="text-14-20 text-neutral-700">@foreach($invoice->product as $key => $product){{$key > 0 ? ', ':''}}{{$product->prod_name}}@endforeach</div>
                      </div>
                      <div class="mb-2">
                        <div class="text-12-16 text-neutral-800 font-bold mb-1">Amount</div>
                        <div class="text-14-20 text-neutral-700">${{number_format($invoice->price,2)}} USD</div>
                      </div>
                      <a target="_blank" href="{{$invoice->url_invoice}}" class="text-green text-14-20 font-semibold underline cursor-pointer">Download</a>
                    </div>
                  @endforeach
                @endif
              </div>
            </div>
            <div id="menuInvoiceItem2" class="rounded-lg w-full bg-white py-8 px-4 lg:px-10 content">
              <div class="flex justify-between items-center mb-8">
                <div class="text-dark font-semibold text-20-26">Refund Request</div>
              </div>
              <div class="mb-6">
                <div class="text-16-24 text-neutral-500 mb-4">Choose which transaction you want to Refund</div>
                <div class="refund">
                  <select id="refundRequest" style="width: 200px;"
                          class="flex px-4 py-3.5 border rounded-lg border-neutral-200"></select>
                </div>
              </div>
              <div class="invoice__reason mb-6">
                <div class="text-neutral-800 text-12-16 mb-1">Tell us why you want to Refund</div>
                <textarea name="message" placeholder="Send us some message" id="" cols="" rows=""
                          class="w-full text-neutral-700 rounded-lg px-4 py-3 border border-neutral-200 lg:max-h-[72px] text-14-20 placeholder:text-neutral-400"></textarea>
              </div>
              <div id="refund-invoice"
                class="bg-green leading-[20px] relative w-fit px-[90px] py-2.5 lg:py-[18px] mx-auto rounded-lg text-center hover:bg-[#0d9b6a] lg:px-[97px] cursor-pointer">
                <span class="text-14-20 font-semibold text-white z-10 relative">Request A Refund</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
  <div class="backdrop fixed inset-0 bg-black/[0.7] z-[51] transition opacity-0 invisible"></div>

@endsection
@push('after-scripts')
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"
          integrity="sha512-894YE6QWD5I59HgZOGReFYm4dnWc1Qt5NtvYSaNcOP+u1T9qYdvdihz0PPSiiqn/+/3e7Jo4EaG7TubfWGUrMQ=="
          crossorigin="anonymous" referrerpolicy="no-referrer"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"
          integrity="sha512-2ImtlRlf2VVmiGZsjm9bEyhjGW4dU7B6TNwh/hx/iSByxNENtj3WVE6o/9Lj4TJeVXPi4bnOIMXFIJJAeufa0A=="
          crossorigin="anonymous" referrerpolicy="no-referrer"></script>
  <script src="https://cdn.jsdelivr.net/npm/@fullcalendar/core@4.4.0/main.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/@fullcalendar/interaction@4.4.0/main.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/@fullcalendar/daygrid@4.4.0/main.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/@fullcalendar/timegrid@4.4.0/main.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/@fullcalendar/list@4.4.0/main.min.js"></script>
  <script src="https://unpkg.com/popper.js/dist/umd/popper.min.js"></script>
  <script src="https://unpkg.com/tooltip.js/dist/umd/tooltip.min.js"></script>
{{--  <script src="{{ mix('/assets/web/js/index.js') }}"></script>--}}
  <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
  <script>
    $('.ahref').click(function (){
      window.open($(this).attr('data-href'), '_blank');
    });
    const data = [];
    const numInv = parseInt($('.numInv').val());
    for (var i = 0; i < numInv; i++) {
      data[i] = {
        id: $(".inv"+i+"id").val(),
        text:
          '<div class="flex">' +
          '        <div class="text-14-20 text-neutral-700"> '+$(".inv"+i+"date").val()+' </div>' +
          '        <div class="text-14-20 text-neutral-700 mx-2 px-2 border-x border-neutral-300"> '+$(".inv"+i+"name").val()+' </div>' +
          '        <div class="text-14-20 text-neutral-700"> '+$(".inv"+i+"price").val()+' </div>' +
          '</div>'
      };
    }

    $("#refundRequest").select2({
      width: "100%",
      minimumResultsForSearch: -1,
      data: data,
      escapeMarkup: function(markup) {
        return markup;
      },
      templateResult: function(data) {
        return data.text;
      },
      templateSelection: function(data) {
        return data.text;
      }
    })
    $("#refund-invoice").click(function(){
      $.ajax({
        url: '{!! route('web.invoice.refund') !!}',
        type: 'POST',
        contentType: "application/json",
        dataType: 'json',
        headers: {'X-CSRF-TOKEN': "{{ csrf_token() }}"},
        data: JSON.stringify({
          'id': $('#refundRequest').val(),
          'reason_refund': $('textarea[name="message"]').val()
        }),
        cache: false,
        processData: false,
        success: function(response) {
          toastr.success('Request Refund Invoice Successful');
          location.reload();
        },
        error: function(xhr, status, error) {
          toastr.error('Request Refund Invoice Faild');
        }
      });
    });
  </script>
@endpush

