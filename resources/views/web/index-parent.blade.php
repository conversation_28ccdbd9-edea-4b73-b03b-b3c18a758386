@extends('web.layouts.commonMaster')
@section('title'){{ __('Q1 Academy') }}
@endsection
@section('content')
  @include('web.layouts.navbar')
  <div class="box__content--main transition page-parent">
    @include('web.layouts.header',['title'=> 'Dashboard'])
    <main class="pt-[57px] min-h-screen bg-neutral-50">
      <div class="w-full max-w-[1320px] mx-auto p-4 lg:py-8 lg:px-[60px]">
        <div class="flex flex-col lg:flex-row gap-4 lg:gap-6">
          <div class="lg:flex-1 min-w-0 order-2 lg:order-1">
            @foreach($students as $key => $student)
              <div class="bg-white rounded-lg py-2 px-4 mb-2 flex items-center">
                <div class="text-16-24 text-dark font-semibold mr-2 truncate">{{$student->name_english}}</div>
                <div class="text-12-16 text-dark truncate">{{$student->name_korea}}</div>
               
              </div>
            <div class="bg-white rounded-lg py-5 px-4 mb-2">
                <div class="flex justify-between items-center">
                  <button 
                    data-student-id="{{ $student->id }}"
                    data-student-time="{{ $student->kakao_message_time ?? '' }}"
                    class="openSetupPopup bg-green hover:bg-[#0d9b6a] focus:bg-[#0d9b6a] rounded-lg px-4 py-2 text-14-20 text-white font-semibold transition-colors">
                    Setup Kakao Message Time
                  </button>
                </div>

                @if($student->kakao_message_time)
                  <div id="kakao-time-{{ $student->id }}" class="text-16 text-gray-600 font-bold py-4">
                    Kakao Message Time: 
                    <span class="font-medium">
                       {{ \Carbon\Carbon::createFromFormat('H:i:s', $student->kakao_message_time)->format('H:i') }}
                    </span>
                  </div>
                @else
                  <div id="kakao-time-{{ $student->id }}" class="text-16 text-gray-600 font-bold py-4">
                    Kakao Message Time not set
                  </div>
                @endif
              </div>
         
                <div class="mb-4 lg:mb-6 relative">
                  <div class="swiper" id="dashboard-swiper-{{$key}}" data-id="{{$key}}">
                    <div class="swiper-wrapper">
                      @foreach($student->classes as $class)
                        @php
                          $last = null;
                          foreach ($class->schedule as $schedule){
                              if (date('Y-m-d') < $schedule->date) {
                                  $last = $schedule;
                              }
                          }
                          if(!$last) continue;
                        @endphp
                        <div class="swiper-slide">
                          <div class="flex-1 shadow-00000005 bg-white rounded-2xl p-4 h-full">
                            <div class="flex items-center justify-between gap-4 mb-8">
                              <div>
                                <h3 class="text-16-24 font-semibold text-neutral-800 mb-1 trim200">{{$class->name}}</h3>
                                <div class="text-14-20 text-neutral-400 text-left">{{$class->subject}}</div>
                              </div>
                               
                              <div class="w-8 h-8 flex items-center justify-center flex-shrink-0">
                                @if($class->subject == 'Reading')
                                  <svg width="33" height="32" viewBox="0 0 33 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M30.8208 6.49858C26.2019 4.94593 21.4083 5.0664 16.8164 6.86028C12.2212 5.06517 7.42387 4.9457 2.8017 6.50205C2.21199 6.70058 1.81641 7.25581 1.81641 7.87805V25.9553C1.81641 26.9268 2.7477 27.6284 3.68082 27.3582C7.1157 26.3636 10.6334 26.2871 14.0811 27.1269V27.127C14.0811 27.6735 14.5242 28.1165 15.0706 28.1165H18.5621C19.1086 28.1165 19.5516 27.6734 19.5516 27.127C22.9994 26.2871 26.5172 26.3636 29.952 27.3581C30.8851 27.6284 31.8164 26.9269 31.8164 25.9554V7.87805C31.8164 7.25187 31.4144 6.69811 30.8208 6.49858Z" fill="url(#paint0_linear_313_412)"/>
                                    <path d="M31.816 7.87716V25.9548C31.816 26.2766 31.7136 26.5683 31.5424 26.8054C31.3683 26.5624 31.1189 26.373 30.8207 26.273C30.2136 26.0688 29.6036 25.8937 28.9916 25.7476C28.1177 25.5388 27.5024 24.7563 27.5024 23.8578V5.6748C28.6148 5.85422 29.723 6.12892 30.8207 6.49775C31.4142 6.69775 31.816 7.25127 31.816 7.87716Z" fill="url(#paint1_linear_313_412)"/>
                                    <path d="M1.81641 19.8809V25.955C1.81641 26.9264 2.7477 27.6281 3.68082 27.3579C7.1157 26.3633 10.6334 26.2867 14.0811 27.1266V27.1266C14.0811 27.6731 14.5242 28.1162 15.0707 28.1162H18.5622C19.1087 28.1162 19.5517 27.6731 19.5517 27.1266C22.9996 26.2867 26.5173 26.3632 29.9521 27.3578C30.8851 27.628 31.8164 26.9264 31.8164 25.955V19.8809H1.81641V19.8809Z" fill="url(#paint2_linear_313_412)"/>
                                    <path d="M29.9516 27.3579C30.8847 27.628 31.816 26.9264 31.816 25.955V19.8809H19.5513V27.1266C22.9991 26.2867 26.5168 26.3632 29.9516 27.3579Z" fill="url(#paint3_linear_313_412)"/>
                                    <path d="M3.68076 27.3579C2.74764 27.628 1.81641 26.9264 1.81641 25.955V19.8809H14.0811V27.1266C10.6333 26.2867 7.11558 26.3632 3.68076 27.3579Z" fill="url(#paint4_linear_313_412)"/>
                                    <path d="M29.589 6.65648C25.3764 5.24037 21.0044 5.35025 16.8164 6.98637C12.6254 5.34913 8.25003 5.24013 4.03433 6.65966C3.49651 6.84078 3.13574 7.34713 3.13574 7.91466V24.4019C3.13574 25.288 3.98509 25.9279 4.83621 25.6815C7.96897 24.7744 10.942 24.7045 14.0864 25.4705V25.4706C14.0864 25.9691 14.4904 26.3731 14.9889 26.3731H18.6439C19.1423 26.3731 19.5464 25.9691 19.5464 25.4706C22.6909 24.7046 25.664 24.7744 28.7967 25.6815C29.6477 25.9279 30.497 25.2881 30.497 24.4021V7.9146C30.497 7.34348 30.1304 6.83848 29.589 6.65648Z" fill="url(#paint5_linear_313_412)"/>
                                    <path d="M29.589 6.65648C25.3764 5.24037 21.0044 5.35025 16.8164 6.98637C12.6254 5.34913 8.25003 5.24013 4.03433 6.65966C3.49651 6.84078 3.13574 7.34713 3.13574 7.91466V24.4019C3.13574 25.288 3.98509 25.9279 4.83621 25.6815C7.96897 24.7744 10.942 24.7045 14.0864 25.4705V25.4706C14.0864 25.9691 14.4904 26.3731 14.9889 26.3731H18.6439C19.1423 26.3731 19.5464 25.9691 19.5464 25.4706C22.6909 24.7046 25.664 24.7744 28.7967 25.6815C29.6477 25.9279 30.497 25.2881 30.497 24.4021V7.9146C30.497 7.34348 30.1304 6.83848 29.589 6.65648Z" fill="url(#paint6_linear_313_412)"/>
                                    <path d="M16.8164 18.7046V26.373H18.6439C19.1423 26.373 19.5464 25.9689 19.5464 25.4705C22.6909 24.7045 25.664 24.7742 28.7966 25.6813C29.6477 25.9278 30.497 25.2879 30.497 24.4019V18.7046H16.8164V18.7046Z" fill="url(#paint7_linear_313_412)"/>
                                    <path d="M4.04374 4.94506C3.50239 5.12706 3.13574 5.63206 3.13574 6.20318V22.6905C3.13574 23.5731 3.98074 24.2181 4.82851 23.9722C8.7975 22.8212 12.888 23.0129 16.8164 24.5477V5.27494C12.6285 3.63889 8.2565 3.52889 4.04374 4.94506Z" fill="url(#paint8_linear_313_412)"/>
                                    <path d="M14.3752 22.5555C11.4478 22.0278 8.58148 22.0277 5.65371 22.5555C5.17113 22.6512 4.78271 22.3522 4.78271 21.866C4.78271 21.307 4.78271 21.0275 4.78271 20.4685C4.78271 19.9823 5.17113 19.493 5.65371 19.3973C8.58148 18.8695 11.4478 18.8695 14.3752 19.3973C14.8578 19.493 15.2462 19.9823 15.2462 20.4685V21.866C15.2462 22.3522 14.8578 22.6513 14.3752 22.5555Z" fill="url(#paint9_linear_313_412)"/>
                                    <path d="M14.3752 17.5903C11.4478 17.0625 8.58148 17.0625 5.65371 17.5903C5.17113 17.686 4.78271 17.387 4.78271 16.9008C4.78271 13.5406 4.78271 10.1804 4.78271 6.82018C4.78271 6.33395 5.17113 5.84465 5.65371 5.74889C8.58154 5.22101 11.4478 5.22112 14.3752 5.74895C14.8578 5.84465 15.2462 6.33395 15.2462 6.82018V16.9008C15.2462 17.3869 14.8578 17.686 14.3752 17.5903Z" fill="url(#paint10_linear_313_412)"/>
                                    <path d="M29.589 4.94506C25.3764 3.52894 21.0044 3.63882 16.8164 5.27494V24.5477C20.7449 23.013 24.8355 22.8211 28.8043 23.9722C29.652 24.218 30.497 23.5732 30.497 22.6905V6.20318C30.497 5.63206 30.1304 5.127 29.589 4.94506Z" fill="url(#paint11_linear_313_412)"/>
                                    <path d="M29.5894 4.94479C26.087 3.76743 22.4745 3.64532 18.9491 4.57779C18.5946 4.67155 18.3462 4.99037 18.3462 5.35708V12.2043L29.9265 23.7847C30.2671 23.5464 30.4974 23.1508 30.4974 22.6902V6.2029C30.4974 5.63179 30.1308 5.12673 29.5894 4.94479Z" fill="url(#paint12_linear_313_412)"/>
                                    <path d="M18.1694 5.36099V11.8435C18.1694 12.2448 18.6547 12.4457 18.9384 12.162L20.4011 10.6993C20.8254 10.275 21.5134 10.275 21.9377 10.6993L23.4004 12.162C23.6841 12.4457 24.1694 12.2448 24.1694 11.8435V4.64016C24.1694 4.21646 23.8214 3.87781 23.3977 3.88387C21.857 3.90599 20.3171 4.12522 18.7941 4.54169C18.4252 4.64263 18.1694 4.97846 18.1694 5.36099Z" fill="url(#paint13_linear_313_412)"/>
                                    <path d="M18.1694 7.646V11.8438C18.1694 12.2451 18.6547 12.4461 18.9384 12.1623L20.4011 10.6996C20.8254 10.2753 21.5134 10.2753 21.9377 10.6996L23.4004 12.1623C23.6841 12.4461 24.1694 12.2451 24.1694 11.8438V7.646H18.1694Z" fill="url(#paint14_linear_313_412)"/>
                                    <defs>
                                      <linearGradient id="paint0_linear_313_412" x1="6.4887" y1="6.59128" x2="26.9074" y2="27.01" gradientUnits="userSpaceOnUse">
                                        <stop stop-color="#F7E07D"/>
                                        <stop offset="1" stop-color="#E69642"/>
                                      </linearGradient>
                                      <linearGradient id="paint1_linear_313_412" x1="31.9906" y1="16.499" x2="29.5758" y2="16.2457" gradientUnits="userSpaceOnUse">
                                        <stop stop-color="#D52C1C" stop-opacity="0"/>
                                        <stop offset="0.28" stop-color="#D12C1E" stop-opacity="0.28"/>
                                        <stop offset="0.5735" stop-color="#C42C23" stop-opacity="0.574"/>
                                        <stop offset="0.8721" stop-color="#AF2B2C" stop-opacity="0.872"/>
                                        <stop offset="1" stop-color="#A42B31"/>
                                      </linearGradient>
                                      <linearGradient id="paint2_linear_313_412" x1="16.8164" y1="23.4495" x2="16.8164" y2="28.6666" gradientUnits="userSpaceOnUse">
                                        <stop stop-color="#D52C1C" stop-opacity="0"/>
                                        <stop offset="0.28" stop-color="#D12C1E" stop-opacity="0.28"/>
                                        <stop offset="0.5735" stop-color="#C42C23" stop-opacity="0.574"/>
                                        <stop offset="0.8721" stop-color="#AF2B2C" stop-opacity="0.872"/>
                                        <stop offset="1" stop-color="#A42B31"/>
                                      </linearGradient>
                                      <linearGradient id="paint3_linear_313_412" x1="25.6836" y1="22.5362" x2="25.6836" y2="27.3098" gradientUnits="userSpaceOnUse">
                                        <stop stop-color="#D52C1C" stop-opacity="0"/>
                                        <stop offset="0.28" stop-color="#D12C1E" stop-opacity="0.28"/>
                                        <stop offset="0.5735" stop-color="#C42C23" stop-opacity="0.574"/>
                                        <stop offset="0.8721" stop-color="#AF2B2C" stop-opacity="0.872"/>
                                        <stop offset="1" stop-color="#A42B31"/>
                                      </linearGradient>
                                      <linearGradient id="paint4_linear_313_412" x1="7.94876" y1="22.5362" x2="7.94876" y2="27.3098" gradientUnits="userSpaceOnUse">
                                        <stop stop-color="#D52C1C" stop-opacity="0"/>
                                        <stop offset="0.28" stop-color="#D12C1E" stop-opacity="0.28"/>
                                        <stop offset="0.5735" stop-color="#C42C23" stop-opacity="0.574"/>
                                        <stop offset="0.8721" stop-color="#AF2B2C" stop-opacity="0.872"/>
                                        <stop offset="1" stop-color="#A42B31"/>
                                      </linearGradient>
                                      <linearGradient id="paint5_linear_313_412" x1="16.8164" y1="19.9595" x2="16.8164" y2="24.4694" gradientUnits="userSpaceOnUse">
                                        <stop stop-color="#EEF4FF"/>
                                        <stop offset="1" stop-color="#CFE7FD"/>
                                      </linearGradient>
                                      <linearGradient id="paint6_linear_313_412" x1="16.8164" y1="21.1752" x2="16.8164" y2="28.8813" gradientUnits="userSpaceOnUse">
                                        <stop stop-color="#8AAADC" stop-opacity="0"/>
                                        <stop offset="1" stop-color="#8AAADC"/>
                                      </linearGradient>
                                      <linearGradient id="paint7_linear_313_412" x1="20.3752" y1="22.5388" x2="16.1692" y2="22.5388" gradientUnits="userSpaceOnUse">
                                        <stop stop-color="#8AAADC" stop-opacity="0"/>
                                        <stop offset="1" stop-color="#8AAADC"/>
                                      </linearGradient>
                                      <linearGradient id="paint8_linear_313_412" x1="-0.0470796" y1="4.23612" x2="17.8596" y2="22.1428" gradientUnits="userSpaceOnUse">
                                        <stop stop-color="#EEF4FF"/>
                                        <stop offset="1" stop-color="#CFE7FD"/>
                                      </linearGradient>
                                      <linearGradient id="paint9_linear_313_412" x1="5.48036" y1="16.572" x2="18.582" y2="29.6737" gradientUnits="userSpaceOnUse">
                                        <stop stop-color="#EEF4FF"/>
                                        <stop offset="1" stop-color="#CFE7FD"/>
                                      </linearGradient>
                                      <linearGradient id="paint10_linear_313_412" x1="10.1337" y1="11.9185" x2="23.2353" y2="25.0201" gradientUnits="userSpaceOnUse">
                                        <stop stop-color="#EEF4FF"/>
                                        <stop offset="1" stop-color="#CFE7FD"/>
                                      </linearGradient>
                                      <linearGradient id="paint11_linear_313_412" x1="16.604" y1="8.04876" x2="29.7161" y2="21.1609" gradientUnits="userSpaceOnUse">
                                        <stop stop-color="#EEF4FF"/>
                                        <stop offset="1" stop-color="#CFE7FD"/>
                                      </linearGradient>
                                      <linearGradient id="paint12_linear_313_412" x1="29.6078" y1="14.2268" x2="18.0195" y2="6.13861" gradientUnits="userSpaceOnUse">
                                        <stop stop-color="#8AAADC" stop-opacity="0"/>
                                        <stop offset="1" stop-color="#8AAADC"/>
                                      </linearGradient>
                                      <linearGradient id="paint13_linear_313_412" x1="18.3241" y1="5.33416" x2="23.0771" y2="10.0872" gradientUnits="userSpaceOnUse">
                                        <stop stop-color="#DC4955"/>
                                        <stop offset="1" stop-color="#C4237C"/>
                                      </linearGradient>
                                      <linearGradient id="paint14_linear_313_412" x1="21.1694" y1="8.11658" x2="21.1694" y2="11.8817" gradientUnits="userSpaceOnUse">
                                        <stop stop-color="#8A1958" stop-opacity="0"/>
                                        <stop offset="1" stop-color="#8A1958"/>
                                      </linearGradient>
                                    </defs>
                                  </svg>
                                @elseif($class->subject == 'Writing')
                                  <svg width="33" height="32" viewBox="0 0 33 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M21.8848 31H1.92409C1.68044 31 1.48291 30.8025 1.48291 30.5589C1.48291 30.3152 1.68044 30.1177 1.92409 30.1177H21.8848C22.1286 30.1177 22.326 30.3152 22.326 30.5589C22.326 30.8025 22.1286 31 21.8848 31Z" fill="#766E6E"/>
                                    <path d="M30.5513 1.93127C29.3925 0.772448 27.5427 0.684272 26.2789 1.72757L13.6293 12.1704C12.3815 13.2006 11.3543 14.4718 10.6092 15.908L8.58447 19.8108L12.6718 23.8981L16.5746 21.8734C18.0109 21.1283 19.2821 20.1011 20.3122 18.8533L30.7551 6.20374C31.7984 4.93992 31.7102 3.09009 30.5513 1.93127Z" fill="url(#paint0_linear_313_5104)"/>
                                    <path d="M30.5516 1.93101C29.6414 1.02083 28.3051 0.771477 27.1611 1.21177C27.565 1.36707 27.9429 1.60712 28.2667 1.93101C29.4255 3.08983 29.5137 4.93965 28.4704 6.20347L18.0275 18.8531C16.9974 20.1008 15.7262 21.1281 14.29 21.8732L11.8916 23.1174L12.6721 23.8979L16.5748 21.8732C18.0111 21.1281 19.2823 20.1009 20.3124 18.8531L30.7553 6.20347C31.7987 4.93965 31.7104 3.08983 30.5516 1.93101Z" fill="url(#paint1_linear_313_5104)"/>
                                    <path d="M12.7084 23.9346L12.0038 24.6392C11.2942 25.3488 10.8956 26.3112 10.8956 27.3148C10.8956 28.9198 9.37532 30.0895 7.82391 29.6781C6.11397 29.2247 4.30021 29.3726 2.68633 30.0969C2.49574 30.1825 2.30039 29.9872 2.38591 29.7965C3.11027 28.1826 3.25815 26.3689 2.80474 24.6589C2.39339 23.1075 3.56309 21.5873 5.16809 21.5873C6.17162 21.5873 7.13403 21.1886 7.84362 20.4791L8.54827 19.7744L12.7084 23.9346Z" fill="url(#paint2_linear_313_5104)"/>
                                    <path d="M8.54806 19.7744L7.84341 20.4791C7.69571 20.6268 7.53653 20.7601 7.36882 20.8801L9.7187 23.23C10.1079 23.6192 10.1079 24.2501 9.7187 24.6392C9.00912 25.3488 8.61047 26.3112 8.61047 27.3148C8.61047 28.3209 8.013 29.1557 7.18506 29.5375C7.39912 29.5752 7.61223 29.6221 7.82371 29.6781C9.37512 30.0895 10.8954 28.9198 10.8954 27.3148C10.8954 26.3112 11.294 25.3488 12.0036 24.6392L12.7082 23.9346L8.54806 19.7744Z" fill="url(#paint3_linear_313_5104)"/>
                                    <path d="M9.25602 17.2267L8.3859 19.0618C8.15178 19.5556 8.25349 20.1433 8.63984 20.5296L11.953 23.8428C12.3394 24.2292 12.927 24.3308 13.4208 24.0967L15.2559 23.2266C15.7106 23.011 15.8146 22.4101 15.4587 22.0542L10.4283 17.0238C10.0726 16.6681 9.47166 16.772 9.25602 17.2267Z" fill="url(#paint4_linear_313_5104)"/>
                                    <path d="M15.4588 22.0543L10.4284 17.0239C10.0726 16.6681 9.47156 16.772 9.25597 17.2267L8.96338 17.8438L13.1738 22.0542C13.5297 22.4101 13.4257 23.0111 12.971 23.2266L11.8625 23.7522L11.953 23.8428C12.3394 24.2292 12.927 24.3308 13.4208 24.0967L15.2559 23.2266C15.7107 23.0111 15.8146 22.4101 15.4588 22.0543Z" fill="url(#paint5_linear_313_5104)"/>
                                    <path d="M8.26068 24.0929C7.7358 23.6295 6.93427 23.6188 6.39827 24.0693C5.93192 24.4613 5.78345 25.0719 5.9528 25.603C6.00039 25.7523 5.96186 25.9156 5.85104 26.0264L3.0141 28.8633C2.75486 29.1226 2.54862 29.4289 2.40039 29.7642C2.39562 29.775 2.3908 29.7859 2.38592 29.7967C2.30039 29.9873 2.49568 30.1826 2.68633 30.0971C2.68633 30.0971 2.68639 30.0971 2.68645 30.097C3.03486 29.9499 3.35186 29.7367 3.61933 29.4693L6.45662 26.632C6.56574 26.5229 6.72686 26.4806 6.87368 26.5282C7.36886 26.6886 7.93415 26.5722 8.32745 26.1789C8.90756 25.5987 8.88527 24.6443 8.26068 24.0929Z" fill="url(#paint6_linear_313_5104)"/>
                                    <defs>
                                      <linearGradient id="paint0_linear_313_5104" x1="8.70794" y1="12.4491" x2="30.2073" y2="12.4491" gradientUnits="userSpaceOnUse">
                                        <stop stop-color="#537983"/>
                                        <stop offset="1" stop-color="#3E5959"/>
                                      </linearGradient>
                                      <linearGradient id="paint1_linear_313_5104" x1="3.31313" y1="12.4489" x2="27.9468" y2="12.4489" gradientUnits="userSpaceOnUse">
                                        <stop stop-color="#537983"/>
                                        <stop offset="1" stop-color="#384949"/>
                                      </linearGradient>
                                      <linearGradient id="paint2_linear_313_5104" x1="1.52792" y1="24.946" x2="11.928" y2="24.946" gradientUnits="userSpaceOnUse">
                                        <stop stop-color="#FFE177"/>
                                        <stop offset="0.5725" stop-color="#FEB137"/>
                                        <stop offset="1" stop-color="#FE646F"/>
                                      </linearGradient>
                                      <linearGradient id="paint3_linear_313_5104" x1="-3.6107" y1="24.7677" x2="12.0171" y2="24.7677" gradientUnits="userSpaceOnUse">
                                        <stop stop-color="#FFE177"/>
                                        <stop offset="0.5725" stop-color="#FEB137"/>
                                        <stop offset="1" stop-color="#FE646F"/>
                                      </linearGradient>
                                      <linearGradient id="paint4_linear_313_5104" x1="8.27984" y1="20.516" x2="16.4799" y2="20.516" gradientUnits="userSpaceOnUse">
                                        <stop stop-color="#F9F3F1"/>
                                        <stop offset="0.5725" stop-color="#EFE2DD"/>
                                        <stop offset="1" stop-color="#CDBFBA"/>
                                      </linearGradient>
                                      <linearGradient id="paint5_linear_313_5104" x1="3.6972" y1="20.516" x2="14.6279" y2="20.516" gradientUnits="userSpaceOnUse">
                                        <stop stop-color="#F9F3F1"/>
                                        <stop offset="0.5725" stop-color="#EFE2DD"/>
                                        <stop offset="1" stop-color="#CDBFBA"/>
                                      </linearGradient>
                                      <linearGradient id="paint6_linear_313_5104" x1="1.98462" y1="26.928" x2="5.18009" y2="26.928" gradientUnits="userSpaceOnUse">
                                        <stop stop-color="#537983"/>
                                        <stop offset="1" stop-color="#384949"/>
                                      </linearGradient>
                                    </defs>
                                  </svg>
                                @endif
                              </div>
                            </div>
                            <div class="flex items-center justify-between gap-2">
                              <div class="flex items-center gap-2">
                                <div class="w-4 h-4 flex-shrink-0">
                                  <svg width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <g clip-path="url(#clip0_393_3376)">
                                      <path d="M16.0659 6.1585L17.2492 4.97516C16.8909 4.55016 16.4992 4.15016 16.0742 3.80016L14.8909 4.9835C13.5992 3.95016 11.9742 3.3335 10.2075 3.3335C6.06585 3.3335 2.70752 6.69183 2.70752 10.8335C2.70752 14.9752 6.05752 18.3335 10.2075 18.3335C14.3575 18.3335 17.7075 14.9752 17.7075 10.8335C17.7075 9.07516 17.0909 7.45016 16.0659 6.1585ZM10.2075 16.6752C6.98252 16.6752 4.37419 14.0668 4.37419 10.8418C4.37419 7.61683 6.98252 5.0085 10.2075 5.0085C13.4325 5.0085 16.0409 7.61683 16.0409 10.8418C16.0409 14.0668 13.4325 16.6752 10.2075 16.6752Z" fill="#575F6B"/>
                                      <path d="M7.70801 0.841309H12.708V2.50798H7.70801V0.841309Z" fill="#575F6B"/>
                                      <path d="M11.0413 11.6746H9.37467V6.67464H11.0413V11.6746Z" fill="#575F6B"/>
                                    </g>
                                    <defs>
                                      <clipPath id="clip0_393_3376">
                                        <rect width="20" height="20" fill="white" transform="translate(0.149414)"/>
                                      </clipPath>
                                    </defs>
                                  </svg>
                                </div>
                                @php
                                  $last = null;
                                  foreach ($class->schedule as $schedule){
                                      if (date('Y-m-d') < $schedule->date) {
                                          $last = $schedule;
                                      }
                                  }
                                @endphp
                                @if($last)
                                  <div class="text-14-20 font-medium text-neutral-500">{{ \Carbon\Carbon::parse($last->date)->format('l') }}, {{ $last->start }}</div>
                                @endif
                              </div>
                              @if($class->type == 'Regular')
                                <a href="{{route('web.grades.index', ['student_id'=>$student->id,'session' => 1, 'term' => $class->term])}}" class="flex items-center gap-2">
                                  <div class="text-14-20 font-semibold text-green">Grade</div>
                                  <div class="w-[18px] h-[18px]">
                                    <svg width="19" height="18" viewBox="0 0 19 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M4.23291 9H14.7329M14.7329 9L9.48291 3.75M14.7329 9L9.48291 14.25" stroke="#10C285" stroke-width="1.66667" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                  </div>
                                </a>
                              @endif
                            </div>
                              <div class="flex items-center justify-between gap-4 py-5">
                               <button 
                                data-student-id="{{ $student->id }}"
                                data-student-name="{{ $student->name_english }}"
                                data-class-id="{{ $class->id }}"
                                data-class-name="{{ $class->name }}"
                                data-last-schedule="{{ $last->date }} {{ $last->start }}"
                                class="openRequestPopup bg-green hover:bg-[#0d9b6a] focus:bg-[#0d9b6a] rounded-lg px-4 py-2 text-14-20 text-white font-semibold transition-colors">
                                Request Make-up Class
                              </button>
                             </div>
                          </div>
                        </div>
                      @endforeach
                        @if($student->lastReport)
                          @php
                            $lastReport = $student->lastReport;
                          @endphp
                          <div class="swiper-slide">
                            <div class="flex-1 shadow-00000005 bg-white rounded-2xl p-4 h-full">
                            <div class="flex items-center justify-between gap-4 mb-8">
                              <div>
                                <h3 class="text-16-24 font-semibold text-neutral-800 mb-1">Monthy report</h3>
                                @if($lastReport->type =='regular')
                                  <div class="text-14-20 text-neutral-400 text-left">Writing, Reading</div>
                                @elseif($lastReport->type == 'private')
                                  <div class="text-14-20 text-neutral-400 text-left">{{$lastReport->classes->subject}}</div>
                                @endif
                              </div>
                              <div class="w-8 h-8 flex items-center justify-center flex-shrink-0">
                                <svg width="33" height="32" viewBox="0 0 33 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                                  <path d="M5.15918 2.87529V29.1253C5.15918 30.1564 6.00277 31 7.03388 31H25.2639C26.2953 31 27.1392 30.1561 27.1392 29.1247V7.90558C27.1392 7.32988 26.9105 6.77776 26.5034 6.37064L21.7685 1.63576C21.3614 1.22871 20.8093 1 20.2336 1H7.03447C6.00306 1 5.15918 1.84388 5.15918 2.87529Z" fill="url(#paint0_linear_313_6375)"/>
                                  <path d="M10.1562 15.4733H22.1431C22.4419 15.4733 22.684 15.2311 22.684 14.9323V13.6132C22.684 13.3144 22.4419 13.0723 22.1431 13.0723H10.1562C9.85741 13.0723 9.61523 13.3144 9.61523 13.6132V14.9323C9.61523 15.2311 9.85741 15.4733 10.1562 15.4733Z" fill="#EBEFF0"/>
                                  <path d="M10.1562 19.2213H22.1431C22.4419 19.2213 22.684 18.9791 22.684 18.6804V17.3613C22.684 17.0625 22.4419 16.8203 22.1431 16.8203H10.1562C9.85741 16.8203 9.61523 17.0625 9.61523 17.3613V18.6804C9.61523 18.9791 9.85741 19.2213 10.1562 19.2213Z" fill="#EBEFF0"/>
                                  <path d="M10.1562 22.9688H22.1431C22.4419 22.9688 22.684 22.7266 22.684 22.4279V21.1088C22.684 20.81 22.4419 20.5679 22.1431 20.5679H10.1562C9.85741 20.5679 9.61523 20.81 9.61523 21.1088V22.4279C9.61523 22.7266 9.85741 22.9688 10.1562 22.9688Z" fill="#EBEFF0"/>
                                  <path d="M10.0069 26.7169H16.0748C16.2911 26.7169 16.4665 26.5415 16.4665 26.3252V24.7076C16.4665 24.4913 16.2911 24.3159 16.0748 24.3159H10.0069C9.79059 24.3159 9.61523 24.4913 9.61523 24.7076V26.3252C9.61523 26.5415 9.79059 26.7169 10.0069 26.7169Z" fill="#EBEFF0"/>
                                  <path d="M21.7687 1.63558C21.4899 1.35676 21.143 1.1617 20.7671 1.06641V7.17946L27.1394 13.5518V7.90534C27.1394 7.32964 26.9107 6.77752 26.5037 6.3704L21.7687 1.63558Z" fill="url(#paint1_linear_313_6375)"/>
                                  <path d="M27.0751 7.379C27.082 7.40912 27.0884 7.43847 27.0942 7.46676H21.3502C20.9435 7.46676 20.6138 7.13706 20.6138 6.73029V1.03418C20.6587 1.04218 20.7058 1.05153 20.7545 1.0623C21.1827 1.157 21.5731 1.37718 21.8832 1.68724L26.4511 6.25518C26.76 6.56406 26.9769 6.95341 27.0751 7.379Z" fill="url(#paint2_linear_313_6375)"/>
                                  <path d="M27.1392 27.3057V29.1244C27.1392 30.1558 26.2953 30.9997 25.2639 30.9997H7.03388C6.00277 30.9997 5.15918 30.1561 5.15918 29.125V27.3057H27.1392Z" fill="url(#paint3_linear_313_6375)"/>
                                  <defs>
                                    <linearGradient id="paint0_linear_313_6375" x1="10.3935" y1="12.6969" x2="24.6638" y2="26.9671" gradientUnits="userSpaceOnUse">
                                      <stop stop-color="#7FAEF4"/>
                                      <stop offset="1" stop-color="#4C8DF1"/>
                                    </linearGradient>
                                    <linearGradient id="paint1_linear_313_6375" x1="25.2733" y1="11.2191" x2="23.0086" y2="4.60146" gradientUnits="userSpaceOnUse">
                                      <stop stop-color="#4C8DF1" stop-opacity="0"/>
                                      <stop offset="1" stop-color="#4256AC"/>
                                    </linearGradient>
                                    <linearGradient id="paint2_linear_313_6375" x1="21.3418" y1="4.45459" x2="23.9725" y2="7.08529" gradientUnits="userSpaceOnUse">
                                      <stop stop-color="#A7C5FD"/>
                                      <stop offset="1" stop-color="#7FAEF4"/>
                                    </linearGradient>
                                    <linearGradient id="paint3_linear_313_6375" x1="16.1492" y1="28.2762" x2="16.1492" y2="31.1931" gradientUnits="userSpaceOnUse">
                                      <stop stop-color="#4C8DF1" stop-opacity="0"/>
                                      <stop offset="1" stop-color="#4256AC"/>
                                    </linearGradient>
                                  </defs>
                                </svg>
                              </div>
                            </div>
                            <div class="flex items-center justify-between gap-4">
                              <div class="flex items-center gap-2">
                                @if($lastReport->type =='regular')
                                  <div class="text-14-20 font-medium text-neutral-500">{{$lastReport->term->type}}, S{{$lastReport->session}}-S{{$lastReport->session + 3}}</div>
                                @elseif($lastReport->type == 'private')
                                  <div class="text-14-20 font-medium text-neutral-500">{{$lastReport->date_private}}</div>
                                @endif
                              </div>
                                <a href="{{route('web.reports.regular',['session' => $lastReport->session, 'term' => $lastReport->term_id,'student_id'=> $student->id])}}" class="flex items-center gap-2">
                                <div class="text-14-20 font-semibold text-green">View</div>
                                <div class="w-[18px] h-[18px]">
                                  <svg width="19" height="18" viewBox="0 0 19 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M4.23291 9H14.7329M14.7329 9L9.48291 3.75M14.7329 9L9.48291 14.25" stroke="#10C285" stroke-width="1.66667" stroke-linecap="round" stroke-linejoin="round"/>
                                  </svg>
                                </div>
                              </a>
                            </div>
                            </div>
                          </div>
                        @endif
                    </div>
                  </div>
                  <div class="swiper-button-next swiper-button-next-{{$key}}"></div>
                  <div class="swiper-button-prev swiper-button-prev-{{$key}}"></div>
                </div>
            @endforeach
              <div class="bg-white rounded-lg border border-neutral-100 py-6 px-4">
                <div class="mb-4 flex items-center justify-between gap-2">
                  <h2 class="text-24-32 font-semibold text-dark">Message</h2>
                  <a href="{{route('web.messages.index')}}" class="rounded-lg border border-neutral-200 p-[11px] flex items-center gap-[10px]">
                    <div class="text-14-20 font-semibold text-neutral-700">Show all</div>
                    <div class="w-[18px] h-[18px] flex-shrink-0">
                      <svg width="19" height="18" viewBox="0 0 19 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M4.23291 9H14.7329M14.7329 9L9.48291 3.75M14.7329 9L9.48291 14.25" stroke="#10C285" stroke-width="1.66667" stroke-linecap="round" stroke-linejoin="round"/>
                      </svg>
                    </div>
                  </a>
                </div>
                <div class="flex flex-col gap-2">
                  @foreach($mes as $key => $me)
                    @if(isset($me->message) && $key <5)
                      <a href="{{route('web.messages.show',$me->message->id)}}" class="p-3 bg-neutral-50 rounded-lg flex justify-content-between items-start gap-6 lg:gap-10">
                        <div class="truncate flex-1">
                          <div class="text-[#242834] mb-2 text-14-20 font-semibold truncate">{{$me->message->name}}</div>
                          <div class="line-clamp-1 text-12-16 text-[#494F5B] truncate">
                            {!! $me->message->content !!}
                          </div>
                        </div>
                        <div class="flex items-center gap-1 flex-shrink-0">
                          <div class="text-12-16 text-[#494F5B]">{{\Carbon\Carbon::parse($me->message->updated_at)->format('Y/m/d H:i A')}}</div>
                          @if($me->is_read == 0)
                            <div class="rounded-full bg-green flex-shrink-0 px-[6px] text-white text-12-16">1</div>
                          @endif
                        </div>
                      </a>
                    @endif
                  @endforeach
                </div>
              </div>

          </div>
          <div class="lg:w-[360px] flex-shrink-0 order-1 lg:order-2">
            <div class="rounded-lg border border-neutral-100 bg-white py-6 px-4 mb-6">
              <div class="flex items-center justify-between mb-4 lg:mb-6">
                <h2 class="text-20-26 lg:text-24-32 font-semibold">Invoice</h2>
                <div>
                  @if($sub)<div class="rounded-full bg-[#FFF0F6] py-[2px] px-2 text-10-16 text-red font-semibold text-center min-w-[55px]">{{$sub->status}}</div>@endif
                </div>
              </div>
              @if($sub)
                <div>
                    <div class="flex flex-col gap-6 mb-6">
                      <div class="rounded-2xl bg-neutral-50 py-3 px-4">
                        <div class="flex items-center justify-between gap-4 mb-2">
                          <div class="text-14-20 font-semibold text-dark">{{$sub->parent->name_english}}</div>
                          <div class="text-12-16 text-[#494F5B]">{{date('Y-m-d', strtotime($sub->created_at))}}</div>
                        </div>
                        <div class="mb-2">
                          <p class="text-12-16 text-neutral-500">
                            @foreach($sub->product as $key => $product)
                              {{$key > 0 ? ', ':''}}{{$product->prod_name}}
                            @endforeach</p>
                        </div>
                        <div class="text-14-20 font-semibold text-green">${{ number_format($sub->price, 2) }} USD</div>
                      </div>
                    </div>
                    <div class="mb-2 flex items-center justify-between">
                      <div class="text-14-20 font-semibold text-neutral-700">Total</div>
                      <div class="text-right text-14-20 font-semibold text-green">${{number_format($sub->price,2)}} USD</div>
                    </div>
                    <a target="_blank" href="{{ config('app.wphost') }}/checkout/order-pay/{{$sub->order_id}}/?pay_for_order=true&key={{$sub->order_key}}" class="block bg-green hover:bg-[#0d9b6a] focus:bg-[#0d9b6a] rounded-lg p-[10px] text-14-20 text-white font-semibold text-center mb-[18px] transition-colors">
                      Pay
                    </a>
                  <a href="{{route('web.invoices')}}" class="flex items-center justify-center gap-[10px]">
                    <div class="text-14-20 text-neutral-700 font-semibold">Show all</div>
                    <div class="w-[18px] h-[18px]">
                      <svg width="19" height="18" viewBox="0 0 19 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M4.23291 9H14.7329M14.7329 9L9.48291 3.75M14.7329 9L9.48291 14.25" stroke="#10C285" stroke-width="1.66667" stroke-linecap="round" stroke-linejoin="round"/>
                      </svg>
                    </div>
                  </a>
                </div>
              @else
                <div>
{{--                  <div class="rounded-lg border border-neutral-100 bg-white py-2 px-4 mb-6">--}}
{{--                    <div class="text-12-16 text-neutral-600 mb-1">Renew at</div>--}}
{{--                    <div class="text-14-20 text-[#494F5B] font-semibold">--}}
{{--                      January 31, 2024 10:15--}}
{{--                    </div>--}}
{{--                  </div>--}}
                  <div class="mb-6">
                    <img src="{{asset('assets/web/images/dashboard-pay.png')}}" alt="dashboard-pay" class="max-w-[205px] mx-auto">
                  </div>
                  <div class="mb-6">
                    <p class="text-14-20 text-neutral-400 font-medium text-center">
                      No outstanding payments.<br/>
                      Your account is up-to-date
                    </p>
                  </div>

                  <a href="{{route('web.invoices')}}" class="flex items-center justify-center gap-[10px]">
                    <div class="text-14-20 text-neutral-700 font-semibold">Show Invoice</div>
                    <div class="w-[18px] h-[18px]">
                      <svg width="19" height="18" viewBox="0 0 19 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M4.23291 9H14.7329M14.7329 9L9.48291 3.75M14.7329 9L9.48291 14.25" stroke="#10C285" stroke-width="1.66667" stroke-linecap="round" stroke-linejoin="round"/>
                      </svg>
                    </div>
                  </a>
                </div>
              @endif
            </div>
            <div class="rounded-lg border border-neutral-100 bg-white py-6 px-4">
              <h2 class="text-20-26 lg:text-24-32 font-semibold mb-4 lg:mb-8">Schedule</h2>
              <div>
                <div id="calendarDashboard" class="calendar__dashboard calendar-event-dot mb-4"></div>
              </div>
              <div class="demo-week-view">
                <div>
                  <div id="demo-1-week"></div>
                </div>
              </div>
              <div class="mb-8">
                <div class="text-14-20 font-semibold text-dark mb-4">Today</div>
                @foreach($students as $student)
                 @foreach($student->classesToday as $classToday)
                    <div class="flex items-center gap-[10px]" style="margin-bottom: 10px;">
                      <div class="flex items-center justify-center  py-4 px-3 rounded-lg ml-[13px] relative font-semibold
                before:block before:{{$classToday->subject == 'Reading' ? 'bg-blue bg-[#E7F6FD]' : 'bg-green bg-greenLight'}} before:w-[3px] before:absolute before:-left-[13px] before:h-full">
                        {{$classToday->scheduleToday->start}}
                      </div>
                      <div class="flex-1 truncate">
                        <div class="text-14-20 font-semibold text-neutral-800 mb-1 truncate">{{$classToday->subject}} Class {{$classToday->name}}</div>
                        <p style="max-width: 200px" class="truncate">{{$student->name_english}}</p>
                      </div>
                    </div>
                 @endforeach
                @endforeach
              </div>
              <a href="{{route('web.schedule.index')}}" class="block bg-green hover:bg-[#0d9b6a] focus:bg-[#0d9b6a] rounded-lg p-[10px] text-14-20 text-white font-semibold text-center transition-colors">
                Show all
              </a>
            </div>
            <div class="rounded-lg bg-white p-4">
              <h2 class="text-24-32 font-semibold mb-4">Drive Folder</h2>
              @foreach($students as $student)
                @foreach($student->documents as $document)
                  <div class="rounded-lg bg-neutral-50 px-4 py-5 flex justify-between items-center gap-4 mb-1">
                    <div class="flex items-center">
                      <div class="mr-2 flex-shrink-0">
                        <svg xmlns="http://www.w3.org/2000/svg" style="height: 24px;width: 24px" width="24" height="24" viewBox="0 0 24 22" fill="none">
                          <path d="M14.8344 0.171631H9.16639C8.46147 0.171631 7.81011 0.54774 7.45767 1.15826L0.405391 13.6193C0.0700372 14.2002 0.052813 14.9116 0.359601 15.5081L3.05966 20.7577C3.39779 21.4151 3.93381 21.64 4.67304 21.64H19.3278C20.0671 21.64 20.6501 21.415 20.9883 20.7577L23.6883 15.5081C23.9951 14.9116 24.072 14.2001 23.7367 13.6193L16.5432 1.15826C16.1908 0.54774 15.5394 0.171631 14.8344 0.171631ZM7.6709 14.5512L12.0004 7.05289L16.3299 14.5512H7.6709Z" fill="url(#paint0_linear_1030_17487)"/>
                          <path d="M16.3301 14.5511L12.7227 21.687H19.328C20.0672 21.687 20.6032 21.4149 20.9414 20.7576L23.6414 15.508C23.9482 14.9115 24.0721 14.2 23.7368 13.6192L21.2523 9.31519H13.3069L16.3301 14.5511Z" fill="url(#paint1_linear_1030_17487)"/>
                          <path d="M23.9999 14.5511C23.9999 13.9962 23.7365 13.6191 23.7365 13.6191C23.8687 14.0867 23.5173 14.5511 23.0314 14.5511H7.67079H7.64797L3.6377 21.5344C3.94335 21.7228 4.30017 21.8281 4.67294 21.8281H19.3277C20.0669 21.8281 20.7441 21.4149 21.0822 20.7576L23.7823 15.508C23.9368 15.2077 23.9999 14.9074 23.9999 14.5511Z" fill="url(#paint2_linear_1030_17487)"/>
                          <path d="M7.67079 14.5513L7.61187 14.6142L3.6377 21.5346C3.94335 21.7231 4.30017 21.8284 4.67294 21.8284H14.9479L7.67079 14.5513Z" fill="url(#paint3_linear_1030_17487)"/>
                          <path d="M5.82611 17.7236L3.6377 21.5344C3.94335 21.7228 4.30017 21.8281 4.67294 21.8281H19.3277C20.0669 21.8281 20.7441 21.4149 21.0822 20.7576L22.6427 17.7236H5.82611Z" fill="url(#paint4_linear_1030_17487)"/>
                          <path d="M7.67107 14.5512H7.67093L12.0003 7.05321L8.59761 1.16003C8.34687 0.725757 7.71606 0.716204 7.46302 1.14921C7.46123 1.15227 7.45949 1.15528 7.4577 1.15834L0.263726 13.6203C-0.071064 14.201 -0.088147 14.912 0.218453 15.5082L1.54208 18.0816L2.91898 20.7586C3.08675 21.0843 3.33787 21.3499 3.63849 21.535C3.63849 21.535 4.0002 21.8283 4.67308 21.8283C4.14232 21.7314 3.86773 21.1379 4.13752 20.6707L7.67107 14.5512Z" fill="url(#paint5_linear_1030_17487)"/>
                          <path d="M8.59752 1.16003C8.34678 0.725757 7.71598 0.716204 7.46294 1.14921C7.46115 1.15227 7.45941 1.15528 7.45762 1.15834L4.20605 6.79108L9.24319 11.8282L12.0002 7.05325L8.59752 1.16003Z" fill="url(#paint6_linear_1030_17487)"/>
                          <defs>
                            <linearGradient id="paint0_linear_1030_17487" x1="18.384" y1="8.93682" x2="14.0774" y2="11.0184" gradientUnits="userSpaceOnUse">
                              <stop stop-color="#FFD833"/>
                              <stop offset="1" stop-color="#F2B631"/>
                            </linearGradient>
                            <linearGradient id="paint1_linear_1030_17487" x1="18.3297" y1="10.5049" x2="18.3297" y2="13.5663" gradientUnits="userSpaceOnUse">
                              <stop stop-color="#F9A126" stop-opacity="0"/>
                              <stop offset="1" stop-color="#F9A126"/>
                            </linearGradient>
                            <linearGradient id="paint2_linear_1030_17487" x1="13.2731" y1="14.4581" x2="14.5727" y2="22.8334" gradientUnits="userSpaceOnUse">
                              <stop stop-color="#7FAEF4"/>
                              <stop offset="1" stop-color="#4C8DF1"/>
                            </linearGradient>
                            <linearGradient id="paint3_linear_1030_17487" x1="9.00675" y1="21.3914" x2="2.17171" y2="14.4601" gradientUnits="userSpaceOnUse">
                              <stop stop-color="#4256AC" stop-opacity="0"/>
                              <stop offset="1" stop-color="#1B1464"/>
                            </linearGradient>
                            <linearGradient id="paint4_linear_1030_17487" x1="13.1402" y1="19.8565" x2="13.1402" y2="23.3252" gradientUnits="userSpaceOnUse">
                              <stop stop-color="#4256AC" stop-opacity="0"/>
                              <stop offset="1" stop-color="#1B1464"/>
                            </linearGradient>
                            <linearGradient id="paint5_linear_1030_17487" x1="3.83309" y1="9.49965" x2="8.99536" y2="13.6295" gradientUnits="userSpaceOnUse">
                              <stop stop-color="#28A265"/>
                              <stop offset="1" stop-color="#28895E"/>
                            </linearGradient>
                            <linearGradient id="paint6_linear_1030_17487" x1="8.08451" y1="6.60665" x2="11.1651" y2="4.7294" gradientUnits="userSpaceOnUse">
                              <stop stop-color="#108372" stop-opacity="0"/>
                              <stop offset="1" stop-color="#006837"/>
                            </linearGradient>
                          </defs>
                        </svg>
                      </div>
                      <div class="grid">
                        <div class="text-14-20 text-neutral-600 font-semibold truncate">{{$document->subject}} {{$document->name}}</div>
                        <div class="text-14-20 text-neutral-600 font-semibold truncate">{{$student->name_english}}</div>
                      </div>
                    </div>
                    <a href="{{$document->link}}" class="border rounded-lg border-neutral-200 py-[7px] px-3 text-12-16 font-semibold text-neutral-700">Open</a>
                  </div>
                @endforeach
              @endforeach
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
  <div class="backdrop fixed inset-0 bg-black/[0.7] z-[51] transition opacity-0 invisible"></div>
   {{-- Modal Setup Kakao --}}
<div id="setupModal" class="hidden fixed inset-0 bg-black/[0.7] flex items-center justify-center">
  <div class="bg-white rounded-lg p-6 w-80 shadow-lg">
    <h2 class="text-lg font-semibold mb-4">Setup Kakao Message Time</h2>
    <input type="hidden" id="setupStudentId">
    <div class="flex gap-2 mb-4">
      <select id="setupHour" class="border rounded p-2 w-1/2">
        @for ($h = 0; $h < 24; $h++)
          <option value="{{ sprintf('%02d', $h) }}">{{ sprintf('%02d', $h) }}</option>
        @endfor
      </select>

      <select id="setupMinute" class="border rounded p-2 w-1/2">
        @for ($m = 0; $m < 60; $m += 5)
          <option value="{{ sprintf('%02d', $m) }}">{{ sprintf('%02d', $m) }}</option>
        @endfor
      </select>
</div>

    <div class="flex justify-end gap-2">
      <button onclick="closeSetupModal()" class="px-4 py-2 bg-gray-300 rounded">Cancel</button>
      <button onclick="saveSetupTime()" class="px-4 py-2 bg-green text-white rounded">Save</button>
    </div>
  </div>
</div>

  {{-- Modal Confirm Request --}}
<div id="requestModal" class="hidden fixed inset-0  bg-black/[0.7] flex items-center justify-center">
  <div class="bg-white rounded-lg p-6 w-80 shadow-lg">
    <h2 class="text-lg font-semibold mb-4">Confirm Request</h2>
    <p id="requestText" class="mb-4 text-sm text-gray-700"></p>
    <input type="hidden" id="requestStudentId">
    <input type="hidden" id="requestClassOfStudent">
    <input type="hidden" id="lastSchedule">
    <input type="hidden" id="className">
    <div class="flex justify-end gap-2">
      <button onclick="closeRequestModal()" class="px-4 py-2 bg-gray-300 rounded">Cancel</button>
      <button onclick="confirmRequest()" class="px-4 py-2 bg-green text-white rounded">Confirm</button>
    </div>
  </div>
</div>
@endsection
@push('after-scripts')
  <script src="{{ mix('/assets/web/js/calendar-dashboard.js') }}"></script>
  <script src="https://cdn.jsdelivr.net/npm/@fullcalendar/core@4.4.0/main.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/@fullcalendar/interaction@4.4.0/main.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/@fullcalendar/daygrid@4.4.0/main.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/@fullcalendar/timegrid@4.4.0/main.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/@fullcalendar/list@4.4.0/main.min.js"></script>
  <script src="https://unpkg.com/popper.js/dist/umd/popper.min.js"></script>
  <script src="https://unpkg.com/tooltip.js/dist/umd/tooltip.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
<script>
  document.addEventListener("DOMContentLoaded", function() {

    // Open Setup Modal
    document.querySelectorAll(".openSetupPopup").forEach(btn => {
      btn.addEventListener("click", function () {
        const studentId = this.dataset.studentId;
        const studentTime = this.dataset.studentTime;
        document.getElementById("setupStudentId").value = studentId;
        if (studentTime) {
        const [hour, minute] = studentTime.split(":");
          document.getElementById("setupHour").value = hour.padStart(2, "0");
          document.getElementById("setupMinute").value = minute.padStart(2, "0");
        } else {
          document.getElementById("setupHour").value = "00";
          document.getElementById("setupMinute").value = "00";
        }

        document.getElementById("setupModal").classList.remove("hidden");
      });
    });

    function closeSetupModal() {
      document.getElementById("setupHour").value = "00";
      document.getElementById("setupMinute").value = "00";
      document.getElementById("setupModal").classList.add("hidden");
    }

    function saveSetupTime() {
      const studentId = document.getElementById("setupStudentId").value;
      const hour = String(document.getElementById("setupHour").value).padStart(2, '0');
      const minute = String(document.getElementById("setupMinute").value).padStart(2, '0');
      const timeInput = `${hour}:${minute}`;

      axios.post("{{ url('/api/setup-time') }}", {
        student_id: studentId,
        time: timeInput
      }, {
        headers: {'X-CSRF-TOKEN': "{{ csrf_token() }}"},
      })
      .then(res => {
        alert(res.data.message || "Saved!");
    
        const timeDiv = document.getElementById(`kakao-time-${studentId}`);
        if (timeDiv) {
          timeDiv.className = "mt-2 text-16 text-gray-600 font-bold py-2";
          timeDiv.innerHTML = `Kakao Message Time: <span class="font-medium">${timeInput}</span>`;
        }
        const setupBtn = document.querySelector(`.openSetupPopup[data-student-id="${studentId}"]`);
        if (setupBtn) {
          setupBtn.setAttribute("data-student-time", timeInput);
        }
        closeSetupModal();
      })
      .catch(err => {
        console.error(err);
        alert(err.response?.data?.message || "Error saving time");
      });
    }

    // Open Request Modal
    document.querySelectorAll(".openRequestPopup").forEach(btn => {
      btn.addEventListener("click", function () {
        const studentId = this.dataset.studentId;
        const studentName = this.dataset.studentName;
        const classId = this.dataset.classId;
        const className = this.dataset.className
        const lastSchedule = this.dataset.lastSchedule;
        document.getElementById("requestStudentId").value = studentId;
        document.getElementById("requestClassOfStudent").value = classId;
        document.getElementById("lastSchedule").value = lastSchedule;
        document.getElementById("className").value = className;
        document.getElementById("requestText").innerText = 
          `Are you sure you want to request a make-up class for ${studentName}?`;
        document.getElementById("requestModal").classList.remove("hidden");
      });
    });

    function closeRequestModal() {
      document.getElementById("requestStudentId").value = '';
      document.getElementById("requestClassOfStudent").value = '';
      document.getElementById("lastSchedule").value = '';
      document.getElementById("className").value = '';
      document.getElementById("requestModal").classList.add("hidden");
    }

    function confirmRequest() {
      const studentId = document.getElementById("requestStudentId").value;
      const classId =  document.getElementById("requestClassOfStudent").value;
      const lastSchedule =  document.getElementById("lastSchedule").value;
      const className =  document.getElementById("className").value;
      console.log(lastSchedule)
      axios.post("{{ url('/api/request-makeup') }}", {
        student_id: studentId,
        class_id: classId,
        last_schedule: lastSchedule,
        class_name: className,
      }, {
        headers: {'X-CSRF-TOKEN': "{{ csrf_token() }}"},
      })
      .then(res => {
        alert(res.data.message || "Request sent!");
        closeRequestModal();
      })
      .catch(err => {
        console.error(err);
        alert(err.response?.data?.message || "Error sending request.");
      });
    }

    window.saveSetupTime = saveSetupTime;
    window.closeSetupModal = closeSetupModal;
    window.confirmRequest = confirmRequest;
    window.closeRequestModal = closeRequestModal;

  });
</script>
@endpush()
