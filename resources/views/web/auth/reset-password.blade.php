@extends('web.layouts.commonMaster')

@section('title', 'Reset Password')


@section('content')
  <header class="py-4 bg-white fixed z-40 top-0 left-0 right-0">
    <div class="container min-[1688px]:px-[120px]">
      <a href="#" class="block max-w-max h-[37px] mx-auto lg:mx-0">
        <svg class="mx-auto logo-full" width="122" height="28" viewBox="0 0 122 28" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g clip-path="url(#clip0_175_1342)">
            <path d="M37.4784 17.8957L36.373 19.1272L35.1974 18.0778C34.7784 18.3666 34.3208 18.5933 33.8266 18.756C33.3324 18.9188 32.7954 19.0011 32.2178 19.0011C31.4623 19.0011 30.7718 18.8679 30.1474 18.6025C29.522 18.3371 28.986 17.9751 28.5376 17.5185C28.0901 17.0619 27.7403 16.5249 27.4881 15.9097C27.2369 15.2944 27.1108 14.6416 27.1108 13.9511C27.1108 13.2606 27.2369 12.6077 27.4881 11.9925C27.7403 11.3772 28.0942 10.8383 28.5518 10.3766C29.0084 9.91491 29.5494 9.54881 30.1748 9.27831C30.7992 9.00781 31.4897 8.87256 32.2453 8.87256C33.0009 8.87256 33.6903 9.00578 34.3157 9.27119C34.9401 9.53661 35.4771 9.89864 35.9245 10.3552C36.372 10.8118 36.7218 11.3488 36.974 11.964C37.2262 12.5792 37.3523 13.2321 37.3523 13.9226C37.3523 14.4819 37.2658 15.0189 37.093 15.5314C36.9201 16.0449 36.675 16.5158 36.3588 16.9439L37.4784 17.8957ZM32.2463 15.5171L33.3507 14.2582L35.0429 15.7968C35.2117 15.5354 35.3367 15.2487 35.4211 14.9365C35.5045 14.6243 35.5472 14.2958 35.5472 13.9501C35.5472 13.4741 35.4649 13.0247 35.3022 12.5996C35.1384 12.1755 34.9106 11.8044 34.6168 11.4871C34.3229 11.1708 33.971 10.9186 33.5602 10.7315C33.1504 10.5454 32.7019 10.4518 32.2168 10.4518C31.7317 10.4518 31.2863 10.5423 30.8806 10.7244C30.4748 10.9064 30.127 11.1535 29.8382 11.4657C29.5494 11.7789 29.3226 12.1471 29.1599 12.5711C28.9962 12.9952 28.9149 13.4457 28.9149 13.9206C28.9149 14.3955 28.9962 14.846 29.1599 15.27C29.3226 15.6941 29.5515 16.0653 29.8454 16.3825C30.1392 16.6998 30.4911 16.952 30.9009 17.1381C31.3107 17.3242 31.7592 17.4178 32.2443 17.4178C32.5707 17.4178 32.8738 17.3761 33.1534 17.2917C33.4331 17.2083 33.6893 17.0913 33.9222 16.9419L32.2443 15.5151L32.2463 15.5171Z" fill="#0F1A30"/>
            <path d="M41.1713 8.97119H42.3743V18.8343H40.6679V10.7203L38.9045 11.2094L38.5415 9.81015L41.1713 8.97119Z" fill="#0F1A30"/>
            <path d="M58.5456 18.8334H56.7263L55.7328 16.469H51.1017L50.0939 18.8334H48.3306L52.6403 8.97021H54.2348L58.5446 18.8334H58.5456ZM53.4111 11.0264L51.7332 14.9436H55.1043L53.4121 11.0264H53.4111Z" fill="#0F1A30"/>
            <path d="M67.3528 17.9872C67.0772 18.1967 66.7812 18.3787 66.465 18.5333C66.1477 18.6868 65.7999 18.8038 65.4226 18.8831C65.0454 18.9624 64.6274 19.0021 64.1708 19.0021C63.4529 19.0021 62.7898 18.8709 62.1847 18.6105C61.5787 18.3492 61.0539 17.9933 60.6105 17.5407C60.1672 17.0882 59.8204 16.5543 59.5682 15.9391C59.317 15.3238 59.1909 14.6608 59.1909 13.953C59.1909 13.2453 59.314 12.5965 59.5621 11.9802C59.8092 11.365 60.157 10.826 60.6044 10.3643C61.0519 9.90262 61.5837 9.53856 62.199 9.27315C62.8142 9.00773 63.4956 8.87451 64.241 8.87451C64.6884 8.87451 65.0962 8.91214 65.4653 8.98637C65.8335 9.06061 66.1721 9.16637 66.4792 9.30162C66.7873 9.43687 67.0711 9.59755 67.3334 9.78466C67.5948 9.97076 67.8419 10.1762 68.0748 10.3999L66.9694 11.6731C66.5779 11.309 66.163 11.0151 65.7247 10.7914C65.2864 10.5677 64.7871 10.4558 64.2277 10.4558C63.761 10.4558 63.3298 10.5463 62.9332 10.7284C62.5366 10.9104 62.1939 11.1575 61.9051 11.4697C61.6163 11.7829 61.3915 12.148 61.2329 12.568C61.0743 12.988 60.9949 13.4405 60.9949 13.9246C60.9949 14.4086 61.0743 14.8642 61.2329 15.2883C61.3915 15.7133 61.6153 16.0835 61.9051 16.4008C62.1939 16.7181 62.5366 16.9672 62.9332 17.1492C63.3288 17.3313 63.761 17.4218 64.2277 17.4218C64.8247 17.4218 65.3372 17.3079 65.7664 17.0791C66.1955 16.8513 66.6195 16.5401 67.0385 16.1486L68.1439 17.2682C67.8927 17.5387 67.6283 17.7787 67.3538 17.9882L67.3528 17.9872Z" fill="#0F1A30"/>
            <path d="M79.321 18.8334H77.5017L76.5082 16.469H71.8771L70.8693 18.8334H69.106L73.4157 8.97021H75.0102L79.3199 18.8334H79.321ZM74.1865 11.0264L72.5086 14.9436H75.8797L74.1875 11.0264H74.1865Z" fill="#0F1A30"/>
            <path d="M89.4766 15.861C89.2254 16.463 88.8675 16.9827 88.4068 17.421C87.9451 17.8593 87.3949 18.204 86.7563 18.4562C86.1177 18.7084 85.415 18.8345 84.6513 18.8345H80.9995V9.0415H84.6513C85.416 9.0415 86.1177 9.16557 86.7563 9.41268C87.3949 9.65979 87.9451 10.0025 88.4068 10.4408C88.8685 10.8791 89.2254 11.3967 89.4766 11.9936C89.7278 12.5906 89.8539 13.2343 89.8539 13.9248C89.8539 14.6153 89.7278 15.261 89.4766 15.862V15.861ZM87.8048 12.6099C87.641 12.2021 87.4122 11.8492 87.1173 11.5492C86.8224 11.2503 86.4645 11.0154 86.0445 10.8465C85.6235 10.6777 85.1567 10.5933 84.6421 10.5933H82.7069V17.2806H84.6421C85.1567 17.2806 85.6245 17.1993 86.0445 17.0345C86.4655 16.8708 86.8234 16.641 87.1173 16.3461C87.4112 16.0512 87.64 15.6993 87.8048 15.2925C87.9685 14.8848 88.0499 14.4383 88.0499 13.9512C88.0499 13.4641 87.9675 13.0167 87.8048 12.6099Z" fill="#0F1A30"/>
            <path d="M99.1856 10.5934H93.6454V13.1113H98.5561V14.6642H93.6454V17.2797H99.2557V18.8325H91.939V9.03955H99.1856V10.5924V10.5934Z" fill="#0F1A30"/>
            <path d="M106.139 16.4269H106.083L103.019 11.8386V18.834H101.313V9.04102H103.145L106.125 13.6721L109.105 9.04102H110.937V18.834H109.231V11.8111L106.139 16.428V16.4269Z" fill="#0F1A30"/>
            <path d="M118.072 18.833H116.337V14.9718L112.449 9.04004H114.478L117.219 13.3772L119.989 9.04004H121.961L118.072 14.9301V18.833Z" fill="#0F1A30"/>
            <path d="M18.4142 5.74863C18.0288 6.03134 17.5966 6.29777 17.1309 6.53878C16.9255 6.64658 16.7119 6.74624 16.4984 6.84386C15.7641 7.16826 14.9628 7.43673 14.1116 7.6452V2.82701H12.8669C12.8669 3.23378 12.5252 3.64462 11.9273 3.95478C11.3619 4.24969 10.6317 4.41138 9.87208 4.41138C9.11244 4.41138 8.38229 4.24867 7.81485 3.95376C7.21791 3.64462 6.87623 3.23378 6.87623 2.82701C6.87623 2.26872 7.85756 1.24468 9.46023 1.24468C10.5107 1.24468 10.9907 1.54772 11.0853 1.81314C11.0965 1.84364 11.1026 1.87517 11.1026 1.90771C11.1026 2.22194 10.5768 2.57278 9.8731 2.57278V3.81749C11.2602 3.81749 12.3473 2.97853 12.3473 1.90771C12.3473 1.72873 12.3158 1.55179 12.2568 1.39111C12.1083 0.973156 11.5398 0.000976562 9.46125 0.000976562C7.26978 0.000976562 5.63253 1.49382 5.63253 2.82803C5.63253 3.7158 6.22031 4.52934 7.24232 5.05916C7.98162 5.44457 8.91618 5.65609 9.87208 5.65609C10.828 5.65609 11.7625 5.44356 12.5008 5.05916C12.63 4.99204 12.753 4.92086 12.8679 4.84459V7.90553V12.7634C12.4937 12.9088 12.1083 13.0268 11.7148 13.1142C11.6883 13.1203 11.6629 13.1254 11.6364 13.1305C11.1626 12.682 10.528 12.4237 9.87412 12.4237C9.22024 12.4237 8.58262 12.681 8.10874 13.1305C8.0823 13.1244 8.05586 13.1203 8.0284 13.1132C7.63587 13.0257 7.25147 12.9078 6.87724 12.7624V6.72081C6.73894 6.66285 6.60572 6.59878 6.47556 6.53268C6.17048 6.37506 5.88777 6.19405 5.63253 5.99473V7.64418C4.78136 7.43673 3.98003 7.16724 3.24581 6.84284C3.03225 6.74624 2.8187 6.64556 2.61328 6.53777C2.15058 6.29981 1.71839 6.03032 1.32687 5.74457L0.337402 5.02255V17.1443C0.337402 21.1947 3.14005 24.8282 5.91523 26.6332C6.23658 26.8407 6.55793 27.0298 6.87724 27.1905C7.92264 27.7223 8.94262 28 9.87412 28C10.8056 28 11.8256 27.7223 12.8679 27.1905C13.1873 27.0298 13.5086 26.8417 13.83 26.6332C16.6051 24.8282 19.4047 21.1967 19.4047 17.1443V5.01747L18.4153 5.74965L18.4142 5.74863ZM12.8669 19.5066L11.8195 16.6267C11.8968 16.5382 11.9639 16.4436 12.0269 16.347C12.0269 16.347 12.0269 16.346 12.028 16.345C12.3127 16.2738 12.5934 16.1894 12.8669 16.0908V19.5076V19.5066ZM6.87623 16.0897C7.14978 16.1884 7.42943 16.2738 7.71519 16.344C7.71519 16.344 7.71519 16.345 7.7162 16.346C7.78027 16.4457 7.84942 16.5402 7.92162 16.6287L6.87623 19.5066V16.0897ZM8.3345 17.0172C8.34873 17.0284 8.36195 17.0395 8.37924 17.0507C8.40466 17.0701 8.43212 17.0863 8.45958 17.1067C8.50941 17.1423 8.56229 17.1728 8.6172 17.2033C8.6172 17.2063 8.62025 17.2063 8.62025 17.2063C8.68127 17.2399 8.74228 17.2724 8.80635 17.3009C8.91414 17.3507 9.02804 17.3955 9.14498 17.428C9.23142 17.4534 9.32193 17.4748 9.41345 17.4921C9.42159 17.4951 9.43277 17.4951 9.44091 17.4972L8.74228 19.4151L8.3406 18.752L7.61146 19.0012L8.3345 17.0162V17.0172ZM10.3246 17.4911C10.4517 17.4687 10.5768 17.4382 10.6988 17.3965C10.7324 17.3853 10.7629 17.3711 10.7934 17.3609C10.8321 17.3467 10.8707 17.3304 10.9073 17.3141C10.9826 17.2806 11.0538 17.245 11.1239 17.2033C11.1961 17.1646 11.2622 17.1199 11.3293 17.0762C11.3324 17.0731 11.3344 17.0711 11.3344 17.0711C11.3598 17.0548 11.3843 17.0375 11.4097 17.0182L12.1327 19.0032L11.4005 18.7541L11.0009 19.4141L10.3022 17.4982C10.3104 17.4982 10.3165 17.4951 10.3246 17.4931V17.4911ZM12.8669 15.5355C12.6849 15.6057 12.5008 15.6708 12.3127 15.7277C12.388 15.4867 12.4266 15.2345 12.4266 14.9793C12.4266 14.4749 12.2802 13.9908 12.0036 13.5779C12.2964 13.5068 12.5852 13.4183 12.8679 13.3166V15.5355H12.8669ZM9.8731 12.9424C10.4416 12.9424 10.9704 13.1722 11.3588 13.5881C11.7137 13.9705 11.908 14.4637 11.908 14.9793C11.908 15.3342 11.8134 15.683 11.6364 15.9911C11.5531 16.1375 11.4534 16.2708 11.3375 16.3908C10.9663 16.7762 10.47 16.9948 9.93717 17.0121L9.88124 17.0151H9.86192L9.80598 17.0121C9.47345 17.0009 9.15414 16.9124 8.87143 16.7518C8.75753 16.6877 8.64974 16.6135 8.55008 16.527C8.50025 16.4853 8.45347 16.4385 8.40568 16.3908V16.3877C8.28873 16.2718 8.18907 16.1386 8.1067 15.9911C7.92976 15.683 7.83519 15.3342 7.83519 14.9793C7.83519 14.4637 8.03247 13.9705 8.38432 13.5901C8.77279 13.1712 9.30159 12.9413 9.8731 12.9413V12.9424ZM7.74061 13.5779C7.46401 13.9908 7.31757 14.4749 7.31757 14.9793C7.31757 15.2345 7.35621 15.4867 7.43452 15.7277C7.24537 15.6708 7.06029 15.6057 6.87724 15.5355V13.3166C7.15995 13.4193 7.44875 13.5068 7.74163 13.5779H7.74061ZM5.63151 24.8841C3.4919 23.1269 1.5811 20.2663 1.5811 17.1443V7.39198C1.73059 7.48046 1.88618 7.56384 2.04177 7.64418C2.26854 7.76113 2.50447 7.87401 2.74039 7.97977C3.6302 8.37332 4.60035 8.68958 5.63151 8.92246V24.8841ZM9.8731 26.7552C8.98838 26.7552 7.93586 26.4064 6.87623 25.7688V19.8005L8.11281 19.3795L8.85821 20.6049L9.87005 17.8277L10.8849 20.6079L11.6283 19.3795L12.8669 19.8005V25.7688C11.8083 26.4064 10.7548 26.7552 9.8731 26.7552ZM18.159 17.1443C18.159 20.2652 16.2523 23.1269 14.1116 24.8841V8.92246C15.1428 8.68958 16.1129 8.37332 17.0028 7.97977C17.2387 7.87401 17.4736 7.76113 17.7044 7.64418C17.86 7.56384 18.0126 7.48046 18.159 7.39504V17.1443Z" fill="#0F1A30"/>
            <path d="M5.15215 4.04492H3.66846V5.52861H5.15215V4.04492Z" fill="#0F1A30"/>
            <path d="M3.35887 2.66504H2.30127V3.72264H3.35887V2.66504Z" fill="#0F1A30"/>
          </g>
          <defs>
            <clipPath id="clip0_175_1342">
              <rect width="121.624" height="28" fill="#0F1A30" transform="translate(0.337402)"/>
            </clipPath>
          </defs>
        </svg>
      </a>
    </div>
  </header>
  <main>
    <div class="lg:flex min-h-screen pt-[69px]">
      <div class="lg:w-1/2 hidden lg:block">
        <div class="image h-full relative">
          <img src="{{asset('src/images/image-login.jpg')}}" alt="login" class="w-full h-full object-cover absolute inset-0">
        </div>
      </div>
      <div style="background: url('{{asset('src/images/image-bg-login.jpg')}}')" class="lg:w-1/2 lg:flex lg:items-center lg:justify-center min-h-[calc(100vh-69px)] px-4 lg:px-0 pt-[80px] pb-10 lg:pt-0 bg-cover">
        <form class="lg:w-[400px]" method="post" action="{{route('web.resetPost')}}">
          @csrf
          <h1 class="text-24-32 font-semibold mb-8 text-center text-white">Update password</h1>
          <div class="mb-8">
            <div class="text-14-20 mb-2 text-neutral-300 text-center">
              You must change your password for the first login
              for security purpose. Thank you for your understanding.
            </div>
          </div>
          <div class="mb-8">
            <label for="inputPassword" class="text-12-16 text-neutral-300 mb-1">Password <span class="text-red">*</span></label>
            <div class="flex">
              <input type="password" id="inputPassword" value="{{old('password')}}" name="password" placeholder="Enter Your Password" class="block w-full border border-neutral-200 rounded-lg py-3 px-4 placeholder:text-neutral-400 text-neutral-700 text-14-20">
              <span class="input-group-text cursor-pointer" style="margin:10px 0 0 -35px" onclick="togglePasswordVisibility('inputPassword', 'passwordIcon')">
                <i id="passwordIcon" class="bx bx-hide"></i>
              </span>
            </div>
            @error('password')
            @if($message != 'Password confirmation does not match.')
              <div style="margin-top: 3px;color: #ff3e1d;font-size: 14px">{{ $message }}</div>
            @endif            @enderror
          </div>
          <div class="mb-8">
            <label for="inputConfirmPassword" class="text-12-16 text-neutral-300 mb-1">Confirmation Password <span class="text-red">*</span></label>
            <div class="flex">
              <input type="password" value="{{old('password_confirmation')}}" id="inputConfirmPassword" name="password_confirmation" placeholder="Enter Your Password" class="block w-full border border-neutral-200 rounded-lg py-3 px-4 placeholder:text-neutral-400 text-neutral-700 text-14-20">
              <span class="input-group-text cursor-pointer" style="margin:10px 0 0 -35px" onclick="togglePasswordVisibility('inputConfirmPassword', 'passwordIconConfirm')">
                <i id="passwordIconConfirm" class="bx bx-hide"></i>
              </span>
            </div>
            @error('password_confirmation')
            <div style="margin-top: 3px;color: #ff3e1d;font-size: 14px">{{ $message }}</div>
            @enderror
            @error('password')
            @if($message == 'Password confirmation does not match.')
                <div style="margin-top: 3px;color: #ff3e1d;font-size: 14px">{{ $message }}</div>
            @endif
            @enderror
          </div>
          <button type="submit" class="w-full bg-green rounded-lg text-16-24 font-semibold text-white p-3">Update password</button>
        </form>
      </div>
    </div>
  </main>
@endsection

@push('after-scripts')
  <script>
    function togglePasswordVisibility(inputId, iconId) {
      var input = document.getElementById(inputId);
      var icon = document.getElementById(iconId);

      if (input.type === "password") {
        input.type = "text";
        icon.classList.remove("bx-hide");
        icon.classList.add("bx-show");
      } else {
        input.type = "password";
        icon.classList.remove("bx-show");
        icon.classList.add("bx-hide");
      }
    }
  </script>

@endpush
