@extends('layouts/contentNavbarLayout')

@section('title', 'Create Class')

@section('page-style')
  <style>
    th{
      padding: 0.625rem 1rem !important;
    }
  </style>
@endsection

@section('vendor-script')
@endsection

@section('page-script')
  <script>
    function handleSelectChange() {
      var oldValues = {!! json_encode(old()) !!};
      var errorMessages = {!! json_encode($errors->messages()) !!};
      var selectElement = document.getElementById("day");
      var selectedOptions = [];
      for (var option of selectElement.selectedOptions) {
        selectedOptions.push(option.value);
      }
      document.getElementById("selectedOptions").innerHTML = "";
      selectedOptions.forEach(function(option) {
        var div = document.createElement("div");
        div.classList.add("d-flex", "justify-content-around", "mb-3");

        var col1 = document.createElement("div");
        col1.classList.add("col-sm-3");
        col1.innerHTML = `<br><p class="text-center">${option.charAt(0).toUpperCase() + option.slice(1)}</p>`;

        var col2 = document.createElement("div");
        col2.classList.add("col-sm-3");
        var inputValueStart = oldValues[option + '_start'] || '';
        var inputStartHasError = errorMessages[option + '_start'];
        col2.innerHTML = `<label for="${option}_start" class="mb-1 required">Time start</label><input type="time" name="${option}_start" class="form-control ${inputStartHasError ? 'is-invalid' : ''}" id="${option}_start" value="${inputValueStart}">`;

        var errorDiv2 = document.createElement("div");
        errorDiv2.classList.add("invalid-feedback");
        errorDiv2.innerHTML = inputStartHasError ? errorMessages[option + '_start'][0] : '';
        col2.appendChild(errorDiv2);

        var col3 = document.createElement("div");
        col3.classList.add("col-sm-3");
        var inputValueEnd = oldValues[option + '_end'] || '';
        var inputEndHasError = errorMessages[option + '_end'];
        col3.innerHTML = `<label for="${option}_end" class="mb-1 required">Time End</label><input type="time" name="${option}_end" class="form-control ${inputEndHasError ? 'is-invalid' : ''}" id="${option}_end" value="${inputValueEnd}">`;

        var errorDiv3 = document.createElement("div");
        errorDiv3.classList.add("invalid-feedback");
        errorDiv3.innerHTML = inputEndHasError ? errorMessages[option + '_end'][0] : '';
        col3.appendChild(errorDiv3);

        div.appendChild(col1);
        div.appendChild(col2);
        div.appendChild(col3);
        document.getElementById("selectedOptions").appendChild(div);
      });
    }
    function hideClasses(type){
      if (type === 'Regular' || type === '') {
        $(".regular").show();
        $(".special").hide();
        $(".private").hide();
        $(".break").show();
        $(".start_date").show();
        $(".session").show();
      } else if (type === 'Special') {
        $(".regular").hide();
        $(".special").show();
        $(".private").hide();
        $(".break").show();
        $(".start_date").show();
        $(".session").show();
      } else if (type === 'Private') {
        $(".regular").hide();
        $(".special").hide();
        $(".private").show();
        $(".break").hide();
        $(".start_date").hide();
        $(".session").hide();
      }
    }

    $(document).ready(function(){
      var type = '{{ old("type") }}';
      hideClasses(type);
      document.getElementById('type').addEventListener('change', function() {
        var type = this.value;
        hideClasses(type);
      });
      var subjectSelect = document.getElementById('subject');
      var levelSelect = document.getElementById('level');

      var selectedSubject = '{{ old("subject") }}';
      var selectedLevel = '{{ old("level") }}';
      if (selectedSubject === 'Reading') {
        renderReadingLevels(selectedLevel);
      } else if (selectedSubject === 'Writing') {
        renderWritingLevels(selectedLevel);
      }

      subjectSelect.addEventListener('change', function() {
        var subject = this.value;
        if (subject === 'Reading') {
          renderReadingLevels();
        } else if (subject === 'Writing') {
          renderWritingLevels();
        } else {
          levelSelect.innerHTML = '<option value="">Select Level</option>';
        }
      });

      function renderReadingLevels(selected) {
        levelSelect.innerHTML = '';
        var readingLevels = ['QR1', 'QR2', 'QR3', 'QR4', 'QR5'];
        readingLevels.forEach(function(level) {
          var option = document.createElement('option');
          option.value = level;
          option.textContent = level;
          if (selected === level) {
            option.selected = true;
          }
          levelSelect.appendChild(option);
        });
      }

      function renderWritingLevels(selected) {
        levelSelect.innerHTML = '';
        var writingLevels = ['QW1', 'QW2', 'QW3', 'QW4','QW5'];
        writingLevels.forEach(function(level) {
          var option = document.createElement('option');
          option.value = level;
          option.textContent = level;
          if (selected === level) {
            option.selected = true;
          }
          levelSelect.appendChild(option);
        });
      }
      handleSelectChange();
      $('.select2').each(function () {
        $(this).select2({
          theme: 'bootstrap4',
          width: 'style',
          placeholder: $(this).attr('placeholder'),
          allowClear: Boolean($(this).data('allow-clear')),
        });
        $("#start_private").datepicker({
          format: "yyyy/mm/dd",
          autoclose: true,
          todayHighlight: true,
          orientation: 'bottom',
          startDate: new Date()
        });
      });
    });
  </script>
@endsection

@section('content')
  <div class="card">
    <div class="col-xxl">
      <div class="mb-4">
        <div class="card-header d-flex align-items-center justify-content-between">
          <h5 class="mb-0">Create Class</h5> <a href="{{route('admin.classes.index')}}" class="btn btn-primary float-end">Back</a>
        </div>
        <div class="card-body">
          <form action="{{route('admin.classes.store')}}" method="post">
            @csrf
            <div class="row mb-3">
              <div class="col-sm-6">
                <label for="basic-default-name" class="mb-1 required">Class Name</label>
                <input maxlength="50" type="text" class="form-control @error('name') is-invalid @enderror" id="basic-default-name" placeholder="Input Name" name="name" value="{{ old('name') }}" />
                @error('name')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              <div class="col-sm-6 regular">
                <label for="term" class="mb-1 required">Term</label>
                <select class="form-select @error('term') is-invalid @enderror" id="term" name="term">
                  <option value="">Term</option>
                  @foreach($terms as $term)
                    <option value="{{$term->id}}" {{ old('term') == $term->id ? 'selected' : '' }}>{{$term->type}}/{{$term->year}}</option>
                  @endforeach
                </select>
                @error('term')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              <div class="col-sm-6 special">
                <label for="duration" class="mb-1 required">Session Duration</label>
                <input type="number" step="1" value="{{old('duration')}}" class="form-control @error('duration') is-invalid @enderror" id="duration" placeholder="Select Year" name="duration" />
                @error('duration')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              <div class="col-sm-6 private">
                <label for="start_private" class="mb-1 required">Start Date</label>
                <div class="d-flex justify-content-center align-items-center">
                  <input class="form-control @error('start_private') is-invalid @enderror" placeholder="Please Select" name="start_private" id="start_private" value="{{old('start_private')}}" autocomplete="off">
                  <i style="margin-left: -28px" class='bx bxs-calendar'></i>
                </div>
                @error('start_private')
                <div style="width: 100%;margin-top: 0.3rem;font-size: 85%; color: var(--bs-form-invalid-color);">{{ $message }}</div>
                @enderror
              </div>
            </div>

            <div class="row mb-3">
              <div class="col-sm-6">
                <label for="type" class="mb-1 required">Class Type</label>
                <select class="form-select @error('type') is-invalid @enderror" id="type" name="type">
                  <option value="">Select Class Type</option>
                  <option value="Regular" @if(old('type') == 'Regular') selected @endif>Regular</option>
{{--                  <option value="Special" @if(old('type') == 'Special') selected @endif>Special</option>--}}
                  <option value="Private" @if(old('type') == 'Private') selected @endif>Private</option>
                </select>
                @error('type')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              <div class="col-sm-6">
                <label for="status" class="mb-1 required">Status</label>
                <select class="form-select @error('status') is-invalid @enderror" id="status" name="status">
                  <option value="active" {{ old('status') == 'active' ? 'selected' : '' }}>Active</option>
                  <option value="inactive" {{ old('status') == 'inactive' ? 'selected' : '' }}>Inactive</option>
                </select>
                @error('status')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
            </div>

            <div class="row mb-3">
              <div class="col-sm-6">
                <label for="class" class="mb-1 required">Class Type Day</label>
                <select class="select2 @error('day') is-invalid @enderror" id="day" multiple name="day[]" onchange="handleSelectChange()" data-id="{{ old('day') ? implode(',', old('day')) : '' }}">
                  @foreach(['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'] as $day)
                    <option value="{{ $day }}" @if(in_array($day, (array) old('day', []))) selected @endif>{{ ucfirst($day) }}</option>
                  @endforeach
                </select>
                @error('day')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              <div class="col-sm-6">
                <label for="subject" class="mb-1 required">Class Subject</label>
                <select class="form-select @error('subject') is-invalid @enderror" id="subject" name="subject">
                  <option value="" {{ old('subject') == '' ? 'selected' : '' }}>Select Subject</option>
                  <option value="Reading" {{ old('subject') == 'Reading' ? 'selected' : '' }}>Reading</option>
                  <option value="Writing" {{ old('subject') == 'Writing' ? 'selected' : '' }}>Writing</option>
                </select>
                @error('subject')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
            </div>
            <div class="row mb-3">
              <div class="col-sm-6" id="selectedOptions">
              </div>
            </div>
            <div class="row mb-3">
              <div class="col-sm-6 regular">
                <label for="level" class="mb-1 required">Class Level</label>
                <select class="form-select @error('level') is-invalid @enderror" id="level" name="level">
                  <option value="">Select Subject</option>
                </select>
                @error('level')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              <div class="col-sm-6 session">
                <label for="session" class="mb-1">Session</label>
                <input type="text" class="form-control @error('session') is-invalid @enderror" id="session" placeholder="Select Session" disabled />
              </div>
            </div>
            <div class="row mb-3">
              <div class="col-sm-6">
                <label for="link" class="mb-1">Insert link</label>
                <input type="text" class="form-control @error('link') is-invalid @enderror" id="link" name="link" value="{{old('link')}}" placeholder="Insert link"/>
                @error('link')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              <div class="col-sm-6">
                <label class="mb-1">No. of Students</label>
                <input type="text" class="form-control" placeholder="No. of Students" disabled />
              </div>
            </div>

            <div class="row">
              <div class="mt-3 d-flex justify-content-end">
                <a href="" class="btn btn-secondary" style="margin-right: 15px;">Reset</a>
                <button type="submit" class="btn btn-primary">Create</button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
@endsection
