@extends('layouts/contentNavbarLayout')

@section('title', 'Update Class')

@section('page-style')
  <style>
    th{
      padding: 0.625rem 1rem !important;
    }
  </style>
@endsection

@section('vendor-script')
@endsection

@section('page-script')
  <script>
    function handleSelectChange() {
      var oldValues = {!! json_encode(old()) !!};
      var errorMessages = {!! json_encode($errors->messages()) !!};
      var selectElement = document.getElementById("day");
      var selectedOptions = [];
      for (var option of selectElement.selectedOptions) {
        selectedOptions.push(option.value);
      }
      let time = [];
      if (typeof oldValues.day !== 'undefined') {
        for(var item of selectedOptions){
          time.push({
            start: oldValues[item + '_start'],
            end: oldValues[item + '_end'],
            day: item,
          });
        }
      }else{
        time = {!! $class->init_schedules !!};
      }

      document.getElementById("selectedOptions").innerHTML = "";
      var disableAttribute = {!! json_encode($class->type == 'Private' && $class->start_date <= now() ? 'disabled' : ($class->type == 'Regular' && $class->terms->start <= now() ? 'disabled' : '')) !!};
      selectedOptions.forEach(function(option) {
        var schedule = time.find(function(scheduleItem) {
          return scheduleItem.day.toLowerCase() === option.toLowerCase();
        });

        var div = document.createElement("div");
        div.classList.add("d-flex", "justify-content-around", "mb-3");

        var col1 = document.createElement("div");
        col1.classList.add("col-sm-3");
        col1.innerHTML = `<br><p class="text-center">${option.charAt(0).toUpperCase() + option.slice(1)}</p>`;

        var col2 = document.createElement("div");
        col2.classList.add("col-sm-3");
        var inputValueStart = oldValues[option + '_start'] || schedule ? schedule.start : '';
        var inputStartHasError = errorMessages[option + '_start'];
        col2.innerHTML = `<label for="${option}_start" class="mb-1 required">Time start</label><input type="time"  name="${option}_start" class="form-control ${inputStartHasError ? 'is-invalid' : ''}" id="${option}_start" value="${inputValueStart}"  ${disableAttribute}>`;

        var errorDiv2 = document.createElement("div");
        errorDiv2.classList.add("invalid-feedback");
        errorDiv2.innerHTML = inputStartHasError ? errorMessages[option + '_start'][0] : '';
        col2.appendChild(errorDiv2);

        var col3 = document.createElement("div");
        col3.classList.add("col-sm-3");
        var inputValueEnd = oldValues[option + '_end'] || schedule ? schedule.end : '';
        var inputEndHasError = errorMessages[option + '_end'];
        col3.innerHTML = `<label for="${option}_end" class="mb-1 required">Time End</label><input type="time" name="${option}_end" class="form-control ${inputEndHasError ? 'is-invalid' : ''}" id="${option}_end" value="${inputValueEnd}" ${disableAttribute}>`;

        var errorDiv3 = document.createElement("div");
        errorDiv3.classList.add("invalid-feedback");
        errorDiv3.innerHTML = inputEndHasError ? errorMessages[option + '_end'][0] : '';
        col3.appendChild(errorDiv3);

        div.appendChild(col1);
        div.appendChild(col2);
        div.appendChild(col3);
        document.getElementById("selectedOptions").appendChild(div);
      });
    }
    function hideClasses(type){
      if (type === 'Regular' || type === '') {
        $(".regular").show();
        $(".special").hide();
        $(".private").hide();
        $(".break").show();
        $(".start_date").show();
        $(".session").show();
      } else if (type === 'Special') {
        $(".regular").hide();
        $(".special").show();
        $(".private").hide();
        $(".break").show();
        $(".start_date").show();
        $(".session").show();
      } else if (type === 'Private') {
        $(".regular").hide();
        $(".special").hide();
        $(".private").show();
        $(".break").hide();
        $(".start_date").hide();
        $(".session").hide();
      }
    }

    $(document).ready(function(){
      var type = '{{ old("type",$class->type) }}';
      hideClasses(type);
      document.getElementById('type').addEventListener('change', function() {
        var type = this.value;
        hideClasses(type);
      });
      var subjectSelect = document.getElementById('subject');
      var levelSelect = document.getElementById('level');

      var selectedSubject = '{{ old("subject",$class->subject) }}';
      var selectedLevel = '{{ old("level",$class->level) }}';
      if (selectedSubject === 'Reading') {
        renderReadingLevels(selectedLevel);
      } else if (selectedSubject === 'Writing') {
        renderWritingLevels(selectedLevel);
      }

      subjectSelect.addEventListener('change', function() {
        var subject = this.value;
        if (subject === 'Reading') {
          renderReadingLevels();
        } else if (subject === 'Writing') {
          renderWritingLevels();
        } else {
          levelSelect.innerHTML = '<option value="">Select Level</option>';
        }
      });

      function renderReadingLevels(selected) {
        levelSelect.innerHTML = '';
        var readingLevels = ['QR1', 'QR2', 'QR3', 'QR4', 'QR5'];
        readingLevels.forEach(function(level) {
          var option = document.createElement('option');
          option.value = level;
          option.textContent = level;
          if (selected === level) {
            option.selected = true;
          }
          levelSelect.appendChild(option);
        });
      }

      function renderWritingLevels(selected) {
        levelSelect.innerHTML = '';
        var writingLevels = ['QW1', 'QW2', 'QW3', 'QW4'];
        writingLevels.forEach(function(level) {
          var option = document.createElement('option');
          option.value = level;
          option.textContent = level;
          if (selected === level) {
            option.selected = true;
          }
          levelSelect.appendChild(option);
        });
      }
      handleSelectChange();
      $('.select2').each(function () {
        $(this).select2({
          theme: 'bootstrap4',
          width: 'style',
          placeholder: $(this).attr('placeholder'),
          allowClear: Boolean($(this).data('allow-clear')),
        });
      });
      $("#start_date").datepicker({
        format: "yyyy/mm/dd",
        autoclose: true,
        todayHighlight: true,
        orientation: 'bottom',
        startDate: new Date()
      });
    });
  </script>
@endsection

@section('content')
  <div class="card">
    <div class="col-xxl">
      <div class="mb-4">
        <div class="card-header d-flex align-items-center justify-content-between">
          <h5 class="mb-0">Edit Class</h5> <a href="{{route('admin.classes.index')}}" class="btn btn-primary float-end">Back</a>
        </div>
        <div class="card-body">
          <form action="{{route('admin.classes.update',$class->id)}}" method="post">
            @method('PUT')
            @csrf
            <div class="row mb-3">
              <div class="col-sm-6">
                <label for="basic-default-name" class="mb-1 required">Class Name</label>
                <input maxlength="50" type="text" class="form-control @error('name') is-invalid @enderror" id="basic-default-name" placeholder="Input Name" name="name" value="{{ old('name',$class->name) }}" />
                @error('name')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              <div class="col-sm-6 regular">
                <label for="term" class="mb-1 required">Term</label>
                <select class="form-select @error('term') is-invalid @enderror" id="term" name="term" {{$class->type == 'Private' && $class->start_date <= now() ? 'disabled' : ($class->type == 'Regular' && $class->terms->start <= now() ? 'disabled' : '')}}>
                  @if($class->type == 'Regular' && $class->terms->start <= now())
                    <option value="">{{$class->terms->type}}/{{$class->terms->year}}</option>
                  @else
                    <option value="">Term</option>
                  @endif
                  @foreach($terms as $term)
                    <option value="{{$term->id}}" {{ old('term',$class->terms ? $class->terms->id : '') == $term->id ? 'selected' : '' }}>{{$term->type}}/{{$term->year}}</option>
                  @endforeach
                </select>
                @error('term')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              <div class="col-sm-6 special">
                <label for="duration" class="mb-1 required">Session Duration</label>
                <input type="number" step="1" value="{{old('duration',$class->duration_session)}}" class="form-control @error('duration') is-invalid @enderror" id="duration" placeholder="Select Year" name="duration" />
                @error('duration')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              <div class="col-sm-6 private">
                <label for="start_private" class="mb-1 required">Start Date</label>
                <div class="d-flex justify-content-center align-items-center">
                  <input name="start_private" id="start_date" {{$class->type == 'Private' && $class->start_date <= now() ? 'disabled' : ''}} value="{{old('start_private',$class->start_date ? Carbon\Carbon::parse($class->start_date)->format('Y/m/d') : '')}}" class="form-control @error('start_private') is-invalid @enderror" autocomplete="off" placeholder="Please Select" />
                  <i style="margin-left: -28px" class='bx bxs-calendar'></i>
                </div>
                @error('start_private')
                <div style="width: 100%;margin-top: 0.3rem;font-size: 85%; color: var(--bs-form-invalid-color);">{{ $message }}</div>
                @enderror
              </div>
            </div>

            <div class="row mb-3">
              <div class="col-sm-6">
                <label for="type" class="mb-1 required">Class Type</label>
                <select class="form-select @error('type') is-invalid @enderror" id="type" name="type" {{$class->type == 'Private' && $class->start_date <= now() ? 'disabled' : ''}} {{$class->type == 'Regular' && $class->terms->start <= now() ? 'disabled' : '' }} >
                  <option value="Regular" @if(old('type',$class->type) == 'Regular') selected @endif>Regular</option>
{{--                  <option value="Special" @if(old('type',$class->type) == 'Special') selected @endif>Special</option>--}}
                  <option value="Private" @if(old('type',$class->type) == 'Private') selected @endif>Private</option>
                </select>
                @error('type')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              <div class="col-sm-6">
                <label for="status" class="mb-1 required">Status</label>
                <select class="form-select @error('status') is-invalid @enderror" id="status" name="status">
                  <option value="active" {{ old('status',$class) == 'active' ? 'selected' : '' }}>Active</option>
                  <option value="inactive" {{ old('status',$class->status) == 'inactive' ? 'selected' : '' }}>Inactive</option>
                </select>
                @error('status')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
            </div>

            <div class="row mb-3">
              <div class="col-sm-6">
                <label for="class" class="mb-1 required">Class Type Day</label>
                @php
                  $selectedDay = !is_null(old('_token')) ? old('day',[]) : array_column($sche, 'day');
                @endphp
                <select class="select2 @error('day') is-invalid @enderror" id="day" multiple name="day[]" onchange="handleSelectChange()" data-id="{{ old('day') ? implode(',', old('day')) : '' }}" {{$class->type == 'Private' && $class->start_date <= now() ? 'disabled' : ''}} {{$class->type == 'Regular' && $class->terms->start <= now() ? 'disabled' : '' }}>
                  @foreach(['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'] as $day)
                    <option value="{{ $day }}" @if(in_array($day,$selectedDay)) selected @endif>{{ ucfirst($day) }}</option>
                  @endforeach
                </select>
                @error('day')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              <div class="col-sm-6">
                <label for="subject" class="mb-1 required">Class Subject</label>
                <select class="form-select @error('subject') is-invalid @enderror" id="subject" name="subject" {{$class->type == 'Private' && $class->start_date <= now() ? 'disabled' : ($class->type == 'Regular' && $class->terms->start <= now() ? 'disabled' : '') }}>
                  <option value="" {{ old('subject') == '' ? 'selected' : '' }}>Select Subject</option>
                  <option value="Reading" {{ old('subject',$class->subject) == 'Reading' ? 'selected' : '' }}>Reading</option>
                  <option value="Writing" {{ old('subject',$class->subject) == 'Writing' ? 'selected' : '' }}>Writing</option>
                </select>
                @error('subject')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
            </div>
            <div class="row mb-3">
              <div class="col-sm-6" id="selectedOptions">
              </div>
            </div>
            <div class="row mb-3">
              <div class="col-sm-6 regular">
                <label for="level" class="mb-1 required">Class Level</label>
                <select class="form-select @error('level',) is-invalid @enderror" id="level" name="level" {{$class->type == 'Regular' && $class->terms->start <= now() ? 'disabled' : '' }}>
                  <option value="">Select Subject</option>
                </select>
                @error('level')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              <div class="col-sm-6 session">
                <label for="session" class="mb-1">Session</label>
                <input type="text" class="form-control @error('session') is-invalid @enderror" id="session" value="{{$lastSession}}" disabled />
              </div>
            </div>
            <div class="row mb-3">
              <div class="col-sm-6">
                <label for="link" class="mb-1">Insert link</label>
                <input type="text" class="form-control @error('link') is-invalid @enderror" id="link" name="link" value="{{old('link',$class->link)}}" placeholder="Insert link"/>
                @error('link')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              <div class="col-sm-6">
                <label class="mb-1">No. of Students</label>
                <input type="text" class="form-control" placeholder="No. of Students" value="{{$class->student_count}}" disabled />
              </div>
            </div>

            <div class="row">
              <div class="mt-3 d-flex justify-content-end">
                <a  href="" class="btn btn-secondary" style="margin-right: 15px;">Reset</a>
                <button type="submit" class="btn btn-primary">Save</button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
@endsection
