@extends('layouts/contentNavbarLayout')

@section('title', 'Edit Monthly Progress Report')

@section('vendor-style')
@endsection

@section('vendor-script')
@endsection

@section('page-script')
  <script>
    $(document).ready(function(){
    });
  </script>
@endsection

@section('content')
  <div class="card" style="height: 100%">
    <div class="card-header d-flex align-items-center justify-content-between">
      <h5 class="mb-0">{{request()->ty == 'Created' ? 'Create' :'Edit'}} Monthly Report</h5> <a href="{{route('admin.reports.show',$id).'?type=private' }}" class="btn btn-primary float-end">Back</a>
    </div>
    <form action="{{route('admin.reports.update',$id)}}" method="post">
      @method('PUT')
      @csrf
      <input hidden name="type" value="{{request()->type}}">
      <input hidden name="term_id" value="{{$class->id}}">
      <input type="hidden" name="ty" value="{{request()->ty ?:'Updated'}}">
      <div class="card-body d-flex align-items-center">
        <div class="col-sm-2">
          <label for="basic-default-name" class="mb-1 required">Date</label>
          @if($report)
            <input hidden name="date_private" value="{{$report->date_private}}">
          @endif
          <select class="form-select @error('date_private') is-invalid @enderror" name="date_private" {{$report ? 'disabled':''}} style="    width: 135px;">
            <option value="">Select Date</option>
            @if($schedule)
              @foreach($schedule as $key => $sche)
                <option value="{{$key}}" {{old('date_private',$report ? $report->date_private : '') == $key ? 'selected' : '' }}>{{$key}}</option>
              @endforeach
            @endif
          </select>
          @error('date_private')
          <div class="invalid-feedback">{{ $message }}</div>
          @enderror
        </div>
      </div>
      <div class="d-flex" style="padding: 0 25px">
        <p>Class:</p>
        <p style="padding: 0 10px">{{$class->name}}</p>
      </div>
      <div style="padding: 0 25px;">
        <div class="row mb-3">
          <div class="col">
            <label for="reading" class="mb-1 required">Report</label>
            <textarea class="form-control @error('reading') is-invalid @enderror" id="reading" name="reading">{{old('reading',$report ? $report->reading : '')}}</textarea>
            @error('reading')
            <div class="invalid-feedback">{{ $message }}</div>
            @enderror
          </div>
        </div>
        <div class="row mb-3">
          <div class="col-sm-2">
            <label for="basic-default-name" class="mb-1">Status</label>
            <select class="form-select" name="status">
              <option value="inactive" {{ old('status',$report ? $report->status : '') === 'inactive' ? 'selected' : '' }}>Inactive</option>
              <option value="active" {{ old('status',$report ? $report->status : '') === 'active' ? 'selected' : '' }}>Active</option>
            </select>
          </div>
        </div>
        <div class="row">
          <div class="mt-3 d-flex justify-content-end">
            @if($report)
              <button style="margin-right: 20px" class="btn btn-danger" type="button" data-bs-toggle="modal" onclick="showConfirmationModal('{{ $report->id }}')" data-bs-target="#confirmDeleteModal">
                Delete
              </button>
            @endif
            <button type="submit" class="btn btn-primary">Save</button>
          </div>
        </div>
      </div>
    </form>
  </div>
  @if($report)
    @include('layouts.sections.modal-delete-confirm', ['route' => route('admin.reports.destroy',['report' => $id, 'report_id' => $report->id, 'term_id' => $report->term_id, 'type' => 'private']),'title' =>'report'])
  @endif
@endsection
