@extends('layouts/contentNavbarLayout')

@section('title', 'Monthly Progress Report')

@section('vendor-style')
@endsection
@section('page-style')
  <style>
    .tab-grade {
      background: #b5b5b5;
      color: white !important;
      font-weight: bold;
      cursor: pointer;
    }

    .tab-active {
      background: white;
      color: black !important;
      border-bottom: 0px !important;
    }
  </style>

@endsection
@section('vendor-script')
@endsection

@section('page-script')
  <script>
    $(document).ready(function () {
    });
  </script>
@endsection

@section('content')
  <div class="card" style="height: 100%">
    <div class="card-header d-flex align-items-center justify-content-between">
      <h5 class="mb-0">Monthly Report</h5> <a
        href="{{route('admin.reports.index')}}"
        class="btn btn-primary float-end">Back</a>
    </div>
    <div class="row">
      <div class="row mb-5" style="margin: 0px">
        <a
          href="{{route('admin.reports.show',$student->id)}}?type=regular"
          class="col-sm-6 text-center border p-2 tab-grade {{$type == 'regular' ? 'tab-active':''}}" data-id="hw1">
          <span class="text-center" id="myLink">Regular Class</span>
        </a>
        <a
          href="{{route('admin.reports.show',$student->id)}}?type=private"
          class="col-sm-6 text-center border p-2 tab-grade {{$type == 'private' ? 'tab-active':''}}" data-id="hw1">
          <span class="text-center" id="myLink">Private Class</span>
        </a>
      </div>
    </div>
    <form>
      <div class="card-body align-items-center">
        <div class="d-flex mb-3" style="padding: 0 10px;">
          <p style="margin-right: 100px">Student Name (English): {{$student->name_english}}</p>
          <p>Student Name (Korea): {{$student->name_korea}}</p>
        </div>
        <div class="row" style="margin: 0px">
          @if($reports)
            @foreach($reports as $key => $report)
              <div style="padding: 20px 20px;background: #f1f1f1;border-radius: 20px;margin-bottom: 20px">
                <div>
                  <div class="d-flex align-items-center justify-content-between"
                       style="font-weight: bold; margin-bottom: 20px">
                    <div class="mb-0">Class: <span style="padding: 0 10px">{{$report['class']->name}}</span></div>
                    <a
                      href="{{ route('admin.reports.edit', ['ty'=>'Created','report' => $student->id, 'term_id' => $report['class']->id, 'type' => 'private']) }}"
                      class="btn btn-primary float-end">Create New</a>
                  </div>
                  @if(count($report['report']))
                    @foreach($report['report'] as $val)
                      <div style="background: #fff; border-radius: 10px;padding: 10px;border:1px solid #ccc"
                           class="mb-3">
                        <p>Date: {{$val->date_private}}</p>
                        <p>Status: <span
                            style="color: {{ $val->status == 'active' ? 'green' : 'red' }}">{{ $val ? ucwords($val->status) : 'Inactive' }}</span>
                        </p>
                        <a
                          href="{{ route('admin.reports.edit', ['report' => $student->id, 'report_id' => $val->id, 'term_id' => $val->term_id, 'type' => 'private']) }}"><i
                            class="bx bx-edit-alt"></i></a>
                      </div>
                    @endforeach
                  @else
                    <div style="" class="mb-3">
                      <p class="text-center">No data available</p>
                    </div>
                  @endif
                </div>
              </div>
            @endforeach

          @else
            <div style="padding: 20px 20px;background: #f1f1f1;border-radius: 20px;margin-bottom: 20px">
              <div>
                <div style="" class="mb-3">
                  <p class="text-center">No data available</p>
                </div>
              </div>
            </div>
          @endif
            <div class="demo-inline-spacing d-flex justify-content-center align-items-center" style="padding:  0 20px;">
              @include('layouts.sections.pagination', ['paginator' => $reports])
            </div>
        </div>
      </div>
    </form>

  </div>
  @include('layouts.sections.modal-delete-confirm', ['route' => route('admin.terms.destroy','user_id'),'title' =>'term'])
@endsection
