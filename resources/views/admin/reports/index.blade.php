@extends('layouts/contentNavbarLayout')

@section('title', 'Monthly Progress Report')

@section('vendor-style')
@endsection

@section('vendor-script')
@endsection

@section('page-script')
  <script>
    $(document).ready(function(){
      $('#class').each(function () {
        $(this).select2({
          theme: 'bootstrap4',
          width: 'style',
          placeholder: $(this).attr('placeholder'),
          allowClear: Boolean($(this).data('allow-clear')),
        });
      });
    });
  </script>
@endsection

@section('content')
  <div class="card" style="height: 100%">
    <div class="d-flex justify-content-sm-between align-items-center">
      <h5 class="card-header">Monthly Report Manage</h5>
    </div>
    <form>
      <div class="card-body d-flex align-items-center">
        <div class="mb-3" style="padding: 0 10px;">
          <label class="mb-1">Search By</label>
          <input maxlength="100" type="text" name="keyword" class="form-control" placeholder="Student Name" value="{{ request()->input('keyword') }}">
        </div>
        <div class="col-sm-2 mb-3">
          <label for="parent" class="mb-1 required">Select Class</label>
          <select class="select2 form-select relationship-select" id="class" name="class">
            <option value="">All</option>
            @foreach($classes as $class)
              <option value="{{$class->id}}" {{ old('class',request()->class) == $class->id ? 'selected' : '' }}>{{$class->name}}</option>
            @endforeach
            @error('parent')
            <div class="invalid-feedback">{{ $message }}</div>
            @enderror
          </select>
        </div>
        <div class="mb-3" style="padding: 0 10px;">
          <button class="btn btn-primary" style="margin-top: 25px;">Search</button>
        </div>
        <a href="{{route('admin.reports.index')}}" class="btn btn-secondary" style="margin-top: 10px;" onclick="resetFilters()">Reset</a>
      </div>
    </form>
    <div class="text-nowrap" style="padding: 0 20px">
      <table class="table table-hover">
        <thead>
        <tr>
          <th class="text-center">Action</th>
          <th onclick="sort('english','{{request()->english == 'desc' ? 'asc' : 'desc'}}')" class="cursor-pointer">
            Student Name (English)
            @include('layouts.sections.sort', ['field' => 'english'])
          </th>
          <th onclick="sort('korea','{{request()->korea == 'desc' ? 'asc' : 'desc'}}')" class="cursor-pointer">
            Student Name (Korean)
            @include('layouts.sections.sort', ['field' => 'korea'])
          </th>
        </tr>
        </thead>
        <tbody class="table-border-bottom-0">
        @forelse($students as $student)
          <tr>
            <td class="text-center"><a style="border: none;
    color: #555555;" href="{{route('admin.reports.show',$student->id)}}?type=regular"><i class='bx bxs-show'></i></a></td>
            <td>{{$student->name_english}}</td>
            <td>{{$student->name_korea}}</td>
          </tr>
        @empty
          <tr>
            <td colspan="6" class="text-center">No data available</td>
          </tr>
        @endforelse
        </tbody>
      </table>
      <div class="demo-inline-spacing d-flex justify-content-center align-items-center" style="padding:  0 20px;">
        @include('layouts.sections.pagination', ['paginator' => $students])
      </div>
    </div>
  </div>
  @include('layouts.sections.modal-delete-confirm', ['route' => route('admin.terms.destroy','user_id'),'title' =>'term'])
@endsection
