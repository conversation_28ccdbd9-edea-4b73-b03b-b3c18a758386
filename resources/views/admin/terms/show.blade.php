@extends('layouts/contentNavbarLayout')

@section('title', 'Edit Term')

@section('page-style')
  <style>
    th{
      padding: 0.625rem 1rem !important;
    }
  </style>
@endsection

@section('vendor-script')
@endsection

@section('page-script')
  <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>
  <script>
    $(document).ready(function(){
      var start_old = {!! json_encode(old('start',$term->start)) !!};
      var break_start_old = {!! json_encode(old('break_start',$term->break_start)) !!};
      valueEndDate(start_old);
      valueBreakEndDate(break_start_old);
      $('#year').datepicker({
        format: "yyyy",
        weekStart: 1,
        orientation: "bottom",
        keyboardNavigation: false,
        viewMode: "years",
        minViewMode: "years",
        startDate: new Date(),
        autoclose: true,
      });
      $("#start").datepicker({
        format: "yyyy/mm/dd",
        autoclose: true,
        todayHighlight: true,
        orientation: 'bottom',
        daysOfWeekDisabled: [0, 2, 3, 4, 5, 6],
        startDate: new Date()
      });
      $("#start").change(function() {
        var selectedDate = $(this).val();
        valueEndDate(selectedDate);
      });
      $("#break_start").change(function() {
        var selectedDate = $(this).val();
        valueBreakEndDate(selectedDate);
      });

      $(".date_break_start").datepicker({
        format: "yyyy/mm/dd",
        autoclose: true,
        todayHighlight: true,
        startDate: new Date()
      });
    });
    function  valueEndDate(data){
      var newDate = moment(data, "YYYY/MM/DD").add(17, 'weeks').subtract(1, 'day');
      var formattedDate = newDate.format("YYYY/MM/DD");
      $("#end").val(formattedDate !== 'Invalid date' ? formattedDate : '');
    }
    function valueBreakEndDate(data){
      var newDate = moment(data, "YYYY/MM/DD").add(1, 'weeks').subtract(1, 'day');
      var formattedDate = newDate.format("YYYY/MM/DD");
      $("#break_end").val(formattedDate !== 'Invalid date' ? formattedDate : '');
    }
  </script>
@endsection

@section('content')
  <div class="card" style="height: 100%">
    <div class="col-xxl">
      <div class="mb-4">
        <div class="card-header d-flex align-items-center justify-content-between">
          <h5 class="mb-0">View Term</h5> <a href="{{route('admin.terms.index')}}" class="btn btn-primary float-end">Back</a>
        </div>
        <div class="card-body">
          <form action="{{route('admin.terms.update',$term->id)}}" method="post" id="form_id">
            @method('PUT')
            @csrf
            <div class="row mb-3">
              <div class="col-sm-6">
                <label for="type" class="mb-1 required">Term</label>
                <select class="form-select @error('term') is-invalid @enderror" id="type" name="type" disabled>
                  <option value="" {{ old('type') == '' ? 'selected' : '' }}>Term</option>
                  <option value="T1" {{ old('type',$term->type) == 'T1' ? 'selected' : '' }}>T1</option>
                  <option value="T2" {{ old('type',$term->type) == 'T2' ? 'selected' : '' }}>T2</option>
                  <option value="T3" {{ old('type',$term->type) == 'T3' ? 'selected' : '' }}>T3</option>
                </select>
                @error('type')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              <div class="col-sm-6">
                <label for="year" class="mb-1 required">Year</label>
                <div class="d-flex justify-content-center align-items-center">
                  <input name="year" id="year" disabled class="form-control @error('year') is-invalid @enderror" value="{{old('year',$term->year)}}" placeholder="Please Select" autocomplete="off" />
                  <i style="margin-left: -28px" class='bx bxs-calendar'></i>
                </div>
                @error('year')
                <div style="width: 100%;margin-top: 0.3rem;font-size: 85%; color: var(--bs-form-invalid-color);">{{ $message }}</div>
                @enderror
              </div>
            </div>

            <div class="row mb-3">
              <div class="col-sm-6">
                <label for="start" class="mb-1 required">Start Date</label>
                <div class="d-flex justify-content-center align-items-center">
                  <input name="start" id="start" disabled value="{{ old('start', $term->start ? \Carbon\Carbon::parse($term->start)->format('Y/m/d'): '') }}" class="form-control @error('start') is-invalid @enderror" autocomplete="off" placeholder="Please Select" />
                  <i style="margin-left: -28px" class='bx bxs-calendar'></i>
                </div>
                @error('start')
                <div style="width: 100%;margin-top: 0.3rem;font-size: 85%; color: var(--bs-form-invalid-color);">{{ $message }}</div>
                @enderror
              </div>
              <div class="col-sm-6">
                <label class="mb-1 required">End Date</label>
                <input class="form-control" disabled name="end" value="{{old('end',$term->end ? \Carbon\Carbon::parse($term->end)->format('Y/m/d'): '')}}" id="end" placeholder="Please Select" readonly style="background-color: #eceef1;opacity: 1">
              </div>
            </div>

            <div class="row mb-3">
              <div class="col-sm-6">
                <label for="break_start" class="mb-1">Break Start Date</label>
                <input name="break_start" disabled id="break_start" value="{{old('break_start',$term->break_start ? \Carbon\Carbon::parse($term->break_start)->format('Y/m/d'): '')}}" class="form-control date_break_start" placeholder="Please Select" autocomplete="off" />
                @error('start')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              <div class="col-sm-6">
                <label for="break_end" class="mb-1">Break End Date</label>
                <input class="form-control" disabled name="break_end" id="break_end" readonly style="background-color: #eceef1;opacity: 1" value="{{old('break_end',$term->break_end ? \Carbon\Carbon::parse($term->break_end)->format('Y/m/d'): '')}}" placeholder="Please Select">
              </div>
            </div>
            <div class="row mb-3">
              <div class="col-sm-6">
                <label for="break_option_start" class="mb-1">Break Start Date (Option)</label>
                <div class="d-flex justify-content-center align-items-center">
                  <input name="break_option_start" disabled id="break_option_start" value="{{old('break_option_start',$term->break_option_start ? \Carbon\Carbon::parse($term->break_option_start)->format('Y/m/d'): '')}}" class="form-control date_break_start" placeholder="Please Select" autocomplete="off"/>
                  <i style="margin-left: -28px" class='bx bxs-calendar'></i>
                </div>
                @error('break_option_start')
                <div
                  style="width: 100%;margin-top: 0.3rem;font-size: 85%; color: var(--bs-form-invalid-color);margin-left: 10px">{{ $message }}</div>
                @enderror
              </div>
              <div class="col-sm-6">
                <label for="break_option_end" class="mb-1">Break End Date (Option)</label>
                <input class="form-control" disabled name="break_option_end" id="break_option_end" value="{{old('break_option_end',$term->break_option_end ? \Carbon\Carbon::parse($term->break_option_end)->format('Y/m/d'): '')}}" placeholder="Please Select" readonly style="background-color: #eceef1;opacity: 1">
              </div>
            </div>
            <div class="row mb-3">
              <div class="col-sm-6">
                <label for="break_option_start" class="mb-1">Session</label>
                <div class="d-flex justify-content-center align-items-center">
                  <input disabled class="form-control" value="{{request()->session}}" placeholder="No.of Session" autocomplete="off"/>
                </div>
              </div>
            </div>
          </form>
          <div class="row">
            <div class="mt-3 d-flex justify-content-end">
              <a  href="{{route('admin.terms.edit',['term' => $term->id, 'session' => request()->session])}}" class="btn btn-primary" style="margin-right: 15px;">Edit</a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
@endsection
