@extends('layouts/contentNavbarLayout')

@section('title', 'Create Term')

@section('page-style')
  <style>
    th {
      padding: 0.625rem 1rem !important;
    }
  </style>
@endsection

@section('vendor-script')
@endsection

@section('page-script')
  <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>
  <script>
    $(document).ready(function () {
      var start_old = {!! json_encode(old('start')) !!};
      var break_start_old = {!! json_encode(old('break_start')) !!};
      var break_option_start_old = {!! json_encode(old('break_option_start')) !!};
      valueEndDate(start_old);
      valueBreakEndDate(break_start_old);
      valueBreakOptionEndDate(break_option_start_old);
      $('#year').datepicker({
        format: "yyyy",
        weekStart: 1,
        orientation: "bottom",
        keyboardNavigation: false,
        viewMode: "years",
        minViewMode: "years",
        autoclose: true
      }).on('changeYear', function(e){
        var selectedYear = e.date.getFullYear();
        var currentYear = new Date().getFullYear();
        if (selectedYear === currentYear) {
          var today = new Date();
          var dayOfWeek = today.getDay();
          var diff = (dayOfWeek === 0 ? 6 : dayOfWeek - 1);
          var nearestMonday = new Date(today);
          nearestMonday.setDate(today.getDate() - diff + 7);
          $("#start").datepicker('setDate', nearestMonday);
        } else {
          var firstMonday = new Date(selectedYear, 0, 1);
          while (firstMonday.getDay() !== 1) {
            firstMonday.setDate(firstMonday.getDate() + 1);
          }
          $("#start").datepicker('setDate', firstMonday);
          $("#break_start").datepicker('update', firstMonday);
        }
      });
      $("#start").datepicker({
        format: "yyyy/mm/dd",
        autoclose: true,
        todayHighlight: true,
        orientation: 'bottom',
        daysOfWeekDisabled: [0, 2, 3, 4, 5, 6],
      });
      $("#start").change(function () {
        var selectedDate = $(this).val();
        valueEndDate(selectedDate);
      });
      $("#break_start").change(function () {
        var selectedDate = $(this).val();
        valueBreakEndDate(selectedDate);
      });

      $("#break_option_start").change(function () {
        var selectedDate = $(this).val();
        valueBreakOptionEndDate(selectedDate);
      });

      $(".date_break_start").datepicker({
        format: "yyyy/mm/dd",
        autoclose: true,
        todayHighlight: true,
      });
    });

    function valueEndDate(data) {
      var newDate = moment(data, "YYYY/MM/DD").add(16, 'weeks').subtract(1, 'day');
      var formattedDate = newDate.format("YYYY/MM/DD");
      $("#end").val(formattedDate !== 'Invalid date' ? formattedDate : '');
    }

    function valueBreakEndDate(data) {
      var newDate = moment(data, "YYYY/MM/DD").add(1, 'weeks').subtract(1, 'day');
      var formattedDate = newDate.format("YYYY/MM/DD");
      $("#break_end").val(formattedDate !== 'Invalid date' ? formattedDate : '');
    }
    function valueBreakOptionEndDate(data) {
      var newDate = moment(data, "YYYY/MM/DD").add(1, 'weeks').subtract(1, 'day');
      var formattedDate = newDate.format("YYYY/MM/DD");
      $("#break_option_end").val(formattedDate !== 'Invalid date' ? formattedDate : '');
    }
  </script>
@endsection

@section('content')
  <div class="card" style="height: 100%">
    <div class="col-xxl">
      <div class="mb-4">
        <div class="card-header d-flex align-items-center justify-content-between">
          <h5 class="mb-0">Create term</h5> <a href="{{route('admin.terms.index')}}" class="btn btn-primary float-end">Back</a>
        </div>
        <div class="card-body">
          <form action="{{route('admin.terms.store')}}" method="post" id="form_id">
            @csrf
            <div class="row mb-3">
              <div class="col-sm-6">
                <label for="type" class="mb-1 required">Term</label>
                <select class="form-select @error('type') is-invalid @enderror" id="type" name="type">
                  <option value="" {{ old('type') == '' ? 'selected' : '' }}>Term</option>
                  <option value="T1" {{ old('type') == 'T1' ? 'selected' : '' }}>T1</option>
                  <option value="T2" {{ old('type') == 'T2' ? 'selected' : '' }}>T2</option>
                  <option value="T3" {{ old('type') == 'T3' ? 'selected' : '' }}>T3</option>
                </select>
                @error('type')
                <div
                  style="width: 100%;margin-top: 0.3rem;font-size: 85%; color: var(--bs-form-invalid-color);">{{ $message }}</div>
                @enderror
              </div>
              <div class="col-sm-6">
                <label for="year" class="mb-1 required">Year</label>
                <div class="d-flex justify-content-center align-items-center">
                  <input name="year" id="year" class="form-control @error('year') is-invalid @enderror"
                         value="{{old('year')}}" placeholder="Please Select" autocomplete="off"/>
                  <i style="margin-left: -28px" class='bx bxs-calendar'></i>
                </div>
                @error('year')
                <div
                  style="width: 100%;margin-top: 0.3rem;font-size: 85%; color: var(--bs-form-invalid-color);">{{ $message }}</div>
                @enderror
              </div>
            </div>

            <div class="row mb-3">
              <div class="col-sm-6">
                <label for="start" class="mb-1 required">Start Date</label>
                <div class="d-flex justify-content-center align-items-center">
                  <input name="start" id="start" value="{{old('start')}}"
                         class="form-control @error('start') is-invalid @enderror" autocomplete="off"
                         placeholder="Please Select"/>
                  <i style="margin-left: -28px" class='bx bxs-calendar'></i>
                </div>
                @error('start')
                <div
                  style="width: 100%;margin-top: 0.3rem;font-size: 85%; color: var(--bs-form-invalid-color);">{{ $message }}</div>
                @enderror
              </div>
              <div class="col-sm-6">
                <label class="mb-1">End Date</label>
                <input class="form-control" name="end" value="{{old('end')}}" id="end" placeholder="Please Select"
                       readonly style="background-color: #eceef1;opacity: 1">
              </div>
            </div>

            <div class="row mb-3">
              <div class="col-sm-6">
                <label for="break_start" class="mb-1 required">Break Start Date</label>
                <div class="d-flex justify-content-center align-items-center">
                  <input name="break_start" id="break_start" value="{{old('break_start')}}"
                         class="form-control date_break_start @error('break_start') is-invalid @enderror" placeholder="Please Select" autocomplete="off"/>
                  <i style="margin-left: -28px" class='bx bxs-calendar'></i>
                </div>
                @error('break_start')
                <div
                  style="width: 100%;margin-top: 0.3rem;font-size: 85%; color: var(--bs-form-invalid-color);">{{ $message }}</div>
                @enderror
              </div>
              <div class="col-sm-6">
                <label for="break_end" class="mb-1">Break End Date</label>
                <input class="form-control" name="break_end" id="break_end" value="{{old('break_end')}}"
                       placeholder="Please Select" readonly style="background-color: #eceef1;opacity: 1">
              </div>
            </div>
            <div class="row mb-3">
              <div class="col-sm-6">
                <label for="break_option_start" class="mb-1">Break Start Date (Option)</label>
                <div class="d-flex justify-content-center align-items-center">
                  <input name="break_option_start" id="break_option_start" value="{{old('break_option_start')}}" class="form-control date_break_start" placeholder="Please Select" autocomplete="off"/>
                  <i style="margin-left: -28px" class='bx bxs-calendar'></i>
                </div>
                @error('break_option_start')
                <div
                  style="width: 100%;margin-top: 0.3rem;font-size: 85%; color: var(--bs-form-invalid-color);margin-left: 10px">{{ $message }}</div>
                @enderror
              </div>
              <div class="col-sm-6">
                <label for="break_option_end" class="mb-1">Break End Date (Option)</label>
                <input class="form-control" name="break_option_end" id="break_option_end" value="{{old('break_option_end')}}" placeholder="Please Select" readonly style="background-color: #eceef1;opacity: 1">
              </div>
            </div>
            <div class="row mb-3">
              <div class="col-sm-6">
                <label for="break_option_start" class="mb-1">Session</label>
                <div class="d-flex justify-content-center align-items-center">
                  <input disabled class="form-control" value="" placeholder="No.of Session" autocomplete="off"/>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="mt-3 d-flex justify-content-end">
                <span class="btn btn-secondary" style="margin-right: 15px;" onclick="$('#form_id').trigger('reset'); ">Reset</span>
                <button type="submit" class="btn btn-primary">Save</button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
@endsection
