@extends('layouts/contentNavbarLayout')

@section('title', 'Invoices Refund')

@section('vendor-style')
@endsection

@section('vendor-script')
@endsection

@section('page-script')
  <script>
    function resetFilters() {
      window.location.href = {{route('admin.invoices.index')}};
    }
  </script>
@endsection

@section('content')
  <div class="card" style="height: 100%">
    <div class="d-flex justify-content-sm-between align-items-center">
      <h5 class="card-header">Invoice management</h5>
    </div>
    <form>
      <div class="card-body d-flex align-items-center">
        <div class="mb-3" style="padding: 0 10px;">
          <label class="mb-1">Search By</label>
          <input name="keyw" class="form-control" placeholder="Name or Phone Number" value="{{request()->input('keyw')}}">
        </div>
        <div class="mb-3" style="padding: 0 10px;">
          <button class="btn btn-primary" style="margin-top: 25px;">Search</button>
        </div>
        <a href="{{route('admin.terms.index')}}" class="btn btn-secondary" style="margin-top: 10px;" onclick="resetFilters()">Reset</a>
      </div>
    </form>
    <div class="text-nowrap" style="padding: 0 20px">
      <table class="table table-hover">
        <thead>
        <tr>
          <th class="text-center">Action</th>
          <th onclick="sort('id','{{request()->id == 'desc' ? 'asc' : 'desc'}}')" class="cursor-pointer">
            Invoice ID
            @include('layouts.sections.sort', ['field' => 'id'])
          </th>
          <th onclick="sort('refund_date','{{request()->refund_date == 'desc' ? 'asc' : 'desc'}}')" class="cursor-pointer">
            Refund Date
            @include('layouts.sections.sort', ['field' => 'refund_date'])
          </th>
          <th onclick="sort('created_at','{{request()->created_at == 'desc' ? 'asc' : 'desc'}}')" class="cursor-pointer">
            Created Date
            @include('layouts.sections.sort', ['field' => 'created_at'])
          </th>
          <th onclick="sort('name','{{request()->name == 'desc' ? 'asc' : 'desc'}}')" class="cursor-pointer">
            Invoice to
            @include('layouts.sections.sort', ['field' => 'name'])
          </th>
          <th onclick="sort('price','{{request()->price == 'desc' ? 'asc' : 'desc'}}')" class="cursor-pointer">
            Amount
            @include('layouts.sections.sort', ['field' => 'price'])
          </th>
          <th onclick="sort('status','{{request()->status == 'desc' ? 'asc' : 'desc'}}')" class="cursor-pointer">
            Status
            @include('layouts.sections.sort', ['field' => 'status'])
          </th>
        </tr>
        </thead>
        <tbody class="table-border-bottom-0">
        @forelse($invoices as $invoice)
          <tr>
            <td style="text-align: center">
              @if($invoice->status == 'request_refund')
                <a style="border: none;
    color: #555555;" href="{{route('admin.invoices.show',$invoice->id)}}"><i class='bx bx bx-edit-alt'></i></a>
              @else
                <a style="border: none;
    color: #555555;" href="{{route('admin.invoices.show',$invoice->id)}}"><i class='bx bxs-show'></i></a>
              @endif
            </td>
            <td>IN{{$invoice->id}}</td>
            <td>
              {{$invoice->refund_date}}
            </td>
            <td>
              {{date('Y-m-d', strtotime($invoice->created_at))}}
            </td>
            <td>
              {{$invoice->name_english}} - {{$invoice->phone}}
            </td>
            <td>
              ${{number_format($invoice->price)}} USD
            </td>
            <td style="text-transform: capitalize;">
              {{$invoice->status == 'request_refund' ? "Pending" : $invoice->status}}
            </td>
          </tr>
        @empty
          <tr>
            <td colspan="7" class="text-center">No data available</td>
          </tr>
        @endforelse
        </tbody>
      </table>
      <div class="demo-inline-spacing d-flex justify-content-center align-items-center" style="padding:  0 20px;">
        @include('layouts.sections.pagination', ['paginator' => $invoices])
      </div>
    </div>
  </div>
@endsection
