@extends('layouts/contentNavbarLayout')

@section('title', 'Edit Invoice')

@section('page-style')
  <style>
    th {
      padding: 0.625rem 1rem !important;
    }
    .alige-right{
      float: right;
      text-align: right;
    }
  </style>
@endsection

@section('vendor-script')
@endsection

@section('page-script')
<script>
  $('#prod,#parent').each(function () {
    $(this).select2({
      theme: 'bootstrap4',
      width: 'style',
      placeholder: $(this).attr('placeholder'),
      allowClear: Boolean($(this).data('allow-clear')),
    });
  });
  $(document.body).on("change", "#prod", function () {
    const prod = $(this).val();
    $('input[name="price"]').val(0);
    $('.tb-invoice').html('');
    $.each(prod, function (key, val) {
      prodSelection(key,val);
    });
  });
  $(document.body).on("change","#parent",function(){
    parentSelection
  });

  function prodSelection(key,prod_id) {
    if (prod_id) {
      fetch(window.location.origin + '/api/prod/' + prod_id)
        .then(response => response.json())
        .then(data => {
          if (data.status) {
            $( ".tb-invoice" ).append( trInvoice(prod_id,parseInt(key)+1,data.prod.name,1,data.prod.price) );
            const tPrice = parseInt($('input[name="price"]').val())+parseInt(data.prod.price);
            console.log(tPrice);
            $('input[name="price"]').val(tPrice);
            $('.amount-due').html('$'+tPrice);
          } else {
            $('#prod'+prod_id).remove();
          }
        })
        .catch(error => {
          $('#prod'+prod_id).remove();
        });
    } else {
      $('#prod'+prod_id).remove();
    }
  }
  function trInvoice(id, no, des, quantiti, amout) {
    return '<tr id="prod"'+id+'><td style="text-align: center">'+no+'</td><td style="text-align: center"><span class="prod_des">'+des+'</span></td> <td style="text-align: center">'+quantiti+'</td><td style="text-align: center"> <span class="prod_price">$'+amout+' USD</span> </td> </tr>'
  }

  function parentSelection() {
    var id = $('#parent').val();
    if (id) {
      fetch(window.location.origin + '/api/parent/' + id)
        .then(response => response.json())
        .then(data => {
          if (data.status) {
            $('.parent_name').html(data.parent.name_english)
            $('.parent_phone').html(data.parent.phone)
            $('.parent_address').html(data.parent.address)
            let cityId = data.parent.city_id;
            let countryId = data.parent.country_id;
            $('.parent_city').html(cityId && countryId ? `${cityId} - ${countryId}` : (cityId || countryId || ''));
          } else {
            $('.parent_name').html('')
            $('.parent_phone').html('')
            $('.parent_address').html('')
            $('.parent_city').html('')
          }
        })
        .catch(error => {
          $('.parent_name').html('')
          $('.parent_phone').html('')
          $('.parent_address').html('')
          $('.parent_city').html('')
        });
    } else {
      $('.parent_name').html('')
      $('.parent_phone').html('')
      $('.parent_address').html('')
      $('.parent_city').html('')
    }
  }
</script>
@endsection

@section('content')
  <div class="card" style="height: 100%">
    <div class="col-xxl">
      <div class="mb-4">
        <div class="card-header d-flex align-items-center justify-content-between">
          <h5 class="mb-0">Edit Invoice</h5> <a href="{{route('admin.invoices.index')}}" class="btn btn-primary float-end">Back</a>
        </div>
        <div class="card-body">
          <form action="{{route('admin.invoices.update',$invoice->id)}}" method="post" id="form_id">
            @method('PUT')
            @csrf
            <div class="row mb-3">
              <div class="col-sm-6">
                <label for="parent" class="mb-1">Select Parent</label>
                <select class="form-select relationship-select @error('parent') is-invalid @enderror" id="parent" name="parent" onchange="parentSelection()" data-old-value="{{ old('parent') }}">
                  <option value="">Choose parent</option>
                  @foreach($parents as $parent)
                    <option value="{{$parent->id}}" {{ old('parent', $invoice->parent_id) == $parent->id ? 'selected' : '' }}>
                      {{$parent->name_english}} {{$parent->phone}}
                    </option>
                  @endforeach
                </select>
                @error('parent')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              <div class="col-sm-6">
                <label class="mb-1 required">Product</label>
                <select class="select2 @error('prod') is-invalid @enderror" id="prod" multiple name="prod[]"
                        data-id="{{ old('prod') ? implode(',', old('prod')) : '' }}">
                  @foreach($prods as $key => $prod)
                    <option value="{{ $key }}" @if(in_array($prod,$prodInvoice)) selected @endif>{{$prod['name']}}</option>
                  @endforeach
                </select>
                @error('prod')
                <div
                  style="width: 100%;margin-top: 0.3rem;font-size: 85%; color: var(--bs-form-invalid-color);">{{ $message }}</div>
                @enderror
                <input hidden name="price" value="{{$invoice->price}}">
              </div>
            </div>
            <div class="row mb-3">
              <div class="col-sm-6">
                <label class="mb-1">Status</label>
                <select class="form-select" name="status" >
                  <option value="init" {{ old('status',$invoice->status) == 'init' ? 'selected' : '' }}>Awaiting Approval</option>
                  <option value="unpaid" {{ old('status',$invoice->status) == 'unpaid' ? 'selected' : '' }}>Approval</option>
                </select>
              </div>
              <div class="col-sm-6">
                <label for="url" class="mb-1 required">Url Invoice</label>
                <div class="d-flex justify-content-center align-items-center">
                  <input name="url_invoice" id="url_invoice" class="form-control @error('url_invoice') is-invalid @enderror"
                         value="{{old('url_invoice', $invoice->url_invoice)}}" placeholder="Please Insert Link" />
                </div>
                @error('url_invoice')
                <div
                  style="width: 100%;margin-top: 0.3rem;font-size: 85%; color: var(--bs-form-invalid-color);">{{ $message }}</div>
                @enderror
              </div>
            </div>
            <div class="row mb-4 p-3 mt-4">
              <div class="col-sm-12">
                <h4 class="text-center">BILLED SERVICES</h4>
                <div class="text-nowrap" style="padding: 0 20px">
                  <table class="table table-hover">
                    <thead>
                    <tr>
                      <th class="text-center">Item No</th>
                      <th class="text-center">Item</th>
                      <th class="text-center">Quantity</th>
                      <th class="text-center">Amount</th>
                    </tr>
                    </thead>
                    <tbody class="table-border-bottom-0 tb-invoice">
                    @foreach($invoice->product as $key => $val)
                      <tr>
                        <td style="text-align: center">
                          {{$key + 1}}
                        </td>
                        <td style="text-align: center">
                          <span class="prod_des"> {{$val->prod_name}}</span>
                        </td>
                        <td style="text-align: center">
                          1
                        </td>
                        <td style="text-align: center">
                          <span class="prod_price">${{$val->price}} USD</span>
                        </td>
                      </tr>
                    @endforeach
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
            <div class="row mb-4 p-3">
              <div class="col-sm-6">
                <p>Billed to</p>
                <p><span class="parent_name">{{$invoice->parent->name_english}}</span></p>
                <p><span class="parent_phone">{{$invoice->parent->phone}}</span></p>
                <p><span class="parent_address">{{$invoice->parent->address}}</span></p>
                @if(!empty($invoice->parent->city_id) && !empty($invoice->parent->country_id))
                  <p>{{$invoice->parent->city_id}} - {{$invoice->parent->country_id}}</p>
                @elseif(!empty($invoice->parent->city_id))
                  <p>{{$invoice->parent->city_id}}</p>
                @elseif(!empty($invoice->parent->country_id))
                  <p>{{$invoice->parent->country_id}}</p>
                @endif              </div>
              <div class="col-sm-6 alige-right">
                <h4>Amount Due: <span class="amount-due">${{$invoice->price}} USD</span></h4>
                <p>Invoice From</p>
                <p>Q1 Academy</p>
                <p>Adress Q1 Academy</p>
                <p>Zipcode, City - Country</p>
              </div>
            </div>
            <div class="row">
              <div class="mt-3 d-flex justify-content-end">
                <button type="submit" class="btn btn-primary">Save</button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
@endsection
