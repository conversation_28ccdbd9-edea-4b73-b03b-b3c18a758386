@extends('layouts/contentNavbarLayout')

@section('title', 'Product')

@section('vendor-style')
    <style>
        .description p{
            margin-bottom: 0;
        }
    </style>
@endsection

@section('vendor-script')
@endsection

@section('page-script')
@endsection

@section('content')
    <div class="card" style="height: 100%">
        <div class="d-flex justify-content-sm-between align-items-center">
            <h5 class="card-header">Product Management</h5>
            <a style="margin-right: 20px;" class="btn btn-primary" href="{{route('admin.invoice-products.create')}}">Create Product</a>
        </div>
        <form>
            <div class="card-body d-flex align-items-center">
                <div class="mb-3" style="padding: 0 10px;">
                    <label class="mb-1">Search By</label>
                    <input name="keyw" class="form-control" placeholder="Name or Description" value="{{request()->input('keyw')}}">
                </div>
                <div class="mb-3" style="padding: 0 10px;">
                    <button class="btn btn-primary" style="margin-top: 25px;">Search</button>
                </div>
                <a href="{{route('admin.invoice-products.index')}}" class="btn btn-secondary" style="margin-top: 10px;">Reset</a>
            </div>
        </form>
        <div class="text-nowrap" style="padding: 0 20px">
            <table class="table table-hover">
                <thead>
                <tr>
                    <th class="text-center">Action</th>
                    <th class="cursor-pointer">
                        Product ID
                    </th>
                    <th class="cursor-pointer">
                        Product Name
                    </th>
                    <th class="cursor-pointer">
                        Price
                    </th>
                    <th class="cursor-pointer">
                        Class
                    </th>
                    <th class="cursor-pointer">
                        Session
                    </th>
                    <th class="cursor-pointer">
                        Description
                    </th>
                </tr>
                </thead>
                <tbody class="table-border-bottom-0">
                @forelse($products as $product)
                    <tr>
                        <td class="text-center">
                            <a style="border: none;color: #555555;" href="{{route('admin.invoice-products.edit',$product['id'])}}">
                                <i class='bx bx bx-edit-alt'></i>
                            </a>
                        </td>
                        <td>{{$product['id']}}</td>
                        <td>{{$product['name']}}</td>
                        <td>{{$product['price']}}$</td>
                        <td>{{!empty($classes[$product['id']]) ? $classes[$product['id']]['get_class']['name'] : ''}}</td>
                        <td>{{!empty($classes[$product['id']]['session']) ? $sessions[$classes[$product['id']]['session']] : ''}}</td>
                        <td class="description">{!! $product['description'] !!}</td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="4" class="text-center">No data available</td>
                    </tr>
                @endforelse
                </tbody>
            </table>
            <div class="demo-inline-spacing d-flex justify-content-center align-items-center" style="padding:  0 20px;">
                @include('layouts.sections.pagination', ['paginator' => $products])
            </div>
        </div>
    </div>
    @include('layouts.sections.modal-delete-confirm', ['route' => route('admin.invoices.destroy','user_id'),'title' =>'invoice'])
@endsection
