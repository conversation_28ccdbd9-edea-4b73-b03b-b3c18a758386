@extends('layouts/contentNavbarLayout')

@section('title', $type.' Product')

@section('page-style')

@endsection

@section('vendor-script')
@endsection

@section('page-script')
    <script>
        $(document).ready(function() {
            $('#class').each(function () {
                $(this).select2({
                    theme: 'bootstrap4',
                    width: 'style',
                    placeholder: $(this).attr('placeholder'),
                    allowClear: Boolean($(this).data('allow-clear')),
                });
            });
            // Function to toggle session visibility based on class type
            function toggleSessionVisibility() {
                const selectedOption = $('#class option:selected');
                const classType = selectedOption.data('type');
                const sessionContainer = $('#session').closest('.col-sm-6');

                if (classType === 'Private') {
                    sessionContainer.hide();
                    $('#session').val('');
                } else {
                    sessionContainer.show();
                }
            }

            toggleSessionVisibility();

            $('#class').on('change', function() {
                toggleSessionVisibility();
            });
        });
    </script>
@endsection

@section('content')
    <div class="card">
        <div class="col-xxl">
            <div class="mb-4">
                <div class="card-header d-flex align-items-center justify-content-between">
                    <h5 class="mb-0">{{$type}} Product</h5> <a href="{{url()->previous()}}" class="btn btn-primary float-end">Back</a>
                </div>
                <div class="card-body">
                    <form action="{{$route}}" method="post" id="form_id">
                        @csrf
                        @if ($method !== 'POST')
                            @method($method)
                        @endif
                        <div class="row mb-3">
                            <div class="col-sm-6">
                                <label for="basic-default-name" class="mb-1 required">Product Name</label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror" id="basic-default-name" placeholder="Name" name="name" value="{{ old('name',$product['name']) }}" />
                                @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-sm-6">
                                <label for="basic-default-price" class="mb-1 required">Price</label>
                                <input type="number" step="0.01" id="basic-default-price" class="form-control @error('name') is-invalid @enderror" placeholder="Price" name="price" value="{{ old('price',$product['price']) }}" />
                                @error('price')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-sm-6">
                                <label for="class" class="mb-1">Class</label>
                                <select class="form-select" id="class" name="class_id">
                                    <option value="">Select Class</option>
                                    @foreach($classes as $class)
                                        <option value="{{$class->id}}" data-type="{{$class->type}}" {{ old('class_id',$product['class_id']) == $class->id ? 'selected' : '' }}>{{$class->name}}</option>
                                    @endforeach
                                </select>
                                @error('class_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-sm-6">
                                <label for="session" class="mb-1">Session</label>
                                <select class="form-select" id="session" name="session">
                                    <option value="">Select session</option>
                                    @foreach($sessions as $key => $label)
                                        <option value="{{ $key }}" {{ old('session', $product['session']) == $key ? 'selected' : '' }}>
                                            {{ $label }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('session')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-sm-12">
                                <label for="basic-default-description" class="mb-1">Description</label>
                                <textarea rows="5" id="basic-default-description" class="form-control @error('description') is-invalid @enderror" placeholder="Description" name="description">{{ old('description', strip_tags($product['description'])) }}</textarea>
                                @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row">
                            <div class="mt-3 d-flex justify-content-end">
                                <span class="btn btn-secondary" style="margin-right: 15px;" onclick="$('#form_id').trigger('reset'); ">Reset</span>
                                <button type="submit" class="btn btn-primary">Save</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection
