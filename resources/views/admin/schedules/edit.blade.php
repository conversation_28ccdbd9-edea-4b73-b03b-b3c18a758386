@extends('layouts/contentNavbarLayout')

@section('title', 'Edit Holiday')

@section('page-style')
  <style>
    td,th{
      border: 1px solid #ccc;
    }
    th{
      padding: 0.625rem 1rem !important;
    }
    .datepicker{
      z-index: 10000 !important;
    }
  </style>
@endsection

@section('vendor-script')
@endsection

@section('page-script')
  <script>
    $(document).ready(function(){

    });
  </script>
@endsection

@section('content')
  <div class="card">
    <div class="col-xxl">
      <div class="mb-4">
        <div class="card-header d-flex align-items-center justify-content-between">
          <h5 class="mb-0">Edit Schedule</h5> <a href="{{route('admin.schedules.index')}}" class="btn btn-primary float-end">Back</a>
        </div>
        <div class="card-body">
          <form action="{{route('admin.schedules.update',$schedule->id)}}" method="post" id="form_id">
            @method('PUT')
            @csrf
            <div class="row mb-3">
              <div class="col-sm-6">
                <label for="basic-default-name" class="mb-1">Class</label>
                <input type="text" disabled class="form-control @error('name') is-invalid @enderror" id="basic-default-name" placeholder="Class Name" name="name" value="{{ old('name',$schedule->classes->name) }}" />
                @error('name')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              <div class="col-sm-6">
                <label for="date" class="mb-1 required">Date</label>
                <input placeholder="yyyy/mm/dd" class="form-control date @error('date') is-invalid @enderror" id="date" name="date" value="{{ old('date',\Carbon\Carbon::parse($schedule->date)->format('Y/m/d')) }}" />
                @error('date')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
            </div>
            <div class="row mb-3">
              <div class="col-sm-6">
                <label for="start" class="mb-1 required">Start Time</label>
                <input type="time"  class="form-control @error('start') is-invalid @enderror" id="start" name="start" value="{{ old('start',$schedule->start) }}" />
                @error('start')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              <div class="col-sm-6">
                <label for="end" class="mb-1 required">End Time</label>
                <input type="time"  class="form-control @error('end') is-invalid @enderror" id="end" name="end" value="{{ old('end',$schedule->end) }}" />
                @error('end')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
            </div>

            <div class="row">
              <div class="mt-3 d-flex justify-content-end">
                <a href="" class="btn btn-secondary" style="margin-right: 15px;" onclick="$('#form_id').trigger('reset')">Reset</a>
                <button type="submit" class="btn btn-primary">Save</button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
@endsection
