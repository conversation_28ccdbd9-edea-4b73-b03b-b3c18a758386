@extends('layouts/contentNavbarLayout')

@section('title', 'Holidays')

@section('vendor-style')
@endsection
@section('page-style')
  <style>
    td,th{
      border: 1px solid #ccc;
    }
  </style>
@endsection

@section('vendor-script')
@endsection

@section('page-script')
  <script>
    function resetForm() {
      $('#session').val(null).trigger('change');
      $('#class').val(null).trigger('change');
      $("input[name='location']").val('');
    }
    $('#class').each(function () {
      $(this).select2({
        theme: 'bootstrap4',
        width: 'style',
        placeholder: $(this).attr('placeholder'),
        allowClear: Boolean($(this).data('allow-clear')),
      });
    });
  </script>
@endsection

@section('content')
  <div class="card" style="height: 100%">
    <div class="d-flex justify-content-sm-between align-items-center">
      <h5 class="card-header">Special Class</h5>
    </div>
    <div class="card-body">
      <form action="">
        <div class="row mb-3">
          <div class="col-sm-2">
            <label for="class" class="mb-1">Select Class</label>
            <select class="select2 form-select relationship-select @error('class') is-invalid @enderror" id="class" name="class" data-old-value="{{ old('class') }}">
              <option value="">Choose class</option>
              @foreach($classes as $class)
                <option value="{{$class->id}}" {{ request()->class == $class->id ? 'selected' : '' }}>
                  {{$class->name}}
                </option>
              @endforeach
              @error('class')
              <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </select>
          </div>
          <div class="col-sm-2">
            <label for="location" class="mb-1">Search By</label>
            <input type="text" class="form-control" name="name" value="{{request()->name}}" id="location" placeholder="Student Name or Student ID">
          </div>
          <div class="col-sm-2">
            <label for="session" class="mb-1">Session</label>
            <select class="form-select" id="session" name="session">
              <option value="">Choose Session</option>
              @for($i = 1; $i <= 16; $i++)
                <option value="{{$i}}" {{$lastSession == $i ? 'selected' : ''}}>{{$i}}</option>
              @endfor
            </select>
          </div>
          <div class="col" style="margin-top: 25px;">
            <button type="submit" class="btn btn-primary">Search</button>
            <a href="{{route('admin.attendance.special')}}" onclick="resetForm()" class="btn btn-secondary">Reset</a>
          </div>
        </div>
      </form>
      <div class="text-nowrap">
        <table class="table table-hover">
          <thead>
          <tr>
            <th rowspan="2" class="text-center" style="vertical-align: middle">Name Student</th>
            @foreach($daysOfWeek as $day)
              <th>{{$day}}</th>
            @endforeach
          </tr>
          <tr>
            @foreach(['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'] as $day)
              <th>{{ucfirst($day)}}</th>
            @endforeach
          </tr>
          </thead>
          <tbody class="table-border">
          @forelse($data as $student)
            <tr>
              <td>{{$student['student']->name_english}}</td>
              @foreach($daysOfWeek as $day)
                @php
                  $attendedStatus = '';
                  foreach($student['schedules'] as $schedule) {
                      if(date('m/d', strtotime($schedule['schedule']->date)) == $day && $schedule['schedule']->date > $student['created_at'] && $schedule['schedule']->date < date('Y-m-d') ){
                            $attendedStatus = 'NA';
                            break;
                      }
                      if(date('m/d', strtotime($schedule['schedule']->date)) == $day) {
                          $attendedStatus = $schedule['attended']['status'] ?? '';
                          break;
                      }
                  }
                @endphp
                <td>{{ $attendedStatus }}</td>
              @endforeach

            </tr>
          @empty
            <tr>
              <td colspan="{{ count($daysOfWeek) + 2 }}" class="text-center">No data available</td>
            </tr>
          @endforelse

          </tbody>
        </table>
        <div class="demo-inline-spacing d-flex justify-content-center align-items-center" style="padding:  0 20px;">
                    @include('layouts.sections.pagination', ['paginator' => $data])
        </div>
      </div>
    </div>


  </div>
  @include('layouts.sections.modal-delete-confirm', ['route' => route('admin.schedules.destroy','user_id'),'title' =>'schedule'])
@endsection
