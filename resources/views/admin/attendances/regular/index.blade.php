@extends('layouts/contentNavbarLayout')

@section('title', 'Attendance')

@section('vendor-style')
@endsection
@section('page-style')
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css">
  <style>
  td,th{
    border: 1px solid #ccc;
  }
</style>
@endsection

@section('vendor-script')
@endsection

@section('page-script')
  <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
  <script>
    function resetForm() {
      $('#session').val(null).trigger('change');
      $('#class').val(null).trigger('change');
      $('#term').val(null).trigger('change');
      $("input[name='name']").val('');
    }
    $('#class').each(function () {
      $(this).select2({
        theme: 'bootstrap4',
        width: 'style',
        placeholder: $(this).attr('placeholder'),
        allowClear: Boolean($(this).data('allow-clear')),
      });
    });
    function attendance(id,data,student_id){
      $.ajax({
        url: window.location.origin + '/admin/ajax-post-attendance',
        method: 'POST',
        data: {
          schedule_id: id,
          status : data,
          user_id: student_id,
          "_token": "{{ csrf_token() }}",
        },
        success: function(response) {
          if(response.status){
            toastr.success('Successful Attendance');
          }
        },
        error: function(xhr, status, error) {
          console.error(error);
        }
      });
    }
    function classChange(id){
      $('#session').val(1);
      $.ajax({
        type: 'GET',
        url: window.location.origin + '/admin/ajax-get-session?id=' + id,
        success: function(data) {
          $('#session').val(data.session ? data.session : 1);
        },
        error: function(error) {
          console.error('Error:', error);
        }
      });
    }
  </script>
@endsection

@section('content')
  <div class="card" style="height: 100%">
    <div class="d-flex justify-content-sm-between align-items-center">
      <h5 class="card-header">Regular Class</h5>
    </div>
    <div id="toast-container" class="toast-top-right"></div>
    <div class="card-body">
      <form action="">
        <div class="row mb-3">
          <div class="col-sm-2">
            <label for="class" class="mb-1">Select Class</label>
            <select class="select2 form-select relationship-select @error('class') is-invalid @enderror" id="class" name="class" onchange="classChange(this.value)" data-old-value="{{ old('class') }}">
              <option value="">Choose class</option>
              @foreach($classes as $class)
                <option session="{{$class->session}}" value="{{$class->id}}" {{ request()->class == $class->id ? 'selected' : '' }}>
                  {{$class->name}}
                </option>
              @endforeach
              @error('class')
              <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </select>
          </div>
          <div class="col-sm-2">
            <label for="location" class="mb-1">Search By</label>
            <input type="text" maxlength="100" class="form-control" name="name" value="{{request()->name}}" id="location" placeholder="Student Name">
          </div>
          <div class="col-sm-2">
            <label for="session" class="mb-1">Session</label>
            <select class="form-select" id="session" name="session">
              <option value="">Choose Session</option>
              @for($i = 1; $i <= 16; $i++)
                <option value="{{$i}}" {{$lastSession == $i ? 'selected' : ''}}>{{$i}}</option>
              @endfor
            </select>
          </div>
          <div class="col" style="margin-top: 25px;">
            <button type="submit" class="btn btn-primary">Search</button>
            <a href="{{route('admin.attendance.regular')}}" onclick="resetForm()" class="btn btn-secondary">Reset</a>
          </div>
        </div>
      </form>

      <div class="text-nowrap">
        @if($cl?->terms->break_start)
          <p>Break Session: {{\Carbon\Carbon::parse($cl->terms->break_start)->format('Y/m/d')}} - {{\Carbon\Carbon::parse($cl->terms->break_end)->format('Y/m/d')}}</p>
        @endif
        @if($cl?->terms->break_option_start)
            <p>Break Session (Optional): {{\Carbon\Carbon::parse($cl->terms->break_option_start)->format('Y/m/d')}} - {{\Carbon\Carbon::parse($cl->terms->break_option_end)->format('Y/m/d')}}</p>
        @endif
        <table class="table table-hover">
          <thead>
          <tr>
            <th rowspan="2" class="text-center" style="vertical-align: middle">Name Student</th>
            <th rowspan="2" class="text-center" style="vertical-align: middle">Year</th>
            <th rowspan="2" class="text-center" style="vertical-align: middle">Term</th>
            @foreach($daysOfWeek as $day)
              <th>{{$day}}</th>
            @endforeach
          </tr>
          <tr>
            @foreach(['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'] as $day)
              <th>{{ucfirst($day)}}</th>
            @endforeach
          </tr>
          </thead>
          <tbody class="table-border">
          @forelse($data as $student)
            <tr>
              <td>{{$student['student']->name_english}}</td>
              <td class="text-center">{{$term['year']}}</td>
              <td class="text-center">{{$term['term']}}</td>
              @foreach($daysOfWeek as $day)
                @php
                  $select = false;
                  $id= 0;
                  $status = null;
                  foreach($student['schedules'] as $schedule) {
                      if(date('m/d', strtotime($schedule['schedule']->date)) == $day) {
                          $id = $schedule['schedule']->id;
                          $status = !empty($schedule['attended']['status']) ?  $schedule['attended']['status'] : null;
                          $select = true;
                          break;
                      }
                  }
                @endphp
                <td>
                  @if($select)
                    <select class="form-select" onchange="attendance({{$id}},this.value,{{$student['student']->id}})">
                      <option value="">Not attended</option>
                      <option value="O" {{$status == 'O' ? 'selected':''}}>O</option>
                      <option value="X" {{$status == 'X' ? 'selected':''}}>X</option>
                      <option value="Late" {{$status == 'Late' ? 'selected':''}}>Late</option>
                      <option value="N/A" {{$status == 'N/A' ? 'selected':''}}>N/A</option>
                    </select>
                  @endif
                </td>
              @endforeach
            </tr>
          @empty
            <tr>
              <td colspan="{{ count($daysOfWeek) + 3 }}" class="text-center">Please Select Class</td>
            </tr>
          @endforelse

          </tbody>
        </table>
        <div class="demo-inline-spacing d-flex justify-content-center align-items-center" style="padding:  0 20px;">
          @include('layouts.sections.pagination', ['paginator' => $data])
        </div>
      </div>
    </div>


  </div>
  @include('layouts.sections.modal-delete-confirm', ['route' => route('admin.schedules.destroy','user_id'),'title' =>'schedule'])
@endsection
