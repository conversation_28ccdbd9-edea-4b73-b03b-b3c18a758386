@extends('layouts/contentNavbarLayout')

@section('title', 'Create Attendance')

@section('page-style')

@endsection

@section('vendor-script')
@endsection

@section('page-script')
  <script>
    $(document).ready(function(){

    });
  </script>
@endsection

@section('content')
  <div class="card">
    <div class="col-xxl">
      <div class="mb-4">
        <div class="card-header d-flex align-items-center justify-content-between">
          <h5 class="mb-0">Create Attendance</h5> <a href="{{url()->previous()}}" class="btn btn-primary float-end">Back</a>
        </div>
        <div class="card-body">
          <form action="{{route('admin.attendance.storePrivate')}}" method="post" id="form_id">
            @csrf
            <div class="row mb-3">
              <div class="col-sm-6">
                <label for="basic-default-name" class="mb-1">Student Name</label>
                <input type="text" class="form-control @error('name') is-invalid @enderror" id="basic-default-name" placeholder="Class Name" name="name" value="{{$student->student->name_english}}" disabled />
                @error('name')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              <div class="col-sm-6">
                <label for="type" class="mb-1 required">Status</label>
                <select class="form-select @error('type') is-invalid @enderror" id="type" name="type">
                  <option value="">Select Status</option>
                  <option value="O" {{ old('type') == 'O' ? 'selected' : '' }}>O</option>
                  <option value="X" {{ old('type') == 'X' ? 'selected' : '' }}>X</option>
                  <option value="Late" {{ old('type') == 'Late' ? 'selected' : '' }}>Late</option>
                  <option value="NA" {{ old('type') == 'NA' ? 'selected' : '' }}>NA</option>
                </select>
                @error('type')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              <input type="hidden" value="{{$student->student->id}}" name="user_id">
              <input type="hidden" value="{{request()->class}}" name="class_id">
            </div>

            <div class="row mb-3">
              <div class="col-sm-6">
                <label for="date" class="mb-1 required">Date</label>
                <input type="date" class="form-control @error('date') is-invalid @enderror" name="date" id="date" value="{{old('date')}}">
                @error('date')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
            </div>

            <div class="row">
              <div class="mt-3 d-flex justify-content-end">
                <span class="btn btn-secondary" style="margin-right: 15px;" onclick="$('#form_id').trigger('reset'); ">Reset</span>
                <button type="submit" class="btn btn-primary">Save</button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
@endsection
