@extends('layouts/contentNavbarLayout')

@section('title', 'Kakao Management')

@section('page-style')
    <style>
        #friends-table th, #friends-table td {
            text-align: center;
            vertical-align: middle;
        }
        #friends-table img.avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
        }
        #friends-table {
            margin-top: 20px;
        }
    </style>
@endsection

@section('vendor-script')
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
@endsection

@section('page-script')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
        const loginBtn = document.getElementById('kakao-login-btn');

        // 🔹 check token
        axios.get("{{ url('/kakao/check-token') }}")
            .then(res => {
                if (res.data.valid) {
                    loginBtn.style.display = 'none';   // hide login if token valid
                } else {
                    loginBtn.style.display = 'inline-block'; // else show login
                }
            })
            .catch(err => {
                console.error(err);
                loginBtn.style.display = 'inline-block'; // fallback -> show login
            });

            const friendsTableBody = document.querySelector('#friends-table tbody');

            document.getElementById('kakao-login-btn').addEventListener('click', function() {
                const width = 500;
                const height = 600;
                const left = (screen.width / 2) - (width / 2);
                const top = (screen.height / 2) - (height / 2);

                const popup = window.open(
                    "{{ url('/kakao/login') }}",
                    'kakaoLogin',
                    `width=${width},height=${height},top=${top},left=${left}`
                );

                const timer = setInterval(() => {
                    if (popup.closed) {
                        clearInterval(timer);
                        const loginBtn = document.getElementById('kakao-login-btn');
                        if (loginBtn) {
                            loginBtn.style.display = 'none';
                        }
                        document.getElementById('get-friends-btn').click();
                    }
                }, 1000);
            });

            document.getElementById('get-friends-btn').addEventListener('click', function() {
                const offset = document.getElementById('offset').value || 0;
                const limit = document.getElementById('limit').value || 50;
                axios.get(`{{ url('/kakao/friends') }}?offset=${offset}&limit=${limit}`)
                    .then(res => {
                        friendsTableBody.innerHTML = '';

                        if(res.data.error){
                            alert(res.data.error);
                            return;
                        }

                        const friends = res.data.friends || [];
                        friends.forEach((friend, index) => {
                            const tr = document.createElement('tr');
                            const avatar = friend.profile_thumbnail_image
                                ? `<img class="avatar" src="${friend.profile_thumbnail_image}" alt="Avatar">`
                                : '-';
                            tr.innerHTML = `
                                <td>${friend.id}</td>
                                <td>${avatar}</td>
                                <td>${friend.profile_nickname || '-'}</td>
                                <td>${friend.uuid || '-'}</td>
                            `;
                            friendsTableBody.appendChild(tr);
                        });
                    })
                    .catch(err => {
                        alert(err.response?.data?.error || 'Error fetching friends');
                    });
            });
        });
    </script>
@endsection

@section('content')
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">Kakao Management</h5>
    </div>
    <div class="card-body">
        <div class="d-flex align-items-center mb-3 gap-2 flex-wrap">
            <button id="kakao-login-btn" class="btn btn-warning">Login with Kakao</button>

            <div class="input-group input-group-sm" style="width: 120px;">
                <span class="input-group-text">From</span>
                <input type="number" id="offset" class="form-control" placeholder="0" min="0">
            </div>

            <div class="input-group input-group-sm" style="width: 120px;">
                <span class="input-group-text">Limit</span>
                <input type="number" id="limit" class="form-control" placeholder="50" min="1">
            </div>

            <button id="get-friends-btn" class="btn btn-success">Get Friends</button>
        </div>


        <table class="table table-striped table-bordered" id="friends-table">
            <thead>
                <tr>
                    <th>ID Friend</th>
                    <th>Avatar</th>
                    <th>Nickname</th>
                    <th>UUID</th>
                </tr>
            </thead>
            <tbody>
                <!-- List friends -->
            </tbody>
        </table>
    </div>
</div>
@endsection
