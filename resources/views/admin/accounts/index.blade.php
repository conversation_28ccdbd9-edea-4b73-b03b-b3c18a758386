@extends('layouts/contentNavbarLayout')

@section('title', 'Account')

@section('vendor-style')
@endsection
@section('page-style')
  <style>
    th{
      padding: 0.625rem 1rem !important;
    }
  </style>
@endsection
@section('vendor-script')
@endsection

@section('page-script')
  <script>
    function resetForm() {
      $("input[name='name']").val('');
      $("input[name='date']").val('');
      document.getElementById("status").selectedIndex = 0;
    }
  </script>
@endsection

@section('content')
  <div class="card" style="height: 100%">
    <div class="d-flex justify-content-sm-between align-items-center">
      <h5 class="card-header">Account Management</h5>
      <a style="margin-right: 20px;" class="btn btn-primary" href="{{route('admin.accounts.create')}}">Create New</a>
    </div>
    <form id="myform">
      <div class="card-body d-flex align-items-center">
        <div class="mb-3" style="padding:  0 10px;">
          <label for="keyword" class="mb-1">Search By</label>
          <input name="name" class="form-control" placeholder="Name" value="{{request()->input('name')}}">
        </div>
        <div>
          <label style="margin-left: 15px;">Date</label>
          <div class="mb-3 d-flex justify-content-center align-items-center" style="padding:  0 10px;">
            <input name="date" class="form-control date" value="{{request()->input('date')}}" placeholder="Please Select" autocomplete="off">
            <i style="margin-left: -28px" class='bx bxs-calendar'></i>
          </div>
        </div>
        <div class="mb-3" style="padding: 0 10px">
          <label for="status" class="mb-1">Status</label>
          <select class="form-select" id="status" name="status">
            <option value="">All</option>
            <option value="active" {{ request()->status == 'active' ? 'selected' : '' }}>Active</option>
            <option value="inactive" {{ request()->status == 'inactive' ? 'selected' : '' }}>Inactive</option>
          </select>
        </div>
        <div style="margin-top: 10px;" style="padding: 0 10px">
          <button type="submit" class="btn btn-primary">Search</button>
          <a href="{{route('admin.accounts.index')}}" class="cursor-pointer btn btn-secondary" onclick="resetForm()">Reset</a>
        </div>
      </div>
    </form>
    <div class="text-nowrap">
      <table class="table table-hover">
        <thead>
        <tr>
          <th style="text-align: center">Action</th>
          <th onclick="sort('name_english','{{request()->name_english == 'desc' ? 'asc' : 'desc'}}')" class="cursor-pointer">Parent Name
            @include('layouts.sections.sort', ['field' => 'name_english'])
          </th>
          <th onclick="sort('email','{{request()->email == 'desc' ? 'asc' : 'desc'}}')" class="cursor-pointer">Email Parent
            @include('layouts.sections.sort', ['field' => 'email'])
          </th>
          <th class="cursor-pointer">UUID Kakao
          </th>
          <th onclick="sort('student_id','{{request()->student_id == 'desc' ? 'asc' : 'desc'}}')" class="cursor-pointer">Account ID (Student)
            @include('layouts.sections.sort', ['field' => 'student_id'])
          </th>
          <th onclick="sort('created_at','{{request()->created_at == 'desc' ? 'asc' : 'desc'}}')" class="cursor-pointer">Date of Creation
            @include('layouts.sections.sort', ['field' => 'created_at'])
          </th>
          <th onclick="sort('status_sort','{{request()->status_sort == 'desc' ? 'asc' : 'desc'}}')" class="cursor-pointer">Status
            @include('layouts.sections.sort', ['field' => 'status_sort'])
          </th>
        </tr>
        </thead>
        <tbody class="table-border-bottom-0">
        @forelse($parents as $parent)
          <tr>
            <td style="text-align: center">
              <a style="border: none;
    color: #555555;" href="{{route('admin.accounts.show',$parent->id)}}"><i class='bx bxs-show'></i></a>
              <a style="border: none;
    color: #555555;" href="{{route('admin.accounts.edit',$parent->id)}}"><i class="bx bx-edit-alt"></i></a>
              <button type="button" style="border: none; padding-left: 0px;background: none;padding-right: 0px;
    color: #555555;" data-bs-toggle="modal" onclick="showConfirmationModal('{{ $parent->ids }}')" data-bs-target="#confirmDeleteModal">
                <i class="bx bx-trash"></i>
              </button>
            </td>
            <td><p style="margin-bottom: 0;text-overflow: ellipsis;width: 250px;overflow: hidden;">{{$parent->name_english}}</p></td>
            <td><p style="margin-bottom: 0;text-overflow: ellipsis;width: 250px;overflow: hidden;">{{$parent->email}}</p></td>
            <td><p style="margin-bottom: 0;text-overflow: ellipsis;width: 150px;overflow: hidden;">{{$parent->uuid_kakao}}</p></td>
            <td>{{$parent->student_id}}</td>
            <td>{{\Carbon\Carbon::parse($parent->student_created_at)->format('Y-m-d')}}</td>
            <td>{{ucfirst($parent->status)}}</td>
          </tr>
        @empty
          <tr>
            <td colspan="9" class="text-center">No data available</td>
          </tr>
        @endforelse
        </tbody>
      </table>
      <div class="demo-inline-spacing d-flex justify-content-center align-items-center" style="padding:  0 20px;">
        @include('layouts.sections.pagination', ['paginator' => $parents])
      </div>
    </div>
  </div>
  @include('layouts.sections.modal-delete-confirm', ['route' => route('admin.accounts.destroy','user_id'),'title' =>'account'])
@endsection
