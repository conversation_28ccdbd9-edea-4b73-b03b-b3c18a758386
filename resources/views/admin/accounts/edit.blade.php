@extends('layouts/contentNavbarLayout')

@section('title', 'Create Account')

@section('page-style')

@endsection

@section('vendor-script')
@endsection

@section('page-script')
  <script>
    $(document).ready(function () {
      $('#parent').each(function () {
        $(this).select2({
          theme: 'bootstrap4',
          width: 'style',
          placeholder: $(this).attr('placeholder'),
          allowClear: <PERSON><PERSON><PERSON>($(this).data('allow-clear')),
        });
      });

    });

    function convertBirthday(birthday) {
      var parts = birthday.split('-');
      return parts[0].substr(2) + parts[1] + parts[2];
    }

    document.getElementById('generate').addEventListener('click', function () {
      var passwordField = document.getElementById('password_parent');
      passwordField.value = generatePassword();
    });

    function generatePassword() {
      var length = 8;
      var charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
      var password = "";
      for (var i = 0; i < length; i++) {
        var charIndex = Math.floor(Math.random() * charset.length);
        password += charset.charAt(charIndex);
      }
      return password;
    }

  </script>
@endsection

@section('content')
  <div class="card">
    <div class="col-xxl">
      <div class="mb-4">
        <div class="card-header d-flex align-items-center justify-content-between">
          <h5 class="mb-0">Edit Account</h5> <a href="{{url()->previous()}}" class="btn btn-primary float-end">Back</a>
        </div>
        <div class="card-body">
          <form action="{{route('admin.accounts.update',$parent->id)}}" method="post" id="form_id">
            @method('PUT')
            @csrf
            <div class="row mb-3">
              <div class="col-sm-6">
                <label for="parent" class="mb-1">Select Parent</label>
                <input disabled value="{{$parent->name_english}} {{$parent->phone}}" class="form-control">
                <input type="hidden" value="{{$parent->id}}" name="parent" class="form-control">
              </div>
              <div class="col-sm-6">
                <label for="status" class="mb-1 required">Status</label>
                <select class="form-select @error('status') is-invalid @enderror" id="status" name="status">
                  <option value="inactive" {{ old('status',$parent->status) === 'inactive' ? 'selected' : '' }}>Inactive</option>
                  <option value="active" {{ old('status',$parent->status) === 'active' ? 'selected' : '' }}>Active</option>
                </select>
                @error('status')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
            </div>
            <h5>Account Information</h5>
            <div class="row mb-3">
              <div class="col-sm-6">
                <label for="user_name_parent" class="mb-1">Account ID (Parent)</label>
                <input style="background-color: #eceef1;opacity: 1;" type="text" readonly class="form-control @error('user_name_parent') is-invalid @enderror" value="{{old('user_name_parent',$parent->user_name ?  : $parent->phone)}}" name="user_name_parent" id="user_name_parent" placeholder="Phone Number">
                @error('user_name_parent')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              <div class="col-sm-3">
                <label for="password_parent" class="mb-1">Password</label>
                <input type="text" style="background-color: #eceef1;opacity: 1;" readonly placeholder="{{$parent->password != null ? '*******'  : ''}}" class="form-control" name="password_parent" id="password_parent" autocomplete="off">
                  @if ($errors->has('password_parent'))
                    <div style="width: 100%; margin-top: 0.3rem; font-size: 85%; color: var(--bs-form-invalid-color);">
                      {{ $errors->first('password_parent') }}
                    </div>
                  @endif
              </div>
              <div class="col-sm-3">
                <p style="margin-top: 25px" id="generate" class="btn btn-dark">Generate Password</p>
              </div>
            </div>
            <div class="row mb-3" id="student">
              @foreach($parent->student as $key => $student)
                <div class="col-sm-12 mb-3">
                  <label for="user_name_student" class="mb-1">Account ID (Student {{$key+1}})</label>
                  <div class="input-group">
                    <input type="text" readonly class="form-control" name="user_name_student_{{ $student->id }}"
                           id="user_name_student" placeholder="Phone Number"
                           value="{{ \Illuminate\Support\Str::slug($student->user_name, '') ?  : \Illuminate\Support\Str::slug($student->account_name, '') }}">
                  </div>
                </div>
              @endforeach
            </div>
            <div class="row mb-3" id="student">
                <div class="col-sm-12 mb-3">
                  <label for="uuid_kakao" class="mb-1">UUID Kakao</label>
                  <div class="input-group">
                    <input type="text" class="form-control" name="uuid_kakao"
                           id="uuid_kakao" placeholder="UUID Kakao"
                           value="{{ $parent->uuid_kakao }}">
                  </div>
                </div>
            </div>
            <div class="row">
              <div class="mt-3 d-flex justify-content-end">
                <button type="submit" class="btn btn-primary">Save</button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
@endsection
