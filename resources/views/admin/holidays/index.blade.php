@extends('layouts/contentNavbarLayout')

@section('title', 'Holidays')

@section('vendor-style')
@endsection

@section('vendor-script')
@endsection

@section('page-script')
@endsection

@section('content')
  <div class="card" style="height: 100%">
    <div class="d-flex justify-content-sm-between align-items-center">
      <h5 class="card-header">Holiday management</h5>
      <a style="margin-right: 20px;" class="btn btn-primary" href="{{route('admin.holidays.create')}}">Create Holiday</a>
    </div>
    <div class="text-nowrap" style="padding: 0 20px">
      <table class="table table-hover">
        <thead>
        <tr>
          <th onclick="sort('id','{{request()->id == 'desc' ? 'asc' : 'desc'}}')" class="cursor-pointer">No
            @include('layouts.sections.sort', ['field' => 'id'])
          </th>
          <th onclick="sort('name','{{request()->name == 'desc' ? 'asc' : 'desc'}}')" class="cursor-pointer">
            Holiday Name
            @include('layouts.sections.sort', ['field' => 'name'])
          </th>
          <th onclick="sort('type','{{request()->type == 'desc' ? 'asc' : 'desc'}}')" class="cursor-pointer" >
            Type of Holiday
            @include('layouts.sections.sort', ['field' => 'type'])
          </th>
          <th onclick="sort('start','{{request()->start == 'desc' ? 'asc' : 'desc'}}')" class="cursor-pointer">
            Start Date
            @include('layouts.sections.sort', ['field' => 'start'])
          </th>
          <th onclick="sort('end','{{request()->end == 'desc' ? 'asc' : 'desc'}}')" class="cursor-pointer">
            End Date
            @include('layouts.sections.sort', ['field' => 'end'])
          </th>
          <th>Action</th>
        </tr>
        </thead>
        <tbody class="table-border-bottom-0">
        @forelse($holidays as $holiday)
          <tr>
            <td>{{ request()->id == 'desc' ? $index-- : $index++ }}</td>
            <td>{{$holiday->name}}</td>
            <td>{{$holiday->type}}</td>
            <td>{{$holiday->start}}</td>
            <td>{{$holiday->end}}</td>
            <td>
              <div class="dropdown">
                <button type="button" class="btn p-0 dropdown-toggle hide-arrow" data-bs-toggle="dropdown"><i class="bx bx-dots-vertical-rounded"></i></button>
                <div class="dropdown-menu">
                  <a class="dropdown-item" href="{{route('admin.holidays.edit',$holiday->id)}}"><i class="bx bx-edit-alt me-1"></i> Edit</a>
                  <button type="button" class="dropdown-item" data-bs-toggle="modal" onclick="showConfirmationModal('{{ $holiday->id }}')" data-bs-target="#confirmDeleteModal">
                    <i class="bx bx-trash me-1"></i> Delete
                  </button>
                </div>
              </div>
            </td>
          </tr>
        @empty
          <tr>
            <td colspan="6" class="text-center">No data available</td>
          </tr>
        @endforelse
        </tbody>
      </table>
      <div class="demo-inline-spacing d-flex justify-content-center align-items-center" style="padding:  0 20px;">
        @include('layouts.sections.pagination', ['paginator' => $holidays])
      </div>
    </div>
  </div>
  @include('layouts.sections.modal-delete-confirm', ['route' => route('admin.holidays.destroy','user_id'),'title' =>'holiday'])
@endsection
