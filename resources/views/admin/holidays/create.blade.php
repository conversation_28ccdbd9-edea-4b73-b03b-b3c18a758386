@extends('layouts/contentNavbarLayout')

@section('title', 'Create Holiday')

@section('page-style')

@endsection

@section('vendor-script')
@endsection

@section('page-script')
  <script>
    $(document).ready(function(){

    });
  </script>
@endsection

@section('content')
  <div class="card">
    <div class="col-xxl">
      <div class="mb-4">
        <div class="card-header d-flex align-items-center justify-content-between">
          <h5 class="mb-0">Create Holiday</h5> <a href="{{url()->previous()}}" class="btn btn-primary float-end">Back</a>
        </div>
        <div class="card-body">
          <form action="{{route('admin.holidays.store')}}" method="post" id="form_id">
            @csrf
            <div class="row mb-3">
              <div class="col-sm-6">
                <label for="basic-default-name" class="mb-1 required">Holiday Name</label>
                <input type="text" class="form-control @error('name') is-invalid @enderror" id="basic-default-name" placeholder="Class Name" name="name" value="{{ old('name') }}" />
                @error('name')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              <div class="col-sm-6">
                <label for="type" class="mb-1 required">Type of Holiday</label>
                <select class="form-select @error('type') is-invalid @enderror" id="type" name="type">
                  <option value="">Select Holiday</option>
                  <option value="Academy Holiday" {{ old('type') == 'Academy Holiday' ? 'selected' : '' }}>Academy Holiday</option>
                  <option value="Exam Holiday" {{ old('type') == 'Exam Holiday' ? 'selected' : '' }}>Exam Holiday</option>
                  <option value="Public Holiday" {{ old('type') == 'Public Holiday' ? 'selected' : '' }}>Public Holiday</option>
                  <option value="Others" {{ old('type') == 'Others' ? 'selected' : '' }}>Others</option>
                </select>
                @error('type')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
            </div>

            <div class="row mb-3">
              <div class="col-sm-6">
                <label for="start" class="mb-1 required">Start Date</label>
                <input type="date" class="form-control @error('start') is-invalid @enderror" name="start" id="start" value="{{old('start')}}">
                @error('start')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              <div class="col-sm-6">
                <label for="end" class="mb-1 required">End Date</label>
                <input type="date" class="form-control @error('end') is-invalid @enderror" name="end" id="end" value="{{old('end')}}" >
                @error('end')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
            </div>

            <div class="row">
              <div class="mt-3 d-flex justify-content-end">
                <span class="btn btn-secondary" style="margin-right: 15px;" onclick="$('#form_id').trigger('reset'); ">Reset</span>
                <button type="submit" class="btn btn-primary">Save</button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
@endsection
