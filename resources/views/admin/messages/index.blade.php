@extends('layouts/contentNavbarLayout')

@section('title', 'Messages')

@section('vendor-style')
@endsection

@section('vendor-script')
@endsection

@section('page-script')
@endsection

@section('content')
  <div class="card" style="height: 100%">
    <div class="d-flex justify-content-sm-between align-items-center">
      <h5 class="card-header">Message Management</h5>
      <a style="margin-right: 20px;" class="btn btn-primary" href="{{route('admin.messages.create')}}">Create Message</a>
    </div>
    <div class="text-nowrap" style="padding: 0 20px">
      <form action="">
        <div class="row mb-3">
          <div class="col-sm-2">
            <label for="type" class="mb-1">Type of Message</label>
            <select class="select2 form-select relationship-select @error('class') is-invalid @enderror" id="type" name="type" data-old-value="{{ old('type') }}">
              <option value="">All</option>
              <option value="Academy" {{ request()->type == 'Academy' ? 'selected' : '' }}>Academy</option>
              <option value="Class" {{ request()->type == 'Class' ? 'selected' : '' }}>Class</option>
              <option value="Student" {{ request()->type == 'Student' ? 'selected' : '' }}>Student</option>
              <option value="Parent" {{ request()->type == 'Parent' ? 'selected' : '' }}>Parent</option>
              @error('type')
              <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </select>
          </div>
          <div class="col-sm-2">
            <label for="name" class="mb-1">Search By</label>
            <input type="text" class="form-control" name="name" value="{{request()->name}}" id="name" placeholder="Title" maxlength="100">
          </div>
          <div class="col-sm-2">
            <label for="keyword" class="mb-1">Search By</label>
            <input type="text" class="form-control" name="keyword" value="{{request()->keyword}}" id="keyword" placeholder="Content" maxlength="100">
          </div>
          <div class="col-sm-2">
            <label for="keyword" class="mb-1">Status</label>
            <select class="form-select" name="status">
              <option value="">All</option>
              <option value="active" {{ request()->status == 'active' ? 'selected' : '' }}>Active</option>
              <option value="inactive" {{ request()->status == 'inactive' ? 'selected' : '' }}>Inactive</option>
            </select>
          </div>
          <div class="col" style="margin-top: 25px;">
            <button class="btn btn-primary">Search</button>
            <a href="{{route('admin.messages.index')}}" class="btn btn-secondary">Reset</a>
          </div>
        </div>
      </form>
      <table class="table table-hover">
        <thead>
        <tr>
          <th>Action</th>
          <th onclick="sort('title','{{request()->title == 'desc' ? 'asc' : 'desc'}}')" class="cursor-pointer">
            Message Title
            @include('layouts.sections.sort', ['field' => 'title'])
          </th>
          <th onclick="sort('content','{{request()->content == 'desc' ? 'asc' : 'desc'}}')" class="cursor-pointer" >
            Message Content
            @include('layouts.sections.sort', ['field' => 'content'])
          </th>
          <th onclick="sort('created_at','{{request()->created_at == 'desc' ? 'asc' : 'desc'}}')" class="cursor-pointer">
            Date of Creation
            @include('layouts.sections.sort', ['field' => 'created_at'])
          </th>
          <th onclick="sort('status_sort','{{request()->status_sort == 'desc' ? 'asc' : 'desc'}}')" class="cursor-pointer">
            Status
            @include('layouts.sections.sort', ['field' => 'status_sort'])
          </th>
        </tr>
        </thead>
        <tbody class="table-border-bottom-0">
        @forelse($messages as $message)
          <tr>
            <td>
              <a href="{{route('admin.messages.show',$message->id)}}" style="border: none;
    color: #555555;"><i class='bx bxs-show'></i> </a>
              <a style="border: none;
    color: #555555;" href="{{route('admin.messages.edit',$message->id)}}"><i class="bx bx-edit-alt"></i></a>
              <button type="button" style="border: none; padding-left: 0px;background: none;padding-right: 0px;
    color: #555555;" data-bs-toggle="modal" onclick="showConfirmationModal('{{ $message->id }}')" data-bs-target="#confirmDeleteModal">
                <i class="bx bx-trash"></i>
              </button>
            </td>
            <td style="overflow: hidden;white-space: nowrap;text-overflow: ellipsis;max-width: 50px;">{{$message->name}}</td>
            <td style="overflow: hidden;white-space: nowrap;text-overflow: ellipsis;max-width: 50px;">{{strip_tags($message->content)}}</td>
            <td>{{$message->created_at}}</td>
            <td>{{ ucfirst($message->status) }}</td>
          </tr>
        @empty
          <tr>
            <td colspan="6" class="text-center">No data available</td>
          </tr>
        @endforelse
        </tbody>
      </table>
      <div class="demo-inline-spacing d-flex justify-content-center align-items-center" style="padding:  0 20px;">
        @include('layouts.sections.pagination', ['paginator' => $messages])
      </div>
    </div>
  </div>
  @include('layouts.sections.modal-delete-confirm', ['route' => route('admin.messages.destroy','user_id'),'title' =>'message'])
@endsection
