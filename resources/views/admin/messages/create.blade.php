@extends('layouts/contentNavbarLayout')

@section('title', 'Create Message')

@section('page-style')
<style>
  #layout-navbar{
    z-index: 10;
  }
</style>
@endsection

@section('vendor-script')
@endsection

@section('page-script')
  <script>
    $(document).ready(function() {
      $('#editor').summernote();
    });
    $('.select2').each(function () {
      $(this).select2({
        theme: 'bootstrap4',
        width: 'style',
        placeholder: $(this).attr('placeholder'),
        allowClear: <PERSON><PERSON>an($(this).data('allow-clear')),
      });
    });

    $('.select2').change(function() {
      $('.select2').not(this).each(function() {
        $(this).val(null).trigger('change.select2');
      });
    });

    var oldType = '{{ old("type") }}';
    if (oldType) {
      handleRadioChange(oldType);
    }

    $('input[type=radio][name=type]').change(function() {
      var value  = this.value;
      handleRadioChange(value);
    });

    function handleRadioChange(value) {
      $('#class, #parent, #student').addClass('d-none');
      if (value === 'student') {
        $('#student').removeClass('d-none');
      } else if (value === 'class') {
        $('#class').removeClass('d-none');
      } else if (value === 'parent') {
        $('#parent').removeClass('d-none');
      }
    }

  </script>
@endsection

@section('content')
  <div class="card">
    <div class="col-xxl">
      <div class="mb-4">
        <div class="card-header d-flex align-items-center justify-content-between">
          <h5 class="mb-0">Create Message</h5> <a href="{{route('admin.messages.index')}}" class="btn btn-primary float-end">Back</a>
        </div>
        <div class="card-body">
          <form action="{{route('admin.messages.store')}}" method="post" id="form_id">
            @csrf
            <div class="row mb-3">
              <div class="col">
                <label for="basic-default-name" class="mb-1 required">Tittle</label>
                <input maxlength="100" type="text" class="form-control @error('name') is-invalid @enderror" id="basic-default-name" placeholder="Title" name="name" value="{{ old('name') }}" />
                @error('name')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
            </div>
            <div class="row mb-3">
              <div class="col">
                <label class="mb-1 required">Message</label>
                <textarea placeholder="Type here" class="form-control" id="editor" name="content">{{old('content')}}</textarea>
                @error('content')
                <div style="width: 100%;margin-top: 0.3rem;font-size: 85%;color: var(--bs-form-invalid-color);">{{ $message }}</div>
                @enderror
              </div>
            </div>
            <div class="row mb-3">
              <div class="col-sm-2">
                <label class="mb-1">Status</label>
                <select class="form-select" name="status">
                  <option value="inactive" {{ old('status') == 'inactive' ? 'selected' : '' }}>Inactive</option>
                  <option value="active" {{ old('status') == 'active' ? 'selected' : '' }}>Active</option>
                </select>
                @error('status')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
            </div>
            <div class="mt-1 d-flex justify-content-around">
              <div class="form-check">
                <input class="form-check-input" type="radio" value="academy" name="type" checked {{ old('type') == 'academy' ? 'checked' : '' }}>
                <label class="form-check-label" for="academy">
                  Academy
                </label>
              </div>
              <div class="form-check">
                <input class="form-check-input" type="radio" value="class" name="type" {{ old('type') == 'class' ? 'checked' : '' }}>
                <label class="form-check-label" for="class">
                  Class
                </label>
              </div>
              <div class="form-check">
                <input class="form-check-input" type="radio" value="student" name="type" {{ old('type') == 'student' ? 'checked' : '' }}>
                <label class="form-check-label" for="student">
                  Student
                </label>
              </div>
              <div class="form-check">
                <input class="form-check-input" type="radio" value="parent" name="type" {{ old('type') == 'parent' ? 'checked' : '' }}>
                <label class="form-check-label" for="parent">
                  Parent
                </label>
              </div>
            </div>
            @error('type')
              <div style="width: 100%;margin-top: 0.3rem;font-size: 85%;color: var(--bs-form-invalid-color);">{{ $message }}</div>
            @enderror
            <div class="col d-none" id="class">
              <label for="classes" class="mb-1 required">Class</label>
              <select class="select2 @error('classes') is-invalid @enderror" id="classes" multiple name="classes[]">
                @foreach($classes as $class)
                  <option value="{{ $class->id }}" @if(in_array($class->id, (array) old('classes', []))) selected @endif>{{ ucfirst($class->name) }}</option>
                @endforeach
              </select>
              @error('classes')
              <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>
            <div class="col d-none" id="student">
              <label for="class" class="mb-1 required">Student</label>
              <select class="select2 @error('students') is-invalid @enderror"  multiple name="students[]">
                @foreach($students as $student)
                  <option value="{{ $student->id }}" @if(in_array($student->id, (array) old('students', []))) selected @endif>{{ ucfirst($student->name_english) }} - {{$student->birthday}}</option>
                @endforeach
              </select>
              @error('students')
              <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>
            <div class="col d-none" id="parent">
              <label for="class" class="mb-1 required">Parent</label>
              <select class="select2 @error('parents') is-invalid @enderror"  multiple name="parents[]">
                @foreach($parents as $parent)
                  <option value="{{ $parent->id }}" @if(in_array($parent->id, (array) old('parents', []))) selected @endif> {{ $parent->name_english }} - {{$parent->user_name}}</option>
                @endforeach
              </select>
              @error('parents')
              <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>
            <div class="row">
              <div class="mt-3 d-flex justify-content-end">
                <span class="btn btn-secondary" style="margin-right: 15px;" onclick="window.location.reload(); ">Reset</span>
                <button type="submit" class="btn btn-primary" onclick="this.form.submit();this.disabled=true;">Save</button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
@endsection
