@extends('layouts/contentNavbarLayout')

@section('title', 'Grade Form')

@section('page-style')
  <style>
    input[type="text"] {
      width: 80px;
      text-align: center;
    }

    .tab-grade {
      background: #b5b5b5;
      color: white !important;
      font-weight: bold;
      cursor: pointer;
    }

    .tab-active {
      background: white;
      color: black !important;
      border-bottom: 0px !important;
    }
  </style>

@endsection

@section('vendor-script')
@endsection

@section('page-script')
  <script>
    $(function () {
      $("#tabs").tabs();
      var type = '{{$title}}'
      if(type == 'Show'){
        $('input').attr('disabled', true);
        $('select').prop('disabled', true);
      }
    });

    function restrictInput(event) {
      const input = event.target;
      const value = input.value.replace(/[^\d.]/g, "");
      if (parseInt(value) >= 1000) {
        input.value = "999";
      } else {
        input.value = value;
      }
    }

    $("input[name='skill']").on('change', function () {
      if (this.value == 'FM') {
        $("#skill1").val('');
        $("#skill2").val('');
      }
    });
    const skill2 = document.getElementById("skill2");
    const skill1 = document.getElementById("skill1");
    skill2.addEventListener("input", restrictInput);
    skill1.addEventListener("input", restrictInput);
  </script>
@endsection

@section('content')
  <div class="card">
    <div class="col-xxl">
      <div class="mb-4">
        <div class="card-header d-flex align-items-center justify-content-between">
          <h5 class="mb-0">{{$title}} Grades</h5> <a
            href="{{route('admin.grades.index',['class'=>$id,'student'=>$student,'session' => $session])}}"
            class="btn btn-primary float-end">Back</a>
        </div>
        <div class="card-body">
          <div class="row mb-5">
            <a
              href="{{ route('admin.grades.'.($title == 'Edit' ? 'edit':'show'), ['grade' => $id, 'student_id' => $student, 'session' => $session,'type'=>'HW1']) }}"
              class="col-sm-4 text-center border p-2 tab-grade {{$type == 'HW1' ? 'tab-active':''}}" data-id="hw1">
              <span class="text-center" id="myLink">HW1</span>
            </a>
            <a
              href="{{ route('admin.grades.'.($title == 'Edit' ? 'edit':'show'), ['grade' => $id, 'student_id' => $student, 'session' => $session,'type'=>'HW2']) }}"
              class="col-sm-4 text-center border p-2 tab-grade {{$type == 'HW2' ? 'tab-active':''}}" data-id="hw2">
              <span class="text-center">HW2</span>
            </a>
            <a
              href="{{ route('admin.grades.'.($title == 'Edit' ? 'edit':'show'), ['grade' => $id, 'student_id' => $student, 'session' => $session,'type'=>'HW3']) }}"
              class="col-sm-4 text-center border p-2 tab-grade {{$type == 'HW3' ? 'tab-active':''}}" data-id="hw3">
              <span class="text-center">HW3</span>
            </a>
          </div>
          <form action="{{route('admin.grades.update',$id)}}" method="post" id="form_id">
            @method('PUT')
            @csrf
            <div class="row mb-3">
              <div class="col-sm-2">
                <label for="basic-default-name" class="mb-1">Writing</label>
                <input type="radio" style="background: blue" class="form-check-input" id="basic-default-name"
                       name="subject" checked disabled>
                @error('name')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              <div class="col-sm-2">
                <label for="basic-default-name" class="mb-1">Reading</label>
                <input type="radio" class="form-check-input" name="subject" disabled>
                @error('name')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
            </div>
            <div class="row mb-3">
              <div class="col-sm-6">
                <label for="attendance" class="mb-1 required">Student Attendance</label>
                <select class="form-select @error('attendance') is-invalid @enderror" id="attendance"
                        name="attendance" {{$title == 'Show' ? 'disabled' : ''}}>
                  <option
                    value="N/A" {{ old('attendance',optional($pointStudent)->attendance) == 'N/A' ? 'selected' : '' }}>N/A
                  </option>
                  <option
                    value="O" {{ old('attendance',optional($pointStudent)->attendance) == 'O' ? 'selected' : '' }}>O
                  </option>
                  <option
                    value="X" {{ old('attendance',optional($pointStudent)->attendance) == 'X' ? 'selected' : '' }}>X
                  </option>
                  <option
                    value="Late" {{ old('attendance',optional($pointStudent)->attendance) == 'Late' ? 'selected' : '' }}>
                    Late
                  </option>

                </select>
                @error('attendance')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              <div class="col-sm-6">
                <label for="participation" class="mb-1 required">Student Participation</label>
                <select class="form-select @error('participation') is-invalid @enderror" id="participation"
                        name="participation" {{$title == 'Show' ? 'disabled' : ''}}>
                  <option
                    value="N/A" {{ old('participation',optional($pointStudent)->participation) == 'N/A' ? 'selected' : '' }}>
                    N/A
                  </option>
                  <option
                    value="A+" {{ old('participation',optional($pointStudent)->participation) == 'A+' ? 'selected' : '' }}>
                    A+
                  </option>
                  <option
                    value="A" {{ old('participation',optional($pointStudent)->participation) == 'A' ? 'selected' : '' }}>
                    A
                  </option>
                  <option
                    value="A-" {{ old('participation',optional($pointStudent)->participation) == 'A-' ? 'selected' : '' }}>
                    A-
                  </option>
                  <option
                    value="B+" {{ old('participation',optional($pointStudent)->participation) == 'B+' ? 'selected' : '' }}>
                    B+
                  </option>
                  <option
                    value="B" {{ old('participation',optional($pointStudent)->participation) == 'B' ? 'selected' : '' }}>
                    B
                  </option>
                  <option
                    value="B-" {{ old('participation',optional($pointStudent)->participation) == 'B-' ? 'selected' : '' }}>
                    B-
                  </option>
                  <option
                    value="C+" {{ old('participation',optional($pointStudent)->participation) == 'C+' ? 'selected' : '' }}>
                    C+
                  </option>
                  <option
                    value="C" {{ old('participation',optional($pointStudent)->participation) == 'C' ? 'selected' : '' }}>
                    C
                  </option>
                  <option
                    value="Fp" {{ old('participation',optional($pointStudent)->participation) == 'Fp' ? 'selected' : '' }}>
                    Fp
                  </option>

                </select>
                @error('participation')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
            </div>

            <div class="row mb-3">
              @if($level != 'QW4')
                <div class="col-sm-6">
                  <label for="transcription" class="mb-1 required">Transcription</label>
                  <select class="form-select @error('transcription') is-invalid @enderror" id="transcription"
                          name="transcription" {{$title == 'Show' ? 'disabled' : ''}}>
                    <option
                      value="N/A" {{ old('transcription',optional($pointStudent)->transcription) == 'N/A' ? 'selected' : '' }}>
                      N/A
                    </option>
                    <option
                      value="Good effort" {{ old('transcription',optional($pointStudent)->transcription) == 'Good effort' ? 'selected' : '' }}>
                      Good effort
                    </option>
                    <option
                      value="Incomplete" {{ old('transcription',optional($pointStudent)->transcription) == 'Incomplete' ? 'selected' : '' }}>
                      Incomplete
                    </option>
                    <option
                      value="Fm" {{ old('transcription',optional($pointStudent)->transcription) == 'Fm' ? 'selected' : '' }}>
                      Fm
                    </option>
                  </select>
                  @error('transcription')
                  <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>
              @endif
              <div class="col-sm-6">
                @if(in_array($level,['QW2','QW3','QW4']) && $type == 'HW1')
                  <label for="prewriting" class="mb-1 required">Prewriting</label>
                  <select class="form-select @error('prewriting') is-invalid @enderror" id="prewriting"
                          name="prewriting" {{$title == 'Show' ? 'disabled' : ''}}>
                    <option
                      value="N/A" {{ old('prewriting',optional($pointStudent)->prewriting) == 'N/A' ? 'selected' : '' }}>
                      N/A
                    </option>
                    <option
                      value="A+" {{ old('prewriting',optional($pointStudent)->prewriting) == 'A+' ? 'selected' : '' }}>
                      A+
                    </option>
                    <option
                      value="A" {{ old('prewriting',optional($pointStudent)->prewriting) == 'A' ? 'selected' : '' }}>A
                    </option>
                    <option
                      value="A-" {{ old('prewriting',optional($pointStudent)->prewriting) == 'A-' ? 'selected' : '' }}>
                      A-
                    </option>
                    <option
                      value="B+" {{ old('prewriting',optional($pointStudent)->prewriting) == 'B+' ? 'selected' : '' }}>
                      B+
                    </option>
                    <option
                      value="B" {{ old('prewriting',optional($pointStudent)->prewriting) == 'B' ? 'selected' : '' }}>B
                    </option>
                    <option
                      value="B-" {{ old('prewriting',optional($pointStudent)->prewriting) == 'B-' ? 'selected' : '' }}>
                      B-
                    </option>
                    <option
                      value="C+" {{ old('prewriting',optional($pointStudent)->prewriting) == 'C+' ? 'selected' : '' }}>
                      C+
                    </option>
                    <option
                      value="C" {{ old('prewriting',optional($pointStudent)->prewriting) == 'C' ? 'selected' : '' }}>C
                    </option>
                    <option
                      value="Fp" {{ old('prewriting',optional($pointStudent)->prewriting) == 'Fp' ? 'selected' : '' }}>
                      Fp
                    </option>
                    <option
                      value="Fm" {{ old('prewriting',optional($pointStudent)->prewriting) == 'Fm' ? 'selected' : '' }}>
                      Fm
                    </option>
                  </select>
                  @error('prewriting')
                  <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                @elseif(in_array($level,['QW2','QW3','QW4']) && $type == 'HW2')
                  <label for="writing" class="mb-1 required">Writing</label>
                  <select class="form-select @error('writing') is-invalid @enderror" id="writing"
                          name="writing" {{$title == 'Show' ? 'disabled' : ''}}>
                    <option
                      value="N/A" {{ old('writing',optional($pointStudent)->writing) == 'N/A' ? 'selected' : '' }}>N/A
                    </option>
                    <option value="A+" {{ old('writing',optional($pointStudent)->writing) == 'A+' ? 'selected' : '' }}>
                      A+
                    </option>
                    <option value="A" {{ old('writing',optional($pointStudent)->writing) == 'A' ? 'selected' : '' }}>A
                    </option>
                    <option value="A-" {{ old('writing',optional($pointStudent)->writing) == 'A-' ? 'selected' : '' }}>
                      A-
                    </option>
                    <option value="B+" {{ old('writing',optional($pointStudent)->writing) == 'B+' ? 'selected' : '' }}>
                      B+
                    </option>
                    <option value="B" {{ old('writing',optional($pointStudent)->writing) == 'B' ? 'selected' : '' }}>B
                    </option>
                    <option value="B-" {{ old('writing',optional($pointStudent)->writing) == 'B-' ? 'selected' : '' }}>
                      B-
                    </option>
                    <option value="C+" {{ old('writing',optional($pointStudent)->writing) == 'C+' ? 'selected' : '' }}>
                      C+
                    </option>
                    <option value="C" {{ old('writing',optional($pointStudent)->writing) == 'C' ? 'selected' : '' }}>C
                    </option>
                    <option value="Fp" {{ old('writing',optional($pointStudent)->writing) == 'Fp' ? 'selected' : '' }}>
                      Fp
                    </option>
                    <option value="Fm" {{ old('writing',optional($pointStudent)->writing) == 'Fm' ? 'selected' : '' }}>
                      Fm
                    </option>
                  </select>
                  @error('prewriting')
                  <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                @elseif($level == 'QW2' && $type == 'HW3')
                  <label for="completed_essay" class="mb-1 required">Completed Essay</label>
                  <select class="form-select @error('completed_essay') is-invalid @enderror" id="completed_essay"
                          name="completed_essay" {{$title == 'Show' ? 'disabled' : ''}}>
                    <option
                      value="N/A" {{ old('completed_essay',optional($pointStudent)->completed_essay) == 'N/A' ? 'selected' : '' }}>
                      N/A
                    </option>
                    <option
                      value="A+" {{ old('completed_essay',optional($pointStudent)->completed_essay) == 'A+' ? 'selected' : '' }}>
                      A+
                    </option>
                    <option
                      value="A" {{ old('completed_essay',optional($pointStudent)->completed_essay) == 'A' ? 'selected' : '' }}>
                      A
                    </option>
                    <option
                      value="A-" {{ old('completed_essay',optional($pointStudent)->completed_essay) == 'A-' ? 'selected' : '' }}>
                      A-
                    </option>
                    <option
                      value="B+" {{ old('completed_essay',optional($pointStudent)->completed_essay) == 'B+' ? 'selected' : '' }}>
                      B+
                    </option>
                    <option
                      value="B" {{ old('completed_essay',optional($pointStudent)->completed_essay) == 'B' ? 'selected' : '' }}>
                      B
                    </option>
                    <option
                      value="B-" {{ old('completed_essay',optional($pointStudent)->completed_essay) == 'B-' ? 'selected' : '' }}>
                      B-
                    </option>
                    <option
                      value="C+" {{ old('completed_essay',optional($pointStudent)->completed_essay) == 'C+' ? 'selected' : '' }}>
                      C+
                    </option>
                    <option
                      value="C" {{ old('completed_essay',optional($pointStudent)->completed_essay) == 'C' ? 'selected' : '' }}>
                      C
                    </option>
                    <option
                      value="Fp" {{ old('completed_essay',optional($pointStudent)->completed_essay) == 'Fp' ? 'selected' : '' }}>
                      Fp
                    </option>
                    <option
                      value="Fm" {{ old('completed_essay',optional($pointStudent)->completed_essay) == 'Fm' ? 'selected' : '' }}>
                      Fm
                    </option>
                  </select>
                  @error('completed_essay')
                  <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                @elseif(in_array($level,['QW3','QW4']) && $type == 'HW3')
                  <label for="revision" class="mb-1 required">Revision</label>
                  <select class="form-select @error('revision') is-invalid @enderror" id="revision"
                          name="revision" {{$title == 'Show' ? 'disabled' : ''}}>
                    <option
                      value="N/A" {{ old('revision',optional($pointStudent)->revision) == 'N/A' ? 'selected' : '' }}>N/A
                    </option>
                    <option
                      value="A+" {{ old('revision',optional($pointStudent)->revision) == 'A+' ? 'selected' : '' }}>A+
                    </option>
                    <option value="A" {{ old('revision',optional($pointStudent)->revision) == 'A' ? 'selected' : '' }}>
                      A
                    </option>
                    <option
                      value="A-" {{ old('revision',optional($pointStudent)->revision) == 'A-' ? 'selected' : '' }}>A-
                    </option>
                    <option
                      value="B+" {{ old('revision',optional($pointStudent)->revision) == 'B+' ? 'selected' : '' }}>B+
                    </option>
                    <option value="B" {{ old('revision',optional($pointStudent)->revision) == 'B' ? 'selected' : '' }}>
                      B
                    </option>
                    <option
                      value="B-" {{ old('revision',optional($pointStudent)->revision) == 'B-' ? 'selected' : '' }}>B-
                    </option>
                    <option
                      value="C+" {{ old('revision',optional($pointStudent)->revision) == 'C+' ? 'selected' : '' }}>C+
                    </option>
                    <option value="C" {{ old('revision',optional($pointStudent)->revision) == 'C' ? 'selected' : '' }}>
                      C
                    </option>
                    <option
                      value="Fp" {{ old('revision',optional($pointStudent)->revision) == 'Fp' ? 'selected' : '' }}>Fp
                    </option>
                    <option
                      value="Fm" {{ old('revision',optional($pointStudent)->revision) == 'Fm' ? 'selected' : '' }}>Fm
                    </option>
                  </select>
                  @error('revision')
                  <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                @endif
              </div>
            </div>

            <div class="row mb-3">
              <div class="col-sm-6">
                <label for="skill" class="mb-1">Techical Writing Skill</label>
                @error('skill')
                <div style="color: red">{{ $message }}</div>
                @enderror
                @error('skill1')
                <div style="color: red">{{ $message }}</div>
                @enderror
                @error('skill2')
                <div style="color: red">{{ $message }}</div>
                @enderror
                <div class="col-sm-2 d-flex">
                  <input style="margin-right: 5px" type="radio" value="notFM"
                         name="skill" {{ old('skill',optional($pointStudent)->skill) == 'notFM' ? 'checked' : '' }} {{$title == 'Show' ? 'disabled' : ''}}>
                  <span class="d-flex align-items-center">
                    <input type="text" name="skill1" value="{{old('skill1',optional($pointStudent)->skill1)}}"
                           id="skill1" class="form-control @error('skill1') is-invalid @enderror" placeholder="..." {{$title == 'Show' ? 'disabled' : ''}}>
                    <span>/</span>
                    <input type="text" name="skill2" value="{{old('skill2',optional($pointStudent)->skill2)}}"
                           id="skill2" class="form-control @error('skill2') is-invalid @enderror" placeholder="..." {{$title == 'Show' ? 'disabled' : ''}}>
                </span>
                  <div class="col-sm-2 d-flex align-items-center"></div>
                  <div class="col-sm-2 d-flex align-items-center">
                    <input style="margin-right: 5px" type="radio" value="FM"
                           name="skill" {{ old('skill',optional($pointStudent)->skill) == 'FM' ? 'checked' : '' }} {{$title == 'Show' ? 'disabled' : ''}}>
                    FM
                  </div>
                </div>
                @error('skill')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              @if($level == 'QW1')
                <div class="col-sm-6">
                  <label for="writing" class="mb-1 required">Writing</label>
                  <select class="form-select @error('writing') is-invalid @enderror" id="writing"
                          name="writing" {{$title == 'Show' ? 'disabled' : ''}}>
                    <option
                      value="N/A" {{ old('writing',optional($pointStudent)->writing) == 'N/A' ? 'selected' : '' }}>N/A
                    </option>
                    <option value="A+" {{ old('writing',optional($pointStudent)->writing) == 'A+' ? 'selected' : '' }}>
                      A+
                    </option>
                    <option value="A" {{ old('writing',optional($pointStudent)->writing) == 'A' ? 'selected' : '' }}>A
                    </option>
                    <option value="A-" {{ old('writing',optional($pointStudent)->writing) == 'A-' ? 'selected' : '' }}>
                      A-
                    </option>
                    <option value="B+" {{ old('writing',optional($pointStudent)->writing) == 'B+' ? 'selected' : '' }}>
                      B+
                    </option>
                    <option value="B" {{ old('writing',optional($pointStudent)->writing) == 'B' ? 'selected' : '' }}>B
                    </option>
                    <option value="B-" {{ old('writing',optional($pointStudent)->writing) == 'B-' ? 'selected' : '' }}>
                      B-
                    </option>
                    <option value="C+" {{ old('writing',optional($pointStudent)->writing) == 'C+' ? 'selected' : '' }}>
                      C+
                    </option>
                    <option value="C" {{ old('writing',optional($pointStudent)->writing) == 'C' ? 'selected' : '' }}>C
                    </option>
                    <option value="Fp" {{ old('writing',optional($pointStudent)->writing) == 'Fp' ? 'selected' : '' }}>
                      Fp
                    </option>
                  </select>
                  @error('writing')
                  <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>
              @endif
            </div>
            <div class=" mb-3">
              <label for="description" class="mb-1">Message and Consultations</label>
              <textarea maxlength="500" class="form-control" placeholder="Type here"
                        name="description" {{$title == 'Show' ? 'disabled' : ''}}>{{old('description',optional($pointStudent)->description)}}</textarea>
            </div>
            <div class=" mb-3">
              <label for="status" class="mb-1 required">Status</label>
              <select class="form-select @error('status') is-invalid @enderror" id="status"
                      name="status" {{$title == 'Show' ? 'disabled' : ''}}>
                <option
                  value="Inactive" {{ old('status',optional($pointStudent)->status) == 'Inactive' ? 'selected' : '' }}>
                  Inactive
                </option>
                <option
                  value="Active" {{ old('status',optional($pointStudent)->status) == 'Active' ? 'selected' : '' }}>
                  Active
                </option>
              </select>
              @error('status')
              <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>
            <input type="hidden" value="{{$session}}" name="session">
            <input type="hidden" value="{{$student}}" name="student">
            <input type="hidden" value="{{request()->type}}" name="type">
            @if($title == 'Edit')
              <div class="row">
                <div class="mt-3 d-flex justify-content-end">
                  <span class="btn btn-secondary" style="margin-right: 15px;" onclick="$('#form_id').trigger('reset')">Reset</span>
                  <button type="submit" class="btn btn-primary">Update</button>
                </div>
              </div>
            @endif
          </form>
        </div>
      </div>
    </div>
  </div>
@endsection
