@extends('layouts/contentNavbarLayout')

@section('title', 'Grade Form')

@section('page-style')
  <style>
    input[type="text"] {
      width: 80px;
      text-align: center;
    }

    .tab-grade {
      background: #b5b5b5;
      color: white !important;
      font-weight: bold;
      cursor: pointer;
    }

    .tab-active {
      background: white;
      color: black !important;
      border-bottom: 0px !important;
    }
  </style>

@endsection

@section('vendor-script')
@endsection

@section('page-script')
  <script>
    jQuery(document).ready(function($){
      var inputScore = "{{ old('score',optional($pointStudent)->score) }}";

      var inputScore1 = "{{ old('input1',optional($pointStudent)->score1) }}";
      var inputScore2 = "{{ old('input2',optional($pointStudent)->score2) }}";
      var type = '{{$title}}'
      if(type == 'Show'){
        $('input').attr('disabled', true);
        $('select').prop('disabled', true);
      }
      if(inputScore1 && inputScore2){
        var result = inputScore1 / inputScore2 * 100;
        var percentValue = result % 1 === 0 ? result.toFixed(0) : parseFloat(result.toFixed(2)).toString();
        if(inputScore2 > 0){
          $(".percent").val(percentValue + '%');
        }else{
          $(".percent").val(0 + '%');
        }        $("#grade").val(findGrade(percentValue));
      }else{
        if(inputScore == 'FM'){
          $(".percent").val('Fm');
          $("#grade").val('Fm');
        }
      }
      $(".radio-score").click(function(){
        if(this.value == 'notFM'){
          $(".percent").val('');
          $("#grade").val('');
          var i1 = $("#input1").val();
          var i2 = $("#input2").val();
          if(i1 && i2 && i1 <= i2){
            var result = i1 / i2 * 100;
            var percentValue = result % 1 === 0 ? result.toFixed(0) : parseFloat(result.toFixed(2)).toString();
            if(i2 > 0){
              $(".percent").val(percentValue + '%');
            }else{
              $(".percent").val(0 + '%');
            }
            $("#grade").val(findGrade(percentValue));
          }
        }else{
          $(".percent").val('Fm');
          $("#grade").val('Fm');
          $("#input1").val('');
          $("#input2").val('');
        }
      });
      $("input[name='vocab_hw']").on('change', function() {
        if(this.value == 'FM'){
          $("#vocab_hw1").val('');
          $("#vocab_hw2").val('');
        }
      });
    });

    const input1 = document.getElementById("input1");
    const input2 = document.getElementById("input2");

    function restrictInput(event) {
      const input = event.target;
      const value = input.value.replace(/[^\d.]/g, "");
      if (parseInt(value) >= 1000) {
        input.value = "999";
      } else {
        input.value = value;
      }
      if($(".radio-score:checked").val() == 'notFM' && parseFloat($("#input1").val()) <= parseFloat($("#input2").val())){
        var result = input1.value / input2.value * 100;
        var percentValue = result % 1 === 0 ? result.toFixed(0) : parseFloat(result.toFixed(2)).toString();
        if(input2.value > 0){
          $(".percent").val(percentValue + '%');
        }else{
          $(".percent").val(0 + '%');
        }
        $("#grade").val(findGrade(percentValue));
      }
    }
    function validateInput(event){
      const input = event.target;
      const value = input.value.replace(/[^\d.]/g, "");
      if (parseInt(value) >= 1000) {
        input.value = "999";
      } else {
        input.value = value;
      }
    }
    const sentence2 = document.getElementById("vocab_hw2");
    const sentence1 = document.getElementById("vocab_hw1");
    sentence2.addEventListener("input", restrictInput);
    sentence1.addEventListener("input", restrictInput);


    input1.addEventListener("input", restrictInput);
    input2.addEventListener("input", restrictInput);


    function findGrade(point) {
      if (point >= 95 && point <= 100) {
        return 'A+';
      } else if (point >= 90 && point < 95) {
        return 'A';
      } else if (point >= 80 && point < 90) {
        return 'A-';
      } else if (point >= 70 && point < 80) {
        return 'B+';
      } else if (point >= 60 && point < 70) {
        return 'B';
      } else if (point >= 50 && point < 60) {
        return 'B-';
      } else if (point >= 40 && point < 50) {
        return 'C+';
      } else if (point >= 30 && point < 40) {
        return 'C';
      } else if (point >= 0 && point < 30) {
        return 'FP/Fm';
      }
    }
  </script>
@endsection

@section('content')
  <div class="card">
    <div class="col-xxl">
      <div class="mb-4">
        <div class="card-header d-flex align-items-center justify-content-between">
          <h5 class="mb-0">{{$title}} Grades</h5> <a
            href="{{route('admin.grades.index',['class'=>$id,'student'=>$student,'session' => $session])}}"
            class="btn btn-primary float-end">Back</a>
        </div>
        <div class="card-body">
          <div class="row mb-5">
            <a
              href="{{ route('admin.grades.'.($title == 'Edit' ? 'edit':'show'), ['grade' => $id, 'student_id' => $student, 'session' => $session,'type'=>'HW1']) }}"
              class="col-sm-4 text-center border p-2 tab-grade {{$type == 'HW1' ? 'tab-active':''}}" data-id="hw1">
              <span class="text-center" id="myLink">HW1</span>
            </a>
          </div>
          <form action="{{route('admin.grades.update',$id)}}" method="post" id="form_id">
            @method('PUT')
            @csrf
            <div class="row mb-3">
              <div class="col-sm-2">
                <input type="radio" class="form-check-input" id="basic-default-name" name="subject"  disabled>
                <label for="basic-default-name" class="mb-1">Writing</label>
                @error('name')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              <div class="col-sm-2">
                <input type="radio" style="background: blue" class="form-check-input" name="subject" checked disabled>
                <label for="basic-default-name" class="mb-1">Reading</label>
                @error('name')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
            </div>
            <div class="row mb-3">
              <h6>A. Class Work</h6>
              <p class="">Score</p>
              @error('score')
              <div style="color: red">{{ $message }}</div>
              @enderror
              @error('input1')
              <div style="color: red">{{ $message }}</div>
              @enderror
              @error('input2')
              <div style="color: red">{{ $message }}</div>
              @enderror
              <div class="col-sm-2 d-flex">
                <input style="margin-right: 5px" type="radio" value="notFM" name="score" class="radio-score" {{ old('score', optional($pointStudent)->score) ?? '' == 'notFM' ? 'checked' : '' }}>
                <span class="d-flex align-items-center">
                    <input type="text" name="input1" value="{{ old('input1', optional($pointStudent)->score1)}}" id="input1" class="form-control @error('input1') is-invalid @enderror" placeholder="...">
                    <span>/</span>
                    <input type="text" name="input2" value="{{old('input2',optional($pointStudent)->score2)}}" id="input2" class="form-control @error('input2') is-invalid @enderror" placeholder="...">
                </span>
              </div>
              <div class="col-sm-2 d-flex align-items-center">
                <input style="margin-right: 5px" type="radio" value="FM" name="score" class="radio-score" {{ old('score',optional($pointStudent)->score) == 'FM' ? 'checked' : '' }}>FM
                <input type="hidden" value="{{$session}}" name="session">
                <input type="hidden" value="{{$student}}" name="student">
                <input type="hidden" value="{{request()->type}}" name="type">
              </div>
              <div class="col-sm-3">
                <input type="text" class="form-control percent" placeholder="%" readonly>
              </div>
              <div class="col-sm-3">
                <input type="text" style="width: 80px" class="form-control" id="grade" placeholder="Grade" readonly>
              </div>
            </div>
            <div class="row mb-3">
              <h6>B. Home Work</h6>
              <h6>Class Participation</h6>
              <div class="col-sm-6">
                <label for="attendance" class="mb-1 required">Student Attendance</label>
                <option value="N/A" {{ old('attendance',optional($pointStudent)->attendance) == 'N/A' ? 'selected' : '' }}>N/A</option>
                <select class="form-select @error('attendance') is-invalid @enderror" id="attendance" name="attendance">
                  <option value="O" {{ old('attendance',optional($pointStudent)->attendance) == 'O' ? 'selected' : '' }}>O</option>
                  <option value="X" {{ old('attendance',optional($pointStudent)->attendance) == 'X' ? 'selected' : '' }}>X</option>
                  <option value="Late" {{ old('attendance',optional($pointStudent)->attendance) == 'Late' ? 'selected' : '' }}>Late</option>
                </select>
                @error('attendance')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              <div class="col-sm-6">
                <label for="" class="mb-1 required">Student Participation</label>
                <select class="form-select @error('participation') is-invalid @enderror" id="participation" name="participation">
                  <option value="N/A" {{ old('participation',optional($pointStudent)->participation) == 'N/A' ? 'selected' : '' }}>N/A</option>
                  <option value="A+" {{ old('participation',optional($pointStudent)->participation) == 'A+' ? 'selected' : '' }}>A+</option>
                  <option value="A" {{ old('participation',optional($pointStudent)->participation) == 'A' ? 'selected' : '' }}>A</option>
                  <option value="A-" {{ old('participation',optional($pointStudent)->participation) == 'A-' ? 'selected' : '' }}>A-</option>
                  <option value="B+" {{ old('participation',optional($pointStudent)->participation) == 'B+' ? 'selected' : '' }}>B+</option>
                  <option value="B" {{ old('participation',optional($pointStudent)->participation) == 'B' ? 'selected' : '' }}>B</option>
                  <option value="B-" {{ old('participation',optional($pointStudent)->participation) == 'B-' ? 'selected' : '' }}>B-</option>
                  <option value="C+" {{ old('participation',optional($pointStudent)->participation) == 'C+' ? 'selected' : '' }}>C+</option>
                  <option value="C" {{ old('participation',optional($pointStudent)->participation) == 'C' ? 'selected' : '' }}>C</option>
                  <option value="Fp" {{ old('participation',optional($pointStudent)->participation) == 'Fp' ? 'selected' : '' }}>Fp</option>
                </select>
                @error('participation')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
            </div>
            <div class="row mb-3">
              <div class="col-sm-6">
                <p class="">Vocab HW</p>
                @error('vocab_hw')
                <div style="color: red">{{ $message }}</div>
                @enderror
                @error('vocab_hw1')
                <div style="color: red">{{ $message }}</div>
                @enderror
                @error('vocab_hw2')
                <div style="color: red">{{ $message }}</div>
                @enderror
                <div class="col-sm-2 d-flex">
                  <input style="margin-right: 5px" type="radio" value="notFM" name="vocab_hw" {{ old('vocab_hw',optional($pointStudent)->vocab_hw) == 'notFM' ? 'checked' : '' }}>
                  <span class="d-flex align-items-center">
                    <input type="text" name="vocab_hw1" value="{{old('vocab_hw1',optional($pointStudent)->vocab_hw1)}}" id="vocab_hw1" class="form-control @error('vocab_hw1') is-invalid @enderror" placeholder="...">
                    <span>/</span>
                    <input type="text" name="vocab_hw2" value="{{old('vocab_hw2',optional($pointStudent)->vocab_hw2)}}"  id="vocab_hw2" class="form-control @error('vocab_hw2') is-invalid @enderror" placeholder="...">
                </span>
                  <div class="col-sm-2 d-flex align-items-center"></div>
                  <div class="col-sm-2 d-flex align-items-center">
                    <input style="margin-right: 5px" type="radio" value="FM" name="vocab_hw" {{ old('vocab_hw',optional($pointStudent)->vocab_hw) == 'FM' ? 'checked' : '' }}>
                    FM
                  </div>
                </div>
              </div>
              <div class="col-sm-6">
                <label for="short_response" class="mb-1 required">Short Response</label>
                <select class="form-select @error('short_response') is-invalid @enderror" id="short_response" name="short_response">
                  <option value="N/A" {{ old('short_response',optional($pointStudent)->short_response) == 'N/A' ? 'selected' : '' }}>N/A</option>
                  <option value="A+" {{ old('short_response',optional($pointStudent)->short_response) == 'A+' ? 'selected' : '' }}>A+</option>
                  <option value="A" {{ old('short_response',optional($pointStudent)->short_response) == 'A' ? 'selected' : '' }}>A</option>
                  <option value="A-" {{ old('short_response',optional($pointStudent)->short_response) == 'A-' ? 'selected' : '' }}>A-</option>
                  <option value="B+" {{ old('short_response',optional($pointStudent)->short_response) == 'B+' ? 'selected' : '' }}>B+</option>
                  <option value="B" {{ old('short_response',optional($pointStudent)->short_response) == 'B' ? 'selected' : '' }}>B</option>
                  <option value="B-" {{ old('short_response',optional($pointStudent)->short_response) == 'B-' ? 'selected' : '' }}>B-</option>
                  <option value="C+" {{ old('short_response',optional($pointStudent)->short_response) == 'C+' ? 'selected' : '' }}>C+</option>
                  <option value="C" {{ old('short_response',optional($pointStudent)->short_response) == 'C' ? 'selected' : '' }}>C</option>
                  <option value="Fp" {{ old('short_response',optional($pointStudent)->short_response) == 'Fp' ? 'selected' : '' }}>Fp</option>
                  <option value="Fm" {{ old('short_response',optional($pointStudent)->short_response) == 'Fm' ? 'selected' : '' }}>Fm</option>
                </select>
                @error('short_response')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
            </div>
            <div class="row mb-3">
              <div class="col-sm-6">
                <label for="description" class="mt-1">Message and Consultations</label>
                <textarea maxlength="500" style="margin-top: 2px" class="form-control" placeholder="Type here" name="description">{{old('description',optional($pointStudent)->description)}}</textarea>
              </div>
              <div class="col-sm-6">
                <label for="status" class="mb-1 required">Status</label>
                <select class="form-select @error('status') is-invalid @enderror" id="status" name="status">
                  <option value="Inactive" {{ old('status',optional($pointStudent)->status) == 'Inactive' ? 'selected' : '' }}>Inactive</option>
                  <option value="Active" {{ old('status',optional($pointStudent)->status) == 'Active' ? 'selected' : '' }}>Active</option>
                </select>
                @error('status')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
            </div>

            @if($title == 'Edit')
              <div class="row">
                <div class="mt-3 d-flex justify-content-end">
                  <span class="btn btn-secondary" style="margin-right: 15px;" onclick="$('#form_id').trigger('reset')">Reset</span>
                  <button type="submit" class="btn btn-primary">Update</button>
                </div>
              </div>
            @endif
          </form>
        </div>
      </div>
    </div>
  </div>
@endsection
