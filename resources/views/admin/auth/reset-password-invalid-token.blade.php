@extends('layouts.blankLayout')

@section('title', 'Forgot Password')

@section('page-style')
  <!-- Page -->
  <link rel="stylesheet" href="{{asset('assets/vendor/css/pages/page-auth.css')}}">
@endsection

@section('content')
  @if(\Session::get('success'))
    <div class="alert alert-success alert-dismissible fade show" style="z-index: 9999;" role="alert">
      <div class="alert-body">
        {{ \Session::get('success') }}
      </div>
      <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
  @endif
  {{ \Session::forget('success') }}
  @if(\Session::get('error'))
    <div class="alert alert-danger alert-dismissible fade show" style="z-index: 9999;" role="alert">
      <div class="alert-body">
        {{ \Session::get('error') }}
      </div>
      <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
  @endif
  <div class="container-xxl">
    <div class="authentication-wrapper authentication-basic container-p-y">
      <div class="authentication-inner py-4">

        <!-- Forgot Password -->
        <div class="card">
          <div class="card-body">
            <!-- Logo -->
            <div class="app-brand justify-content-center">
              <a href="{{url('/')}}" class="app-brand-link gap-2">
                <img src="{{asset('assets/img/logo.png')}}" alt="">
              </a>
            </div>
            <!-- /Logo -->
            <h4 class="mb-2 text-center fw-bold">Invalid Link</h4>
            <p class="text-center">Please check your email for password reset link</p>
            <p class="fw-bold text-center mt-3">Password reset link is expired</p>
            <p class="text-center mt-3">If you don't see an email from us within a few minutes, be sure to check your spam folder</p>
            @if(!empty($type) && $type  == 'web')
              <a href="{{route('web.forgot')}}" class="btn btn-primary d-flex m-auto">Resend Reset Password Email</a>
            @else
            <a href="{{route('forgot-password')}}" class="btn btn-primary d-flex m-auto">Resend Reset Password Email</a>
            @endif
          </div>
        </div>
        <!-- /Forgot Password -->
      </div>
    </div>
  </div>
@endsection
