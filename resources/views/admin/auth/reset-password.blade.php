@extends('layouts.blankLayout')

@section('title', 'Forgot Password Basic - Pages')

@section('page-style')
  <!-- Page -->
  <link rel="stylesheet" href="{{asset('assets/vendor/css/pages/page-auth.css')}}">
  <script>
    function togglePasswordVisibility(inputId) {
      var passwordInput = document.getElementById(inputId);
      var toggleIcon = document.getElementById(inputId + '-toggle-icon');

      if (passwordInput.type === "password") {
        passwordInput.type = "text";
        toggleIcon.classList.remove("bx-hide");
        toggleIcon.classList.add("bx-show");
      } else {
        passwordInput.type = "password";
        toggleIcon.classList.remove("bx-show");
        toggleIcon.classList.add("bx-hide");
      }
    }
  </script>
@endsection

@section('content')
  <div class="container-xxl">
    <div class="authentication-wrapper authentication-basic container-p-y">
      <div class="authentication-inner py-4">

        <!-- Forgot Password -->
        <div class="card">
          <div class="card-body">
            <!-- Logo -->
            <div class="app-brand justify-content-center">
              <a href="{{url('/')}}" class="app-brand-link gap-2">
                <img src="{{asset('assets/img/logo.png')}}" alt="">
              </a>
            </div>
            <!-- /Logo -->
            <h4 class="mb-2 text-center">New password</h4>
            <form id="formAuthentication" class="mb-3" action="{{route('reset-passwordPost',$token->token)}}" method="POST">
              @csrf
              <div class="mb-3">
                <label for="password" class="form-label required">Password</label>
                <div class="input-group input-group-merge">
                  <input type="password" id="password" class="form-control" name="password" placeholder="8-32 characters" aria-describedby="password" value="{{ old('password') }}" />
                  <span class="input-group-text cursor-pointer" onclick="togglePasswordVisibility('password')"><i id="password-toggle-icon" class="bx bx-hide"></i></span>
                </div>
                <i style="font-size: 12px">including at least 1 capital letter and 1 number</i>
                @error('password')
                <div style="color: red">{{ $message }}</div>
                @enderror
              </div>

              <div class="mb-3">
                <label for="password_confirmation" class="form-label required">Confirm password</label>
                <div class="input-group input-group-merge">
                  <input type="password" id="password_confirmation" class="form-control" name="password_confirmation" placeholder="8-32 characters" value="{{ old('password_confirmation') }}" />
                  <span class="input-group-text cursor-pointer" onclick="togglePasswordVisibility('password_confirmation')"><i id="password_confirmation-toggle-icon" class="bx bx-hide"></i></span>
                </div>
                @error('password_confirmation')
                <div style="color: red">{{ $message }}</div>
                @enderror
              </div>

              <button class="btn btn-primary d-grid w-100">Update</button>
            </form>
            <div class="text-center">
              <a href="{{route('adminLogin')}}" class="d-flex align-items-center justify-content-center">
                <i class="bx bx-chevron-left scaleX-n1-rtl bx-sm"></i>
                Back to login
              </a>
            </div>
          </div>
        </div>
        <!-- /Forgot Password -->
      </div>
    </div>
  </div>
@endsection
