@extends('layouts.blankLayout')

@section('title', 'Forgot Password')

@section('page-style')
  <!-- Page -->
  <link rel="stylesheet" href="{{asset('assets/vendor/css/pages/page-auth.css')}}">
@endsection

@section('content')
  @if(\Session::get('success'))
    <div class="alert alert-success alert-dismissible fade show" style="z-index: 9999;" role="alert">
      <div class="alert-body">
        {{ \Session::get('success') }}
      </div>
      <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
  @endif
  {{ \Session::forget('success') }}
  @if(\Session::get('error'))
    <div class="alert alert-danger alert-dismissible fade show" style="z-index: 9999;" role="alert">
      <div class="alert-body">
        {{ \Session::get('error') }}
      </div>
      <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
  @endif
  <div class="container-xxl">
    <div class="authentication-wrapper authentication-basic container-p-y">
      <div class="authentication-inner py-4">

        <!-- Forgot Password -->
        <div class="card">
          <div class="card-body">
            <!-- Logo -->
            <div class="app-brand justify-content-center">
              <a href="{{url('/')}}" class="app-brand-link gap-2">
                <img src="{{asset('assets/img/logo.png')}}" alt="">
              </a>
            </div>
            <!-- /Logo -->
            <h4 class="mb-2 text-center fw-bold">Forgot Password</h4>
            <p class="text-center">Password Reset Send Mail</p>
            <img class="d-flex m-auto" style="width: 40px" src="{{asset('assets/img/icon-check.jpg')}}" alt="">
            <div class="mt-3">
              <p class="text-center">Please check your email for password reset link</p>
              <p class="text-center">This link only valid for <b>24 hours</b></p>
            </div>
          </div>
        </div>
        <!-- /Forgot Password -->
      </div>
    </div>
  </div>
@endsection
