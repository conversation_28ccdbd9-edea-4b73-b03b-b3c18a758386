Hi {{$dataParent['name_english']}},

Your account have successfully created at {{config('app.name')}}. Please use the following credentials to sign in {{config('app.name')}}.

<p>Parent account: {{$dataParent['email']}} created by Admin</p>
@if(isset($password))
  <p>Parent password: {{$password}}</p>
@endif
Student account:
@foreach($dataStudent as $key => $student)
  <span>{{$student->user_name}}</span>
  @if (!$loop->last)
    <span>,</span>
  @endif
@endforeach
created by Admin

Thank you for joining us!
