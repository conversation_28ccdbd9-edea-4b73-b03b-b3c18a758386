// Simple Task Warning System
(function() {
    'use strict';

    let checkInterval = null;
    let notificationContainer = null;
    let lastNotificationTime = 0;

    function init() {
        // Only run for logged in users
        if (!isUserLoggedIn()) return;
        
        // Don't run on task pages
        if (isOnTaskPage()) return;

        createNotificationContainer();
        startChecking();
        
        // Handle page visibility
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                stopChecking();
            } else if (!isOnTaskPage()) {
                startChecking();
            }
        });

        // Stop checking when navigating to task page
        window.addEventListener('beforeunload', () => {
            stopChecking();
        });
    }

    function isUserLoggedIn() {
        // Check if user is logged in
        return document.querySelector('meta[name="csrf-token"]') !== null;
    }

    function isOnTaskPage() {
        // Check if current page is a task page
        const path = window.location.pathname;
        return path.includes('/homeworks/') && path.split('/').length >= 4;
    }

    function createNotificationContainer() {
        if (notificationContainer) return;

        notificationContainer = document.createElement('div');
        notificationContainer.id = 'task-warning-container';
        notificationContainer.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            pointer-events: none;
        `;
        document.body.appendChild(notificationContainer);
    }

    function startChecking() {
        if (checkInterval) return;
        
        // Check immediately
        checkActiveTask();
        
        // Then check every 60 seconds (1 minute)
        checkInterval = setInterval(checkActiveTask, 60000);
    }

    function stopChecking() {
        if (checkInterval) {
            clearInterval(checkInterval);
            checkInterval = null;
        }
    }

    async function checkActiveTask() {
        try {
            const response = await fetch('/api/check-active-task', {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json'
                },
                credentials: 'same-origin'
            });

            if (response.ok) {
                const data = await response.json();
                
                if (data.has_active_task && data.tasks.length > 0) {
                    const now = Date.now();
                    
                    // Only show notification once per minute
                    if (now - lastNotificationTime >= 60000) {
                        showWarning(data.tasks[0]); // Show first task warning
                        lastNotificationTime = now;
                    }
                }
            }
        } catch (error) {
            console.log('Task check failed:', error);
        }
    }

    function showWarning(task) {
        // Clear existing notifications
        notificationContainer.innerHTML = '';

        const notification = document.createElement('div');
        notification.style.cssText = `
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 16px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(255,107,107,0.4);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 14px;
            cursor: pointer;
            pointer-events: auto;
            max-width: 350px;
            transform: translateX(100%);
            opacity: 0;
            transition: all 0.3s ease;
            border-left: 4px solid rgba(255,255,255,0.6);
        `;

        const remainingTime = Math.max(0, Math.floor(task.remaining_seconds));
        const timeDisplay = remainingTime > 0 ? `${remainingTime}s` : 'Time up!';

        notification.innerHTML = `
            <div style="display: flex; align-items: center; justify-content: space-between;">
                <div style="flex: 1;">
                    <div style="font-weight: 600; margin-bottom: 6px; display: flex; align-items: center;">
                        <span style="margin-right: 8px; font-size: 16px;">⏰</span>
                        Task Time Warning
                    </div>
                    <div style="opacity: 0.9; font-size: 12px; margin-bottom: 4px;">
                        You have an unfinished task!
                    </div>
                    <div style="opacity: 0.8; font-size: 11px;">
                        Click here to return to your task
                    </div>
                </div>
                <div style="margin-left: 16px; text-align: center;">
                    <div style="background: rgba(255,255,255,0.2); padding: 8px 12px; border-radius: 20px; font-weight: bold; font-size: 13px;">
                        ${timeDisplay}
                    </div>
                </div>
            </div>
        `;

        // Click handler
        notification.addEventListener('click', () => {
            window.location.href = task.task_url;
        });

        // Hover effects
        notification.addEventListener('mouseenter', () => {
            notification.style.transform = 'translateX(-5px) scale(1.02)';
        });

        notification.addEventListener('mouseleave', () => {
            notification.style.transform = 'translateX(0) scale(1)';
        });

        notificationContainer.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
            notification.style.opacity = '1';
        }, 100);

        // Auto hide after 10 seconds
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            notification.style.opacity = '0';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 10000);
    }

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

})();
