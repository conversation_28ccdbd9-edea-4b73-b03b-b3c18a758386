import { useEffect, useState } from "react";
import { useSearchParams } from "react-router-dom";
import { getListMainHomework, deleteMainHomework } from "../../services/admin/main-homeworks";
import { getList as getClasses } from '../../services/admin/classes.js';
import {
  Box,
  Typography,
  Button,
  Snackbar, Stack, Pagination, PaginationItem, Autocomplete, Grid
} from '@mui/material';
import AlertDialog from "../dialogs/AlertDialog.jsx";
import MainHomeWorkModal from './MainHomeWorkModal';
import { config } from "../../pages/admin/vocabulary/utils";
import KeyboardDoubleArrowLeftIcon from "@mui/icons-material/KeyboardDoubleArrowLeft";
import KeyboardDoubleArrowRightIcon from "@mui/icons-material/KeyboardDoubleArrowRight";
import { listAll as listAllTag, listVocabularyHomework } from '../../services/admin/tag-vocabularies';
import { useForm, Controller } from 'react-hook-form';
import { CustomTextField } from '../../pages/admin/vocabulary/components/VocaForm';
import RealDataNestedTable from '../../pages/admin/components/TableList';
import { toast } from 'react-toastify';

export default function List() {
  const [mainHomeworks, setMainHomeworks] = useState([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [openSnack, setOpenSnack] = useState(false);
  const [currentId, setCurrentId] = useState(false);
  const [openModal, setOpenModal] = useState(false);
  const [hiddenSession, setHiddenSession] = useState(false);
  const [sessions, setSessions] = useState([]);
  const [indexData, setIndexData] = useState(null);
  const [mode, setMode] = useState('create');
  const [vocabularies, setVocabularies] = useState([]);
  const [tags, setTags] = useState([]);
  const [vocabulariesSelected, setVocabulariesSelected] = useState([]);
  const [meta, setMeta] = useState(null);
  const [classList, setClassList] = useState([]);
  const [searchParams, setSearchParams] = useSearchParams();

  const defaultKeyword = searchParams.get("keyword") || "";
  const classId = searchParams.get("class");
  const sessionValue = searchParams.get("session");

  const defaultClass = classId ? { id: Number(classId), name: "" } : null;
  const defaultSession = sessionValue
    ? { value: Number(sessionValue), label: `Session ${sessionValue}` }
    : null;

  const { control, handleSubmit, setValue, reset, watch } = useForm({
    defaultValues: {
      keyword: defaultKeyword,
      class: defaultClass,
      session: defaultSession,
    }
  });

  const buildParams = (formData, page = 1) => ({
    page,
    keyword: formData?.keyword || null,
    class: formData?.class?.id || null,
    session: formData?.session?.value || null,
  });

  const loadMainHomeworks = async (params) => {
    try {
      const { data: { data, meta } } = await getListMainHomework(params);
      setMeta(meta);
      setMainHomeworks(data);
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    const sessions = Array.from({ length: 16 }, (_, i) => ({
      label: `Session ${i + 1}`,
      value: i + 1
    }));
    setSessions(sessions);
    const fetchData = async () => {
      const { data: { data: vocabularies } } = await listVocabularyHomework();
      setVocabularies(vocabularies);
    }
    const fetchTags = async () => {
      const { data: { data: tags } } = await listAllTag();
      setTags(tags);
    }
    loadMainHomeworks(buildParams(watch(), 1));
    fetchData();
    fetchTags();
  }, [])

  const handleChangePage = (event, newPage) => {
    loadMainHomeworks(buildParams(watch(), newPage));
  };

  useEffect(() => {
    async function fetchClasses() {
      const { data } = await getClasses();
      setClassList(data);
    }
    fetchClasses();
  }, []);

  const handleAction = async () => {
    if (!currentId) return
    try {
      await deleteMainHomework(currentId);
      setCurrentId(0);
      setOpenDialog(false);
      toast.success('Delete successfully');
      setMainHomeworks(prev => prev.filter((_, index) => index !== currentId));
      loadMainHomeworks(buildParams(watch(), 1));
    } catch (error) {
      console.error(error);
    }
  }

  const handleSnackClose = () => {
    setOpenSnack(false);
  };

  const handleReset = () => {
    reset({
      keyword: '',
      class: '',
      session: '',
    });
    const params = buildParams({}, 1);
    setSearchParams({
      keyword: params.keyword || "",
      class: params.class || "",
      session: params.session || "",
      page: 1,
    });
    loadMainHomeworks(buildParams({}, 1));
  };

  const onSubmit = async (formData) => {
    const params = buildParams(formData, 1);
    setSearchParams({
      keyword: params.keyword || "",
      class: params.class || "",
      session: params.session || "",
      page: 1,
    });
    loadMainHomeworks(params);
  };

  const handleClassChange = async (value) => {
    if (value) {
      if (value.type === 'Private') {
        setValue('session', null);
        setHiddenSession(true);
      } else {
        setHiddenSession(false);
      }
    } else {
      setHiddenSession(false);
      setValue('session', null);
    }
  };

  const handleClickOpen = (item, indexItem) => {
    if(meta?.current_page > 1){
      indexItem = indexItem - (meta?.per_page * (meta?.current_page - 1));
    }
    setIndexData(indexItem);
    setMode('edit');
    setVocabulariesSelected([]);
    setOpenModal(true);
  };

  const handleCopy = (item, indexItem) => {
    if(meta?.current_page > 1){
      indexItem = indexItem - (meta?.per_page * (meta?.current_page - 1));
    }
    setIndexData(indexItem);
    setMode('duplicate');
    setVocabulariesSelected([]);
    setOpenModal(true);
  };
  const handleDelete = (item, indexItem) => {
    if(meta?.current_page > 1){
      indexItem = indexItem - (meta?.per_page * (meta?.current_page - 1));
    }
    setIndexData(indexItem);
    setCurrentId(item.id);
    setOpenDialog(true);
  };

  const handleCreate = () => {
    setIndexData(null);
    setMode('create');
    setVocabulariesSelected([]);
    setOpenModal(true);
  };

  const handleAddHomework = (item) => {
    window.location.href = `/admin/homeworks/create?main_homework_id=${item.id}`;
  };

  const handleEditHomework = (item) => {
    window.location.href = `/admin/homeworks/${item.id}/edit`;
  };

  const columnsLevel = [
    {
      level: 0,
      id: '',
      childrenKey: 'homeworks',
      cols: [
        {
          field: 'id',
          header: 'ID',
          width: '10%'
        },
        {
          field: 'title',
          header: 'Title',
          width: '20%'
        },

        {
          field: 'get_class.name',
          header: 'Class',
          width: '20%'
        },
        {
          field: 'session',
          header: 'Session',
          width: '10%'
        },
        {
          field: 'schedule.date',
          header: 'Date',
          width: '10%'
        },
        {
          field: 'description',
          header: 'Description',
          width: '30%'
        },
        {
          field: 'action',
          header: 'Action',
          width: '10%',
          action: {
            add: {
              fnc: handleAddHomework,
              title: 'Add Homework',
              color: 'primary'
            },
            edit: {
              fnc: handleClickOpen,
              title: 'Edit',
              color: 'primary'
            },
            copy: {
              fnc: handleCopy,
              title: 'Duplicate main homework',
              color: 'primary'
            },
            delete: {
              fnc: handleDelete,
              title: 'Delete',
              color: 'error'
            }
          }
        }
      ],
      rowComponent: {
        icon: 'add',
        title: 'Add Homework',
        color: 'info',
        fnc: handleAddHomework
      }
    },
    {
      level: 1,
      id: 'homeworks',
      childrenKey: 'account_homeworks',
      cols: [
        {
          field: 'id',
          header: 'ID',
          width: '10%'
        },
        {
          field: 'title',
          header: 'Title',
          width: '20%'
        },

        {
          field: 'type',
          header: 'Type',
          width: '10%'
        },
        {
          field: 'homework',
          header: 'Homework',
          width: '10%'
        },
        {
          field: 'description',
          header: 'Description',
          width: '30%'
        },
        {
          field: 'status',
          header: 'Status',
          width: '10%'
        },
        {
          field: 'action',
          header: 'Action',
          width: '10%',
          action: {
            edit: {
              fnc: handleEditHomework,
              title: 'Edit',
              color: 'primary'
            }
          }
        }
      ]
    }
  ];

  return (
    <>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
        <Typography variant='h6'>Main Homework Management</Typography>
        <Button variant="contained" onClick={() => handleCreate()}>
          Create New Main Homework
        </Button>
      </Box>

      <Grid container spacing={2.75} paddingY={3}>
        <Grid item size={2}>
          <Controller
            name="keyword"
            control={control}
            defaultValue=""
            render={({ field }) => (
              <CustomTextField
                {...field}
                label="Search by title or description"
                size="small"
                fullWidth
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    handleSubmit(onSubmit)();
                  }
                }}
              />
            )}
          />
        </Grid>
        <Grid item size={2}>
          <Controller
            name="class"
            control={control}
            render={({ field }) => (
              <Autocomplete
                {...field}
                options={classList}
                getOptionLabel={(option) => option?.name || ''}
                onChange={(_, value) => {
                  field.onChange(value);
                  handleClassChange(value);
                }}
                value={
                  classList.find((c) => c.id === field.value?.id) || null
                }
                isOptionEqualToValue={(option, value) => option.id === value.id}
                renderInput={(params) => (
                  <CustomTextField {...params} label="Class" size="small" />
                )}
              />
            )}
          />
        </Grid>
        {!hiddenSession && (
          <Grid item size={2}>
            <Controller
              name="session"
              control={control}
              render={({ field }) => (
                <Autocomplete
                  {...field}
                  options={sessions}
                  getOptionLabel={(option) => option?.label || ''}
                  onChange={(_, value) => {
                    field.onChange(value);
                  }}
                  value={
                  sessions.find((c) => c.value === field.value?.value) || null
                  }
                  isOptionEqualToValue={(option, value) => option.value === value.value}
                  renderInput={(params) => (
                    <CustomTextField {...params} label="Session" size="small" />
                  )}
                />
              )}
            />
          </Grid>
        )}

        <Grid item size={4}>
          <Box>
            <Button
              variant='contained'
              sx={{
                bgcolor: config.colors.primary,
                fontSize: '0.9375rem',
                textTransform: 'capitalize',
                mr: 1
              }}
              onClick={handleSubmit(onSubmit)}
            >
              Search
            </Button>

            <Button
              variant='contained'
              sx={{ mr: 2.5, bgcolor: '#8592A3', fontSize: '0.9375rem', textTransform: 'capitalize' }}
              onClick={handleReset}
            >
              Reset
            </Button>
          </Box>
        </Grid>
      </Grid>

      <RealDataNestedTable columns={columnsLevel} data={mainHomeworks} page={meta?.current_page} pageSize={10} />

      {meta && meta?.last_page > 1 && (
        <>
          <Stack spacing={2} my={2.5} alignItems={'center'}>
            <Pagination
              count={meta.last_page}
              page={meta.current_page}
              onChange={handleChangePage}
              variant='outlined'
              shape='rounded'
              renderItem={item => (
                <PaginationItem
                  {...item}
                  sx={{
                    '&.MuiPaginationItem-root': {
                      color: config.colors.primary,
                      backgroundColor: '#f0f2f4',
                      border: 'none'
                    },
                    '&.Mui-selected': {
                      backgroundColor: config.colors.primary,
                      color: 'white'
                    },
                    '&:hover': {
                      backgroundColor: '#e1e4e8'
                    },
                    '& .MuiSvgIcon-root': {
                      color: config.colors.bodyColor
                    }
                  }}
                  slots={{
                    previous: KeyboardDoubleArrowLeftIcon,
                    next: KeyboardDoubleArrowRightIcon
                  }}
                />
              )}
            />
          </Stack>
        </>
      )}
      <AlertDialog
        open={openDialog}
        setOpen={setOpenDialog}
        handleAction={handleAction}
      />

      <Snackbar
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
        open={openSnack}
        onClose={handleSnackClose}
        message="Action completed successfully"
        autoHideDuration={3000}
      />

      <MainHomeWorkModal
        open={openModal}
        onClose={() => setOpenModal(false)}
        dataComponent={{ classes: classList, indexData, mode }}
        setMainHomeworks={setMainHomeworks}
        mainHomeworks={mainHomeworks}
        tags={tags}
        vocabularies={vocabularies}
        setVocabularies={setVocabularies}
        vocabulariesSelected={vocabulariesSelected}
        setVocabulariesSelected={setVocabulariesSelected}
      />
    </>
  );
}
