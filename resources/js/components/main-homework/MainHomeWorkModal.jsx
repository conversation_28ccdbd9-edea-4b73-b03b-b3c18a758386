import React, { useEffect, useState } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Box, Autocomplete, Checkbox, FormControlLabel
} from '@mui/material';
import { useForm, Controller, useWatch } from 'react-hook-form';
import { createMainHomework, update as updateMainHomework, duplicateMainHomework } from '../../services/admin/main-homeworks.js';
import { getList as getListSessions } from '../../services/admin/sessions';
import { toast } from 'react-toastify';
import TagVocabulary from '../exam/TagVocabulary';

export default function MainHomeWorkModal({ open, onClose, dataComponent,
  setMainHomeworks, mainHomeworks, tags,
  setTags, vocabularies, vocabulariesSelected,
  setVocabulariesSelected }) {
  const {
    control,
    handleSubmit,
    watch,
    setValue,
    reset,
    formState: { errors }
  } = useForm({
    defaultValues: {
      title: "",
      class: "",
      session: "",
      schedule_id: "",
      description: "",
      has_vocabulary: false,
    }
  });

  const [sessionOptions, setSessionOptions] = useState([]);
  const [scheduleOptions, setScheduleOptions] = useState([]);
  const [classes, setClasses] = useState([]);
  const [hiddenSession, setHiddenSession] = useState(false);
  const [isDisable, setIsDisable] = useState(false);
  const [tagSelected, setTagSelected] = useState(null);
  const selectedClass = watch("class");
  const hasVocabulary = useWatch({
    control,
    name: 'has_vocabulary',
  }) ?? false;

  useEffect(() => {
    if (dataComponent.classes.length > 0) {
      setClasses(dataComponent.classes);
    }
  }, [dataComponent.classes]);

  useEffect(() => {
    if (dataComponent.mode === 'edit') return;
    if (selectedClass) {
      setValue('schedule_id', null);
      setValue('session', null);
      const classData = classes.find(cls => cls.id === selectedClass);
      if (classData) {
        setHiddenSession(classData.type === 'Private');
        fetchSessions(selectedClass,classData.type === 'Private');
      }
    } else {
      setSessionOptions([]);
      setScheduleOptions([]);
    }
  }, [selectedClass]);

  useEffect(() => {
    if (!open) {
      clearData()
    }
    if (mainHomeworks[dataComponent.indexData]?.schedule && dataComponent?.mode === 'edit') {
      const schedule = mainHomeworks[dataComponent.indexData].schedule;
      setScheduleOptions([schedule]);
      setValue('schedule_id', mainHomeworks[dataComponent.indexData].schedule.id);
    } else if (!mainHomeworks[dataComponent.indexData]?.schedule && !mainHomeworks[dataComponent.indexData]?.session && dataComponent?.mode === 'edit') {
      const options = {
        is_no_set_value: true
      };
      fetchSessions(mainHomeworks[dataComponent.indexData]?.class, true, options);
      setValue('schedule_id', null);
    }
  }, [open, reset]);

  useEffect(() => {
    clearData();
    if (mainHomeworks.length > 0 && dataComponent.indexData !== null && mainHomeworks.length >= 0) {
      const dataMainHomework = mainHomeworks[dataComponent.indexData];
      if (dataMainHomework?.get_class?.type === 'Private') {
        setHiddenSession(true);
      } else {
        setHiddenSession(false);
      }
      if (dataComponent.mode === 'duplicate') {
        return;
      }
      reset({
        title: dataMainHomework.title,
        class: dataMainHomework.class,
        session: dataMainHomework.session,
        description: dataMainHomework.description,
        schedule_id: dataMainHomework.schedule_id,
        has_vocabulary: !!dataMainHomework.has_vocabulary,
      });
      setTagSelected(dataMainHomework.tag);
      setIsDisable(!!dataMainHomework.has_vocabulary);
    }
  }, [dataComponent.indexData, open]);

  const onSubmit = async (data) => {
    if(dataComponent.mode === 'duplicate'){
      const res = await duplicateMainHomework(mainHomeworks[dataComponent.indexData].id,data);
      onClose();
      window.location.href = '/admin/main-homeworks';
      return;
    }
    if (data.has_vocabulary) {
      data.vocabularies = vocabulariesSelected;
    }
    if (dataComponent.mode === 'edit') {
      try {
        const res = await updateMainHomework(mainHomeworks[dataComponent.indexData].id, data);
        setMainHomeworks(prev => {
          prev[dataComponent.indexData] = res.data.data;
          return prev;
        });
        toast.success('Updated successfully');
        onClose();
      } catch (err) {
        toast.error(err.response?.data?.message || 'Something went wrong!');
      }
      return;
    }
    try {
      const res = await createMainHomework(data);
      window.location.href = '/admin/homeworks/create?main_homework_id=' + res.data.data.id;
      onClose();
    } catch (err) {
      toast.error(err.response?.data?.message || 'Something went wrong!');
    }
    reset();
  };
  function clearData() {
    reset({
      title: '',
      class: '',
      session: '',
      schedule_id: '',
      description: '',
      has_vocabulary: false,
      vocabularies: [],
    });
    setSessionOptions([]);
    setIsDisable(false);
    setTagSelected(null);
  }

  const fetchSessions = async (selectedClass, isPrivate, options = {}) => {
    try {
      const res = await getListSessions({ class_id: selectedClass });
      const sessions = res.data || [];
      if (isPrivate) {
        setScheduleOptions(sessions ?? []);
        if (options?.is_no_set_value) return;
        setValue('schedule_id', sessions[0]?.id || null);
        return;
      }
      if (Array.isArray(sessions) && sessions.length > 0) {
        setSessionOptions(sessions);
        setValue('session', sessions[0]);
      } else {
        setValue('session', null);
        setSessionOptions([]);
      }
    } catch (err) {
      toast.error(err.response?.data?.message || 'Something went wrong!');
      setSessionOptions([]);
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>
        {dataComponent.mode.charAt(0).toUpperCase() + dataComponent.mode.slice(1)} Main Homework
        {dataComponent.mode === 'duplicate' && (
          <> {mainHomeworks[dataComponent.indexData].title} - Class: {mainHomeworks[dataComponent.indexData].get_class?.name}
            {mainHomeworks[dataComponent.indexData].session && (
              <> - Session: {mainHomeworks[dataComponent.indexData].session}</>
            )}
          </>
        )}
      </DialogTitle>
      <DialogContent dividers>
        <Box display="flex" flexDirection="column" gap={2} mt={1}>
          {dataComponent.mode !== 'duplicate' && (
            <Controller
              name="title"
              control={control}
              rules={{ required: "Title is required" }}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Title"
                  fullWidth
                  size="small"
                  error={!!errors.title}
                  helperText={errors.title?.message}
                />
              )}
            />
          )}

          <Controller
            name="class"
            control={control}
            rules={{ required: 'This field is required.' }}
            render={({ field }) => (
              <Autocomplete
                options={classes}
                getOptionLabel={(option) => option.name || ''}
                value={classes.find(cls => cls.id === field.value) || null}
                isOptionEqualToValue={(option, value) => option.id === value.id}
                onChange={(event, newValue) => {
                  field.onChange(newValue?.id || null);
                }}
                disabled={dataComponent.mode === 'edit'}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    size="small"
                    label="Class"
                    placeholder="-- Select Class --"
                    error={!!errors.class}
                    helperText={errors.class?.message}
                  />
                )}
              />
            )}
          />
          {!hiddenSession ? (
            <Controller
              name="session"
              control={control}
              rules={{ required: "Session is required" }}
              render={({ field }) => (
                <Autocomplete
                  options={sessionOptions}
                  getOptionLabel={(option) => `Session ${option}`}
                  value={field.value || null}
                  onChange={(_, value) => field.onChange(value)}
                  disabled={!sessionOptions.length}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label="Session"
                      placeholder="-- Select Session --"
                      size="small"
                      fullWidth
                      disabled={dataComponent.mode === 'edit'}
                      error={!!errors.session}
                      helperText={errors.session?.message}
                    />
                  )}
                />
              )}
            />
          ) : (
            <Controller
              name="schedule_id"
              control={control}
              rules={{ required: "Schedule is required" }}
              render={({ field }) => (
                <Autocomplete
                  options={scheduleOptions}
                  getOptionLabel={(option) => `${option.day} - ${option.date}`}
                  value={
                    scheduleOptions.find((opt) => opt.id === field.value) || null
                  }
                  onChange={(_, value) => field.onChange(value ? value.id : null)}
                  disabled={(dataComponent.mode === 'edit' && scheduleOptions.length === 1) || !scheduleOptions.length}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label="Schedule"
                      placeholder="-- Select Schedule --"
                      size="small"
                      fullWidth
                      disabled={dataComponent.mode === 'edit' && scheduleOptions.length === 1}
                      error={!!errors.schedule_id}
                      helperText={errors.schedule_id?.message}
                    />
                  )}
                />
              )}
            />
          )}

          {dataComponent.mode !== 'duplicate' && (
            <>
              <Controller
                name="has_vocabulary"
                control={control}
                defaultValue={false}
                render={({ field }) => (
                  <FormControlLabel
                    control={
                      <Checkbox
                        {...field}
                        checked={field.value}
                        disabled={isDisable}
                      />
                    }
                    label="Has vocabulary"
                  />
                )}
              />

              {hasVocabulary && (
                <TagVocabulary
                  tags={tags}
                  setTags={setTags}
                  tagSelected={tagSelected}
                  vocabularies={vocabularies}
                  vocabulariesSelected={vocabulariesSelected}
                  setVocabulariesSelected={setVocabulariesSelected}
                />
              )}

              <Controller
                name="description"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Description"
                    multiline
                    size="small"
                    rows={4}
                    fullWidth
                  />
                )}
              />
            </>
          )}
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button variant="contained" onClick={handleSubmit(onSubmit)}>
          {dataComponent.mode === 'duplicate' ? 'Duplicate' : 'Save'}
        </Button>
      </DialogActions>
    </Dialog>
  );
}
