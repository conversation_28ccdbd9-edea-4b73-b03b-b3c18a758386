import { TypeTask } from '../../../enums/HomeworkType';
import { getTaskTime } from './calculateTime';
import getUnansweredQuestionNumbers from './getUnansweredQuestionNumbers ';
import { processTaskQuestions } from './processTaskQuestions';
import { restoreTaskFromStorage } from './taskStorage';

export const initTasksFromData = (tasksData, isResultPage = false, accountId) => {
  return tasksData.map((task, index) => {
    const isFirstTask = index === 0;
    const detailTask = task?.task;
    const accountHomeWorkTask = task?.account_homework_tasks;
    const startTime = accountHomeWorkTask?.start_time;
    const endTime = accountHomeWorkTask?.end_time || null;
    const restoredAnswers = !endTime ? restoreTaskFromStorage(accountId, task?.homework_id, task?.task_id) : {};
    const processedQuestions = processTaskQuestions(detailTask?.questions, isResultPage, restoredAnswers);
    const scoreOfTask = isResultPage ? accountHomeWorkTask?.score : 0;
    const maxSubmitTime = task?.max_submit_time || 0;
    const calculateTime = startTime ? getTaskTime(startTime, maxSubmitTime, endTime, isResultPage) : 0;
    const calculateMinTime = task?.min_submit_time && startTime? getTaskTime(startTime, task?.min_submit_time, endTime, isResultPage) : 0;
    const previousTask = tasksData?.[index - 1];
    const previousAccountHomeworkTask = previousTask?.account_homework_tasks;

    const isLastTask = index === tasksData.length - 1;
    const totalScore = processedQuestions.reduce((sum, q) => sum + (q.score || 0), 0);
    const showUnlockTask = (detailTask?.type === TypeTask.REQUIRE_ADMIN_UNLOCK &&
        !isFirstTask &&
        !accountHomeWorkTask?.task_unlock &&
        previousAccountHomeworkTask?.start_time) || isFirstTask && detailTask?.type === TypeTask.REQUIRE_ADMIN_UNLOCK && accountHomeWorkTask?.task_unlock !== 1;
    const showStart =
      (detailTask?.type === TypeTask.NORMAL &&
        !isFirstTask &&
        previousAccountHomeworkTask?.start_time &&
        previousAccountHomeworkTask?.end_time) || accountHomeWorkTask?.task_unlock == 1 ||
      isFirstTask ||
      (detailTask?.type === TypeTask.REQUIRE_ADMIN_UNLOCK &&
        !isFirstTask &&
        accountHomeWorkTask?.task_unlock &&
        previousAccountHomeworkTask?.start_time &&
        previousAccountHomeworkTask?.end_time) ||
      (detailTask?.type === TypeTask.REQUIRE_ADMIN_UNLOCK &&
        isFirstTask &&
        accountHomeWorkTask?.task_unlock == 1);
    const unansweredQuestions = endTime ? getUnansweredQuestionNumbers(processedQuestions) : [];
    const requestUnlocks = detailTask?.unlock_request?.length > 0 ? detailTask?.unlock_request.find((rq) => rq.unlock_id === detailTask.id && rq.account_id === accountId) : null;
    return {
      ...task,
      title: detailTask?.title || '',
      description: task?.task.description || '',
      min_submit_time: task.min_submit_time || 0,
      maxSubmitTime: maxSubmitTime,
      questions: processedQuestions,
      examStarted: !!startTime,
      timeLeft: calculateTime,
      isLoaded: false,
      isLoading: false,
      isSubmitted: !!endTime,
      startTime: startTime,
      showStart: showStart,
      isCountDown: !endTime,
      scoreOfTask: scoreOfTask,
      end: endTime,
      showUnlockTask,
      isLastTask,
      totalScoreTask: totalScore,
      requestUnlocks: requestUnlocks,
      type: detailTask?.type,
      unansweredQuestions: unansweredQuestions,
      minTime: calculateMinTime,
    };
  });
};
