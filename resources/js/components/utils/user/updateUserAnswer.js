// utils/updateUserAnswer.js

import QuestionType from '../../../enums/QuestionType';

export const updateUserAnswerByType = (taskQuestions, questionId, value, indexOrIsChecked, typeOperation) => {
    return taskQuestions.map(q => {
        if (q.id !== questionId) return q;

        let updatedUserAnswer = q.userAnswer;
        const questionType = q.type;

        switch (questionType) {
            case QuestionType.SINGLE_CHOICE:
                updatedUserAnswer = [value];
                break;

            case QuestionType.MULTIPLE_CHOICE:
                const currentAnswers = Array.isArray(q.userAnswer) ? [...q.userAnswer] : [];
                const isChecked = indexOrIsChecked;
                if (isChecked) {
                    updatedUserAnswer = [...currentAnswers, value];
                } else {
                    updatedUserAnswer = currentAnswers.filter(item => item !== value);
                }
                break;

            case QuestionType.ESSAY:
            case QuestionType.FILL_IN_THE_BLANK:
            case QuestionType.TRANSCRIPTION:
                updatedUserAnswer = value;
                break;

            case QuestionType.MAKE_SENTENCE:
                updatedUserAnswer = Array.isArray(q.userAnswer) ? [...q.userAnswer] : [''];
                if (typeOperation === 'add') {
                    updatedUserAnswer.push('');
                } else if (typeOperation === 'delete') {
                    const indexToDelete = value;
                    if (updatedUserAnswer.length > 1) {
                        updatedUserAnswer = updatedUserAnswer.filter((_, idx) => idx !== indexToDelete);
                    } else {
                        updatedUserAnswer = [''];
                    }
                } else {
                    const inputIndex = indexOrIsChecked;
                    if (typeof inputIndex === 'number' && updatedUserAnswer[inputIndex] !== undefined) {
                        updatedUserAnswer[inputIndex] = value;
                    } else {
                        console.warn('Unexpected index or value for MAKE_SENTENCE:', {
                            inputIndex,
                            value
                        });
                        updatedUserAnswer = [value];
                    }
                }
                break;

            case QuestionType.MAKE_PARAGRAPH:
                updatedUserAnswer = Array.isArray(q.userAnswer)
                    ? [...q.userAnswer]
                    : [{ vocabulary_id: null, text: '' }];
                 
                if (typeOperation === 'add') {
                    updatedUserAnswer.push({ vocabulary_id: null, text: '' });
                } else if (typeOperation === 'delete') {
                    const indexToDelete = value;
                    if (updatedUserAnswer.length > 1) {
                        updatedUserAnswer = updatedUserAnswer.filter((_, idx) => idx !== indexToDelete);
                    } else {
                        updatedUserAnswer = [{ vocabulary_id: null, text: '' }];
                    }
                } else if (typeOperation === 'update_text') {
                    const inputIndex = indexOrIsChecked;
                   
                    if (typeof inputIndex === 'number') {
                        if (updatedUserAnswer[inputIndex]) {
                            updatedUserAnswer[inputIndex].text = value;
                        } else {
                            const newData = { vocabulary_id: null, text: value };
                            updatedUserAnswer.splice(inputIndex, 0, newData);
                        }
                    }
                } else if (typeOperation === 'update_vocab') {
                    const inputIndex = indexOrIsChecked;
                    if (typeof inputIndex === 'number') {
                        if (updatedUserAnswer[inputIndex]) {
                            updatedUserAnswer[inputIndex].vocabulary_id = value;
                        } else {
                            const newData = { vocabulary_id: value, text: '' };
                            updatedUserAnswer.splice(inputIndex, 0, newData);
                        }
                    }
                } else {
                    updatedUserAnswer = value;
                }

                break;

            case QuestionType.FLASH_CARD:
                updatedUserAnswer = value;
                break;
            default:
                console.warn('Unknown question type:', questionType);
                updatedUserAnswer = q.userAnswer;
                break;
        }

        return {
            ...q,
            userAnswer: updatedUserAnswer,
            isCorrect: null // Reset correctness state when answer changes
        };
    });
};
