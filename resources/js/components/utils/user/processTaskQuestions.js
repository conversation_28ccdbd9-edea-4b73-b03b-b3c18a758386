import QuestionType from '../../../enums/QuestionType';

export function processTaskQuestions(task, isResultPage, restoredAnswers = {}) {
  return task.map(q => {
    const restored = restoredAnswers?.[q.id];
    let userAnswer;
    if (restored !== undefined) {
      userAnswer = restored;
    } else if (q.type === QuestionType.SINGLE_CHOICE) {
      userAnswer =
        q.account_answers !== null && q.account_answers !== undefined
          ? [q.options?.[q.account_answers?.selected_options?.[0]]]
          : [];
    } else if (q.type === QuestionType.MULTIPLE_CHOICE) {
      userAnswer = Array.isArray(q.account_answers?.selected_options)
        ? q.account_answers.selected_options.map(index => q.options?.[index]).filter(Boolean)
        : [];
    } else if (q.type === QuestionType.MAKE_SENTENCE) {
      userAnswer = Array.isArray(q.account_answers?.selected_options) ? q.account_answers.selected_options : [''];
    } else if (q.type === QuestionType.MAKE_PARAGRAPH) {
      if (Array.isArray(q.account_answers?.selected_options)) {
        userAnswer = q.account_answers.selected_options;
      } else {
        userAnswer = (q.vocabularies || []).map(vocab => ({
          vocabulary_id: vocab.id,
          text: ''
        }));
      }
    }
    else if (
      q.type === QuestionType.FILL_IN_THE_BLANK ||
      q.type === QuestionType.TRANSCRIPTION
    ) {
      userAnswer =
        q.account_answers?.answer !== null && q.account_answers?.answer !== undefined
          ? q.account_answers.answer
          : '';
    }else if (q.type === QuestionType.ESSAY) {
      userAnswer = q.account_answers?.answer !== null && q.account_answers?.answer !== undefined
        ? q.account_answers.answer
        : null;
    }else{
      userAnswer = null;
    }

    return {
      ...q,
      options: typeof q.options === 'string' ? JSON.parse(q.options) : q.options,
      userAnswer,
      isCorrect: null,
      score: isResultPage ? q.account_answers?.score || 0 : 0,
      feedback: isResultPage ? q.account_answers?.feedback || '' : ''
    };
  });
}
