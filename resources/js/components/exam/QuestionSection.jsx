import React, { useEffect } from 'react';
import {
  Paper,
  Box,
  Typography,
  FormControlLabel,
  Radio,
  Checkbox,
  RadioGroup,
  FormGroup,
  TextField,
  Button,
  Select,
  MenuItem
} from '@mui/material';
import QuestionType from '../../enums/QuestionType';
import ClearIcon from '@mui/icons-material/Clear';
import { Controller } from 'react-hook-form';
import { FlashCard } from './FlashCard';
import { env } from '../../config/env';
import { Editor } from '@tinymce/tinymce-react';
import { tinymceInit } from '../../config/tinymce-init';

const QUESTION_TYPE_LABELS = {
  1: 'Single answer',
  2: 'Multiple answer',
  3: 'Fill in the blank',
  4: 'Essay',
  5: 'Make sentences',
  6: 'Make paragraph',
  7: 'Flashcard',
  8: 'Transcription',
};

export default function QuestionSection({
  questions,
  setQuestionRef,
  currentQuestion,
  onAnswerChange,
  disabled,
  isResultPage = false,
  currentTask,
  currentTaskIndex,
  control,
  isGrades = false,
  grades,
}) {
  useEffect(() => {
    const handleKeyDown = e => {
      if (disabled) {
        if ((e.ctrlKey || e.metaKey) && (e.key === 'c' || e.key === 'C')) {
          e.preventDefault();
        }
      }
    };
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [disabled]);


  const handleFlashcardChange = (questionId, optionValue) => {
    onAnswerChange(questionId, optionValue, null, QuestionType.FLASH_CARD);
  };

  const handleCheckboxChange = (questionId, optionValue, isChecked) => {
    if (disabled) return;
    onAnswerChange(questionId, optionValue, isChecked, QuestionType.MULTIPLE_CHOICE);
  };

  const handleSingleChoiceChange = (questionId, value) => {
    if (disabled) return;
    onAnswerChange(questionId, value, null, QuestionType.SINGLE_CHOICE);
  };

  const handleFillInTheBlankChange = (questionId, value) => {
    if (disabled) return;
    onAnswerChange(questionId, value, null, QuestionType.FILL_IN_THE_BLANK);
  };

  const handleEditorChange = (questionId, content, type = QuestionType.ESSAY) => {
    if (disabled) return;
    onAnswerChange(questionId, content, null, type);
  };

  const handleMakeParagraphChange = (questionId, index, field, value) => {
    if (disabled) return;

    const operation = field === 'text' ? 'update_text' : 'update_vocab';
    onAnswerChange(questionId, value, index, operation);
  };
  const handleMakeSentenceEssayChange = (questionId, index, value) => {
    if (disabled) return;
    onAnswerChange(questionId, value, index, QuestionType.MAKE_SENTENCE);
  };

  const handleAddSentenceEssayInput = questionId => {
    if (disabled) return;
    onAnswerChange(questionId, '', 0, 'add');
  };

  const handleDeleteSentenceEssayInput = (questionId, index) => {
    if (disabled) return;
    onAnswerChange(questionId, index, null, 'delete');
  };

  return (
    <Box display='flex' flexDirection='column' gap={3}>
      {questions.map((q, index) => (
        <Paper
          key={q.id}
          ref={el => setQuestionRef(index + 1, el)}
          elevation={1}
          sx={{
            p: 3.5,
            display: 'flex',
            flexDirection: 'column',
            gap: 2.5,
            scrollMarginTop: '300px',
            border: index + 1 === currentQuestion ? '2px solid' : '1px solid',
            borderColor: index + 1 === currentQuestion ? 'primary.main' : 'grey.300',
            boxShadow:
              index + 1 === currentQuestion
                ? '0px 4px 12px rgba(0,0,0,0.15)'
                : '0px 4px 8px rgba(0,0,0,0.1)',
            transition: 'border-color 0.3s, box-shadow 0.3s',
            userSelect: 'none'
          }}
          onContextMenu={e => e.preventDefault()}
        >
          <Box display='flex' justifyContent='space-between' alignItems='center'>
            <Typography sx={{ mb: 1, fontWeight: 'bold', fontSize: '0.9rem' }}>
              Question {index + 1} - {QUESTION_TYPE_LABELS[q.type]}
            </Typography>
            { q.type !== QuestionType.FLASH_CARD && (
              <Typography sx={{ mb: 1, fontWeight: 'bold', fontSize: '0.9rem', color: 'success.main' }}>
                {q.pivot.score || 0} pts
              </Typography>
            )}

          </Box>
          <Box sx={{ whiteSpace: 'pre-wrap' }}>
            <Editor
              tinymceScriptSrc="/tinymce/tinymce.min.js"
              init={{
                ...tinymceInit,
                menubar: false,
                toolbar: false,
                plugins: 'autoresize',
                autoresize_bottom_margin: 0,
                autoresize_min_height: 100,
                autoresize_max_height: 1000,
              }}
              disabled={true}
              value={q.content || ''}
            />
            {(q.type === QuestionType.MAKE_PARAGRAPH || q.type === QuestionType.MAKE_SENTENCE) && (
              <Typography
                variant='body1'
                dangerouslySetInnerHTML={{
                  __html:
                    q.vocabularies && q.vocabularies.length > 0
                      ? q.vocabularies.map(v => v.word).join(', ')
                      : ''
                }}
              />
            )}
            {q.type === QuestionType.MAKE_PARAGRAPH && (
              <Typography>Minimum number of words required in the paragraph: {q.min_word}</Typography>
            )}
          </Box>

          {/* Render inputs based on question type */}
          {q.type === QuestionType.SINGLE_CHOICE && (
            <RadioGroup
              value={Array.isArray(q.userAnswer) && q.userAnswer.length > 0 ? q.userAnswer[0] : ''}
              onChange={e => handleSingleChoiceChange(q.id, e.target.value)}
            >
              {Array.isArray(q.options) &&
                q.options.map((option, idx) => {
                  const isSelected = Array.isArray(q.userAnswer) && q.userAnswer.includes(option);
                  const isCorrect =
                    Array.isArray(q.correct_options) && q.correct_options.includes(idx);
                  let bgColor = 'transparent';
                  let tick = null;
                  if (isGrades || isResultPage) {
                    if (isSelected && isCorrect) {
                      tick = <span style={{ color: 'green', marginLeft: 15, fontSize: '16px' }}>✔</span>;
                    } else if (isSelected && !isCorrect) {
                      tick = <span style={{ color: 'red', marginLeft: 15, fontSize: '16px' }}>❌</span>;
                    } else if (!isSelected && isCorrect) {
                      tick = <span style={{ color: 'green', marginLeft: 15, fontSize: '16px' }}>✔</span>;
                    }
                  }

                  return (
                    <FormControlLabel
                      label={
                        <span>
                          {option}
                          {tick}
                        </span>
                      }
                      sx={{
                        backgroundColor: bgColor,
                        borderRadius: 2,
                        marginBottom: 1
                      }}
                      key={idx}
                      value={option}
                      control={<Radio disabled={disabled} />}
                    />
                  );
                })}
            </RadioGroup>
          )}

          {q.type === QuestionType.MULTIPLE_CHOICE && (
            <FormGroup>
              {Array.isArray(q.options) &&
                q.options.map((option, idx) => {
                  const isSelected = Array.isArray(q.userAnswer) && q.userAnswer.includes(option);
                  const isCorrect =
                    Array.isArray(q.correct_options) && q.correct_options.includes(idx);

                  let tick = null;
                  let bgColor = 'transparent';
                  if (isGrades || isResultPage) {
                    if (isSelected && isCorrect) {
                      tick = <span style={{ color: 'green', marginLeft: 15, fontSize: '16px' }}>✔</span>; // Correct and selected
                    } else if (isSelected && !isCorrect) {
                      tick = <span style={{ color: 'red', marginLeft: 15, fontSize: '16px' }}>❌</span>; // Incorrect and selected
                    } else if (!isSelected && isCorrect) {
                      tick = <span style={{ color: 'green', marginLeft: 15, fontSize: '16px' }}>✔</span>; // Correct but not selected
                    }
                  }

                  return (
                    <FormControlLabel
                      sx={{
                        backgroundColor: bgColor,
                        borderRadius: 2,
                        marginBottom: 1
                      }}
                      key={idx}
                      control={
                        <Checkbox
                          checked={
                            Array.isArray(q.userAnswer) && q.userAnswer.includes(option)
                          }
                          onChange={e => handleCheckboxChange(q.id, option, e.target.checked)}
                          disabled={disabled}
                        />
                      }
                      label={
                        <span>
                          {option}
                          {tick}
                        </span>
                      }
                    />
                  );
                })}
            </FormGroup>
          )}

          {q.type === QuestionType.FILL_IN_THE_BLANK && (
            <TextField
              fullWidth
              multiline
              rows={3}
              variant='outlined'
              value={q.userAnswer || ''}
              onChange={e => handleFillInTheBlankChange(q.id, e.target.value)}
              disabled={disabled}
              inputProps={{
                style: { userSelect: 'auto' }
              }}
            />
          )}

          {([QuestionType.ESSAY].includes(q.type)) && (
            <Editor
              tinymceScriptSrc="/tinymce/tinymce.min.js"
              init={{
                ...tinymceInit,
                browser_spellcheck: false,
                contextmenu: false,
                placeholder: 'Enter the explanation...',
              }}
              disabled={disabled}
              value={
                q.userAnswer === null && !disabled
                  ? q.sample_answer
                  : q.userAnswer || ''
              }
              onEditorChange={content => handleEditorChange(q.id, content)}
            />
          )}
          {q.type === QuestionType.TRANSCRIPTION && (
            <>
              <Box display="flex" justifyContent="center" mt={2}>
                <Box
                  component="img"
                  src={q.image_url}
                  alt="Preview"
                  sx={{
                    maxWidth: "100%",
                    height: "auto",
                    width: 600,
                    borderRadius: 2,
                    boxShadow: 3,
                  }}
                />
              </Box>
              <Editor
                tinymceScriptSrc="/tinymce/tinymce.min.js"
                init={{
                  plugins: 'anchor autolink charmap codesample emoticons image link lists media searchreplace table visualblocks wordcount',
                  toolbar: 'undo redo | blocks fontfamily fontsize | bold italic underline strikethrough | link image media table | align lineheight | numlist bullist indent outdent | emoticons charmap | removeformat',
                  license_key: 'gpl',
                }}
                disabled={disabled}
                value={q.userAnswer || ''}
                onEditorChange={content => handleEditorChange(q.id, content, QuestionType.TRANSCRIPTION)}
              />
            </>
          )}
          {q.type === QuestionType.MAKE_PARAGRAPH && (
            <Box>
              {Array.isArray(q.userAnswer) &&
                q.userAnswer.map((item, i) => (
                  <Box key={i} display="flex" alignItems="flex-start" mb={1} gap={1}>
                    <Select
                      size="small"
                      value={item.vocabulary_id ?? ''}
                      onChange={e => handleMakeParagraphChange(q.id, i, 'vocabulary_id', e.target.value)}
                      sx={{ minWidth: 150 }}
                      displayEmpty
                      renderValue={(selected) => {
                        if (!selected) {
                          return <em style={{ color: '#888' }}>Select vocabulary</em>;
                        }
                        const selectedVoca = q.vocabularies?.find(v => v.id == selected);
                        return selectedVoca?.word || '';
                      }}
                    >
                      <MenuItem value="" disabled>
                        <em>Select vocabulary</em>
                      </MenuItem>

                      {(q.vocabularies || []).map(v => (
                        <MenuItem key={v.id} value={v.id}>
                          {v.word}
                        </MenuItem>
                      ))}
                    </Select>

                    <TextField
                      fullWidth
                      variant='outlined'
                      size='small'
                      placeholder={`Paragraph ${i + 1}`}
                      value={item.text || ''}
                      onChange={e => handleMakeParagraphChange(q.id, i, 'text', e.target.value)}
                      disabled={disabled}
                      multiline
                      minRows={1}
                    />

                    <Button
                      onClick={() => onAnswerChange(q.id, i, null, 'delete')}
                      disabled={disabled || q.userAnswer.length <= q.min_word}
                    >
                      <ClearIcon />
                    </Button>
                  </Box>
                ))}

              {isResultPage ||
                (!currentTask?.isSubmitted && (
                  <Button
                    sx={{ mt: 2 }}
                    size='small'
                    variant='outlined'
                    onClick={() => onAnswerChange(q.id, null, null, 'add')}
                    disabled={disabled}
                  >
                    Add Paragraph
                  </Button>
                ))}
            </Box>
          )}

          {q.type === QuestionType.MAKE_SENTENCE && (
            <Box>
              {Array.isArray(q.userAnswer) &&
                q.userAnswer.map((answer, i) => (
                  <Box key={i} display='flex' alignItems='center' mb={1}>
                    {/* <Typography variant='body2' color='text.primary' fontSize={'0.875rem'}>
                      {i + 1}.
                    </Typography> */}
                    <TextField
                      fullWidth
                      variant='outlined'
                      size='xs'
                      placeholder={`Sentence ${i + 1}`}
                      value={answer}
                      onChange={e => handleMakeSentenceEssayChange(q.id, i, e.target.value)}
                      disabled={disabled}
                      multiline
                      minRows={1}
                      sx={{ mr: 1 }}
                    />
                    {/* <Button
                      onClick={() => handleDeleteSentenceEssayInput(q.id, i)}
                      disabled={disabled || q.userAnswer.length === 1}
                    >
                      <ClearIcon />
                    </Button> */}
                  </Box>
                ))}
              {/* {isResultPage ||
                (!currentTask?.isSubmitted && (
                  <Button
                    sx={{ mt: 3 }}
                    size='small'
                    variant='outlined'
                    onClick={() => handleAddSentenceEssayInput(q.id)}
                    disabled={disabled}
                  >
                    Add Sentence
                  </Button>
                ))} */}
            </Box>
          )}
          {q?.explanation && isResultPage && (
            <Box mt={2} p={2} bgcolor='grey.100' borderRadius={1} border='1px solid' borderColor='grey.300'>
              <Typography variant='subtitle2' color='text.secondary' gutterBottom>
                Explanation:
              </Typography>
              <Typography variant='body2' color='text.primary' dangerouslySetInnerHTML={{ __html: q.explanation }} />
            </Box>
          )}
          {
            q.type === QuestionType.FLASH_CARD && (
              <FlashCard
                grades={grades}
                question={q}
                handleFlashcardChange={handleFlashcardChange}
                disabled={disabled}
              />
            )
          }
          {isResultPage && (q.type === QuestionType.ESSAY || q.feedback) && (
            <Box
              sx={{
                mt: 3,
                p: 3,
                bgcolor: 'grey.50',
                borderRadius: 2,
                border: '1px solid',
                borderColor: 'grey.200',
                boxShadow: '0 2px 8px rgba(0,0,0,0.06)',
                position: 'relative',
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '4px',
                  height: '100%',
                  bgcolor: 'info.main',
                  borderRadius: '2px 0 0 2px'
                }
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Typography
                  variant='h6'
                  sx={{
                    fontSize: '1rem',
                    fontWeight: 600,
                    color: 'info.main',
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1
                  }}
                >
                  💬 Feedback
                </Typography>
              </Box>
              {q.type === QuestionType.ESSAY ? (
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  {q.account_answers?.account_essay_feedback.map((item, index) => (
                    <Box
                      key={index}
                      sx={{
                        p: 2.5,
                        bgcolor: 'white',
                        borderRadius: 1.5,
                        border: '1px solid',
                        borderColor: 'grey.100',
                        boxShadow: '0 1px 4px rgba(0,0,0,0.04)',
                        position: 'relative'
                      }}
                    >
                      {q.evaluation_criteria && q.evaluation_criteria[index] && (
                        <Typography
                          variant="subtitle2"
                          sx={{
                            fontWeight: 600,
                            color: 'primary.main',
                            mb: 1.5,
                            display: 'flex',
                            alignItems: 'center',
                            gap: 1
                          }}
                        >
                          <Box
                            sx={{
                              width: 6,
                              height: 6,
                              borderRadius: '50%',
                              bgcolor: 'primary.main'
                            }}
                          />
                          {q.evaluation_criteria[index].title}
                        </Typography>
                      )}
                      <Typography
                        variant="body1"
                        component="div"
                        dangerouslySetInnerHTML={{ __html: item.feedback }}
                        sx={{
                          color: 'text.primary',
                          lineHeight: 1.6,
                          '& p': { margin: '0 0 8px 0' },
                          '& p:last-child': { margin: 0 }
                        }}
                      />
                      <Typography
                        variant="caption"
                        sx={{
                          position: 'absolute',
                          top: 8,
                          right: 12,
                          bgcolor: item.score >= (q.evaluation_criteria?.[index]?.rate * 0.8) ? 'success.main' :
                            item.score >= (q.evaluation_criteria?.[index]?.rate * 0.6) ? 'warning.main' : 'error.main',
                          color: 'white',
                          px: 1.5,
                          py: 0.5,
                          borderRadius: 1,
                          fontSize: '0.7rem',
                          fontWeight: 600
                        }}
                      >
                        {item.score}/{q.evaluation_criteria?.[index]?.rate || 0}
                      </Typography>
                    </Box>
                  ))}
                </Box>
              ) : (
                <Box
                  sx={{
                    p: 2.5,
                    bgcolor: 'white',
                    borderRadius: 1.5,
                    border: '1px solid',
                    borderColor: 'grey.100',
                    boxShadow: '0 1px 4px rgba(0,0,0,0.04)'
                  }}
                >
                  <Typography
                    variant='body1'
                    sx={{
                      color: 'text.primary',
                      lineHeight: 1.6
                    }}
                  >
                    {q.feedback}
                  </Typography>
                </Box>
              )}
            </Box>
          )}

          {isGrades && (
            <Box
              mt={2}
              p={2}
              bgcolor='grey.100'
              borderRadius={1}
              border='1px solid'
              borderColor='grey.300'
              display={'flex'}
              flexDirection={'column'}
              alignItems={'flex-end'}
              gap={2}
            >
              {q.type > 3 && (
                <Controller
                  name={`tasks.${currentTaskIndex}.task.questions.${index}.account_answers.score`}
                  control={control}
                  rules={{
                    required: 'This field is required.'
                  }}
                  render={({ field, fieldState: { error } }) => (
                    <div>
                      <TextField
                        {...field}
                        fullWidth
                        label='Score'
                        variant='outlined'
                        size='small'
                        type='number'
                        inputProps={{ min: 0, max: q?.pivot?.score }}
                        sx={{ width: '120px' }}
                        error={!!error}
                      />
                      <Typography variant='body2' color='error'>
                        {error?.message}
                      </Typography>
                    </div>
                  )}
                />
              )}

              <Controller
                name={`tasks.${currentTaskIndex}.task.questions.${index}.account_answers.feedback`}
                control={control}
                render={({ field, fieldState: { error } }) => (
                  <>
                    <TextField
                      {...field}
                      fullWidth
                      label='Feedback'
                      variant='outlined'
                      size='small'
                      multiline
                    />
                  </>
                )}
              />
            </Box>
          )}
        </Paper>
      ))}
    </Box>
  );
}
