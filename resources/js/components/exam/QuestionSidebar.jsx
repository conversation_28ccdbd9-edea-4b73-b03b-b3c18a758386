import React, { useEffect, useRef, useState } from 'react';
import {
  <PERSON>,
  Typography,
  Button,
  IconButton,
  Divider,
  Paper,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Grid, MenuItem, Autocomplete, FormControl, InputLabel, Select, FormHelperText
} from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import AddIcon from '@mui/icons-material/Add';
import PublishIcon from '@mui/icons-material/Publish';

import SortableDialog from './SortableDialog';
import { useForm, Controller } from "react-hook-form";
import SaveIcon from "@mui/icons-material/Save";
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { validateQuestions } from "../utils/validateQuestions";
import QuestionType from "../../enums/QuestionType";
import { create as createQuestion, getList as getQuestions } from '../../services/admin/questions';
import { create as createTask, deleteTask, update as updateTask } from '../../services/admin/tasks';
import {
  create as createTaskHomework,
  sortTasks,
  update as updateTaskHomework,
  deleteTaskHomework,
  createTasKImport
} from '../../services/admin/task-homework';
import { TaskType, TaskTypeText } from '../../enums/TaskType';
import { PageType } from "../../enums/PageType";
import TagVocabulary from './TagVocabulary';

export const QuestionSidebar = ({
  tasks,
  setTasks,
  selectedTaskId,
  setSelectedTaskId,
  setSelectedQuestionIndex,
  selectedQuestionIndex,
  homeworkId,
  questions,
  setQuestions,
  listTasksImport,
  typePage,
  taskId,
  tags,
  setTags,
  vocabularies,
  vocabulariesSelected,
  setVocabulariesSelected,
  tagSelected,
  setTagSelected,
  typeQuestion,
  classOptions,
  sessionOptions
}) => {
  const [openModal, setOpenModal] = useState(false);
  const { control, handleSubmit, reset, setValue,watch, getValues, formState: { errors, isSubmitting } } = useForm();

  const [editMode, setEditMode] = useState(false);
  const [openImport, setOpenImport] = useState(false);

  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [taskToDeleteId, setTaskToDeleteId] = useState(null);

  const [openSortQuestionModal, setOpenSortQuestionModal] = useState(false);
  const [openSortTaskModal, setOpenSortTaskModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const [hiddenSession, setHiddenSession] = useState(true);
  const validTypes = [QuestionType.MAKE_SENTENCE, QuestionType.MAKE_PARAGRAPH, QuestionType.FLASH_CARD];
  const watchClassId = watch('class_id');
  const watchSession = watch('session');
  const initialSession = useRef(null);
  const initialClassId = useRef(null);

  useEffect(() => {
    const selectedClass = classOptions.find(cls => cls.id === watchClassId);
    if(!selectedClass) setHiddenSession(true);
    if(!selectedClass || !watchClassId) return;
    if(selectedClass.type === 'Private'){
      setHiddenSession(true);
      setValue('session', null);
    }else{
      setHiddenSession(false);
    }

    if (initialSession.current === null) {
      initialSession.current = watchSession;
    }
    if (initialClassId.current === null) {
      initialClassId.current = watchClassId;
    }

    if ((initialSession.current !== watchSession) || (initialClassId.current !== watchClassId) || !taskId) {
      const title = selectedClass.type === 'Private'
        ? selectedClass.name
        : `${selectedClass?.terms?.type}-${selectedClass?.terms?.year}-${selectedClass.level}-${selectedClass.name}${watchSession ? `-S${watchSession}` : ''}`;
      setValue('title', title);
    }
  }, [watchClassId, watchSession]);


  const onSubmit = async ({ title, criteria, min_submit_time, max_submit_time, type, description, task_id, root, class_id, session }) => {
    if (typePage === PageType.TASK) {
      if (taskId) {
        const taskData = {
          title,
          criteria,
          type,
          is_bank: 1,
          description,
          class_id,
          session
        }
        const { data: { data: dataUpdate } } = await updateTask(taskId, taskData);
        toast.success('Updated successfully');
        setTasks(prev => {
          prev[selectedTaskId] = dataUpdate
          return prev
        })
        setOpenModal(false);
        resetModalState();
        return;
      }
      const { data: { data: task } } = await createTask({
        title,
        criteria,
        is_bank: 1,
        type,
        description,
        class_id,
        session
      });
      setTasks(prev => [...prev, task]);
      setSelectedTaskId(tasks.length);
      setOpenModal(false)
      window.location.href = `/admin/tasks/${task.id}/edit`;
      return;
    }
    if (openImport) {
      const taskImport = {
        'task_id': task_id.id,
        'homework_id': homeworkId,
        'min_submit_time': min_submit_time,
        'max_submit_time': max_submit_time,
      }
      const { data: { data: task, tag } } = await createTasKImport(taskImport);
      if (tag) {
        setTagSelected(tag);
      }
      task.min_submit_time = taskImport.min_submit_time;
      task.max_submit_time = taskImport.max_submit_time;
      setTasks(prev => {
        const updated = [...prev, task];
        setQuestions([]);
        if (updated.length === 1) {
          setSelectedTaskId(-1);
          setTimeout(() => {
            setSelectedTaskId(0);
          }, 0);
        } else {
          setSelectedTaskId(updated.length - 1);
        }
        return updated;
      });
      toast.success('Import successfully');
      setOpenModal(false);
      return
    }
    const taskData = {
      title,
      criteria,
      is_bank: 0,
      type,
      description,
      root
    }

    const taskHomeworkData = {
      homework_id: homeworkId,
      min_submit_time,
      max_submit_time
    }
    if (!editMode) {
      const { data: { data: task } } = await createTask(taskData);
      if (homeworkId) {
        const { data: { data: taskHomework } } = await createTaskHomework({ ...taskHomeworkData, task_id: task.id });
        task.min_submit_time = taskHomework.min_submit_time;
        task.max_submit_time = taskHomework.max_submit_time;
        setQuestions([]);
        setTasks(prev => [...prev, task]);
        setSelectedTaskId(tasks.length);
      }
      toast.success('Created successfully');
      setOpenModal(false);
      resetModalState();
    } else {
      const taskId = tasks[selectedTaskId]?.id
      taskData.homework_id = homeworkId
      taskData.min_submit_time = min_submit_time
      taskData.max_submit_time = max_submit_time
      const { data: { data: data } } = await updateTaskHomework(taskId, taskData);
      taskData.id = data.id
      setTasks(prev => {
        prev[selectedTaskId] = taskData
        return prev
      })
      toast.success('Updated successfully');
      setOpenModal(false);
      resetModalState();
    }
  };

  const resetModalState = () => {
    setEditMode(false);
    reset({
      title: '',
      criteria: '',
      min_submit_time: '',
      max_submit_time: '',
      type: '',
      description: ''
    });
  };

  const handleClickEdit = () => {
    if (tasks[selectedTaskId]) {
      reset(tasks[selectedTaskId]);
      setEditMode(true);
      setOpenModal(true);
      setOpenImport(false);
    }
  };

  const handleDeleteConfirmed = async () => {
    if (taskId) {
      await deleteTask(tasks[taskToDeleteId]?.id);
      setDeleteConfirmOpen(false);
      toast.success('Deleted successfully');
      window.location.href = `/admin/tasks`;
      return;
    }
    const data = {
      homework_id: homeworkId,
    }
    await deleteTaskHomework(tasks[taskToDeleteId]?.id, data);
    const taskData = tasks.filter((_, index) => index !== taskToDeleteId);
    setTasks(taskData);
    setTaskToDeleteId(null);
    if (taskData){
      if(selectedTaskId === 0){
        setSelectedTaskId(-1);
        setTimeout(() => {
          setSelectedTaskId(0);
        }, 0);
      }else{
        setSelectedTaskId(0);
      }
    }
    if (!taskData) {
      setQuestions([])
    }
    toast.success('Deleted successfully');
    setDeleteConfirmOpen(false);
  };

  const handleAddQuestion = () => {
    const newQuestion = {
      type: QuestionType.SINGLE_CHOICE,
      content: '',
      sample_answer: '',
      explanation: '',
      score: 2,
      correct_options: [],
      words: [],
      min_word: 1,
      answer: [],
      options: [
        { value: '' },
        { value: '' },
        { value: '' },
        { value: '' },
      ],
      image_url: ''
    };
    setQuestions(prev => [...prev, newQuestion]);
    setSelectedQuestionIndex(questions.length);
  };

  const handleSubmitQuestion = async () => {
    if(questions.length <= 0){
      toast.error('Must have at least 1 question');
      return
    }
    setLoading(true);
    const [questionsValidate, error] = validateQuestions(questions, typePage);
    if (error) {
      setQuestions(questionsValidate);
      toast.error('Please fill in all required fields');
      setLoading(false);
      return;
    }
    try {
      await createQuestion({ questions, task_id: tasks[selectedTaskId]?.id, vocabularies: vocabulariesSelected });
      const questionsData = await getQuestions({ task_id: tasks[selectedTaskId]?.id });
      setQuestions(questionsData?.data?.data);
      toast.success('Save successfully');
    } catch (err) {
      toast.error(err.response?.data?.message || 'Something went wrong!');
    }
    setLoading(false);
  };

  const handleSaveSortedQuestions = async (sortedItems) => {
    if (questions.length === 0) {
      toast.error('Must have at least 1 question');
      setOpenSortQuestionModal(false);
      return;
    }
    const [questionsValidate, error] = validateQuestions(questions, typePage);
    if (error) {
      setQuestions(questionsValidate)
      toast.error('Please fill in all required fields');
      setOpenSortQuestionModal(false);
      return;
    }
    try {
      await createQuestion({ questions: sortedItems, task_id: tasks[selectedTaskId]?.id, vocabularies: vocabulariesSelected });
      const questionsData = await getQuestions({ task_id: tasks[selectedTaskId]?.id });
      setQuestions([...questionsData?.data?.data]);
      setSelectedQuestionIndex(0);
      toast.success('Sorted successfully');
    } catch (err) {
      toast.error(err.response?.data?.message || 'Something went wrong!');
    }
    setOpenSortQuestionModal(false);
  };

  const handleSaveSortedTasks = async (sortedItems) => {
    const { data: { data: tasks } } = await sortTasks({ homework_id: homeworkId, tasks: sortedItems });
    toast.success('Sorted successfully');
    setTasks(tasks);
    setOpenSortTaskModal(false);
  };

  const changeQuestionHandler = (index) => {
    questions[index].hasError = false;
    setSelectedQuestionIndex(index)
  }

  const handleOpenSortQuestionModal = () => {
    setQuestions([...questions]);
    setOpenSortQuestionModal(true);
  };

  const handleOpenSortTaskModal = () => {
    setTasks([...tasks]);
    setOpenSortTaskModal(true);
  };

  return (
    <>
      <Paper elevation={3} sx={{ p: 2 }}>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="subtitle1" fontWeight="bold">List Tasks</Typography>
          {!taskId && (
            <>
              <Box mt={1} display="flex" gap={1}>
                {(typePage !== PageType.TASK && (
                  <>
                    <Button
                      size="small"
                      variant="contained"
                      startIcon={<PublishIcon />}
                      onClick={() => {
                        setEditMode(false);
                        setOpenModal(true);
                        setOpenImport(true);
                        resetModalState();
                      }}
                    >
                      Import
                    </Button>
                  </>
                ))}
                <Button
                  size="small"
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={() => {
                    reset({
                      title: '',
                      criteria: 'Technical Writing',
                      min_submit_time: 100,
                      max_submit_time: 300,
                      type: TaskType.NORMAL,
                      description: ''
                    });
                    setEditMode(false);
                    setOpenModal(true);
                    setOpenImport(false);
                    // resetModalState();
                  }}
                >
                  Add new task
                </Button>

                {(typePage !== PageType.TASK && (
                  <>
                    <Button size="small" variant="outlined" onClick={() => handleOpenSortTaskModal()}>
                      Sort tasks
                    </Button>
                  </>
                ))}
              </Box>
            </>
          )}
        </Box>

        <Box mt={1} display="flex" gap={1} flexWrap="wrap">
          {tasks.length > 0 ? (
            tasks.map((task, index) => (
              <Button
                key={index}
                variant={index === selectedTaskId ? "contained" : "outlined"}
                size="small"
                onClick={() => setSelectedTaskId(index)}
              >
                {task.title}
              </Button>
            ))
          ) : (
            <Box p={1}>
              <Typography color="text.secondary">Tasks not found</Typography>
            </Box>
          )}
        </Box>

        {tasks.length > 0 && selectedTaskId !== null && !!tasks[selectedTaskId] && (
          <>
            {typePage === PageType.TASK && tasks[selectedTaskId].class_id && (
              <Box mt={2}>
                <Typography>Class: {tasks[selectedTaskId]?.related_class?.name}</Typography>
              </Box>
            )}
            {typePage === PageType.TASK && tasks[selectedTaskId].session && (
              <Box mt={2}>
                <Typography>Session: {tasks[selectedTaskId].session}</Typography>
              </Box>
            )}
            <Box mt={2}>
              <Typography>Evaluation criteria: {tasks[selectedTaskId]?.criteria}</Typography>
            </Box>

            <Box mt={2}>
              <Typography>Type: {tasks[selectedTaskId]?.type ? TaskTypeText[tasks[selectedTaskId].type] : ''}</Typography>
            </Box>

            {(typePage !== PageType.TASK) && (
              <>
                {tasks[selectedTaskId].min_submit_time > 0 && (
                  <Box mt={2}>
                    <Typography>Time limit: {tasks[selectedTaskId].min_submit_time + " seconds"}</Typography>
                  </Box>
                )}

                <Box mt={2}>
                  <Typography>Time Duration: {tasks[selectedTaskId].max_submit_time + " seconds"}</Typography>
                </Box>
              </>
            )}

            <Box mt={2}>
              <Typography>Description: {tasks[selectedTaskId]?.description}</Typography>
            </Box>
            {(typePage !== PageType.TASK && tasks[selectedTaskId].root > 0 ) && (
              <>
                <Box mt={2}>
                  <Typography>
                    Link task import:
                    <Typography
                      component="a"
                      href={`/admin/tasks/${tasks[selectedTaskId].root}/edit`}
                      target="_blank"
                      rel="noopener noreferrer"
                      color="primary"
                      sx={{ textDecoration: 'underline', cursor: 'pointer' }}
                    >
                      Detail
                    </Typography>
                  </Typography>
                </Box>
              </>
            )}

            <Box mt={1} display="flex" gap={1}>
              <IconButton color="primary" onClick={handleClickEdit}>
                <EditIcon />
              </IconButton>
              <IconButton color="error" onClick={() => {
                setDeleteConfirmOpen(true);
                setTaskToDeleteId(selectedTaskId);
              }}>
                <DeleteIcon />
              </IconButton>
            </Box>

            <Divider sx={{ my: 2 }} />
            <Box sx={{
              display: questions.length > 0 && validTypes.includes(typeQuestion)
                ? 'block'
                : 'none'
            }}
            >
              <TagVocabulary
                tags={tags}
                setTags={setTags}
                tagSelected={tagSelected}
                vocabularies={vocabularies}
                vocabulariesSelected={vocabulariesSelected}
                setVocabulariesSelected={setVocabulariesSelected}
              />
            </Box>

            <Typography variant="subtitle1">List questions</Typography>

            <Box mt={1} display="flex" gap={1}>
              <Button variant="contained" onClick={() => handleAddQuestion()} startIcon={<AddIcon />}>
                Add new question
              </Button>
              <Button variant="outlined" onClick={() => handleOpenSortQuestionModal()}>
                Sort questions
              </Button>
              <Button
                type="submit"
                variant="contained"
                color="primary"
                size="small"
                startIcon={<SaveIcon />}
                sx={{ height: 40 }}
                onClick={handleSubmitQuestion}
                disabled={loading}
              >
                {loading ? 'Saving' : 'Save'}
              </Button>
            </Box>

            <Box mt={2}>
              {!questions.length ? (
                <Typography color="text.secondary">Questions not found</Typography>
              ) : (
                <Grid container spacing={1}>
                  {questions.map((question, index) => {
                    const isActive = selectedQuestionIndex === index;
                    const hasError = question.hasError;
                    return (
                      <Grid key={question.id ?? question.uid ?? index}>
                        <Paper
                          elevation={isActive ? 4 : 1}
                          sx={{
                            width: 40,
                            height: 40,
                            p: 0,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            cursor: 'pointer',
                            border: hasError
                              ? '2px solid red'
                              : isActive
                                ? '2px solid #1976d2'
                                : '1px solid #ddd',
                            backgroundColor: hasError
                              ? '#fdecea'
                              : isActive
                                ? '#e3f2fd'
                                : '#fff',
                            transition: 'all 0.2s ease-in-out',
                            '&:hover': {
                              backgroundColor: hasError
                                ? '#f9d0cc'
                                : isActive
                                  ? '#bbdefb'
                                  : '#f5f5f5',
                            },
                          }}
                          onClick={() => (changeQuestionHandler(index))}
                        >
                          <Typography
                            fontWeight={isActive ? 'bold' : 'normal'}
                            color={hasError ? 'error' : 'inherit'}
                          >
                            {index + 1}
                          </Typography>
                        </Paper>
                      </Grid>
                    );
                  })}
                </Grid>
              )}
            </Box>
          </>
        )}
      </Paper>

      <Dialog open={openModal} onClose={() => setOpenModal(false)}>
        <DialogTitle>
          {openImport
            ? "Import Task"
            : editMode
              ? "Edit Task"
              : "Add New Task"}
        </DialogTitle>
        <form onSubmit={handleSubmit(onSubmit)}>
          <DialogContent>
            <Controller
              name="root"
              control={control}
              defaultValue={0}
              render={({ field }) => (
                <input type="hidden" {...field} />
              )}
            />
            {typePage === PageType.TASK && (
              <>
                <Box>
                  <Controller
                    name="class_id"
                    control={control}
                    render={({ field }) => (
                      <Autocomplete
                        options={classOptions}
                        getOptionLabel={(option) => option.name || ''}
                        value={classOptions.find(cls => cls.id === field.value) || null}
                        isOptionEqualToValue={(option, value) => option.id === value.id}
                        onChange={(event, newValue) => {
                          field.onChange(newValue?.id || null);
                        }}
                        renderInput={(params) => (
                          <TextField
                            {...params}
                            size="small"
                            label="Class"
                            placeholder="Select Class"
                            error={!!errors.class_id}
                            helperText={errors.class_id?.message}
                          />
                        )}
                      />
                    )}
                  />
                </Box>
                {!hiddenSession && (
                  <Box mt={1}>
                    <Controller
                      name='session'
                      control={control}
                      rules={{
                        validate: (value) => {
                          if (watchClassId && !value) return 'Please select session';
                          return true;
                        }
                      }}
                      render={({ field }) => (
                        <FormControl fullWidth size="small" error={!!errors.session}>
                          <InputLabel id="session-label">Session</InputLabel>
                          <Select
                            {...field}
                            labelId="session-label"
                            label="Session"
                          >
                            {sessionOptions.map(option => (
                              <MenuItem key={option.value} value={option.value}>{option.label}</MenuItem>
                            ))}
                          </Select>
                          <FormHelperText>{errors.session?.message}</FormHelperText>
                        </FormControl>
                      )}
                    />
                  </Box>
                )}
              </>
            )}
            {!openImport && (
              <>
                <Controller
                  name="title"
                  control={control}
                  rules={{
                    required: "This field is required.",
                    validate: (value) => value.trim() !== "" || "This field is required."
                  }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Name Task"
                      fullWidth
                      margin="dense"
                      size="small"
                      error={!!errors.title}
                      helperText={errors.title?.message}
                    />
                  )}
                />

                <Controller
                  name="type"
                  control={control}
                  defaultValue="1"
                  rules={{ required: "This field is required." }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      select
                      label="Type task"
                      fullWidth
                      margin="dense"
                      size="small"
                      error={!!errors.type}
                      helperText={errors.type?.message}
                    >
                      {Object.entries(TaskTypeText).map(([value, label]) => (
                        <MenuItem key={value} value={Number(value)}>
                          {label}
                        </MenuItem>
                      ))}
                    </TextField>
                  )}
                />
                <Controller
                  name="criteria"
                  control={control}
                  rules={{
                    required: "This field is required.",
                    validate: (value) => value.trim() !== "" || "This field is required."
                  }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Evaluation Criteria"
                      fullWidth
                      margin="dense"
                      size="small"
                      error={!!errors.criteria}
                      helperText={errors.criteria?.message}
                    />
                  )}
                />
              </>
            )}

            {openImport && (
              <>
                <Controller
                  name="task_id"
                  control={control}
                  rules={{ required: "Please select a task to import." }}
                  render={({ field }) => (
                    <Autocomplete
                      options={listTasksImport}
                      getOptionLabel={(option) => option.title || ''}
                      isOptionEqualToValue={(option, value) => option?.id === value?.id}
                      value={field.value || null}
                      onChange={(_, newValue) => field.onChange(newValue)}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          label="Select Task to Import"
                          margin="dense"
                          fullWidth
                          size="small"
                          error={!!errors.importedTask}
                          helperText={errors.importedTask?.message}
                        />
                      )}
                    />
                  )}
                />
              </>
            )}

            {typePage !== PageType.TASK && (
              <>
                <Controller
                  name="min_submit_time"
                  control={control}
                  rules={{
                    validate: (value) =>
                      value === null ||
                      value === "" ||
                      Number(value) > 0 ||
                      "Minimum submit time must be greater than 0.",
                  }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Minimum Submit Time (seconds)"
                      type="number"
                      fullWidth
                      margin="dense"
                      size="small"
                      onKeyDown={(e) => {
                        if (['.', ',', '-', '+', 'e', 'E'].includes(e.key)) {
                          e.preventDefault();
                        }
                      }}
                      error={!!errors.min_submit_time}
                      helperText={errors.min_submit_time?.message}
                    />
                  )}
                />

                <Controller
                  name="max_submit_time"
                  control={control}
                  rules={{
                    required: "This field is required.",
                    validate: (value) => {
                      const min = Number(getValues("min_submit_time"));
                      const max = Number(value);
                      if (max <= 0) return "Maximum duration must be greater than 0.";
                      if (max <= min) return "Maximum duration must be greater than minimum submit time.";
                      return true;
                    },
                  }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Maximum Duration (seconds)"
                      type="number"
                      fullWidth
                      margin="dense"
                      size="small"
                      onKeyDown={(e) => {
                        if (['.', ',', '-', '+', 'e', 'E'].includes(e.key)) {
                          e.preventDefault();
                        }
                      }}
                      error={!!errors.max_submit_time}
                      helperText={errors.max_submit_time?.message}
                    />
                  )}
                />
              </>
            )}

            {!openImport && (
              <>
                <Box mt={1}>
                  <Controller
                    name="description"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label="Description"
                        multiline
                        fullWidth
                        variant="outlined"
                        size="small"
                        error={!!errors.description}
                        helperText={errors.description?.message}
                      />
                    )}
                  />
                </Box>
              </>
            )}
          </DialogContent>

          <DialogActions>
            <Button onClick={() => setOpenModal(false)}>Cancel</Button>
            <Button type="submit" disabled={isSubmitting}>{isSubmitting ? 'Saving...' : 'Save'}</Button>
          </DialogActions>
        </form>
      </Dialog>

      <Dialog open={deleteConfirmOpen} onClose={() => setDeleteConfirmOpen(false)}>
        <DialogTitle>Delete Task?</DialogTitle>
        <DialogContent>
          <Typography>Do you want to delete this task?</Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteConfirmOpen(false)}>Cancel</Button>
          <Button color="error" onClick={handleDeleteConfirmed}>Delete</Button>
        </DialogActions>
      </Dialog>

      <SortableDialog
        open={openSortQuestionModal}
        onClose={() => setOpenSortQuestionModal(false)}
        onSave={handleSaveSortedQuestions}
        items={questions}
        title="Sort questions"
      />

      <SortableDialog
        open={openSortTaskModal}
        onClose={() => setOpenSortTaskModal(false)}
        onSave={handleSaveSortedTasks}
        items={tasks}
        title="Sort tasks"
      />
    </>
  );
};
