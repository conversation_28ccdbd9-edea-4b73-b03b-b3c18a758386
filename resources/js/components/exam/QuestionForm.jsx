import { useEffect, useState } from 'react';
import { Grid } from '@mui/material';
import { QuestionSidebar } from './QuestionSidebar';
import { QuestionEditor } from './QuestionEditor';
import { getHomeworkTasks } from '../../services/admin/task-homework';
import { useParams } from 'react-router-dom';
import { getList as getQuestions } from '../../services/admin/questions';
import { getListTaskImport, getTask } from "../../services/admin/tasks";
import { PageType } from "../../enums/PageType";
import { listVocabularyHomework, listAll as listAllTag } from '../../services/admin/tag-vocabularies';
import { getEvaluationCriteria } from '../../services/admin/question-evaluation-criteria';
import { getList } from '../../services/admin/classes';

export default function QuestionForm({ typePage, taskId }) {
  const [criteria, setCriteria] = useState([]);
  const [tasks, setTasks] = useState([]);
  const [selectedQuestionIndex, setSelectedQuestionIndex] = useState(null);
  const [selectedTaskId, setSelectedTaskId] = useState(0);
  const [selectedTask, setSelectedTask] = useState(null);
  const [questions, setQuestions] = useState([]);
  const [listTasksImport, setListTasksImport] = useState([]);
  const { id: homeworkId } = useParams();
  const [vocabularies, setVocabularies] = useState([]);
  const [tags, setTags] = useState([]);
  const [vocabulariesSelected, setVocabulariesSelected] = useState([]);
  const [tagSelected, setTagSelected] = useState(null);
  const [typeQuestion, setTypeQuestion] = useState(null);
  const [classOptions, setClassOptions] = useState([]);
  const [sessionOptions, setSessionOptions] = useState([]);


  useEffect(() => {
    const fetchQuestion = async () => {
      const { data: { data: questions } } = await getQuestions({ task_id: tasks[selectedTaskId]?.id });
      setQuestions(questions);
      setSelectedQuestionIndex(questions.length ? 0 : null);
    }
    fetchQuestion();
    setSelectedTask(tasks[selectedTaskId])
  }, [selectedTaskId]);


  useEffect(() => {
    if (listTasksImport.length || typePage === PageType.TASK) return;
    const getTaskImport = async () => {
      const { data: { data: tasks } } = await getListTaskImport({homework_id: homeworkId});
      setListTasksImport(tasks);
    }
    getTaskImport();
  }, [])

  useEffect(() => {
    const getEvaluationCriteriaData = async () => {
      const { data: { data: defaultCriteria } } = await getEvaluationCriteria();
      setCriteria(defaultCriteria);
    }
    getEvaluationCriteriaData();
  }, [])
  useEffect(() => {
    const fetchData = async () => {
      if( typePage === PageType.TASK){
        const { data } = await getList();
        setClassOptions(data ?? []);
        const sessionOptions = [];
        for (let i = 1; i <= 16; i++) {
          sessionOptions.push({ label: `Session ${i}`, value: i });
        }
        setSessionOptions(sessionOptions);
      }
      if (typePage === PageType.TASK && !taskId) return;
      if (typePage === PageType.TASK && taskId) {
        const { data: { data: tasks, tag } } = await getTask(taskId);
        if (tag) {
          setTagSelected(tag);
        }
        setTasks(tasks);
        setSelectedTask(tasks[0])
        const { data: { data: questions } } = await getQuestions({ task_id: tasks[0]?.id });
        setQuestions(questions);
        setSelectedQuestionIndex(questions.length ? 0 : null);
        return;
      }
      const { data: { data: tasks, tag } } = await getHomeworkTasks({ homework_id: homeworkId });
      if (tag) {
        setTagSelected(tag);
      }
      if (!tasks.length) {
        return;
      }
      setTasks(tasks);
      setSelectedTask(tasks[0])
      const { data: { data: questions } } = await getQuestions({ task_id: tasks[0]?.id });
      setQuestions(questions);
      setSelectedQuestionIndex(questions.length ? 0 : null);
    }
    fetchData();
  }, []);

  useEffect(() => {
    const fetchData = async () => {
      const { data: { data: vocabularies } } = await listVocabularyHomework();
      setVocabularies(vocabularies);
    }
    const fetchTags = async () => {
      const { data: { data: tags } } = await listAllTag();
      setTags(tags);
    }
    fetchData();
    fetchTags();
  }, [])

  return (
    <Grid container spacing={2}>
      <Grid size={4}>
        <QuestionSidebar
          tasks={tasks}
          setTasks={setTasks}
          selectedTaskId={selectedTaskId}
          setSelectedTaskId={setSelectedTaskId}
          setSelectedQuestionIndex={setSelectedQuestionIndex}
          selectedQuestionIndex={selectedQuestionIndex}
          homeworkId={homeworkId}
          questions={questions}
          setQuestions={setQuestions}
          listTasksImport={listTasksImport}
          typePage={typePage}
          taskId={taskId}
          tags={tags}
          setTags={setTags}
          vocabularies={vocabularies}
          vocabulariesSelected={vocabulariesSelected}
          setVocabulariesSelected={setVocabulariesSelected}
          tagSelected={tagSelected}
          setTagSelected={setTagSelected}
          typeQuestion={typeQuestion}
          classOptions={classOptions}
          sessionOptions={sessionOptions}
        />
      </Grid>
      <Grid size={8}>
        <QuestionEditor
          defaultCriteria={criteria}
          questions={questions}
          questionIndex={selectedQuestionIndex}
          setQuestions={setQuestions}
          setSelectedQuestionIndex={setSelectedQuestionIndex}
          vocabulariesSelected={vocabulariesSelected}
          setTypeQuestion={setTypeQuestion}
          tasks={tasks}
        />
      </Grid>
    </Grid>
  );
}
