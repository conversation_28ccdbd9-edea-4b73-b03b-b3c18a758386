import React from 'react';
import { Dialog, DialogTitle, DialogContent, DialogActions, Button, Typography } from '@mui/material';

export default function TimeUpModal({ open, onClose }) {
  return (
    <Dialog open={open} onClose={onClose} maxWidth='xs' fullWidth>
      <DialogTitle>⏰ Time is up!</DialogTitle>
      <DialogContent>
        <Typography>Your exam time has ended. Click "OK" to return to the homework page.</Typography>
      </DialogContent>
      <DialogActions>
        <Button variant='contained' color='primary' onClick={onClose}>
          OK
        </Button>
      </DialogActions>
    </Dialog>
  );
}
