import React from 'react';
import { Card, Box, Typography, Button, Grid } from '@mui/material';
import ArrowBackIosNewIcon from '@mui/icons-material/ArrowBackIosNew';
import ArrowForwardIosIcon from '@mui/icons-material/ArrowForwardIos';

export default function QuestionNavigation({
  totalQuestions,
  currentQuestion,
  onJumpToQuestion,
  onNextQuestion,
  onPrevQuestion,
  errors,
  currentTaskIndex,
  currentTask,
}) {
  const hasError = (taskIndex, qIndex) => {
    return (
      !!errors?.tasks?.[taskIndex]?.task?.questions?.[qIndex]?.account_answers?.score ||
      currentTask?.unansweredQuestions?.includes(qIndex + 1)
    );
  };

  return (
    <Card elevation={1} sx={{ p: 2, display: 'flex', flexDirection: 'column', gap: 2 , flex :1}}>
      <Typography variant='h6' sx={{ fontWeight: 'bold', color: 'text.primary' }}>
        Question Navigation
      </Typography>

      <Grid container spacing={1} overflow={'auto'}>
        {Array.from({ length: totalQuestions }, (_, i) => i + 1).map(num => (
          <Grid item xs={2.4} key={num}>
            <Button
              variant='contained'
              onClick={() => onJumpToQuestion(num)}
              sx={{
                width: '100%',
                py: 0.5,
                fontSize: '0.7rem',
                minHeight: '32px',
                fontWeight: 400,
                // Only change color based on the current question
                // bgcolor: num === currentQuestion ? 'primary.main' : 'grey.300',
                bgcolor: hasError(currentTaskIndex, num - 1) ? 'error.main' : 'grey.300',
                color: num === currentQuestion ? 'white' : 'text.primary',
                '&:hover': {
                  bgcolor: num === currentQuestion ? 'primary.dark' : 'grey.400'
                },
              }}
            >
              {num}
            </Button>
          </Grid>
        ))}
      </Grid>

      <Box sx={{ display: 'flex', gap: 1, pt: 2, borderTop: 1, borderColor: 'grey.300' }}>
        <Button
          variant='contained'
          onClick={onPrevQuestion}
          size='small' // Using 'small' as a standard size prop for Button
          sx={{
            flexGrow: 1,
            fontSize: '0.75rem',
            py: 0.5,
            minHeight: '32px',
            bgcolor: 'primary.main',
            '&:hover': { bgcolor: 'primary.dark' }
          }}
          startIcon={<ArrowBackIosNewIcon sx={{ fontSize: 16 }} />}
        >
          Previous
        </Button>

        <Button
          variant='contained'
          onClick={onNextQuestion}
          size='small' // Using 'small' as a standard size prop for Button
          sx={{
            flexGrow: 1,
            fontSize: '0.75rem',
            py: 0.5,
            minHeight: '32px',
            bgcolor: 'primary.main',
            '&:hover': { bgcolor: 'primary.dark' }
          }}
          endIcon={<ArrowForwardIosIcon sx={{ fontSize: 16 }} />}
        >
          Next
        </Button>
      </Box>
    </Card>
  );
}
