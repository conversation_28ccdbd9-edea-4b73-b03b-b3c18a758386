import React, { useState } from 'react';
import {
  <PERSON>alog, <PERSON>alogTitle, DialogContent, DialogActions,
  Button, TextField, FormControl, InputLabel, Select, MenuItem
} from '@mui/material';
import QuestionType from "../../enums/QuestionType";

const AddQuestionModal = ({ open, onClose, onSubmit }) => {
  const [questionType, setQuestionType] = useState('');
  const [questionContent, setQuestionContent] = useState('');

  const handleTypeChange = (e) => {
    setQuestionType(e.target.value);
  };

  const handleContentChange = (e) => {
    setQuestionContent(e.target.value);
  };

  const handleSubmit = () => {
    if (questionType && questionContent) {
      onSubmit({ type: questionType, content: questionContent });
      setQuestionType('');
      setQuestionContent('');
      onClose();
    }
  };

  return (
    <Dialog open={open} onClose={onClose} fullWidth>
      <DialogTitle>Add new question</DialogTitle>
      <DialogContent>
        <TextField
          autoFocus
          margin="dense"
          label="Title"
          type="text"
          placeholder="Enter question title: ex: Q1"
          fullWidth
          value={questionContent}
          onChange={handleContentChange}
        />
        <FormControl fullWidth margin="dense">
          <InputLabel>Type of question</InputLabel>
          <Select
            value={questionType}
            onChange={handleTypeChange}
            label="Choose type of question"
          >
            <MenuItem value={QuestionType.MULTIPLE_CHOICE}>Multiple choice</MenuItem>
            <MenuItem value="one_choice">One answer</MenuItem>
            <MenuItem value="fill_blank">Fill in the blank</MenuItem>
          </Select>
        </FormControl>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button onClick={handleSubmit} variant="contained">Save</Button>
      </DialogActions>
    </Dialog>
  );
};

export default AddQuestionModal;
