import { Card, Box, Typography, Button } from '@mui/material'; // Removed unused imports
import AutorenewIcon from '@mui/icons-material/Autorenew';

// Add new props: examStarted and onStartExam
export default function ExamInfoCard({
  homeworkDetail,
  timeLeft, // không cần
  formatTime,
  onSubmit,
  examStarted,
  onStartExam,
  isTaskSubmitted,
  isResultPage = false,
  currentTask,
  onRequestTask = () => { }, // Default to empty function if not provided
  totalTimeHomework,
  isGrades = false,
  totalScore,
  onSubmitTask,
  checkStatusUnlockRequest = () => { },
}) {
  const handleReload = () => {
    // window.location.reload();
    checkStatusUnlockRequest()
  };
  //   const requestUnlock = async () => {
  //     await createRequestUnlock({account_id, unlock_type: 1, unlock_id: currentTask.id});
  //   }
  return (
    <Card elevation={1} sx={{ p: 2, display: 'flex', flexDirection: 'column', gap: 2 ,overflow:"auto"}}>
      <Typography variant='h5' component='h2' sx={{ fontWeight: 'bold', color: 'text.primary' }}>
        {homeworkDetail?.title}
      </Typography>
      <Typography variant='body2' sx={{ color: 'text.secondary' }}>
        Description: {homeworkDetail?.description}
      </Typography>

      {isResultPage && (
        <>
          <Typography variant='body2' sx={{ color: 'text.secondary' }}>
            Total score homework: {isGrades ? totalScore : homeworkDetail?.score || 0}
          </Typography>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant='body2' sx={{ color: 'text.secondary' }}>
              ⏰ Total Time
            </Typography>
            <Typography
              variant='h6'
              sx={{ fontWeight: 'bold', color: !isTaskSubmitted ? 'primary.main' : 'disable' }}
            >
              {formatTime(totalTimeHomework)}
            </Typography>
          </Box>
          {isGrades && (
            <Box display={'flex'} justifyContent={'flex-end'}>
              <Button variant='contained' size='small' onClick={onSubmitTask}>
                Save Grades
              </Button>
            </Box>
          )}
        </>
      )}

      {!isResultPage &&
        (examStarted ? (
          <>
            {timeLeft >= 0 && (
              <>
                <Box
                  sx={{
                    borderTop: 1,
                    borderColor: 'grey.300',
                    pt: 2,
                    display: 'flex',
                    flexDirection: 'column',
                    gap: 1
                  }}
                >
                  <Box
                    sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}
                  >
                    <Typography variant='body2' sx={{ color: 'text.secondary' }}>
                      ⏰ Time Remaining
                    </Typography>
                    <Typography
                      variant='h6'
                      sx={{
                        fontWeight: 'bold',
                        color: !isTaskSubmitted ? 'primary.main' : 'disable'
                      }}
                    >
                      {timeLeft === 0? "00:00" : formatTime(timeLeft)}
                    </Typography>
                  </Box>
                    <Box
                    sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}
                  >
                    <Typography variant='body2' sx={{ color: 'text.secondary' }}>
                      ⏰ Minimum Time Remaining
                    </Typography>
                    <Typography
                      variant='h6'
                      sx={{
                        fontWeight: 'bold',
                        color: !isTaskSubmitted ? 'primary.main' : 'disable'
                      }}
                    >
                      {currentTask.minTime  === 0? "00:00" : formatTime(currentTask.minTime)}
                    </Typography>
                  </Box>
                </Box>
                <Box sx={{ mt: 3, display: 'flex', justifyContent: 'right' }}>
                  <Button
                    type='submit'
                    variant='contained'
                    color='primary'
                    size='small'
                    sx={{ px: 4 }}
                    onClick={onSubmit}
                    disabled={timeLeft === 0 || isTaskSubmitted}
                  >
                    Submit
                  </Button>
                </Box>
              </>
            )}
          </>
        ) : currentTask?.showUnlockTask ? (
          <Box sx={{ mt: 3, display: 'flex', justifyContent: 'center', gap: 2 }}>
            <Button
              variant='outlined'
              color='warning'
              size='small'
              onClick={onRequestTask}
              disabled={currentTask?.requestUnlocks ? true : false}
            >
              {currentTask?.requestUnlocks ? 'Requesting' : 'Request Unlock'}
            </Button>
            {currentTask?.requestUnlocks && (
              <Button
                variant='contained'
                size='small'
                color='primary'
                onClick={handleReload}
                startIcon={<AutorenewIcon />}
              >
                Reload
              </Button>
            )}
          </Box>
        ) : currentTask?.showStart ? (
          <Box sx={{ mt: 3, display: 'flex', justifyContent: 'center' }}>
            <Button
              variant='contained'
              color='primary'
              size='small'
              sx={{ px: 6, py: 1.5 }}
              onClick={onStartExam}
            >
              Start Task
            </Button>
          </Box>
        ) : null)}
    </Card>
  );
}
