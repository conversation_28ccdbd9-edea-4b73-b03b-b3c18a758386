import { useCallback, useEffect, useMemo, useState } from 'react';
import { use<PERSON><PERSON>, Controller, useFieldArray } from 'react-hook-form';
import {
  Box, Typography, Paper, InputLabel, Select, MenuItem, FormControl, TextField, RadioGroup,
  FormControlLabel, Radio, Button, Checkbox, FormGroup, Chip, Autocomplete, IconButton
} from '@mui/material';
import DeleteIcon from "@mui/icons-material/Delete";
import QuestionType from "../../enums/QuestionType";
import { PageType } from "../../enums/PageType";
import { Editor } from '@tinymce/tinymce-react';
import { tinymceInit } from '../../config/tinymce-init';
import { uploadImage } from '../../services/admin/questions';
import AddIcon from '@mui/icons-material/Add';
import { useDropzone } from "react-dropzone";
import { toast } from 'react-toastify';

export const QuestionEditor = ({
  questions,
  questionIndex,
  setQuestions,
  setSelectedQuestionIndex,
  vocabulariesSelected,
  setTypeQuestion,
  defaultCriteria,
  tasks
}) => {
  const DEFAULT_FORM_VALUES = {
    type: QuestionType.SINGLE_CHOICE,
    content: '',
    id: null,
    sample_answer: '',
    explanation: '',
    score: 2,
    correct_options: [],
    words: [],
    min_word: 1,
    answer: [],
    options: [
      { value: '' },
      { value: '' },
      { value: '' },
      { value: '' },
    ],
    evaluation_criteria: defaultCriteria.length ? defaultCriteria : [],
    image_url: '',
  };

  const question = questions[questionIndex];

  const defaultValues = useMemo(() => ({
    ...DEFAULT_FORM_VALUES,
    ...question,
    evaluation_criteria: [
      ...question?.evaluation_criteria?.length ? question?.evaluation_criteria : defaultCriteria,
      { 'title': '', 'description': '', 'rate': 0 }
    ],
  }), [question]);

  const { control,
    watch,
    setValue,
    getValues,
    reset,
    trigger,
  } = useForm({
    defaultValues,
    mode: 'onChange',
  });

  const { fields: optionFields, append, remove, replace } = useFieldArray({
    control,
    name: "options",
  });

  const watchValue = watch();
  const deleteHandler = () => {
    const newQuestions = questions.filter((_, index) => index !== questionIndex);
    setQuestions(newQuestions);
    setSelectedQuestionIndex(0);
  }

  useEffect(() => {
    trigger();
    setQuestions(prev => {
      if (prev.length) prev[questionIndex] = watchValue;
      return prev;
    })
  }, [watchValue]);

  const watchType = watch('type');

  useEffect(() => {
    if(questions.length > 0 && watchType !== QuestionType.FLASH_CARD && questions[questionIndex].score <= 0) {
      setValue('score', 2);
    }
    setTypeQuestion(watchType);
  }, [watchType]);

  useEffect(() => {
    const selectedIds = vocabulariesSelected.map(v => v.id);

    const updated = questions.map(q => {
      if ([QuestionType.MAKE_SENTENCE, QuestionType.MAKE_PARAGRAPH, QuestionType.FLASH_CARD].includes(q.type)) {
        return {
          ...q,
          words: vocabulariesSelected.length > 0
            ? (Array.isArray(q.words) ? q.words : [q.words]).filter(id => selectedIds.includes(id))
            : [],
        };
      }
      return q;
    });

    setQuestions(updated);
  }, [vocabulariesSelected]);

  useEffect(() => {
    const question = questions[questionIndex];
    if (!question) return;
    const dataQuestion = {
      id: question.id ?? DEFAULT_FORM_VALUES.id,
      title: question.title ?? DEFAULT_FORM_VALUES.title,
      type: question.type ?? DEFAULT_FORM_VALUES.type,
      content: question.content ?? DEFAULT_FORM_VALUES.content,
      explanation: question.explanation ?? DEFAULT_FORM_VALUES.explanation,
      root: question.root ?? DEFAULT_FORM_VALUES.root,
      options: question.options ?? DEFAULT_FORM_VALUES.options,
      answer: question.answer ?? DEFAULT_FORM_VALUES.answer,
      correct_options: question.correct_options ?? DEFAULT_FORM_VALUES.correct_options,
      words: question.words ?? DEFAULT_FORM_VALUES.words,
      min_word: question.min_word ?? DEFAULT_FORM_VALUES.min_word,
      score: question.score ?? DEFAULT_FORM_VALUES.score,
      evaluation_criteria: question.evaluation_criteria ?? DEFAULT_FORM_VALUES.evaluation_criteria,
      image_url: question.image_url ?? DEFAULT_FORM_VALUES.image_url,
      sample_answer: question.sample_answer ?? DEFAULT_FORM_VALUES.sample_answer,
    };
    reset(dataQuestion);
  }, [questionIndex, questions, reset]);

  const typeConfig = {
    [QuestionType.SINGLE_CHOICE]: {
      correct_options: [],
      options: [
        { value: '' },
        { value: '' },
        { value: '' },
        { value: '' },
      ],
    },
    [QuestionType.MULTIPLE_CHOICE]: {
      correct_options: [],
      options: [
        { value: '' },
        { value: '' },
        { value: '' },
        { value: '' },
      ],
    },
    [QuestionType.FILL_IN_THE_BLANK]: {
      correct_options: []
    },
    [QuestionType.MAKE_SENTENCE]: {
      words: []
    },
    [QuestionType.MAKE_PARAGRAPH]: {
      words: [],
      min_word: 1
    },
    [QuestionType.FLASH_CARD]: {
      words: vocabulariesSelected.map(v => v.id),
      score: 0
    },
    [QuestionType.TRANSCRIPTION]: {
      image_url: ''
    },
    [QuestionType.ESSAY]: {
      evaluation_criteria: [
      ...question?.evaluation_criteria?.length ? question?.evaluation_criteria : defaultCriteria,
      { 'title': '', 'description': '', 'rate': 0 }
    ],
    }
  };

  const handleTypeChange = (newType) => {
    if(newType === QuestionType.ESSAY) {
      setValue('score', watchCriteria.reduce((sum, criteria) => {
        return sum + (Number(criteria.rate) || 20);
      }, 0));
    } else if(newType === QuestionType.FLASH_CARD) {
      setValue('score', 0);
    } else {
      setValue('score', 2);
    }
    setValue('type', newType);
    const config = typeConfig[newType];
    if (config) {
      Object.entries(config).forEach(([key, val]) => setValue(key, val));
    }
  };

  const handleChooseAllWords = (type = null) => {
    if(type === QuestionType.MAKE_SENTENCE) {
      const currentQuestion = questions[questionIndex];

      const hasMissingFields = (currentQuestion.score <= 0);

      if (hasMissingFields) {
        toast.error(`Please complete the current question (score) before creating new questions`);
        return;
      }
      if(vocabulariesSelected.length === 0) {
        toast.error(`Please select at least one vocabulary`);
        return;
      }
      const wordFirst = vocabulariesSelected[0].id || '';
      const textWordFirst = vocabulariesSelected[0].word || '';
      if (((Array.isArray(currentQuestion.words) && currentQuestion.words.length === 0) || !currentQuestion.words)
        && vocabulariesSelected.length > 0) {
        if(!currentQuestion.content){
          setValue('content', `Make a sentence using the word: <strong>${textWordFirst}</strong>`);
        }
        setValue('words', Array.isArray(currentQuestion.words) ? [...currentQuestion.words, wordFirst] : wordFirst);
        setQuestions(prev => {
          prev[questionIndex] = {
            ...prev[questionIndex],
            words: Array.isArray(currentQuestion.words) ? [...currentQuestion.words, wordFirst] : wordFirst,
            content: prev[questionIndex].content || `Make a sentence using the word: <strong>${textWordFirst}</strong>`,
          };
          return prev;
        });
      }
      currentQuestion.content = questions[questionIndex].content;

      const usedWords = new Set();
      questions.forEach(q => {
        if (q.type === QuestionType.MAKE_SENTENCE && q.words) {
          if (Array.isArray(q.words)) {
            q.words.forEach(wordId => usedWords.add(wordId));
          } else {
            usedWords.add(q.words);
          }
        }
      });

      const unusedWords = vocabulariesSelected.filter(vocab =>
        !usedWords.has(vocab.id)
      );

      if (unusedWords.length === 0) {
        toast.info('All vocabularies already have MAKE_SENTENCE questions');
        return;
      }

      const newQuestions = unusedWords.map(vocab => ({
        type: QuestionType.MAKE_SENTENCE,
        content: currentQuestion.content.replace(textWordFirst, vocab.word),
        words: vocab.id,
        score: currentQuestion.score,
        explanation: currentQuestion.explanation ? `Explanation for word: <strong>${vocab.word}</strong>` : '',
        sample_answer: '',
        options: [...(currentQuestion.options || [{ value: '' }, { value: '' }, { value: '' }, { value: '' }])],
        correct_options: [],
        answer: [],
        min_word: 1,
        evaluation_criteria: [...(currentQuestion.evaluation_criteria || [])],
        image_url: ''
      }));

      setQuestions(prev => [...prev, ...newQuestions]);
      toast.success(`Created ${newQuestions.length} new MAKE_SENTENCE questions with same settings`);

      return;
    }

    const selectedIds = vocabulariesSelected.map(v => v.id);
    setValue('min_word', selectedIds.length);
    setValue('words', selectedIds);
  }

  const watchCriteria = watch('evaluation_criteria');

  useEffect(() => {
    if (watchType === QuestionType.ESSAY && Array.isArray(watchCriteria)) {
      const totalScore = watchCriteria.reduce((sum, criteria) => {
        return sum + (Number(criteria.rate) || 0);
      }, 0);

      if (watchValue.score !== totalScore) {
        setValue('score', totalScore, { shouldDirty: false });
      }
    }
  }, [watchCriteria, watchType, setValue, watchValue]);


  const CriteriaItem = useCallback(({ criteria, index, field }) => {
    const isDefault = index < defaultCriteria.length;

    return (
      <Box key={index} sx={{ mb: 3, p: 2, border: '1px solid #e0e0e0', borderRadius: 1 }}>
        <Box sx={{ display: 'flex', gap: 2, alignItems: 'flex-start', mb: 3 }}>
          <TextField
            label="Title"
            size="small"
            value={criteria.title}
            onChange={(e) =>
              field.onChange(updateCriteria(field.value, index, "title", e.target.value))
            }
            placeholder={
              index < defaultCriteria.length
                ? criteria.title
                : "Enter custom criteria title"
            }
            sx={{ width: "90%" }}
            disabled={isDefault}
            error={!criteria.title || criteria.title.trim() === ""}
            helperText={
              !criteria.title || criteria.title.trim() === "" ? "This field is required" : ""
            }
          />
          <TextField
            label="Score"
            type="number"
            size="small"
            value={criteria.rate}
            onChange={(e) =>
              field.onChange(
                updateCriteria(field.value, index, "rate", Number(e.target.value))
              )
            }
            slotProps={{ htmlInput: { min: 0 } }}
            placeholder="0"
            sx={{ width: "10%", minWidth: 80 }}
            error={criteria.rate === null || criteria.rate === undefined || criteria.rate < 0}
            helperText={
              criteria.rate === null || criteria.rate === undefined
                ? "Score is required"
                : criteria.rate < 0
                  ? "Score must be ≥ 0"
                  : ""
            }
          />
        </Box>
        {isDefault ? (
          <Box sx={{
            pt: 1,
            backgroundColor: '#f8f9fa',
            border: '1px solid #e0e0e0',
            borderRadius: 1,
          }}>
            <Typography
              component="div"
              dangerouslySetInnerHTML={{ __html: criteria.description }}
            />
          </Box>
        ) : (
          <Editor
            tinymceScriptSrc="/tinymce/tinymce.min.js"
            init={{
              ...tinymceInit,
              placeholder: 'Enter the description...',
            }}
            value={criteria.description}
            onEditorChange={(value) => field.onChange(updateCriteria(field.value, index, 'description', value))}
          />
        )}

      </Box>
    )
  }, [updateCriteria, defaultCriteria]);

  const updateCriteria = useCallback((fieldValue, index, key, value) => {
    const updatedCriteria = [...(fieldValue)];
    updatedCriteria[index] = { ...updatedCriteria[index], [key]: value };
    return updatedCriteria;
  }, []);

  const renderEvaluationCriteria = () => {
    return (
      <Box sx={{ mt: 2 }}>
        <Typography>
          Evaluation Criteria
        </Typography>

        <Controller
          name="evaluation_criteria"
          control={control}
          defaultValue={defaultValues.evaluation_criteria || []}
          render={({ field }) => (
            <Box>
              {(field.value || defaultValues.evaluation_criteria)?.map((criteria, index) => (
                <CriteriaItem
                  key={index}
                  criteria={criteria}
                  index={index}
                  field={field}
                />
              ))}
            </Box>
          )}
        />
      </Box>
    )
  };

  const renderAnswerInputs = () => {
    switch (watchType) {
      case QuestionType.SINGLE_CHOICE:
        return (
          <Box>
            <Controller
              name="correct_options"
              control={control}
              rules={{ required: 'Must select a correct answer' }}
              render={({ field, fieldState }) => (
                <div>
                  <RadioGroup
                    value={field.value}
                    onChange={(e) => field.onChange(e.target.value)}
                  >
                    {optionFields.map((item, idx) => (
                      <Box key={idx} sx={{ mb: 2 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                          <FormControlLabel
                            value={String(idx)}
                            control={<Radio />}
                            label={`Answer ${String.fromCharCode(65 + idx)}`}
                          />
                        </Box>
                        <Controller
                          name={`options.${idx}.value`}
                          control={control}
                          rules={{ required: 'Answer is required' }}
                          render={({ field: answerField, fieldState: answerFieldState }) => (
                            <Box sx={{ display: 'flex', alignItems: 'start', gap: 1 }}>
                              <TextField
                                {...answerField}
                                fullWidth
                                size="small"
                                error={!!answerFieldState.error}
                                helperText={answerFieldState.error?.message}
                              />
                              <IconButton onClick={() => remove(idx)} variant="outlined" color="error" disabled={optionFields.length <= 1}>
                                <DeleteIcon />
                              </IconButton>
                            </Box>
                          )}
                        />
                      </Box>
                    ))}
                  </RadioGroup>
                  {fieldState.error && (
                    <Typography color="error" variant="caption" sx={{ display: 'block', mt: 1 }}>
                      {fieldState.error.message}
                    </Typography>
                  )}
                </div>
              )}
            />
            <Button onClick={() => append('')} variant="outlined" startIcon={<AddIcon />} sx={{ mt: 1 }}>
              Add Option
            </Button>
          </Box>
        );

      case QuestionType.MULTIPLE_CHOICE:
        return (
          <Box>
            <Controller
              name="correct_options"
              control={control}
              rules={{ validate: (value) => (value && value.length > 0) || 'Must select at least one answer' }}
              render={({ field, fieldState }) => (
                <div>
                  <FormGroup>
                    {optionFields.map((item, idx) => (
                      <Box key={idx} sx={{ mb: 2 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                          <FormControlLabel
                            control={
                              <Checkbox
                                checked={Array.isArray(field.value) && field.value.some(val => String(val) === String(idx))}
                                onChange={(e) => {
                                  const newValue = e.target.checked
                                    ? [...field.value, idx]
                                    : field.value.filter((ans) => String(ans) !== String(idx));
                                  field.onChange(newValue);
                                }}
                              />
                            }
                            label={`Answer ${String.fromCharCode(65 + idx)}`}
                          />
                        </Box>
                        <Controller
                          name={`options.${idx}.value`}
                          control={control}
                          rules={{ required: 'Answer is required' }}
                          render={({ field: answerField, fieldState: answerFieldState }) => (
                            <>
                              <Box sx={{ display: 'flex', alignItems: 'start', gap: 1 }}>
                                <TextField
                                  {...answerField}
                                  fullWidth
                                  size="small"
                                  error={!!answerFieldState.error}
                                  helperText={answerFieldState.error?.message}
                                />
                                <IconButton variant="outlined" color="error"
                                            onClick={() => {
                                              const currentCorrect = getValues("correct_options") || [];
                                              const updatedCorrect = currentCorrect
                                                .filter((opt) => opt !== idx)
                                                .map((opt) => (opt > idx ? opt - 1 : opt));
                                              setValue("correct_options", updatedCorrect);
                                              remove(idx);
                                            }}
                                            disabled={optionFields.length <= 1}>
                                  <DeleteIcon />
                                </IconButton>
                              </Box>
                            </>
                          )}
                        />
                      </Box>
                    ))}
                  </FormGroup>
                  {fieldState.error && (
                    <Typography color="error" variant="caption" sx={{ display: 'block', mt: 1 }}>
                      {fieldState.error.message}
                    </Typography>
                  )}
                </div>
              )}
            />
            <Button onClick={() => append('')} variant="outlined" startIcon={<AddIcon />} sx={{ mt: 1 }}>
              Add Option
            </Button>
          </Box>
        );

      case QuestionType.FILL_IN_THE_BLANK:
        return (
          <Controller
            name="answer"
            control={control}
            defaultValue={[]}
            rules={{ required: 'At least one correct answer is required' }}
            render={({ field, fieldState }) => {
              const [inputValue, setInputValue] = useState('');

              const addItems = (value) => {
                if (value && value.trim() !== '') {
                  const currentValue = Array.isArray(field.value) ? field.value : [];
                  const splitItems = value
                    .split(',')
                    .map((item) => item.trim())
                    .filter((item) => item !== '');

                  const uniqueItems = [...new Set(splitItems)];

                  const newItems = uniqueItems.filter((item) => !currentValue.includes(item));

                  if (newItems.length > 0) {
                    const updatedValue = [...currentValue, ...newItems];
                    field.onChange(updatedValue);
                  }
                }
              };

              const handleKeyDown = (event) => {
                if (event.key === 'Enter') {
                  event.preventDefault();
                  addItems(inputValue);
                  setInputValue('');
                }
              };

              const handleInputChange = (event, newInputValue, reason) => {
                setInputValue(newInputValue);
              };

              const handlePaste = (event) => {
                event.preventDefault();
                const pastedText = event.clipboardData.getData('text');

                if (pastedText) {
                  addItems(pastedText);
                  setInputValue('');
                }
              };

              return (
                <Autocomplete
                  {...field}
                  multiple
                  freeSolo
                  options={[]}
                  value={Array.isArray(field.value) ? field.value : []}
                  inputValue={inputValue}
                  onChange={(event, newValue) => {
                    field.onChange(newValue);
                  }}
                  onInputChange={handleInputChange}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label="Correct answers"
                      size="small"
                      error={!!fieldState.error}
                      helperText={fieldState.error?.message || "Press Enter or comma to add answers"}
                      onKeyDown={handleKeyDown}
                      onPaste={handlePaste}
                    />
                  )}
                />
              );
            }}
          />
        );

        case QuestionType.ESSAY:
        return (
          <Box>
            <Typography sx={{ mb: 1 }}>Sample Answer</Typography>
            <Controller
              name="sample_answer"
              control={control}
              render={({ field, fieldState }) => (
                <Box>
                  <Editor
                    tinymceScriptSrc="/tinymce/tinymce.min.js"
                    init={{
                      ...tinymceInit,
                      placeholder: 'Enter sample answer...',
                    }}
                    onEditorChange={(value) => field.onChange(value)}
                    value={field.value}
                  />
                </Box>
              )}
            />
          </Box>
        );

      case QuestionType.MAKE_SENTENCE:
        return (
          <>
            <Controller
              name="words"
              control={control}
              rules={{
                validate: (value) => value || 'Must select a word'
              }}
              render={({ field, fieldState }) => {
                return (
                  <div>
                    <Typography variant="subtitle2" sx={{ mb: 1 }}>
                      Select a word for making sentences:
                    </Typography>
                    <Box display={'flex'} alignItems={'center'} gap={2}>
                      <Box flex={8}>
                        <Autocomplete
                          options={vocabulariesSelected}
                          getOptionLabel={(option) => option.word}
                          value={
                            vocabulariesSelected.find(
                              (vocab) => String(vocab.id) === String(Array.isArray(field.value) ? field.value[0] : field.value)
                            ) || null
                          }
                          onChange={(event, newValue) => {
                            field.onChange(newValue ? newValue.id : null);
                          }}
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              label="Search and select a word"
                              placeholder="Type to search..."
                              size="small"
                              error={!!fieldState.error} />
                          )}
                          filterSelectedOptions
                        />
                      </Box>
                      <Box flex={2}>
                        <Button variant={'contained'} onClick={() => handleChooseAllWords(QuestionType.MAKE_SENTENCE)}>Choose all</Button>
                      </Box>
                    </Box>
                    {fieldState.error && (
                      <Typography color="error" variant="caption" sx={{ display: 'block', mt: 1 }}>
                        {fieldState.error.message}
                      </Typography>
                    )}
                  </div>
                );
              }}
            />
          </>
        );

      case QuestionType.MAKE_PARAGRAPH:
        return (
          <Box>
            <Controller
              name="words"
              control={control}
              rules={{
                validate: (value) => (value && value.length > 0) || 'Must select at least one word'
              }}
              render={({ field, fieldState }) => {
                return (
                  <div>
                    <Typography variant="subtitle2" sx={{ mb: 1 }}>
                      Select words for paragraph writing:
                    </Typography>
                    <Box display={'flex'} alignItems={'center'} gap={2}>
                      <Box flex={8}>
                        <Autocomplete
                          multiple
                          options={vocabulariesSelected}
                          getOptionLabel={(option) => option.word}
                          value={Array.isArray(field.value) ?
                            vocabulariesSelected.filter(vocab => field.value.includes(vocab.id)) : []}
                          onChange={(event, newValue) => {
                            const selectedIds = newValue.map(vocab => vocab.id);
                            setValue('min_word', selectedIds.length);
                            field.onChange(selectedIds);
                          }}
                          renderTags={(value, getTagProps) =>
                            (Array.isArray(value) ? value : []).map((option, index) => {
                              const { key, ...tagProps } = getTagProps({ index });
                              return (
                                <Chip
                                  key={key}
                                  variant="outlined"
                                  label={option.word}
                                  size="small"
                                  {...tagProps}
                                />
                              );
                            })
                          }
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              label="Search and select words"
                              placeholder="Type to search..."
                              size="small"
                              error={!!fieldState.error}
                            />
                          )}
                          filterSelectedOptions
                        />
                      </Box>
                      <Box flex={2}>
                        <Button variant={'contained'} onClick={() => handleChooseAllWords()}>Choose all</Button>
                      </Box>
                    </Box>
                    {fieldState.error && (
                      <Typography color="error" variant="caption" sx={{ display: 'block', mb: 2 }}>
                        {fieldState.error.message}
                      </Typography>
                    )}
                  </div>
                );
              }}
            />

            <Controller
              name="min_word"
              control={control}
              rules={{
                required: 'Minimum word count is required',
                min: { value: 1, message: 'Minimum must be at least 1' },
              }}
              readOnly={true}
              render={({ field, fieldState }) => (
                <TextField
                  {...field}
                  label="Minimum words to use"
                  type="number"
                  size="small"
                  fullWidth
                  sx={{ mt: 2 }}
                  error={!!fieldState.error}
                  onKeyDown={(e) => {
                    if (['.', ',', '-', '+', 'e', 'E'].includes(e.key)) {
                      e.preventDefault();
                    }
                  }}
                  helperText={fieldState.error?.message || 'Enter minimum number of words that must be used in the paragraph'}
                />
              )}
            />
          </Box>
        );

      case QuestionType.FLASH_CARD:
        return (
          <Box>
            <Controller
              name="words"
              control={control}
              rules={{
                validate: (value) => (value && value.length > 0) || 'Must select at least one word'
              }}
              render={({ field, fieldState }) => {
                return (
                  <div>
                    <Typography variant="subtitle2" sx={{ mb: 1 }}>
                      Select words for flashcard:
                    </Typography>
                    <Box display={'flex'} alignItems={'center'} gap={2}>
                      <Box flex={8}>
                        <Autocomplete
                          multiple
                          options={vocabulariesSelected}
                          getOptionLabel={(option) => option.word}
                          value={Array.isArray(field.value) ?
                            vocabulariesSelected.filter(vocab => field.value.includes(vocab.id)) : []}
                          onChange={(event, newValue) => {
                            const selectedIds = newValue.map(vocab => vocab.id);
                            field.onChange(selectedIds);
                          }}
                          renderTags={(value, getTagProps) =>
                            (Array.isArray(value) ? value : []).map((option, index) => {
                              const { key, ...tagProps } = getTagProps({ index });
                              return (
                                <Chip
                                  key={key}
                                  variant="outlined"
                                  label={option.word}
                                  size="small"
                                  {...tagProps}
                                />
                              );
                            })
                          }
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              label="Search and select words"
                              placeholder="Type to search..."
                              size="small"
                              error={!!fieldState.error}
                            />
                          )}
                          filterSelectedOptions
                        />
                      </Box>
                      <Box flex={2}>
                        <Button variant={'contained'} onClick={() => handleChooseAllWords()}>Choose all</Button>
                      </Box>
                    </Box>
                    {fieldState.error && (
                      <Typography color="error" variant="caption" sx={{ display: 'block', mb: 2 }}>
                        {fieldState.error.message}
                      </Typography>
                    )}
                  </div>
                );
              }}
            />
          </Box>
        );

      case QuestionType.TRANSCRIPTION:
        return (
          <>
            <Controller
              name="image_url"
              control={control}
              rules={{
                required: 'Please upload an image',
              }}
              render={({ field, fieldState }) => {
                const onDrop = useCallback(
                  (acceptedFiles) => {
                    const file = acceptedFiles[0];
                    if (file) {
                      uploadImage(file)
                        .then((res) => {
                          field.onChange(res.data.url);
                        })
                        .catch((err) => {
                          toast.error(err.response?.data?.message || 'Upload failed. Please try again.');
                        });
                    }
                  },
                  [field]
                );

                const { getRootProps, getInputProps, isDragActive } = useDropzone({
                  onDrop,
                  accept: { "image/*": [] },
                  multiple: false,
                });

                return (
                  <Box display="flex" flexDirection="column" gap={2}>
                    <input
                      type="file"
                      hidden
                      accept="image/*"
                      onChange={(e) => {
                        const file = e.target.files?.[0];
                        if (file) {
                          uploadImage(file)
                            .then((res) => {
                              field.onChange(res.data.url);
                            })
                            .catch((err) => {
                              console.error("Upload failed:", err);
                            });
                        }
                      }}
                    />
                    {field.value && (
                      <Box display="flex" justifyContent="center" mt={2}>
                        <Box
                          component="img"
                          src={
                            typeof field.value === "string"
                              ? field.value
                              : URL.createObjectURL(field.value)
                          }
                          alt="Preview"
                          sx={{
                            maxWidth: "100%",
                            height: "auto",
                            width: 600,
                            borderRadius: 2,
                            boxShadow: 3,
                          }}
                        />
                      </Box>
                    )}
                    <Box
                      {...getRootProps()}
                      sx={{
                        border: "2px dashed #ccc",
                        borderRadius: 2,
                        p: 4,
                        textAlign: "center",
                        cursor: "pointer",
                        bgcolor: isDragActive ? "grey.100" : "transparent",
                        "&:hover": { borderColor: "primary.main" },
                      }}
                    >
                      <input {...getInputProps()} />
                      {isDragActive ? (
                        <Typography>Drop the image here ...</Typography>
                      ) : (
                        <Typography>
                          Drag & drop an image here, or click to select a file
                        </Typography>
                      )}
                    </Box>
                    {fieldState.error && (
                      <Typography variant="caption" color="error">
                        {fieldState.error.message}
                      </Typography>
                    )}
                  </Box>
                );
              }}
            />
          </>
        );

      default:
        return null;
    }
  };

  return (
    <Paper elevation={3} sx={{ p: 2, overflowY: 'auto', height: 'calc(100dvh - 180px)', minHeight: '100%' }}>
      <Typography variant="h6">Question Details</Typography>

      {tasks.length > 0 && questions[questionIndex] ? (
        <Box mt={2} component="form">
          <Box sx={{ display: 'flex', gap: 2, mb: 2, alignItems: 'start' }}>
            <FormControl fullWidth size="small" sx={{ width: 1 / 3 }}>
              <InputLabel id="question-type-label">Question Type</InputLabel>
              <Controller
                name="type"
                control={control}
                render={({ field }) => (
                  <Select
                    {...field}
                    labelId="question-type-label"
                    label="Question Type"
                    onChange={(e) => {
                      field.onChange(e.target.value);
                      handleTypeChange(e.target.value);
                    }}
                  >
                    <MenuItem value={QuestionType.SINGLE_CHOICE}>Single Answer</MenuItem>
                    <MenuItem value={QuestionType.MULTIPLE_CHOICE}>Multiple Answer</MenuItem>
                    <MenuItem value={QuestionType.FILL_IN_THE_BLANK}>Fill in the Blank</MenuItem>
                    <MenuItem value={QuestionType.ESSAY}>Essay</MenuItem>
                    <MenuItem value={QuestionType.MAKE_SENTENCE}>Make Sentences</MenuItem>
                    <MenuItem value={QuestionType.MAKE_PARAGRAPH}>Make Paragraph</MenuItem>
                    <MenuItem value={QuestionType.FLASH_CARD}>Flashcard</MenuItem>
                    <MenuItem value={QuestionType.TRANSCRIPTION}>Transcription</MenuItem>
                  </Select>
                )}
              />
            </FormControl>

            {watchType != QuestionType.FLASH_CARD && (
              <>
                <Controller
                  name="score"
                  control={control}
                  rules={{
                    required: 'Score is required',
                    min: { value: 1, message: 'Score must be at least 1' },
                  }}
                  render={({ field, fieldState }) => (
                    <TextField
                      {...field}
                      size="small"
                      label="Score"
                      type="number"
                      sx={{ width: 120 }}
                      onKeyDown={(e) => {
                        if (['.', ',', '-', '+', 'e', 'E'].includes(e.key)) {
                          e.preventDefault();
                        }
                      }}
                      error={!!fieldState.error}
                      helperText={fieldState.error?.message}
                      slotProps={{
                        input: { readOnly: [QuestionType.ESSAY, QuestionType.FLASH_CARD].includes(watchType) }
                      }}
                    />
                  )}
                />
              </>
            )}

            <Button
              variant="outlined"
              color="error"
              size="small"
              startIcon={<DeleteIcon />}
              onClick={() => deleteHandler()}
              sx={{ height: 40 }}
            >
              Delete Question
            </Button>
          </Box>

          <Box sx={{ mb: 3 }}>
            <Typography sx={{ mb: 1 }}>Question Content</Typography>
            <Controller
              name="content"
              control={control}
              rules={{ required: 'Question content is required' }}
              render={({ field, fieldState }) => (
                <Box>
                  <Editor
                    tinymceScriptSrc="/tinymce/tinymce.min.js"
                    init={{
                      ...tinymceInit,
                      placeholder: 'Enter the question...',
                    }}
                    onEditorChange={(value) => field.onChange(value)}
                    value={field.value}
                  />
                  {fieldState.error && (
                    <Typography color="error" variant="caption" sx={{ mt: 1, display: 'block' }}>
                      {fieldState.error.message}
                    </Typography>
                  )}
                </Box>
              )}
            />
          </Box>

          <Box sx={{ mb: 3 }}>
            {
              watchType !== QuestionType.ESSAY && (<Typography sx={{ mb: 1 }}>
                Answers {watchType === QuestionType.FILL_IN_THE_BLANK && "(Fill in and press Enter to add an answer.)"}
              </Typography>
              )
            }
            {renderAnswerInputs()}
          </Box>

          <Box sx={{ mb: 3 }}>
            <Typography sx={{ mb: 1 }}>Explanation</Typography>
            <Controller
              name="explanation"
              control={control}
              render={({ field }) => (
                <Editor
                  tinymceScriptSrc="/tinymce/tinymce.min.js"
                  init={{
                    ...tinymceInit,
                    placeholder: 'Enter the explanation...',
                  }}
                  onEditorChange={(value) => field.onChange(value)}
                  value={field.value}
                />
              )}
            />
          </Box>
          {
            watchType == QuestionType.ESSAY && renderEvaluationCriteria()
          }
        </Box>
      ) : (
        <Typography mt={2} color="text.secondary">
          Please add a question
        </Typography>
      )}
    </Paper>
  );
};
