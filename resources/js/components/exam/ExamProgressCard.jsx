import React from 'react';
import { Card, Box, Typography, LinearProgress, Button } from '@mui/material';
import BookIcon from '@mui/icons-material/Book';
import ArrowBackIosNewIcon from '@mui/icons-material/ArrowBackIosNew';
import ArrowForwardIosIcon from '@mui/icons-material/ArrowForwardIos';

export default function ExamProgressCard({
  tasks,
  correctCount,
  wrongCount,
  answeredCount,
  totalQuestionsInPart,
  currentTaskIndex,
  totalTasks,
  onNextTask,
  onPrevTask,
  currentTask,
  isResultPage,
  formatTime
}) {
  // const progressPercentage = totalQuestionsInPart > 0 ? (answeredCount / totalQuestionsInPart) * 100 : 0;
  return (
    <Card elevation={1} sx={{ p: 2, display: 'flex', flexDirection: 'column', gap: 2 , overflow:"auto" }}>
      <Typography variant='h6' sx={{ fontWeight: 'bold', color: 'text.primary' }}>
        Task Sections ({currentTaskIndex + 1} / {totalTasks})
      </Typography>
      <Box sx={{ borderRadius: 1, p: 1.5, display: 'flex', flexDirection: 'column', gap: 1 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', color: 'info.dark' }}>
          <BookIcon sx={{ mr: 1 }} />
          <Typography variant='body1'>{tasks?.[currentTaskIndex]?.title}</Typography>{' '}
          {/* Changed to .title */}
        </Box>
        {/*
                <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                    Completion:{' '}
                    <Typography component="span" sx={{ fontWeight: 'bold' }}>
                        {answeredCount}/{totalQuestionsInPart}
                    </Typography>
                </Typography>
                <LinearProgress
                    variant="determinate"
                    value={progressPercentage}
                    sx={{
                        height: 6,
                        borderRadius: 3,
                        bgcolor: 'info.light',
                        '& .MuiLinearProgress-bar': { bgcolor: 'info.dark' },
                    }}
                /> */}
        {/* <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}>
                    <Typography variant="caption" sx={{ fontWeight: 'medium', color: 'success.main' }}>
                        Correct: {correctCount}
                    </Typography>
                    <Typography variant="caption" sx={{ fontWeight: 'medium', color: 'error.main' }}>
                        Incorrect: {wrongCount}
                    </Typography>
                </Box> */}
      </Box>
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5, mt: 1 }}>
        {isResultPage && (
          <>
            <Typography variant='body2' sx={{ color: 'text.secondary' }}>
              Score task:
              <Typography component='span' sx={{ fontWeight: 'bold', ml: 0.5 }}>
                {currentTask?.totalScoreTask ?? 0}
              </Typography>
            </Typography>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Typography variant='body2' sx={{ color: 'text.secondary' }}>
                ⏰ Total Time
              </Typography>
              <Typography
                variant='h6'
                sx={{ fontWeight: 'bold', color: 'text.secondary' }}
              >
                {formatTime(currentTask.timeLeft)}
              </Typography>
            </Box>
          </>
        )}
      </Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>
        <Button
          variant='outlined'
          startIcon={<ArrowBackIosNewIcon />}
          size='small'
          onClick={onPrevTask}
          disabled={currentTaskIndex === 0}
        >
          Prev Task
        </Button>
        <Button
          variant='contained'
          size='small'
          endIcon={<ArrowForwardIosIcon />}
          onClick={onNextTask}
          disabled={currentTaskIndex === totalTasks - 1}
        >
          Next Task
        </Button>
      </Box>
    </Card>
  );
}
