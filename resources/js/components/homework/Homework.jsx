import { Box, <PERSON><PERSON>, Card, Typography } from '@mui/material';

import React from 'react';
import HomeworkForm from './HomeworkForm';
export default function Homework({ homeworkId }) {
  return (
    <Card elevation={1} sx={{ p: 2, display: 'flex', flexDirection: 'column', gap: 2 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
        <Box sx={{ mb: 2 }}>
          <Typography variant="h6">
            {homeworkId ? 'Edit Homework' : 'Create Homework'}
          </Typography>
        </Box>
        <Box sx={{ mb: 2 }}>
          <Button
            component="a"
            href="/admin/main-homeworks"
            variant="contained"
            color="primary"
          >
            Back
          </Button>
        </Box>
      </Box>
      <HomeworkForm homeworkId={homeworkId} />
    </Card>
  );
}
