// src/components/StudentStatusTable.js

import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Box,
  TableContainer,
  Paper
} from '@mui/material';
import HomeworkAction from './HomeworkAction';

export default function StudentStatusTable({
                                             homeworks,
                                             students,
                                             handleOpenConfirmDialog,
                                             handleOpenConfirmUnlockDialog,
                                             fetchData,
                                             role,
                                             isResult,
                                             main,
                                             handleAutotUnlock
                                           }) {


  return (
    <Box
      sx={{
        ml: 4,
        mt: 2,
        mb: 2,
        borderLeft: '2px solid',
        borderColor: 'green',
        pl: 3
      }}
    >
      <TableContainer component={Paper} elevation={0} sx={{ border: '1px solid', borderColor: 'divider' }}>
        <Table sx={{ tableLayout: 'fixed' }}>
          <TableHead>
            <TableRow>
              <TableCell sx={{ width: '5%' }}></TableCell>
              <TableCell
                sx={{
                  fontWeight: 'bold',
                  backgroundColor: '#fff',
                  width: '15%'
                }}
              >
                ID
              </TableCell>
              <TableCell
                sx={{
                  fontWeight: 'bold',
                  backgroundColor: '#fff',
                  width: '20%'
                }}
              >
                Title
              </TableCell>
              <TableCell
                sx={{
                  fontWeight: 'bold',
                  backgroundColor: '#fff',
                  width: '20%'
                }}
              >
                User Name
              </TableCell>
              <TableCell
                align='center'
                sx={{
                  fontWeight: 'bold',
                  backgroundColor: '#fff',
                  width: '40%'
                }}
              >
                Action
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {homeworks.length > 0 ? (
              homeworks.map(hw =>
                students.map(student => {
                  const accountHomework = hw.account_homeworks.find(
                    ah => ah.account_id === student.id
                  );
                  const status = accountHomework
                    ? accountHomework.end_time
                      ? 'Completed'
                      : 'In Progress'
                    : 'Not Started';
                  if (isResult ) {
                    if(accountHomework && accountHomework.end_time){
                    return (
                            <TableRow key={`${hw.id}-${student.id}`}>
                              <TableCell sx={{ width: '5%' }} />
                              <TableCell sx={{ width: '15%' }}>HW{hw.homework}</TableCell>
                              <TableCell sx={{ width: '20%' }}>{hw.title}</TableCell>
                              <TableCell sx={{ width: '20%' }}>{student.user_name}</TableCell>
                              <TableCell sx={{ width: '40%', textAlign: 'center' }}>
                                <HomeworkAction
                                  hw={hw}
                                  role={role}
                                  handleOpenConfirmDialog={handleOpenConfirmDialog}
                                  handleOpenConfirmUnlockDialog={handleOpenConfirmUnlockDialog}
                                  fetchData={fetchData}
                                  accountId={student.id}
                                  handleAutotUnlock={handleAutotUnlock}
                                  isResult={isResult}
                                  main={main}
                                />
                              </TableCell>
                            </TableRow>
                          );
                    }
                  }else{
                    return (
                      <TableRow key={`${hw.id}-${student.id}`}>
                        <TableCell sx={{ width: '5%' }} />
                        <TableCell sx={{ width: '15%' }}>HW{hw.homework}</TableCell>
                        <TableCell sx={{ width: '20%' }}>{hw.title}</TableCell>
                        <TableCell sx={{ width: '20%' }}>{student.user_name}</TableCell>
                        <TableCell sx={{ width: '40%', textAlign: 'center' }}>
                          <HomeworkAction
                            hw={hw}
                            role={role}
                            handleOpenConfirmDialog={handleOpenConfirmDialog}
                            handleOpenConfirmUnlockDialog={handleOpenConfirmUnlockDialog}
                            fetchData={fetchData}
                            accountId={student.id}
                            handleAutotUnlock={handleAutotUnlock}
                            isResult={isResult}
                            main={main}
                          />
                        </TableCell>
                      </TableRow>
                    );
                  }
                
                })
              )
            ) : (
              <TableRow>
                <TableCell colSpan={3} align='center'>
                  No homework assigned.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
}
