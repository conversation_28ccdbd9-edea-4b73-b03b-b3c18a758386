import React, { useEffect, useState } from 'react';
import KeyboardDoubleArrowRightIcon from '@mui/icons-material/KeyboardDoubleArrowRight';
import KeyboardDoubleArrowLeftIcon from '@mui/icons-material/KeyboardDoubleArrowLeft';
import { useSearchParams } from 'react-router-dom';
import {
  Box,
  Button,
  TableRow,
  TextField,
  TableContainer,
  Table,
  TableHead,
  TableCell,
  TableBody,
  Paper,
  IconButton,
  Tooltip,
  Pagination,
  PaginationItem,
  Dialog,
  DialogTitle,
  DialogContent,
  List,
  ListItem,
  ListItemText
} from '@mui/material';
import { CircularProgress } from '@mui/material';
import VisibilityIcon from '@mui/icons-material/Visibility';
import { fetchLearnedVocabularies } from '../../../services/user/homework';
import { formatHtml } from '../../common/FormatHtml';
import { TableCellCustom } from '../../../pages/admin/components/TableList';

export default function StatisticalVocabList({ role }) {
  const [vocabList, setVocabList] = useState([]);
  const [searchParams, setSearchParams] = useSearchParams();
  const pageParam = parseInt(searchParams.get("page") || "1", 10);
  const searchInputParam = searchParams.get("search") || "";
  
  const [search, setSearch] = useState(searchInputParam);
  const [page, setPage] = useState(pageParam);

  const [rowsPerPage] = useState(10);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedVocab, setSelectedVocab] = useState(null);

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const fetchData = async (params) => {
    setLoading(true);
    try {
      const {
        data: { data, meta }
      } = await fetchLearnedVocabularies(
        {
          page: params?.page ?? page,
          per_page: rowsPerPage,
          search: params?.search ?? search,
        },
        role
      );
      setTotal(meta?.last_page);
      setVocabList(data);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [page, rowsPerPage]);

  const handleOpenDialog = (vocab) => {
    setSelectedVocab(vocab);
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedVocab(null);
  };

  const handleSearchClick = () => {
    setSearchParams({ page: 1, search });
    setPage(1);
    fetchData();
  };

  const handleReset = () => {
    setPage(1);
    setSearch('');
    const param = {search: '', page: 1 }
    setSearchParams(param);
    fetchData(param);
  };

  return (
    <Box sx={{ px: 10, maxWidth: '100%', pt: 10 }}>
      <Box
        sx={{
          backgroundColor: '#ffffff',
          p: 2,
          borderRadius: 3,
          position: 'sticky',
          top: 70,
          zIndex: 10,
          mb: 2,
          display: 'flex',
          gap: 2
        }}
      >
        <TextField
          placeholder='Search by word'
          variant='outlined'
          size='small'
          fullWidth
          value={search}
          onChange={e => {
            setSearch(e.target.value);
            setPage(1);
          }}
          onKeyDown={e => {
              if (e.key === 'Enter') {
                  if (loading) return;
                  handleSearchClick();
              }
          }}
        />
          <Button variant='contained' className='btn-primary' onClick={handleSearchClick}>
            Search
          </Button>
          <Button
            variant='contained'
            sx={{ mr: 2.5, bgcolor: '#8592A3', fontSize: '0.9375rem', textTransform: 'capitalize' }}
            onClick={handleReset}
        >
            Reset
          </Button>
        </Box>
      <Box>
        <TableContainer
          component={Paper}
        >
          <Table>
            <TableHead sx={{ backgroundColor: 'white', zIndex: 1 }}>
              <TableRow>
                <TableCell sx={{ fontWeight: 'bold',  width: '10%' ,textAlign: 'center' }}>ID</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>Word</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>Definition</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>Description</TableCell>
                <TableCell sx={{ fontWeight: 'bold',  textAlign: 'center' }}>Frequency in flashcards</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>Sentences with Vocabulary</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCellCustom colSpan={6} align='center'>
                    <CircularProgress size={24} />
                  </TableCellCustom>
                </TableRow>
              ) : vocabList.length === 0 ? (
                <TableRow>
                  <TableCellCustom colSpan={6} align='center'>
                    No data available.
                  </TableCellCustom>
                </TableRow>
              ) : (
                vocabList.map((vocab, index) => (
                  <React.Fragment key={vocab.vocabulary_id}>
                    <TableRow>
                      <TableCellCustom sx={{ textAlign: 'center' }}>{page * rowsPerPage - rowsPerPage + index + 1}</TableCellCustom>
                      <TableCellCustom>{vocab.word}</TableCellCustom>
                      <TableCellCustom
                        sx={{
                          maxWidth: 200,
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap',
                                                  }}
                      >
                        {vocab.definition}
                      </TableCellCustom>
                      <TableCellCustom
                        sx={{
                          maxWidth: 200,
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap',
                                                  }}
                      >
                        {formatHtml(vocab.description).slice(0, 100)}
                      </TableCellCustom>
                      <TableCellCustom sx={{ textAlign: 'center' }}>{vocab.vocabulary_grades_count}</TableCellCustom>
                      <TableCellCustom>
                        <Tooltip title='List vocabularies'>
                          <IconButton color='success' onClick={() => handleOpenDialog(vocab)}>
                            <VisibilityIcon />
                          </IconButton>
                        </Tooltip>
                      </TableCellCustom>
                    </TableRow>
                  </React.Fragment>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </Box>
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>Sentences with Vocabulary for: {selectedVocab?.word}</DialogTitle>
        <DialogContent dividers>
          {(() => {
            let count = 0;
            return (
              <>
                {selectedVocab?.answers?.map((ans, i) => {
                  if (ans && ans.trim() !== '') {
                    count++;
                    return (
                      <ListItem key={i} divider>
                        <ListItemText primary={`${count}. ${ans}`} />
                      </ListItem>
                    );
                  }
                  return null;
                })}
                {count === 0 && <p>Sentences with Vocabulary not found.</p>}
              </>
            );
          })()}
        </DialogContent>
      </Dialog>
      <Box my={2.5} display='flex' justifyContent='flex-end'>
        {total > 1 && (
          <Pagination
            count={total}
            page={page}
            onChange={handleChangePage}
            shape='rounded'
            renderItem={item => (
              <PaginationItem
                {...item}
                color='primary'
                slots={{
                  previous: KeyboardDoubleArrowLeftIcon,
                  next: KeyboardDoubleArrowRightIcon
                }}
              />
            )}
          />
        )}
       
      </Box>
    </Box>
  );
}
