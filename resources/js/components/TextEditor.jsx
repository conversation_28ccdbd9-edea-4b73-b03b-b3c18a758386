import { Editor } from '@tinymce/tinymce-react';
import { memo, useMemo, useRef } from 'react';

const tinyApiKey = process.env.MIX_TINYMCE_API_KEY ?? '';

const TextEditor = memo(({ init, onInit, ...props }) => {
  const editorRef = useRef(null);
  const editorConfig = useMemo(() => {
    const {
      plugins = 'anchor autolink charmap codesample emoticons image link lists media searchreplace table visualblocks wordcount',
      toolbar = `undo redo | blocks fontfamily fontsize | formatselect | bold italic underline strikethrough backcolor | align lineheight | numlist bullist indent outdent | removeformat | table tabledelete | tableprops tablerowprops tablecellprops | tableinsertrowbefore tableinsertrowafter tabledeleterow | tableinsertcolbefore tableinsertcolafter tabledeletecol`,
    } = init || {};

    return {
      height: 200,
      minRows: 5,
      branding: false,
      menubar: false,
      toolbar_sticky: true,
      skin: 'oxide',
      ...init,
      plugins,
      toolbar,
    };
  }, [init]);

  const handleInit = useMemo(() => (evt, editor) => {
    editorRef.current = editor;
    onInit?.(evt, editor);
  }, [onInit]);

  return (
    <></>
  );
});

TextEditor.displayName = 'TextEditor';

export default TextEditor;
