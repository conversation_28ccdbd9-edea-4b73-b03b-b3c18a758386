import { Box, <PERSON>ton, Card, Typography } from '@mui/material';
import BookIcon from '@mui/icons-material/Book';
import { getDurationInHoursMinutes } from '../utils/formatTime';

const GradeInfoTask = ({
  tasks,
  currentTaskIndex,
  onSubmit,
  onJumpToTask,
  isUser
}) => {
  return (
    <Card elevation={1} sx={{ p: 2, display: 'flex', flexDirection: 'column', overflow:"auto", flex:1 , justifyContent:"space-evenly"}}>
      <Typography variant='h6' sx={{ fontWeight: 'bold', color: 'text.primary' }}>
        Task Sections ({currentTaskIndex + 1}/{tasks.length})
      </Typography>
      <Box sx={{ borderRadius: 1,  display: 'flex', flexDirection: 'column', gap: 1 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', color: 'info.dark' }}>
          <BookIcon sx={{ mr: 1 }} />
          <Typography variant='body1'>{tasks[currentTaskIndex]?.title}</Typography>
        </Box>
      </Box>
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5, mt: 1 }}>
        <Typography variant='body2' sx={{ color: 'text.secondary' }}>
          Score task:
          <Typography component='span' sx={{ fontWeight: 'bold', ml: 0.5 }}>
            {tasks[currentTaskIndex]?.total_score_actual} of {tasks[currentTaskIndex]?.total_score} pts
          </Typography>
        </Typography>
        <Typography variant='body2' sx={{ color: 'text.secondary' }}>
          Evaluation criteria:
          <Typography component='span' sx={{ fontWeight: 'bold', ml: 0.5 }}>
            {tasks[currentTaskIndex]?.criteria}
          </Typography>
        </Typography>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant='body2' sx={{ color: 'text.secondary' }}>
            ⏰ Total Time
          </Typography>
          <Typography
            variant='h6'
            sx={{ fontWeight: 'bold', color: 'text.secondary' }}
          >
            {tasks.length > 0
              ? getDurationInHoursMinutes(tasks[currentTaskIndex].start_time, tasks[currentTaskIndex].end_time)
              : '00:00'}
          </Typography>
        </Box>
      </Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}>
        <Box display={'flex'} gap={2} alignItems={'center'} sx={{flexWrap:'wrap'}}>
          {tasks.map((task, index) => (
            <Button
              key={task.id || index}
              variant={index === currentTaskIndex ? 'contained' : 'outlined'}
              size='small'
              onClick={() => onJumpToTask && onJumpToTask(index)}
              sx={{
                minWidth: '40px',
                height: '32px',
                fontSize: '0.875rem',
                fontWeight: index === currentTaskIndex ? 'bold' : 'normal'
              }}
            >
              {index + 1}
            </Button>
          ))}
        </Box>
        {!isUser && (
          <Button sx={{ maxHeight: '55px' }} size='small' variant="contained" onClick={onSubmit}>
            Save Grades
          </Button>
        )}
      </Box>
    </Card>
  );
};

export default GradeInfoTask;
