import { Box, Card, Typography } from '@mui/material';
import React from 'react';
import { getDurationInHoursMinutes } from '../utils/formatTime';

const GradeInfoHomework = ({
  homeworkDetail
                           }) => {
  return (
    <Card elevation={1} sx={{ p: 2, display: 'flex', flexDirection: 'column', gap: 2, overflow:"auto" }}>
      <Typography variant='h5' component='h2' sx={{ fontWeight: 'bold', color: 'text.primary' }}>
        {homeworkDetail?.title}
      </Typography>
      <Typography variant='body2' sx={{ color: 'text.secondary' }}>
        Description: {homeworkDetail?.description}
      </Typography>

      <Typography variant='body2' sx={{ color: 'text.secondary' }}>
        Total score homework:
        <Typography component='span' sx={{ fontWeight: 'bold', ml: 0.5 }}>
          {homeworkDetail?.total_score_actual} of {homeworkDetail?.total_score} pts
        </Typography>
      </Typography>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant='body2' sx={{ color: 'text.secondary' }}>
          ⏰ Total Time
        </Typography>
        <Typography
          variant='h6'
          sx={{ fontWeight: 'bold', color: 'primary.main' }}
        >
          {getDurationInHoursMinutes(homeworkDetail?.start_time, homeworkDetail?.end_time)}
        </Typography>
      </Box>
    </Card>
  );
};

export default GradeInfoHomework;
