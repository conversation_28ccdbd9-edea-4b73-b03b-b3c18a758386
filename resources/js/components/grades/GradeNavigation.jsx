import { Box, Button, Card, Grid, Typography } from '@mui/material';
import ArrowBackIosNewIcon from '@mui/icons-material/ArrowBackIosNew';
import ArrowForwardIosIcon from '@mui/icons-material/ArrowForwardIos';
import React from 'react';

const GradeNavigation = ({
  tasks,
  currentTaskIndex,
  currentQuestionIndex,
  onJumpToQuestion,
  onNextQuestion,
  onPrevQuestion
                         }) => {
  return (
    <Card elevation={1} sx={{ p: 2, display: 'flex', flexDirection: 'column', gap: 2, flex: 1 }}>
      <Typography variant='h6' sx={{ fontWeight: 'bold', color: 'text.primary' }}>
        Question Navigation
      </Typography>

      <Grid container spacing={1} overflow={'auto'}>
        {Array.from({ length: tasks[currentTaskIndex]?.questions.length }, (_, i) => i + 1).map(num => (
          <Grid item xs={2.4} key={num}>
            <Button
              variant={num === currentQuestionIndex + 1 ? 'contained' : 'outlined'}
              onClick={() => onJumpToQuestion(num)}
              sx={{
                minHeight: '32px',
                minWidth: '40px',
                height: '32px',
                fontSize: '0.875rem',
                borderColor: num === currentQuestionIndex + 1 ? 'primary.main' : 'grey.300',
                color: num === currentQuestionIndex + 1 ? 'white' : 'text.primary',
                backgroundColor: num === currentQuestionIndex + 1 ? 'primary.main' : 'transparent',
                '&:hover': {
                  backgroundColor: num === currentQuestionIndex + 1 ? 'primary.dark' : 'grey.100',
                },
              }}
            >
              {num}
            </Button>
          </Grid>
        ))}
      </Grid>

      <Box sx={{ display: 'flex', gap: 1, pt: 2, borderTop: 1, borderColor: 'grey.300' }}>
        <Button
          variant='contained'
          onClick={onPrevQuestion}
          size='small'
          disabled={currentQuestionIndex === 0}
          sx={{
            fontSize: '0.75rem',
            py: 0.5,
            px: 1.5,
            minHeight: '28px',
          }}
          startIcon={<ArrowBackIosNewIcon sx={{ fontSize: 16 }} />}
        >
          Previous
        </Button>

        <Button
          variant='contained'
          onClick={onNextQuestion}
          size='small'
          disabled={currentQuestionIndex === tasks[currentTaskIndex]?.questions.length - 1}
          sx={{
            fontSize: '0.75rem',
            py: 0.5,
            px: 1.5,
            minHeight: '28px',
          }}
          endIcon={<ArrowForwardIosIcon sx={{ fontSize: 16 }} />}
        >
          Next
        </Button>

      </Box>
    </Card>
  );
};

export default GradeNavigation;
