import { Box } from '@mui/material';
import GradeInfoTask from './GradeInfoTask';
import GradeInfoHomework from './GradeInfoHomework';
import GradeNavigation from './GradeNavigation';
const GradeSideBar = ({
  tasks,
  homeworkDetail,
  currentTaskIndex,
  setCurrentTaskIndex,
  currentQuestionIndex,
  setCurrentQuestionIndex,
  onSubmit,
  isUser
}) => {

  const onJumpToQuestion = (num) => {
    setCurrentQuestionIndex(num - 1);
  };

  const onPrevQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1);
    }
  };
  const onNextQuestion = () => {
    if (currentQuestionIndex < tasks[currentTaskIndex]?.questions.length - 1) {
      setCurrentQuestionIndex(prev => prev + 1);
    }
  };
  return (
    <>
      <Box sx={{
        flexBasis: { xs: '100%', md: '25%' },
        flexGrow: 1,
        minWidth: { md: '280px' },
        position: 'sticky',
        top: 0,
        height: '100%',
        overflowY: 'auto',
        display: 'flex',
        flexDirection: 'column',
        gap: '5px'
      }}>
        <GradeInfoHomework
          homeworkDetail={homeworkDetail}
        />

        <GradeInfoTask
          tasks={tasks}
          currentTaskIndex={currentTaskIndex}
          onSubmit={onSubmit}
          onJumpToTask={(index) => {
            setCurrentTaskIndex(index)
            setCurrentQuestionIndex(0)
          }}
          isUser={isUser}
        />

        <GradeNavigation
          tasks={tasks}
          currentTaskIndex={currentTaskIndex}
          currentQuestionIndex={currentQuestionIndex}
          onJumpToQuestion={onJumpToQuestion}
          onNextQuestion={onNextQuestion}
          onPrevQuestion={onPrevQuestion}
        />
      </Box>
    </>
  )
}
export default GradeSideBar;
