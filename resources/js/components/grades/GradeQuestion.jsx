import React, { useEffect, useRef } from 'react';
import {
  Paper,
  Box,
  Typography,
  TextField,
  RadioGroup,
  Radio,
  FormControlLabel,
  FormGroup,
  Checkbox,
  Chip,
  Card,
  CardContent,
  Divider,
  Stack,
  Autocomplete, Select, MenuItem
} from '@mui/material';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CancelIcon from '@mui/icons-material/Cancel';
import EditIcon from '@mui/icons-material/Edit';
import AssignmentIcon from '@mui/icons-material/Assignment';
import GradeIcon from '@mui/icons-material/Grade';
import { Controller } from "react-hook-form";
import QuestionTypeText from '../../enums/QuestionTypeText';
import QuestionType from '../../enums/QuestionType';
import { Editor } from '@tinymce/tinymce-react';
import { FlashCard } from "../exam/FlashCard";
import { tinymceInit } from '../../config/tinymce-init';
import { env } from '../../config/env';

const GradeQuestion = ({
  control,
  tasks,
  currentTaskIndex,
  currentQuestionIndex,
  setValue,
  trigger,
  watch,
  isUser,
  grades,
  handleFlashcardChange
}) => {
  const listTypeHasScore = [
    QuestionType.SINGLE_CHOICE,
    QuestionType.MULTIPLE_CHOICE,
    QuestionType.FILL_IN_THE_BLANK,
    QuestionType.MAKE_SENTENCE,
    QuestionType.MAKE_PARAGRAPH,
    QuestionType.TRANSCRIPTION,
  ];
  const questionRefs = useRef([]);

  useEffect(() => {
    if (questionRefs.current[currentQuestionIndex]) {
      questionRefs.current[currentQuestionIndex].scrollIntoView({
        behavior: "smooth",
        block: "center",
      });
    }
  }, [currentQuestionIndex]);

  const getQuestionTypeColor = (type) => {
    const colors = {
      [QuestionType.SINGLE_CHOICE]: 'primary',
      [QuestionType.MULTIPLE_CHOICE]: 'secondary',
      [QuestionType.FILL_IN_THE_BLANK]: 'success',
      [QuestionType.ESSAY]: 'warning',
      [QuestionType.MAKE_SENTENCE]: 'info',
      [QuestionType.MAKE_PARAGRAPH]: 'error',
      [QuestionType.TRANSCRIPTION]: 'default',
    };
    return colors[type] || 'default';
  };

  return (
    <Box sx={{
      flexBasis: { xs: '100%', md: '50%' },
      flexGrow: 1,
      minWidth: { md: '450px' },
      height: '100%',
      overflowY: 'auto',
      pr: 2,
      pl: 1,
      '&::-webkit-scrollbar': {
        width: '6px',
        backgroundColor: 'transparent'
      },
      '&::-webkit-scrollbar-thumb': {
        backgroundColor: 'rgba(0,0,0,0.2)',
        borderRadius: '3px'
      },
      '&::-webkit-scrollbar-track': {
        backgroundColor: 'transparent'
      }
    }}>
      {!!tasks[currentTaskIndex] && (
        <Box
          display="flex"
          justifyContent="space-between"
          alignItems="center"
          p={2}
          borderRadius={2}
          boxShadow={1}
          bgcolor="background.paper"
          mb={2}
        >
          <Typography variant="h6" fontWeight="bold">
            {tasks[currentTaskIndex]?.title}
          </Typography>

          <Typography variant="body1" color="text.secondary" fontWeight="bold">
            Total Score: {tasks[currentTaskIndex]?.total_score_actual} / {tasks[currentTaskIndex]?.total_score} pts
          </Typography>
        </Box>
      )}
      <Stack spacing={3} sx={{ pb: 3 }}>
        {tasks[currentTaskIndex]?.questions.map((q, index) => {
          const selectedOptions = q.account_answers?.selected_options || [];
          const correctOptions = q.correct_options || [];
          const isCurrentQuestion = index === currentQuestionIndex;

          const isCorrect = (optIdx) => correctOptions.includes(optIdx);
          const isSelected = (optIdx) => selectedOptions.includes(optIdx);
          const isSingle = q.type === QuestionType.SINGLE_CHOICE;
          const isMultiple = q.type === QuestionType.MULTIPLE_CHOICE;

          return (
            <Card
              key={index}
              ref={el => questionRefs.current[index] = el}
              elevation={isCurrentQuestion ? 8 : 2}
              sx={{
                border: isCurrentQuestion ? '2px solid' : '1px solid',
                borderColor: isCurrentQuestion ? 'primary.main' : 'grey.300',
                borderRadius: 3,
                overflow: 'visible',
                position: 'relative',
                transition: 'all 0.3s ease',
                '&:hover': {
                  elevation: 4,
                  borderColor: 'primary.light'
                },
                ...(isCurrentQuestion && {
                  backgroundColor: 'primary.50',
                  '&::before': {
                    content: '""',
                    position: 'absolute',
                    top: -2,
                    left: -2,
                    right: -2,
                    bottom: -2,
                    background: 'linear-gradient(45deg, #1976d2, #42a5f5)',
                    borderRadius: 3,
                    zIndex: -1,
                    opacity: 0.1
                  }
                })
              }}
            >
              {/* Question Header */}
              <CardContent sx={{ pb: 2 }}>
                <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
                  <Box display="flex" alignItems="center" gap={2}>
                    <Box display="flex" alignItems="center" gap={1}>
                      <AssignmentIcon color="primary" fontSize="small" />
                      <Typography variant="h6" fontWeight={600} color="primary.main">
                        Question {index + 1}
                      </Typography>
                    </Box>
                    <Chip
                      label={QuestionTypeText[q.type]}
                      color={getQuestionTypeColor(q.type)}
                      size="small"
                      variant="outlined"
                    />
                  </Box>

                  {/* Score Section */}
                  <Box display="flex" alignItems="center" gap={2}>
                    {listTypeHasScore.includes(q.type) && !isUser && (
                      <Controller
                        name={`tasks.${currentTaskIndex}.questions.${index}.account_answers.score`}
                        control={control}
                        rules={{
                          required: 'Score is required',
                          min: { value: 0, message: 'Score must be >= 0' },
                          max: { value: q.score, message: `Max score is ${q.score}` },
                        }}
                        render={({ field, fieldState }) => (
                          <Box display="flex" alignItems="center">
                            <RadioGroup
                              row
                              {...field}
                              onChange={(e) => field.onChange(Number(e.target.value))}
                            >
                              <Stack direction="row" spacing={1} flexWrap="wrap">
                                {Array.from({ length: q.score + 1 }).map((_, i) => (
                                  <Box
                                    key={i}
                                    display="flex"
                                    flexDirection="column"
                                    alignItems="center"
                                    sx={{ minWidth: 32 }}
                                  >
                                    <Radio
                                      value={i}
                                      checked={field.value === i}
                                      onChange={(e) => field.onChange(Number(e.target.value))}
                                      size="small"
                                    />
                                    <Typography variant="caption">{i}</Typography>
                                  </Box>
                                ))}
                              </Stack>
                            </RadioGroup>
                            {fieldState.error && (
                              <Typography variant="caption" color="error" sx={{ ml: 2 }}>
                                {fieldState.error.message}
                              </Typography>
                            )}
                          </Box>
                        )}
                      />
                    )}
                    <Chip
                      icon={<GradeIcon />}
                      label={`${q.account_answers?.score ?? 0} / ${q.score} pts`}
                      color="success"
                      variant="outlined"
                    />
                  </Box>
                </Box>

                {/* Question Content */}
                <Paper
                  elevation={0}
                  sx={{
                    p: 3,
                    backgroundColor: 'grey.50',
                    borderRadius: 2,
                    border: '1px solid',
                    borderColor: 'grey.200'
                  }}
                >
                  <Editor
                    tinymceScriptSrc="/tinymce/tinymce.min.js"
                    init={{
                      ...tinymceInit,
                      menubar: false,
                      toolbar: false,
                      plugins: 'autoresize',
                      autoresize_bottom_margin: 0,
                      autoresize_min_height: 100,
                      autoresize_max_height: 1000,
                    }}
                    disabled={true}
                    value={q.content || ''}
                  />
                </Paper>

                {/* Multiple Choice Options */}
                {(isSingle || isMultiple) && q.options?.length > 0 && (
                  <Box mt={3}>
                    <Typography variant="subtitle1" fontWeight={600} mb={2} color="text.primary">
                      Answer Options:
                    </Typography>
                    <Paper elevation={0} sx={{ p: 2, backgroundColor: 'grey.50', borderRadius: 2 }}>
                      {isSingle ? (
                        <RadioGroup value={selectedOptions[0] ?? -1}>
                          {q.options.map((option, i) => {
                            const isCorrectAns = isCorrect(i);
                            const isWrong = isSelected(i) && !isCorrectAns;
                            const isSelectedAns = isSelected(i);

                            return (
                              <Box key={i} mb={1} sx={{
                                border: '2px solid',
                                borderColor: isCorrectAns
                                  ? 'success.main'
                                  : isWrong
                                    ? 'error.main'
                                    : 'grey.300',
                                borderRadius: 2,
                                backgroundColor: isCorrectAns
                                  ? 'success.50'
                                  : isWrong
                                    ? 'error.50'
                                    : isSelectedAns
                                      ? 'action.selected'
                                      : 'transparent',
                                transition: 'all 0.2s ease',
                                position: 'relative',
                                overflow: 'hidden'
                              }}>
                                <FormControlLabel
                                  value={i}
                                  control={<Radio disabled />}
                                  label={
                                    <Box display="flex" alignItems="center" justifyContent="space-between" width="100%">
                                      <Typography
                                        variant="body2"
                                        sx={{
                                          fontWeight: isSelectedAns ? 600 : 400,
                                          color: isWrong ? 'error.main' : 'inherit'
                                        }}
                                      >
                                        {option}
                                      </Typography>
                                      <Box display="flex" gap={1} sx={{ ml: 1 }}>
                                        {isCorrectAns && (
                                          <Chip label="Correct" color="success" size="small" />
                                        )}
                                        {isSelectedAns && (
                                          <Chip
                                            label="Selected"
                                            color={isWrong ? "error" : "success"}
                                            size="small"
                                          />
                                        )}
                                      </Box>
                                    </Box>
                                  }
                                  sx={{
                                    width: '100%',
                                    ml: 0,
                                    borderRadius: 1,
                                    backgroundColor: isSelectedAns ?
                                      (isWrong ? 'warning.50' : 'success.50') :
                                      'transparent',
                                    '&:hover': {
                                      backgroundColor: 'action.hover'
                                    }
                                  }}
                                />
                              </Box>
                            );
                          })}
                        </RadioGroup>
                      ) : (
                        <FormGroup>
                          {q.options.map((option, i) => {
                            const isCorrectAns = isCorrect(i);
                            const isSelectedAns = isSelected(i);
                            const isWrong = isSelectedAns && !isCorrectAns;

                            return (
                              <Box key={i} mb={1} sx={{
                                border: '2px solid',
                                borderColor: isCorrectAns
                                  ? 'success.main'
                                  : isWrong
                                    ? 'error.main'
                                    : 'grey.300',
                                borderRadius: 2,
                                backgroundColor: isCorrectAns
                                  ? 'success.50'
                                  : isWrong
                                    ? 'error.50'
                                    : isSelectedAns
                                      ? 'action.selected'
                                      : 'transparent',
                                transition: 'all 0.2s ease',
                                position: 'relative',
                                overflow: 'hidden'
                              }}>
                                <FormControlLabel
                                  control={<Checkbox checked={isSelectedAns} disabled />}
                                  label={
                                    <Box display="flex" alignItems="center" justifyContent="space-between" width="100%">
                                      <Typography
                                        variant="body2"
                                        sx={{
                                          fontWeight: isSelectedAns ? 600 : 400,
                                          color: isWrong ? 'error.main' : 'inherit'
                                        }}
                                      >
                                        {option}
                                      </Typography>
                                      <Box display="flex" gap={1} sx={{ ml: 1 }}>
                                        {isCorrectAns && (
                                          <Chip label="Correct" color="success" size="small" />
                                        )}
                                        {isSelectedAns && (
                                          <Chip
                                            label="Selected"
                                            color={isWrong ? "error" : "success"}
                                            size="small"
                                          />
                                        )}
                                      </Box>
                                    </Box>
                                  }
                                  sx={{
                                    width: '100%',
                                    ml: 0,
                                    borderRadius: 1,
                                    '&:hover': {
                                      backgroundColor: 'action.hover'
                                    }
                                  }}
                                />
                              </Box>
                            );
                          })}
                        </FormGroup>
                      )}
                    </Paper>
                  </Box>
                )}

                {/* Fill in the Blank */}
                {q.type === QuestionType.FILL_IN_THE_BLANK && (
                  <Box mt={3}>
                    <Typography variant="subtitle1" fontWeight={600} mb={2}>
                      Student Answer:
                    </Typography>
                    <Paper
                      elevation={0}
                      sx={{
                        p: 1,
                        border: '2px solid',
                        borderColor: q.account_answers?.score === q.score ? 'success.main' : 'error.main',
                        borderRadius: 2,
                        backgroundColor: q.account_answers?.score === q.score ? 'success.50' : 'error.50',
                      }}
                    >
                      <Box display="flex" alignItems="center" gap={2}>
                        {q.account_answers?.score === q.score ? (
                          <CheckCircleIcon color="success" />
                        ) : (
                          <CancelIcon color="error" />
                        )}
                        <Typography variant="body1" fontWeight={500}>
                          {q.account_answers?.answer || <em style={{ color: '#666' }}>(No answer provided)</em>}
                        </Typography>
                      </Box>
                    </Paper>

                    {q.account_answers?.score !== q.score && (
                      <Box mt={2}>
                        <Typography variant="subtitle1" fontWeight={600} mb={1}>
                          Correct Answer:
                        </Typography>
                        <Paper
                          elevation={0}
                          sx={{
                            p: 1,
                            border: '2px solid',
                            borderColor: 'success.main',
                            borderRadius: 2,
                            backgroundColor: 'success.50',
                          }}
                        >
                          <Box display="flex" alignItems="center" gap={2}>
                            <CheckCircleIcon color="success" />
                            <Autocomplete
                              multiple
                              freeSolo
                              value={Array.isArray(q.answer) ? q.answer : JSON.parse(q.answer || '[]')}
                              options={[]}
                              slotProps={{
                                input: { readOnly: true }
                              }}
                              disableClearable
                              renderInput={(params) => (
                                <TextField
                                  {...params}
                                  label="Correct answers"
                                  size="small"
                                />
                              )}
                            />
                          </Box>
                        </Paper>
                      </Box>
                    )}
                  </Box>
                )}

                {q.type === QuestionType.TRANSCRIPTION && (
                  <>
                    <Box display="flex" justifyContent="center" mt={2}>
                      <Box
                        component="img"
                        src={q.image_url}
                        alt="Preview"
                        sx={{
                          maxWidth: "100%",
                          height: "auto",
                          width: 600,
                          borderRadius: 2,
                          boxShadow: 3,
                        }}
                      />
                    </Box>
                    <Box mt={2}>
                      <Editor
                        tinymceScriptSrc="/tinymce/tinymce.min.js"
                        init={{
                          plugins: 'anchor autolink charmap codesample emoticons image link lists media searchreplace table visualblocks wordcount',
                          toolbar: 'undo redo | blocks fontfamily fontsize | bold italic underline strikethrough | link image media table | align lineheight | numlist bullist indent outdent | emoticons charmap | removeformat',
                          license_key: 'gpl',
                          browser_spellcheck: true,
                          contextmenu: false,
                        }}
                        disabled={true}
                        value={q.account_answers.answer || 'No answer provided'}
                      />
                    </Box>
                  </>
                )}

                {/* Essay Display */}
                {q.type === QuestionType.ESSAY && (
                  <Box mt={3}>
                    <Typography variant="subtitle1" fontWeight={600} mb={2} display="flex" alignItems="center" gap={1}>
                      <EditIcon fontSize="small" />
                      Student's Essay:
                    </Typography>
                    <Paper
                      elevation={0}
                      sx={{
                        p: 3,
                        border: '1px solid',
                        borderColor: 'grey.300',
                        borderRadius: 2,
                        backgroundColor: 'grey.50',
                        minHeight: 100
                      }}
                    >
                      <Typography
                        variant="body1"
                        color="text.primary"
                        component="div"
                        dangerouslySetInnerHTML={{ __html: q.account_answers?.answer || '<em>No essay submitted</em>' }}
                        sx={{ lineHeight: 1.6 }}
                      />
                    </Paper>
                  </Box>
                )}

                {/* Make Sentence/Paragraph */}
                {(q.type === QuestionType.MAKE_SENTENCE || q.type === QuestionType.MAKE_PARAGRAPH) && (
                  <Box mt={3}>
                    {q.vocabularies && q.vocabularies.length > 0 && (
                      <Box mb={2}>
                        <Typography variant="subtitle1" fontWeight={600} mb={1}>
                          Vocabulary Words:
                        </Typography>
                        <Box display="flex" flexWrap="wrap" gap={1}>
                          {q.vocabularies.map((v, i) => (
                            <Chip key={i} label={v.word} size="small" variant="outlined" color="primary" />
                          ))}
                        </Box>
                      </Box>
                    )}

                    {q.type === QuestionType.MAKE_PARAGRAPH && (
                      <Box>
                        <Typography variant="body2" color="text.secondary" mb={2}>
                          Minimum words required: {q.min_word}
                        </Typography>
                        {Array.isArray(q.account_answers?.selected_options) &&
                          q.account_answers.selected_options.map((item, i) => {
                            const selectedVoca = q.vocabularies?.find(v => v.id === item.vocabulary_id);
                            return (
                              <Box key={i} display="flex" alignItems="flex-start" mb={1} gap={1}>
                                <Select
                                  size="small"
                                  value={item.vocabulary_id ?? ''}
                                  sx={{ minWidth: 150 }}
                                  displayEmpty
                                  disabled
                                  renderValue={(selected) => {
                                    if (!selected) {
                                      return <em style={{ color: '#888' }}>Select vocabulary</em>;
                                    }
                                    return selectedVoca?.word || '';
                                  }}
                                >
                                  <MenuItem value="" disabled>
                                    <em>Select vocabulary</em>
                                  </MenuItem>

                                  {(q.vocabularies || []).map(v => (
                                    <MenuItem key={v.id} value={v.id}>
                                      {v.word}
                                    </MenuItem>
                                  ))}
                                </Select>

                                <TextField
                                  fullWidth
                                  variant="outlined"
                                  size="small"
                                  placeholder={`Paragraph ${i + 1}`}
                                  value={item.text || ''}
                                  disabled
                                  multiline
                                  minRows={1}
                                />
                              </Box>
                            );
                          })}
                      </Box>
                    )}

                    {q.type === QuestionType.MAKE_SENTENCE && (
                      <Box>
                        <Typography variant="subtitle1" fontWeight={600} mb={2}>
                          Student's Sentences:
                        </Typography>
                        <Stack spacing={1}>
                          {q.account_answers?.selected_options?.map((answer, i) => (
                            <Paper key={i} elevation={0} sx={{ p: 2, backgroundColor: 'grey.50' }}>
                              <Box display="flex" alignItems="center" gap={2}>
                                <Typography variant="body2" fontWeight={600} color="primary.main">
                                  {i + 1}.
                                </Typography>
                                <Typography variant="body1" sx={{ flex: 1 }}>
                                  {answer || <em>(No sentence provided)</em>}
                                </Typography>
                              </Box>
                            </Paper>
                          ))}
                        </Stack>
                      </Box>
                    )}
                  </Box>
                )}
                {
                  q.type === QuestionType.FLASH_CARD && (
                    <FlashCard
                      grades={grades}
                      question={q}
                      handleFlashcardChange={handleFlashcardChange}
                      isGradeMode={true}
                      disabled={true}
                    />
                  )
                }

                <Divider sx={{ my: 3 }} />

                {/* Grading Section */}
                {q.type === QuestionType.ESSAY ? (
                  <Box>
                    <Typography variant="h6" fontWeight={600} mb={3} display="flex" alignItems="center" gap={1}>
                      <GradeIcon color="primary" />
                      Evaluation Criteria
                    </Typography>

                    {q.evaluation_criteria?.map(({ title, description, rate, id }, criteriaIndex) => {
                      const feedback = q.account_answers?.account_essay_feedback?.find(
                        item => item.evaluation_criteria_id === id
                      );

                      return (
                        <Card
                          key={`criteria-${id}-${criteriaIndex}`}
                          elevation={1}
                          sx={{ mb: 3, border: '1px solid', borderColor: 'grey.200' }}
                        >
                          <CardContent>
                            <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
                              <Box flex={1}>
                                <Typography variant="subtitle1" fontWeight={600} color="primary.main" mb={1}>
                                  {title}
                                </Typography>
                                <Typography
                                  variant="body2"
                                  color="text.secondary"
                                  component="div"
                                  dangerouslySetInnerHTML={{ __html: description }}
                                />
                              </Box>
                              {isUser ? (
                                <Box display="flex" alignItems="center" gap={1} ml={3}>
                                  <Controller
                                    name={`tasks.${currentTaskIndex}.questions.${index}.account_answers.account_essay_feedback.${criteriaIndex}.score`}
                                    control={control}
                                    defaultValue={feedback?.score || 0}
                                    render={({ field, fieldState }) => (
                                      <TextField
                                        {...field}
                                        type="number"
                                        variant="outlined"
                                        size="small"
                                        label="Score"
                                        error={!!fieldState.error}
                                        disabled={true}
                                        helperText={fieldState.error?.message}
                                        sx={{ width: 80 }}
                                      />
                                    )}
                                  />
                                  <Typography variant="body2" color="text.secondary">
                                    / {rate}
                                  </Typography>
                                </Box>
                              ) : (
                                <Box display="flex" alignItems="center" gap={1} ml={3}>
                                  <Controller
                                    name={`tasks.${currentTaskIndex}.questions.${index}.account_answers.account_essay_feedback.${criteriaIndex}.score`}
                                    control={control}
                                    defaultValue={feedback?.score || 0}
                                    render={({ field, fieldState }) => (
                                      <Box display="flex" alignItems="center">
                                        <RadioGroup
                                          row
                                          {...field}
                                          onChange={(e) => {
                                            const newScore = Number(e.target.value);
                                            field.onChange(newScore);

                                            const currentFeedbacks =
                                              watch(
                                                `tasks.${currentTaskIndex}.questions.${index}.account_answers.account_essay_feedback`
                                              ) || [];
                                            const totalScore = currentFeedbacks.reduce(
                                              (sum, feedback, feedbackIndex) => {
                                                const score =
                                                  feedbackIndex === criteriaIndex
                                                    ? newScore
                                                    : feedback?.score || 0;
                                                return sum + score;
                                              },
                                              0
                                            );
                                            setValue(
                                              `tasks.${currentTaskIndex}.questions.${index}.account_answers.score`,
                                              totalScore
                                            );
                                            trigger(
                                              `tasks.${currentTaskIndex}.questions.${index}.account_answers.score`
                                            );
                                          }}
                                        >
                                          <Stack direction="row" spacing={1} flexWrap="wrap">
                                            {Array.from({ length: rate + 1 }).map((_, i) => (
                                              <Box
                                                key={i}
                                                display="flex"
                                                flexDirection="column"
                                                alignItems="center"
                                                sx={{ minWidth: 32 }}
                                              >
                                                <Radio
                                                  value={i}
                                                  checked={field.value === i}
                                                  onChange={field.onChange}
                                                  disabled={isUser}
                                                  size="small"
                                                />
                                                <Typography variant="caption">{i}</Typography>
                                              </Box>
                                            ))}
                                          </Stack>
                                        </RadioGroup>
                                      </Box>
                                    )}
                                  />
                                </Box>
                              )}
                            </Box>

                            <Box mt={3}>
                              <Typography variant="subtitle2" fontWeight={600} mb={2}>
                                Feedback:
                              </Typography>
                              <Controller
                                name={`tasks.${currentTaskIndex}.questions.${index}.account_answers.account_essay_feedback.${criteriaIndex}.feedback`}
                                control={control}
                                defaultValue={feedback?.feedback || ''}
                                render={({ field }) => (
                                  <Paper elevation={0}>
                                    <Editor
                                      tinymceScriptSrc="/tinymce/tinymce.min.js"
                                      init={{
                                        ...tinymceInit,
                                        placeholder: `Provide feedback for "${title}"...`,
                                      }}
                                      disabled={isUser}
                                      onEditorChange={(content) => field.onChange(content)}
                                      value={field.value}
                                    />
                                  </Paper>
                                )}
                              />
                            </Box>

                            <Controller
                              name={`tasks.${currentTaskIndex}.questions.${index}.account_answers.account_essay_feedback.${criteriaIndex}.evaluation_criteria_id`}
                              control={control}
                              defaultValue={id}
                              render={({ field }) => (
                                <input type="hidden" {...field} />
                              )}
                            />
                          </CardContent>
                        </Card>
                      );
                    })}
                  </Box>
                ) : (
                  /* General Feedback */
                  <Box>
                    <Typography variant="subtitle1" fontWeight={600} mb={2}>
                      Feedback:
                    </Typography>
                    <Controller
                      name={`tasks.${currentTaskIndex}.questions.${index}.account_answers.feedback`}
                      control={control}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          fullWidth
                          label={isUser ? '' : 'Enter your feedback...'}
                          variant="outlined"
                          multiline
                          rows={3}
                          onFocus={(e) => isUser && e.target.blur()}
                          InputProps={{ readOnly: isUser}}
                          // disabled={isUser}
                          placeholder={`${isUser? '' : 'Provide constructive feedback to help the student improve...'}`}
                        />
                      )}
                    />
                  </Box>
                )}
                {isUser && tasks?.[currentTaskIndex]?.questions?.[index]?.explanation && (
                  <Box mt={3}>
                    <Typography variant="subtitle2" fontWeight={600} mb={2}>
                      Explanation:
                    </Typography>
                    <Controller
                      name={`tasks.${currentTaskIndex}.questions.${index}.explanation`}
                      control={control}
                      defaultValue={''}
                      render={({ field }) => (
                        <Paper elevation={0}>
                          <Editor
                            tinymceScriptSrc="/tinymce/tinymce.min.js"
                            init={{
                              menubar: false,
                              toolbar: 'bold italic underline | bullist numlist | undo redo',
                              placeholder: `Explanation...`,
                              height: 250,
                              license_key: 'gpl',
                              browser_spellcheck: true,
                              contextmenu: false,
                            }}
                            disabled={isUser}
                            onEditorChange={(content) => field.onChange(content)}
                            value={field.value}
                          />
                        </Paper>
                      )}
                    />
                  </Box>
                )}
              </CardContent>
            </Card>
          );
        })}
      </Stack>
    </Box>
  );
};

export default GradeQuestion;
