import { createRoot } from 'react-dom/client';
import { BrowserRouter, Routes } from 'react-router-dom';
import React from 'react';
const rootEl = document.getElementById('root-assignment-result');
if (!rootEl) {
  throw new Error('Root element not found');
}
createRoot(rootEl).render(
  <React.StrictMode>
    <BrowserRouter basename='/admin/assignment-result'>
      <Routes basename='admin/assignment-result'>
        {/* <Route path='/' element={<FlashCard />} /> */}
        {/* <Route path='/create' element={<VocabularyForm />} />
        <Route path='/:id' element={<VocabularyForm />} /> */}
      </Routes>
    </BrowserRouter>
  </React.StrictMode>
);
