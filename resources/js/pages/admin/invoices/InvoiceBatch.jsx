import React, { useState, useMemo } from "react";
import {
  Box,
  Typography,
  Button,
  Checkbox,
  TextField,
  Autocomplete,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
} from "@mui/material";
import { toast } from 'react-toastify';
import { createInvoiceBatch } from '../../../services/admin/invoices';

const InvoiceBatch = ({ products, students }) => {
    const [submitted, setSubmitted] = useState(false);
    const [studentValue, setStudentValue] = useState(null);
    const [studentInput, setStudentInput] = useState("");


    const productsArray = useMemo(() =>
      Object.values(products).map(product => ({
        ...product,
        price: parseFloat(product.price) || 0
      })), [products]);

    const [rows, setRows] = useState(
        students
            .filter((s) => Array.isArray(s.products) && s.products.length > 0)
            .map((s) => ({
                studentId: s.id,
                name: s.name,
                parentName: s.parent_name,
                parentId: s.parent_id,
                productIds: s.products,
            }))
    );

    const handleRemoveRow = (studentId) => {
    setRows((prev) => prev.filter((r) => r.studentId !== studentId));
  };

  const handleAddRow = (student) => {
    if (!student) return;
    setRows((prev) => [
      ...prev,
      { studentId: student.id, name: student.name, productIds: student.products, parentId: student.parent_id, parentName: student.parent_name },
    ]);
  };

  const handleProductChange = (studentId, newProducts) => {
    setRows((prev) =>
        prev.map((r) =>
            r.studentId === studentId
                ? { ...r, productIds: newProducts.map((p) => p.id) }
                : r
        )
    );
  };

  const calcTotal = (productIds) =>
      productIds
          .map((id) => products[id]?.price ? parseFloat(products[id].price) : 0)
          .reduce((a, b) => a + b, 0);

  const handleCreateInvoice = async () => {
      setSubmitted(true);
      const invalid = rows.some(r => r.productIds.length === 0);
      if (invalid) {
          toast.error("Please select at least one product for each student");
          return;
      }

      const invoices = rows.map((r) => ({
        studentId: r.studentId,
        studentName: r.name,
        parentId: r.parentId,
        products: r.productIds
          .map((id) => products[id])
          .filter(Boolean)
          .map(p => ({
            ...p,
            price: parseFloat(p.price) || 0
          })),
        total: calcTotal(r.productIds),
      }));
      try {
        await createInvoiceBatch(invoices);
        window.location.href = `/admin/invoices`;
      } catch (err) {
        toast.error(err.response?.data?.message || 'Something went wrong!');
      }
  };

  const availableStudents = useMemo(() =>
          students.filter((s) => !rows.some((r) => r.studentId === s.id)),
      [students, rows]
  );

  return (
      <Box sx={{ p: 3 }}>
        <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center", mb: 2 }}>
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            Create Invoice Batch
          </Typography>
          <Button
            component="a"
            href="/admin/invoices"
            variant="contained"
            color="primary"
            size="large"
          >
            Back
          </Button>
        </Box>

        <TableContainer component={Paper} sx={{ mt: 2 }}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell sx={{ fontWeight: 600, width: 200 }}>
                  Student Name
                </TableCell>
                <TableCell sx={{ fontWeight: 600, width: 200 }}>
                  Parent Name - Phone
                </TableCell>
                <TableCell sx={{ fontWeight: 600 }}>
                  Products
                </TableCell>
                <TableCell sx={{ fontWeight: 600 }}>
                  Url Invoice
                </TableCell>
                <TableCell sx={{ fontWeight: 600, width: 150, textAlign: 'center' }}>
                  Total Amount
                </TableCell>
                <TableCell sx={{ fontWeight: 600, width: 100, textAlign: 'center' }}>
                  Action
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {rows.map((row, index) => {
                const total = calcTotal(row.productIds);
                const selectedProducts = productsArray.filter((p) =>
                  row.productIds.includes(p.id)
                );

                return (
                  <TableRow key={row.studentId} hover>
                    <TableCell>
                      <Typography variant="body2" fontWeight={500}>
                        {row.name}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" fontWeight={500}>
                        {row.parentName}
                      </Typography>
                    </TableCell>

                    <TableCell>
                      <Autocomplete
                        multiple
                        size="small"
                        disableCloseOnSelect
                        options={productsArray}
                        getOptionLabel={(option) => option.name}
                        value={selectedProducts}
                        onChange={(e, newValue) =>
                          handleProductChange(row.studentId, newValue)
                        }
                        renderInput={(params) => (
                          <TextField
                            {...params}
                            size="small"
                            label="Choose products"
                            error={submitted && row.productIds.length === 0}
                            helperText={
                              submitted && row.productIds.length === 0
                                ? "Please select at least one product"
                                : ""
                            }
                          />
                        )}
                        renderOption={(props, option, { selected }) => {
                          const { key, ...otherProps } = props;
                          return (
                            <li key={key} {...otherProps}>
                              <Checkbox checked={selected} />
                              <Box>
                                <Typography variant="body2">{option.name}</Typography>
                                <Typography variant="caption" color="text.secondary">
                                  {option.price.toLocaleString()}$
                                </Typography>
                              </Box>
                            </li>
                          );
                        }}
                        sx={{ minWidth: 300 }}
                        isOptionEqualToValue={(option, value) => option.id === value.id}
                        getOptionKey={(option) => option.id}
                      />
                    </TableCell>
                    <TableCell>
                      <Button
                        variant="contained"
                        color="primary"
                        size="small"
                        href={
                          row.productIds.length > 0
                            ? `/admin/invoices/pdf/${row.parentId}?ids=${row.productIds.join(",")}&student_id=${row.studentId}`
                            : undefined
                        }
                        target="_blank"
                        disabled={row.productIds.length === 0}
                      >
                        Preview
                      </Button>
                    </TableCell>

                    <TableCell align="center">
                      <Typography
                        variant="body2"
                        fontWeight={500}
                        color={total > 0 ? 'success.main' : 'text.secondary'}
                      >
                        {total.toLocaleString()}$
                      </Typography>
                    </TableCell>

                    <TableCell align="center">
                      <Box sx={{ display: "flex", gap: 2, justifyContent: "center" }}>
                        <Button
                          variant="contained"
                          color="primary"
                          size="small"
                          href={
                            row.productIds.length > 0
                              ? `/admin/invoices/detail/${row.parentId}?ids=${row.productIds.join(",")}&student_id=${row.studentId}`
                              : undefined
                          }
                          target="_blank"
                          disabled={row.productIds.length === 0}
                        >
                          Detail
                        </Button>
                        <Button
                          variant="outlined"
                          color="error"
                          size="small"
                          onClick={() => handleRemoveRow(row.studentId)}
                        >
                          Delete
                        </Button>
                      </Box>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </TableContainer>

        {availableStudents.length > 0 && (
            <Box sx={{ mt: 2, display: "flex", gap: 2, alignItems: "center" }}>
              <Autocomplete
                options={availableStudents}
                getOptionLabel={(option) => option.name}
                value={studentValue}
                onChange={(e, value) => {
                  handleAddRow(value);
                  setStudentValue(null);
                  setStudentInput("");
                }}
                inputValue={studentInput}
                onInputChange={(e, newInput) => setStudentInput(newInput)}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    size="small"
                    label="Choose student"
                    placeholder="Search student"
                  />
                )}
                sx={{ width: 300 }}
                isOptionEqualToValue={(option, value) => option.id === value.id}
                getOptionKey={(option) => option.id}
              />
              <Typography variant="body2" color="text.secondary">
                  {availableStudents.length} more students can be added
              </Typography>
            </Box>
        )}

        {rows.length > 0 && (
            <Box sx={{ mt: 3, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
              <Typography variant="h6" gutterBottom>
                Statistics
              </Typography>
              <Typography variant="body2">
                Total students: {rows.length}
              </Typography>
              <Typography variant="body2">
                Total: {rows.reduce((sum, row) => sum + calcTotal(row.productIds), 0).toLocaleString()}$
              </Typography>
            </Box>
        )}

        <Box sx={{ mt: 4 }}>
          <Button
              variant="contained"
              color="primary"
              onClick={handleCreateInvoice}
              disabled={rows.length === 0}
              size="large"
          >
            Create {rows.length} invoices
          </Button>
        </Box>
      </Box>
  );
};

export default InvoiceBatch;
