import { createRoot } from 'react-dom/client';
import { BrowserRouter, Route, Routes } from 'react-router-dom';
import React from 'react';
import Grades from './Grades';
import GradeDetail from './GradeDetail';
import { ToastContainer } from 'react-toastify';
import GradesLC from './GradesLC';
import GradesLCDetail from './GradesLCDetail';
import InvoiceBatch from '../invoices/InvoiceBatch';

const rootEl = document.getElementById('grades-root');
if (!rootEl) {
  throw new Error('Root element not found');
}
const products = window.products;

const students = window.students;
createRoot(rootEl).render(
  <React.StrictMode>
    <ToastContainer position="top-right" autoClose={3000} />
    <BrowserRouter basename='/admin'>
      <Routes>
        <Route path='/v2/grades' element={<Grades />} />
        <Route path='/v2/grades/:id' element={<GradeDetail />} />

        <Route path='/grades-lc' element={<GradesLC />} />
        <Route path='/grades-lc/:homeworkId' element={<GradesLCDetail />} />
        <Route path='/invoices-batch' element={<InvoiceBatch products={products} students={students} />} />
      </Routes>
    </BrowserRouter>
  </React.StrictMode>
);
