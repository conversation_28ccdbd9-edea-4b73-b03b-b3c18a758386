import {
  Autocomplete,
  Box,
  Button, CircularProgress,
  Grid, Pagination, PaginationItem,
  Paper, Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow, Tooltip
} from '@mui/material';
import { useEffect, useState, useRef } from 'react';
import Header from '../vocabulary/components/Header';
import { CustomTextField } from '../vocabulary/components/VocaForm';
import { config } from '../vocabulary/utils';
import { getList as getListClasses } from '../../../services/admin/classes';
import { studentListByClassId } from '../../../services/admin/students';
import { getHomeworkStudent } from '../../../services/admin/homeworks';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useForm, Controller } from "react-hook-form";
import { getMainHomeworksByClass } from '../../../services/admin/main-homeworks';
import KeyboardDoubleArrowLeftIcon from '@mui/icons-material/KeyboardDoubleArrowLeft';
import KeyboardDoubleArrowRightIcon from '@mui/icons-material/KeyboardDoubleArrowRight';

const Grades = () => {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const [classes, setClasses] = useState([]);
  const [students, setStudents] = useState([]);
  const [sessions, setSessions] = useState([]);
  const [hiddenSession, setHiddenSession] = useState(false);
  const [homeworks, setHomeworks] = useState([]);
  const [listMainHomeworks, setListMainHomeworks] = useState([]);
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [meta, setMeta] = useState(null);
  const prevValueRef = useRef(null);

  const { control, handleSubmit, setValue, reset, watch } = useForm({
    defaultValues: {
      class: null,
      student: null,
      session: null,
      main_homework: null
    }
  });

  const buildParams = (params, page = 1) => ({
    page,
    class: params?.class_id || null,
    student: params?.student_id || null,
    session: params?.session || null,
    main_homework_id: params?.main_homework_id || null,
  });

  const loadHomeworks = async (params) => {
    setLoading(true);
    try {
      const { data: { data: homeworks, meta } } = await getHomeworkStudent(params);
      setHomeworks(homeworks);
      setMeta(meta);
    } catch (error) {
      console.error('Failed to load homeworks:', error);
      setHomeworks([]);
      setMeta(null);
    } finally {
      setLoading(false);
    }
  };

  const onSubmit = async (formData) => {
    const newSearchParams = {};

    if (formData.class) newSearchParams.class_id = formData.class.id;
    if (formData.student) newSearchParams.student_id = formData.student.student.id;
    if (formData.session) newSearchParams.session = formData.session.value;
    if (formData.main_homework) newSearchParams.main_homework_id = formData.main_homework.id;

    setSearchParams(newSearchParams);
  };

  const handleReset = async () => {
    reset({
      class: null,
      student: null,
      session: null,
      main_homework: null
    });
    setStudents([]);
    setHiddenSession(false);
    setListMainHomeworks([]);
    setSearchParams({});
    await loadHomeworks(buildParams({}), 1);
  };

  const handleChangePage = async (event, newPage) => {
    const currentParams = Object.fromEntries(searchParams.entries());
    setSearchParams({ ...currentParams, page: newPage });
  };

  const handleClassChange = async (value, shouldReset = true) => {
    if (prevValueRef.current?.id === value?.id) {
      return;
    }

    prevValueRef.current = value;

    if (shouldReset) {
      setValue("student", null);
      setStudents([]);
      setListMainHomeworks([]);
      setValue("main_homework", null);
    }

    if (value) {
      if (value.type === 'Private') {
        if (shouldReset) {
          setValue("session", null);
        }
        try {
          const { data: { data: listMainHomeworks } } = await getMainHomeworksByClass({ class_id: value.id });
          setListMainHomeworks(listMainHomeworks);
        } catch (error) {
          console.error('Failed to fetch main homeworks:', error);
          setListMainHomeworks([]);
        }
        setHiddenSession(true);
      } else {
        setHiddenSession(false);
        setListMainHomeworks([]);
      }

      try {
        const { data: students } = await studentListByClassId({ id: value.id });
        setStudents(students);
      } catch (err) {
        console.error("Failed to load students", err);
        setStudents([]);
      }
    } else {
      setHiddenSession(false);
      setStudents([]);
      setListMainHomeworks([]);
      if (shouldReset) {
        setValue("session", null);
      }
    }
  };

  const restoreFormFromURL = async () => {
    const classId = searchParams.get('class_id');
    const studentId = searchParams.get('student_id');
    const sessionValue = searchParams.get('session');
    const mainHomeworkId = searchParams.get('main_homework_id');

    let restoredParams = {
      class_id: classId,
      student_id: studentId,
      session: sessionValue,
      main_homework_id: mainHomeworkId
    };

    if (classId && classes.length > 0) {
      const selectedClass = classes.find(c => c.id == classId);
      if (selectedClass) {
        setValue('class', selectedClass);

        await handleClassChange(selectedClass, false);

        if (studentId && students.length > 0) {
          const selectedStudent = students.find(s => s.student.id == studentId);
          if (selectedStudent) {
            setValue('student', selectedStudent);
          }
        }

        if (mainHomeworkId && listMainHomeworks.length > 0) {
          const selectedMainHomework = listMainHomeworks.find(mh => mh.id == mainHomeworkId);
          if (selectedMainHomework) {
            setValue('main_homework', selectedMainHomework);
          }
        }
      }
    }

    if (sessionValue && sessions.length > 0) {
      const selectedSession = sessions.find(s => s.value == sessionValue);
      if (selectedSession) {
        setValue('session', selectedSession);
      }
    }

    const page = parseInt(searchParams.get('page')) || 1;
    await loadHomeworks(buildParams(restoredParams, page));
  };

  const loadInitialData = async () => {
    try {
      const { data: classList } = await getListClasses();
      setClasses(classList);

      // Generate sessions
      const sessions = Array.from({ length: 16 }, (_, i) => ({
        label: `Session ${i + 1}`,
        value: i + 1
      }));
      setSessions(sessions);

    } catch (error) {
      console.error('Failed to load initial data:', error);
    } finally {
      setInitialLoading(false);
    }
  };

  useEffect(() => {
    loadInitialData();
  }, []);

  useEffect(() => {
    if (!initialLoading && classes.length > 0) {
      restoreFormFromURL();
    }
  }, [searchParams, initialLoading, classes.length]);

  useEffect(() => {
    const studentId = searchParams.get('student_id');
    if (studentId && students.length > 0) {
      const selectedStudent = students.find(s => s.student.id == studentId);
      if (selectedStudent && !watch('student')) {
        setValue('student', selectedStudent);
      }
    }
  }, [students, searchParams, setValue, watch]);

  useEffect(() => {
    const mainHomeworkId = searchParams.get('main_homework_id');
    if (mainHomeworkId && listMainHomeworks.length > 0) {
      const selectedMainHomework = listMainHomeworks.find(mh => mh.id == mainHomeworkId);
      if (selectedMainHomework && !watch('main_homework')) {
        setValue('main_homework', selectedMainHomework);
      }
    }
  }, [listMainHomeworks, searchParams, setValue, watch]);

  if (initialLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box padding={2.5}>
      <Header title='Homework Result' />
      <Grid container spacing={2.75} paddingY={3}>
        <Grid item size={2}>
          <Controller
            name="class"
            control={control}
            render={({ field }) => (
              <Autocomplete
                {...field}
                options={classes}
                getOptionLabel={(option) => option?.name || ''}
                isOptionEqualToValue={(option, value) => option?.id === value?.id}
                onChange={(_, value) => {
                  field.onChange(value);
                  handleClassChange(value);
                }}
                renderInput={(params) => (
                  <CustomTextField {...params} label="Class" size="small" />
                )}
              />
            )}
          />
        </Grid>

        <Grid item size={2}>
          <Controller
            name="student"
            control={control}
            render={({ field }) => (
              <Autocomplete
                {...field}
                options={students}
                getOptionLabel={(option) =>
                  option?.student?.name_english || option?.student?.name_korea || ''
                }
                isOptionEqualToValue={(option, value) => option?.student?.id === value?.student?.id}
                onChange={(_, value) => {
                  field.onChange(value);
                }}
                renderInput={(params) => (
                  <CustomTextField {...params} label="Student" size="small" />
                )}
                disabled={students.length === 0}
              />
            )}
          />
        </Grid>

        {hiddenSession ? (
          <Grid item size={2}>
            <Controller
              name="main_homework"
              control={control}
              render={({ field }) => (
                <Autocomplete
                  {...field}
                  value={field.value || null}
                  options={listMainHomeworks}
                  getOptionLabel={(option) => option?.title || ''}
                  isOptionEqualToValue={(option, value) => option?.id === value?.id}
                  onChange={(_, value) => field.onChange(value)}
                  renderInput={(params) => (
                    <CustomTextField {...params} label="Main Homework" size="small" />
                  )}
                  disabled={listMainHomeworks.length === 0}
                />
              )}
            />
          </Grid>
        ) : (
          <Grid item size={2}>
            <Controller
              name="session"
              control={control}
              render={({ field }) => (
                <Autocomplete
                  {...field}
                  options={sessions}
                  getOptionLabel={(option) => option?.label || ''}
                  isOptionEqualToValue={(option, value) => option?.value === value?.value}
                  onChange={(_, value) => {
                    field.onChange(value);
                  }}
                  renderInput={(params) => (
                    <CustomTextField {...params} label="Session" size="small" />
                  )}
                />
              )}
            />
          </Grid>
        )}

        <Grid item size={4}>
          <Box>
            <Button
              variant='contained'
              sx={{
                bgcolor: config.colors.primary,
                fontSize: '0.9375rem',
                textTransform: 'capitalize',
                mr: 1
              }}
              onClick={handleSubmit(onSubmit)}
              disabled={loading}
            >
              Search
            </Button>

            <Button
              variant='contained'
              sx={{
                mr: 2.5,
                bgcolor: '#8592A3',
                fontSize: '0.9375rem',
                textTransform: 'capitalize'
              }}
              onClick={handleReset}
              disabled={loading}
            >
              Reset
            </Button>
          </Box>
        </Grid>
      </Grid>

      {loading ? (
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
          <CircularProgress />
        </Box>
      ) : (
        <>
          <TableContainer component={Paper} elevation={3}>
            <Table aria-label='collapsible table'>
              <TableHead>
                <TableRow>
                  <TableCell />
                  <TableCell>ID</TableCell>
                  <TableCell>Student Name</TableCell>
                  <TableCell>Title Main Homework</TableCell>
                  <TableCell>Title Homework</TableCell>
                  <TableCell>Homework</TableCell>
                  <TableCell>Class</TableCell>
                  <TableCell>Session</TableCell>
                  <TableCell>Description</TableCell>
                  <TableCell>Action</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {homeworks.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={10} align='center'>
                      No data available
                    </TableCell>
                  </TableRow>
                ) : (
                  homeworks?.map((row, index) => (
                    <TableRow key={row.id ? `row-${row.id}` : `row-index-${index}`}>
                      <TableCell />
                      <TableCell>{(meta?.current_page - 1) * meta?.per_page + index + 1}</TableCell>
                      <TableCell>
                        <Tooltip title={row.account?.name_english || ''}>
                          <span>
                            {row.account?.name_english?.length > 20
                              ? row.account.name_english.slice(0, 20) + '...'
                              : row.account?.name_english}
                          </span>
                        </Tooltip>
                      </TableCell>
                      <TableCell>{row?.main_homework?.title || ''}</TableCell>
                      <TableCell>{row?.homework?.title || ''}</TableCell>
                      <TableCell>{row?.homework?.homework ? 'HW'.concat(row.homework.homework) : '-'}</TableCell>
                      <TableCell>{row?.main_homework?.get_class?.name || ''}</TableCell>
                      <TableCell>{row?.main_homework?.session || ''}</TableCell>
                      <TableCell>
                        <Tooltip title={row?.homework?.description || ''}>
                          <span>
                            {row?.homework?.description?.length > 20
                              ? row.homework.description.slice(0, 20) + '...'
                              : row?.homework?.description || ''}
                          </span>
                        </Tooltip>
                      </TableCell>
                      <TableCell>
                        <Button
                          variant='contained'
                          sx={{
                            bgcolor: config.colors.primary,
                            fontSize: '0.9375rem',
                            textTransform: 'capitalize',
                            mr: 1
                          }}
                          className='btn btn-primary'
                          onClick={() => navigate(`/v2/grades/${row.id}`)}
                        >
                          Edit
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
          {meta?.last_page > 1 && (
            <Stack spacing={2} my={2.5} alignItems={'center'}>
              <Pagination
                count={meta?.last_page}
                page={meta?.current_page || 1}
                onChange={handleChangePage}
                variant='outlined'
                shape='rounded'
                disabled={loading}
                renderItem={item => (
                  <PaginationItem
                    {...item}
                    sx={{
                      '&.MuiPaginationItem-root': {
                        color: config.colors.primary,
                        backgroundColor: '#f0f2f4',
                        border: 'none'
                      },
                      '&.Mui-selected': {
                        backgroundColor: config.colors.primary,
                        color: 'white'
                      },
                      '&:hover': {
                        backgroundColor: '#e1e4e8'
                      },
                      '& .MuiSvgIcon-root': {
                        color: config.colors.bodyColor
                      }
                    }}
                    slots={{
                      previous: KeyboardDoubleArrowLeftIcon,
                      next: KeyboardDoubleArrowRightIcon
                    }}
                  />
                )}
              />
            </Stack>
          )}
        </>
      )}
    </Box>
  );
};

export default Grades;
