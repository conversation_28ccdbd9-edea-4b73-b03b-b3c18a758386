import {
  Autocomplete,
  Box,
  Button, CircularProgress,
  Grid,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow
} from '@mui/material';
import { useEffect, useState } from 'react';
import Header from '../vocabulary/components/Header';
import { CustomTextField } from '../vocabulary/components/VocaForm';
import { config } from '../vocabulary/utils';
import { getList as getListClasses } from '../../../services/admin/classes';
import { studentListByClassId } from '../../../services/admin/students';
import { getDetail } from '../../../services/admin/sessions';
import { toast } from 'react-toastify';
import { getStudentHomeworkList } from '../../../services/admin/homeworks';
import { useForm, Controller } from "react-hook-form";
import { Status, StatusText } from '../../../enums/Status';
import { getMainHomeworksByClass } from '../../../services/admin/main-homeworks';
import { useSearchParams } from 'react-router-dom';

const GradesLC = () => {
  const [classes, setClasses] = useState([]);
  const [students, setStudents] = useState([]);
  const [sessions, setSessions] = useState([]);
  const [hiddenSession, setHiddenSession] = useState(false);
  const [homeworks, setHomeworks] = useState([]);
  const [mainHomeworks, setMainHomeworks] = useState(null);
  const [listMainHomeworks, setListMainHomeworks] = useState([]);
  const [loading, setLoading] = useState(false);

  const [searchParams, setSearchParams] = useSearchParams();

  const { control, handleSubmit, setValue, getValues, reset, watch } = useForm({
    defaultValues: {
      class: null,
      student: null,
      session: null,
      main_homework: null
    }
  });

  const handleReset = () => {
    reset({
      class: null,
      student: null,
      session: null,
      main_homework: null
    });
    setStudents([]);
    setHomeworks([]);
    setMainHomeworks(null);
    setHiddenSession(false);
    setListMainHomeworks([]);
    setSearchParams({});
  };

  const onSubmit = async (data) => {
    if (!data.class || !data.student) {
      toast.error('Please select class and student');
      return;
    }
    if (!hiddenSession && !data.session) {
      toast.error('Please select session');
      return;
    }
    if (hiddenSession && !data.main_homework) {
      toast.error('Please select main homework');
      return;
    }

    const params = {
      classId: data.class.id || '',
      studentId: data.student.student?.id || '',
      sessionId: data.session?.value  || '',
      mainHomeworkId: data?.main_homework?.id || ''
    };
    setSearchParams(params);

    try {
      setLoading(true);
      const { data: { homeworks, main_homework: mainHomeworks } } = await getStudentHomeworkList({
        class: params.classId,
        student: params.studentId,
        session: params.sessionId || null,
        main_homework_id: params.mainHomeworkId || null
      });
      setHomeworks(homeworks);
      setMainHomeworks(mainHomeworks);
    } catch (error) {
      console.error(error);
      toast.error('Failed to load data');
    } finally {
      setLoading(false);
    }
  };

  const fetchData = async () => {
    const { data } = await getListClasses();
    setClasses(data);
  };

  useEffect(() => {
    const sessions = Array.from({ length: 16 }, (_, i) => ({
      label: `Session ${i + 1}`,
      value: i + 1
    }));
    setSessions(sessions);
    fetchData();
  }, []);

  useEffect(() => {
    const initializeFormFromUrl = async () => {
      setLoading(true);
      const classId = searchParams.get('classId');
      const studentId = searchParams.get('studentId');
      const sessionId = searchParams.get('sessionId');
      const mainHomeworkId = searchParams.get('mainHomeworkId');
      
      if (classId) {
        try {
          const { data: classesList } = await getListClasses();
          setClasses(classesList);
          const selectedClass = classesList.find(c => c.id === parseInt(classId));
          if (selectedClass) {
            setValue('class', selectedClass);

            const { data: studentsList } = await studentListByClassId({ id: selectedClass.id });
            setStudents(studentsList);
            const selectedStudent = studentsList.find(s => s.student.id === parseInt(studentId));
            if (selectedStudent) {
              setValue('student', selectedStudent);
            }

            if (selectedClass.type === 'Private') {
              setHiddenSession(true);
              const { data: { data: listMainHomeworks } } = await getMainHomeworksByClass({ class_id: selectedClass.id });
              setListMainHomeworks(listMainHomeworks);
              const selectedMainHomework = listMainHomeworks.find(mh => mh.id === parseInt(mainHomeworkId));
              if (selectedMainHomework) {
                setValue('main_homework', selectedMainHomework);
              }
            } else {
              setHiddenSession(false);
              const sessionsList = Array.from({ length: 16 }, (_, i) => ({ label: `Session ${i + 1}`, value: i + 1 }));
              const selectedSession = sessionsList.find(s => s.value === parseInt(sessionId));
              if (selectedSession) {
                setValue('session', selectedSession);
              }
            }

            const params = {
              class: selectedClass.id,
              student: selectedStudent?.student?.id,
              session: parseInt(sessionId) || null,
              main_homework_id: parseInt(mainHomeworkId) || null
            };
            const { data: { homeworks, main_homework: mainHomeworksData } } = await getStudentHomeworkList(params);
            setHomeworks(homeworks);
            setMainHomeworks(mainHomeworksData);
          }
        } catch (error) {
          console.error('Failed to initialize form from URL:', error);
          toast.error('Failed to load data from URL parameters.');
        }
      }
      setLoading(false);
    };

    initializeFormFromUrl();
  }, [searchParams, setValue]);

  const handleClassChange = async (value) => {
    setValue("student", null);
    setStudents([]);
    setListMainHomeworks([]);
    setValue("main_homework", null);
    setHomeworks([]);
    setMainHomeworks(null);

    if (value) {
      if (value.type === 'Private') {
        setValue("session", null);
        try {
          const { data: { data: listMainHomeworks } } = await getMainHomeworksByClass({ class_id: value.id });
          setListMainHomeworks(listMainHomeworks);
        } catch (error) {
          console.error('Failed to fetch current session:', error);
        }
        setHiddenSession(true);
      } else {
        setHiddenSession(false);
        setValue("session", sessions[0]);

        try {
          const { data: { session } } = await getDetail({ id: value.id });
          const selectedSession = sessions.find(s => s.value === (session || 1));
          setValue("session", selectedSession);
        } catch (error) {
          console.error('Failed to fetch current session:', error);
        }
      }

      try {
        const { data: students } = await studentListByClassId({ id: value.id });
        setStudents(students);
      } catch (err) {
        console.error("Failed to load students", err);
        setStudents([]);
      }
    } else {
      setHiddenSession(false);
      setValue("session", null);
    }
  };
  const [classValue, studentValue, sessionValue] = watch(['class', 'student', 'session']);

  return (
    <Box padding={2.5}>
      <Header title='Grades LC Management' />
      <Grid container spacing={2.75} py={3} mt={2}>
        <Grid item size={2}>
          <Controller
            name="class"
            control={control}
            render={({ field }) => (
              <Autocomplete
                {...field}
                options={classes}
                getOptionLabel={(option) => option?.name || ''}
                onChange={(_, value) => {
                  field.onChange(value);
                  handleClassChange(value);
                }}
                renderInput={(params) => (
                  <CustomTextField {...params} label="Class" size="small" />
                )}
              />
            )}
          />
        </Grid>

        <Grid item size={2}>
          <Controller
            name="student"
            control={control}
            render={({ field }) => (
              <Autocomplete
                {...field}
                options={students}
                getOptionLabel={(option) =>
                  option?.student?.name_english || option?.student?.name_korea || ''
                }
                onChange={(_, value) => {
                  field.onChange(value);
                }}
                renderInput={(params) => (
                  <CustomTextField {...params} label="Student" size="small" />
                )}
              />
            )}
          />
        </Grid>

        {!!hiddenSession && students.length > 0 && (
          <Grid item size={2}>
            <Controller
              name="main_homework"
              control={control}
              defaultValue={null}
              render={({ field }) => (
                <Autocomplete
                  {...field}
                  value={field.value ?? null}
                  options={listMainHomeworks}
                  getOptionLabel={(option) =>
                    option?.title
                      ? `${option.title}`
                      : ''
                  }
                  onChange={(_, value) => field.onChange(value)}
                  renderInput={(params) => (
                    <CustomTextField {...params} label="Main Homework" size="small" />
                  )}
                />
              )}
            />
          </Grid>
        )}

        {!hiddenSession && (
          <Grid item size={2}>
            <Controller
              name="session"
              control={control}
              render={({ field }) => (
                <Autocomplete
                  {...field}
                  options={sessions}
                  getOptionLabel={(option) => option?.label || ''}
                  onChange={(_, value) => {
                    field.onChange(value);
                  }}
                  renderInput={(params) => (
                    <CustomTextField {...params} label="Session" size="small" />
                  )}
                />
              )}
            />
          </Grid>
        )}

        <Grid item size={4}>
          <Box>
            <Button
              variant='contained'
              sx={{
                bgcolor: config.colors.primary,
                fontSize: '0.9375rem',
                textTransform: 'capitalize',
                mr: 1
              }}
              onClick={handleSubmit(onSubmit)}
            >
              Search
            </Button>

            <Button
              variant='contained'
              sx={{ mr: 2.5, bgcolor: '#8592A3', fontSize: '0.9375rem', textTransform: 'capitalize' }}
              onClick={handleReset}
            >
              Reset
            </Button>
          </Box>
        </Grid>
      </Grid>

      {loading ? (
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
          <CircularProgress />
        </Box>
      ) : (
        <TableContainer component={Paper} elevation={3}>
          <Table aria-label='collapsible table'>
            <TableHead>
              <TableRow>
                <TableCell>ID</TableCell>
                <TableCell>Title Main Homework</TableCell>
                <TableCell>Title Homework</TableCell>
                <TableCell>Homework</TableCell>
                <TableCell>Class</TableCell>
                <TableCell>Session</TableCell>
                <TableCell>Description</TableCell>
                <TableCell>Result</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Action</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {homeworks.length === 0 && !loading ? (
                <TableRow>
                  <TableCell colSpan={11} align='center'>
                   {(searchParams.toString() || classValue || studentValue || sessionValue)
                      ? 'No data available'
                      : 'Please select class and student'}
                  </TableCell>
                </TableRow>
              ) : (
                homeworks?.map((row, index) => (
                  <TableRow key={row.id ? `row-${row.id}` : `row-index-${index}`}>
                    <TableCell>{index + 1}</TableCell>
                    <TableCell>{mainHomeworks?.title}</TableCell>
                    <TableCell>{row.title}</TableCell>
                    <TableCell>{row?.homework ? 'HW'.concat(row.homework) : '-'}</TableCell>
                    <TableCell>{mainHomeworks?.get_class?.name || '-'}</TableCell>
                    <TableCell>{mainHomeworks?.session || '-'}</TableCell>
                    <TableCell>{row.description}</TableCell>
                    <TableCell>
                      {
                        row?.account_homework?.id
                          ? (row.account_homework?.end_time ? 'Completed' : 'Not Complete')
                          : 'Not Started'
                      }
                    </TableCell>
                    <TableCell>
                      {mainHomeworks?.grade_homework?.status ? StatusText[mainHomeworks?.grade_homework?.status] : StatusText[Status.INACTIVE]}
                    </TableCell>
                    <TableCell>
                      <Button
                        component="a"
                        href={`/admin/grades-lc/${row.id}?student_id=${getValues('student')?.student?.id ?? ''}`}
                        variant="contained"
                        sx={{
                          bgcolor: config.colors.primary,
                          fontSize: '0.9375rem',
                          textTransform: 'capitalize',
                          mr: 1
                        }}
                        className="btn btn-primary"
                      >
                        Edit
                      </Button>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>
      )}
    </Box>
  );
};

export default GradesLC;
