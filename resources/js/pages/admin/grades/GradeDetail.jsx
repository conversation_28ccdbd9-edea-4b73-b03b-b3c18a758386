import { Box, Button } from '@mui/material';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { getGrade, updateGrade, updateTotalGrade } from '../../../services/admin/grades';
import { useParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import GradeSideBar from '../../../components/grades/GradeSideBar';
import GradeQuestion from '../../../components/grades/GradeQuestion';
import { updateVocabularyGrades } from '../../../services/admin/vocabulary-grades';

const GradeDetail = ({ isUser = false }) => {
  const HEADER_HEIGHT = 70;
  const PAGE_PADDING_Y = 15;
  const { id } = useParams();
  const { control, reset, handleSubmit, watch, setValue, trigger } = useForm({
    defaultValues: {
      tasks: [],
    },
    mode: 'onChange',
  });

  const [homeworkDetail, setHomeworkDetail] = useState(null);
  const [tasks, setTasks] = useState([]);
  const [links, setLinks] = useState([]);
  const [currentTaskIndex, setCurrentTaskIndex] = useState(0);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [grades, setGrades] = useState({});
  const [vocabularyGrades, setVocabularyGrades] = useState([]);

  useEffect(() => {
    async function fetchData() {
      const { data: { tasks, homework, links, vocabularyGrades } } = await getGrade(id);
      setVocabularyGrades(vocabularyGrades);
      setLinks(links);
      setTasks(tasks);
      setHomeworkDetail(homework);
      reset({ tasks });
    }
    fetchData();
  }, [id]);

  useEffect(() => {
    if(!vocabularyGrades.length) return
    const newGrades = vocabularyGrades.reduce((acc, { vocabulary_id, grade }) => {
      acc[vocabulary_id] = grade;
      return acc;
    }, {});
    setGrades(newGrades);
  }, [vocabularyGrades]);

  const handleFlashcardChange = (_, newVotes) => {
    setVocabularyGrades(prev => prev.map(item => ({ ...item, grade: newVotes[item.vocabulary_id] || item.grade })));
  }

  useEffect(() => {
    if (tasks.length > 0) {
      reset({ tasks });
    }
  }, [currentTaskIndex, tasks]);

  const onSubmit = async (data) => {
    if (data.tasks.length === 0) return;
    const dataUpdate = data.tasks[currentTaskIndex].questions;
    await updateGrade(dataUpdate);
    await updateVocabularyGrades(vocabularyGrades);
    data.tasks[currentTaskIndex].total_score_actual = dataUpdate.reduce((sum, q) => {
      const score = q.account_answers?.score ?? 0;
      return sum + Number(score);
    }, 0);

    const homeworkTotalScoreActual = data.tasks.reduce((sum, task) => {
      const taskScore = task.total_score_actual ?? 0;
      return sum + Number(taskScore);
    }, 0);

    await updateTotalGrade({
      total_homework: homeworkTotalScoreActual,
      account_homework_id: id,
      task_id: data.tasks[currentTaskIndex].id,
      total_task: data.tasks[currentTaskIndex].total_score_actual,
    });

    setHomeworkDetail(prev => ({
      ...prev,
      total_score_actual: homeworkTotalScoreActual,
    }));

    setTasks([...data.tasks]);
    toast.success('Updated successfully');
  };
  return (
    <>
      {links && links.length > 0 && (
        <Box sx={{ display: 'flex', gap: 2, justifyContent: 'space-around', alignItems: 'center', borderBottom: 1, borderColor: 'grey.300', mb: 1 }}>
          {links.map((link) => {
            const isActive = parseInt(id) === link.id;
            return (
              <Button
                key={link.id}
                href={'/admin/v2/grades/' + link.id}
                component="a"
                variant="text"
                sx={{
                  px: 2,
                  py: 1,
                  textTransform: 'uppercase',
                  color: isActive ? 'primary.main' : 'text.primary',
                  borderBottom: isActive ? '2px solid' : 'none',
                  borderColor: 'primary.main',
                  fontWeight: isActive ? 600 : 400,
                  cursor: 'pointer',
                  textDecoration: 'none',
                  '&:hover': {
                    textDecoration: 'none',
                  }
                }}
              >
                {link.label}
              </Button>
            );
          })}
        </Box>
      )}
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          minHeight: `calc(100vh - 114px)`,
          bgcolor: 'grey.100',
          overflow: 'hidden',
          maxHeight: `calc(100vh - 114px)`
        }}
      >
        <Box sx={{
          flexGrow: 1,
          display: 'flex',
          gap: 3,
          alignItems: 'flex-start',
          marginTop: '0',
          height: `calc(100vh - ${HEADER_HEIGHT}px)`,
          padding: `${PAGE_PADDING_Y}px`,
          boxSizing: 'border-box',
          overflow: 'hidden'
        }}>
          <GradeSideBar
            tasks={tasks}
            setTasks={setTasks}
            homeworkDetail={homeworkDetail}
            setHomeworkDetail={setHomeworkDetail}
            currentTaskIndex={currentTaskIndex}
            setCurrentTaskIndex={setCurrentTaskIndex}
            currentQuestionIndex={currentQuestionIndex}
            setCurrentQuestionIndex={setCurrentQuestionIndex}
            onSubmit={handleSubmit(onSubmit)}
            isUser={isUser}
          />

          <GradeQuestion
            control={control}
            setValue={setValue}
            trigger={trigger}
            watch={watch}
            tasks={tasks}
            currentTaskIndex={currentTaskIndex}
            currentQuestionIndex={currentQuestionIndex}
            setHomeworkDetail={setHomeworkDetail}
            isUser={isUser}
            grades={grades}
            handleFlashcardChange={handleFlashcardChange}
          />
        </Box>
      </Box>
    </>
  );
};

export default GradeDetail;
