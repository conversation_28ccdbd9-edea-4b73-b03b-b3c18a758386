import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Typography,
  Tabs,
  Tab,
  RadioGroup,
  FormControlLabel,
  Radio,
  TextField,
  FormControl, InputLabel, Select, MenuItem, Grid, FormLabel
} from '@mui/material';
import { getStudentHomeworkDetail, updateHomeworkStatus } from '../../../services/admin/homeworks';
import { useParams, useSearchParams } from 'react-router-dom';
import { useForm, Controller } from 'react-hook-form';
import { toast } from 'react-toastify';
import gradeFormat from '../../../components/utils/formatGrade';


const GradeLCDetail = () => {
  const [tabIndex, setTabIndex] = useState(0);
  const [grades, setGrades] = useState([]);
  const [dataHomework, setDataHomework] = useState(null);
  const [links, setLinks] = useState([]);
  const { control, handleSubmit, reset, trigger, register, watch, setValue,
    formState: { errors, isSubmitting , touchedFields} } = useForm({
       mode: 'onTouched'
    });

  const { homeworkId } = useParams();
  const [searchParams] = useSearchParams();

  const studentId = searchParams.get('student_id');
  const onSubmit = async (data) => {
    const payload = {
      ...data,
      homework_id: homeworkId,
      student_id: studentId
    };
    await updateHomeworkStatus(payload);
    toast.success('Updated successfully');
  };

  const scoreType = watch('scoreType');
  const rangeFrom = watch('rangeFrom');
  const rangeTo = watch('rangeTo');

  useEffect(() => {
    trigger();
    if (scoreType === 'range' && rangeFrom && rangeTo) {
      const from = parseFloat(rangeFrom);
      const to = parseFloat(rangeTo);
      if (!isNaN(from) && !isNaN(to) && from <= to && to > 0) {
        let percent = (from / to) * 100;
        percent = Number.isInteger(percent) ? percent.toString() : percent.toFixed(2);
        setValue('percentage', `${percent}%`);
        setValue('grade', gradeFormat(percent));
      } else {
        setValue('percentage', '');
        setValue('grade', '');
      }
    } else if (scoreType === 'fm') {
      setValue('percentage', 'FM');
      setValue('grade', 'FM');
      setValue('rangeFrom', '');
      setValue('rangeTo', '');
    }
  }, [rangeFrom, rangeTo, scoreType]);




  useEffect(() => {
    async function fetchData() {
      const { data: { data, data_homework, links } } = await getStudentHomeworkDetail({ homework_id: homeworkId, student_id: studentId });
      reset(data_homework);
      setDataHomework(data_homework);
      setGrades(data);
      setLinks(links);
    }
    fetchData();
  }, []);

  return (
    <Box p={3} sx={{ bgcolor: '#fff', borderRadius: 2, boxShadow: 1 }}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
        <Typography variant="h6">Edit Grades</Typography>
        <Button component="a" href="/admin/grades-lc" variant="contained" sx={{ bgcolor: '#6C63FF', textTransform: 'none' }}>
          Back
        </Button>
      </Box>

      {links && links.length > 0 && (
        <Box sx={{ display: 'flex', gap: 2, justifyContent: 'space-around', alignItems: 'center', borderBottom: 1, borderColor: 'grey.300', mb: 1 }}>
          {links.map((link) => {
            const isActive = parseInt(homeworkId) === link.id;
            return (
              <Button
                key={link.id}
                href={'/admin/grades-lc/' + link.id + '?student_id=' + studentId}
                component="a"
                variant="text"
                sx={{
                  px: 2,
                  py: 1,
                  textTransform: 'uppercase',
                  color: isActive ? 'primary.main' : 'text.primary',
                  borderBottom: isActive ? '2px solid' : 'none',
                  borderColor: 'primary.main',
                  fontWeight: isActive ? 600 : 400,
                  cursor: 'pointer',
                  textDecoration: 'none',
                  '&:hover': {
                    textDecoration: 'none',
                  }
                }}
              >
                {link.label}
              </Button>
            );
          })}
        </Box>
      )}

      <form onSubmit={handleSubmit(onSubmit)}>
        <Box mb={2}>
          <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
            A. Class Work
          </Typography>
          <Typography variant="subtitle2" color="text.secondary">
            Score
          </Typography>
          <Box display="flex" gap={2} alignItems={'start'} mt={1}>
            <Controller
              name="scoreType"
              control={control}
              defaultValue="range"
              render={({ field }) => (
                <Box alignItems={'start'}>
                  <RadioGroup style={{ alignItems: 'start' }} row {...field}>
                    <FormControlLabel
                      value="range"
                      style={{ alignItems: 'start' }}
                      control={<Radio />}
                      label={
                        <Box display="flex" style={{ alignItems: 'start' }} gap={1}>
                          <TextField
                            size="small"
                            type="number"
                            inputProps={{ min: 0, max: 1000 }}
                            sx={{ width: 100 }}
                            {...register('rangeFrom', {
                              validate: (value) => {
                                if(!touchedFields.rangeFrom) return true
                                if (scoreType !== 'range') return true;
                                if (!value) return 'Required';
                                if (value > 1000) return 'Must be less than 1000';
                                if (value < 0) return 'Must be greater than 0';
                                if (isNaN(value)) return 'Must be a number';
                                if (parseFloat(value) > parseFloat(rangeTo)) return 'Must be less than To';
                                return true;
                              }
                            })}
                            error={!!errors.rangeFrom}
                            helperText={touchedFields.rangeFrom ? errors.rangeFrom?.message : ''}
                            disabled={scoreType !== 'range'}
                          />
                          <TextField
                            size="small"
                            type="number"
                            inputProps={{ min: 1, max: 1000 }}
                            sx={{ width: 100 }}
                            {...register('rangeTo', {
                              validate: (value) => {
                                if(!touchedFields.rangeTo) return true
                                if (scoreType !== 'range') return true;
                                if (!value) return 'Required';
                                if (value > 1000) return 'Must be less than 1000';
                                if (value < 1) return 'Must be greater than 0';
                                if (isNaN(value)) return 'Must be a number';
                                if (parseFloat(value) < parseFloat(rangeFrom)) return 'Must be greater than From';
                                return true;
                              }
                            })}
                            error={!!errors.rangeTo}
                            helperText={touchedFields.rangeTo ? errors.rangeTo?.message : ""}
                            disabled={scoreType !== 'range'}
                          />
                        </Box>
                      }
                    />
                    <FormControlLabel value="fm" control={<Radio />} label="FM" />
                  </RadioGroup>
                </Box>
              )}
            />

            <Box display="flex" gap={2}>
              <Controller
                name="percentage"
                control={control}
                defaultValue=""
                render={({ field }) => (
                  <TextField size="small" sx={{ width: 80 }} {...field} disabled placeholder="%" />
                )}
              />
              <Controller
                name="grade"
                control={control}
                defaultValue=""
                render={({ field }) => (
                  <TextField size="small" sx={{ width: 120 }} {...field} disabled placeholder="Grade" />
                )}
              />
            </Box>
          </Box>
        </Box>

        <Box mb={2}>
          <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
            B. Home Work
          </Typography>
        </Box>

        <Box>
          <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
            Class Participation
          </Typography>

          <Grid container spacing={2}>
            <Grid item size={4}>
              <FormControl fullWidth>
                <InputLabel id="attendance-label">Attendance</InputLabel>
                <Controller
                  name="attendance"
                  control={control}
                  defaultValue="NA"
                  render={({ field }) => (
                    <Select
                      {...field}
                      size="small"
                      labelId="attendance-label"
                      id="attendance"
                      label="Attendance"
                    >
                      <MenuItem value="O">O</MenuItem>
                      <MenuItem value="X">X</MenuItem>
                      <MenuItem value="Late">Late</MenuItem>
                      <MenuItem value="NA">N/A</MenuItem>
                    </Select>
                  )}
                />
              </FormControl>
            </Grid>

            <Grid item size={4}>
              <FormControl fullWidth>
                <InputLabel id="participation-label">Participation</InputLabel>
                <Controller
                  name="participation"
                  control={control}
                  defaultValue="NA"
                  render={({ field }) => (
                    <Select
                      {...field}
                      size="small"
                      labelId="participation-label"
                      id="participation"
                      label="Participation"
                    >
                      <MenuItem value="A+">A+</MenuItem>
                      <MenuItem value="A">A</MenuItem>
                      <MenuItem value="A-">A-</MenuItem>
                      <MenuItem value="B+">B+</MenuItem>
                      <MenuItem value="B">B</MenuItem>
                      <MenuItem value="B-">B-</MenuItem>
                      <MenuItem value="C+">C+</MenuItem>
                      <MenuItem value="C">C</MenuItem>
                      <MenuItem value="Fp">Fp</MenuItem>
                      <MenuItem value="NA">N/A</MenuItem>
                    </Select>
                  )}
                />
              </FormControl>
            </Grid>

            <Grid item size={4}>
              <FormControl fullWidth>
                <InputLabel id="status-label">Status</InputLabel>
                <Controller
                  name="status"
                  control={control}
                  defaultValue="inactive"
                  render={({ field }) => (
                    <Select
                      {...field}
                      size="small"
                      labelId="status-label"
                      id="status"
                      label="Status"
                    >
                      <MenuItem value="active">Active</MenuItem>
                      <MenuItem value="inactive">Inactive</MenuItem>
                    </Select>
                  )}
                />
              </FormControl>
            </Grid>
            {grades.length > 0 && grades.map((item, index) => (
              <Grid item size={6} key={index}>
                <FormControl component="fieldset" sx={{ width: '100%', p: 2, border: '1px solid #ccc', borderRadius: 2 }}>
                  <FormLabel component="legend" sx={{ fontWeight: 'bold', mb: 1 }}>
                    {item?.criteria}
                  </FormLabel>

                  <RadioGroup
                    row
                    value={item?.total_score > 0 ? 'range' : 'fm'}
                  >
                    {item?.total_score > 0 && (
                      <FormControlLabel
                        value="range"
                        control={<Radio />}
                        label={
                          <Box display="flex" alignItems="center" gap={1}>
                            <TextField
                              size="small"
                              type="number"
                              sx={{ width: 100 }}
                              value={item?.total_score > 0 ? item.score : ''}
                              slotProps={{
                                input: { readOnly: true }
                              }}
                            />
                            <TextField
                              size="small"
                              type="number"
                              sx={{ width: 100 }}
                              value={item?.total_score || ''}
                              slotProps={{
                                input: { readOnly: true }
                              }}
                            />
                          </Box>
                        }
                      />
                    )}
                    {item?.total_score <= 0 && (
                      <FormControlLabel
                        value="fm"
                        label={'FM'}
                        control={<Radio />}
                      />
                    )}
                  </RadioGroup>
                </FormControl>
              </Grid>
            ))}

            <Grid item size={grades.length % 2 === 0 ? 12 : 6}>
              <Controller
                name="message"
                control={control}
                defaultValue=""
                render={({ field }) => (
                  <TextField
                    {...field}
                    size="small"
                    fullWidth
                    label="Message and Consultations"
                    multiline
                    rows={4}
                    error={!!errors.message}
                    helperText={errors.message?.message}
                  />
                )}
              />
            </Grid>
          </Grid>
        </Box>
        <Box sx={{ mt: 3 }}>
          <Button
            type="submit"
            variant="contained"
            color="primary"
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Saving...' : 'Save'}
          </Button>
        </Box>
      </form>
    </Box>
  );
};

export default GradeLCDetail;
