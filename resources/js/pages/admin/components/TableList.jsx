import React, { useState } from 'react';
import {
    Box,
    Typography,
    Paper,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    IconButton,
    Chip,
    Avatar,
    Divider,
    Collapse,
    styled,
    Tooltip
} from '@mui/material';
import { KeyboardArrowDown, KeyboardArrowUp } from '@mui/icons-material';
import EditIcon from '@mui/icons-material/Edit';
import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import { HomeworkTypeText } from '../../../enums/HomeworkType';
const StatusColor = {
    active: 'success'
};

const LevelColors = {
    0: 'red',
    1: 'green',
    2: 'blue',
    3: 'error'
};

export const TableCellCustom = styled(TableCell)(({ theme }) => ({
    padding: '8px',
    fontSize: '0.875rem'
}));

export const TypographyCustom = styled(Typography)(({ theme }) => ({
    fontSize: '0.875rem'
}));
const NestedTable = ({ data, columns, levels, page, pageSize }) => {
    const [expandedRows, setExpandedRows] = useState({});

    const toggleRow = (rowId, level = 0) => {
        const key = `${level}-${rowId}`;
        setExpandedRows(prev => ({
            ...prev,
            [key]: !prev[key]
        }));
    };

    const renderIconAction = key => {
        switch (key) {
            case 'edit':
                return <EditIcon />;
            case 'add':
                return <AddIcon />;
            case 'delete':
                return <DeleteIcon />;
            case 'copy':
              return <ContentCopyIcon  />;
            default:
                return null;
        }
    };

    const getValue = (obj, path) => {
        return path.split('.').reduce((acc, key) => acc?.[key], obj);
    };

    const renderCellContent = (item, columnKey, action, indexItem) => {

        switch (columnKey) {
            case 'id':
            return (
                <TypographyCustom variant="body1" fontWeight="medium">
                 {indexItem}
                </TypographyCustom>
            );
            case 'type':
                return <Chip label={HomeworkTypeText[getValue(item, columnKey)]} size='small' variant='outlined' />;
            case 'status':
                return (
                    <Chip
                        label={getValue(item, columnKey)}
                        size='small'
                        color={StatusColor[getValue(item, columnKey)] || 'grey.300'}
                    />
                );

            case 'score':
            case 'grade':
                return (
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                        <Box
                            sx={{
                                width: 48,
                                height: 4,
                                borderRadius: 2
                            }}
                        />
                        <TypographyCustom variant='body2' fontWeight='medium'>
                            {getValue(item, columnKey)}
                        </TypographyCustom>
                    </Box>
                );

            case 'action':
                return (
                    <Box display={'flex'} sx={{ gap: 1 }}>
                        {action &&
                            Object.keys(action).map(key => (
                                <Tooltip title={action[key].title || ''} key={key}>
                                    <IconButton
                                        color={action[key].color || ''}
                                        onClick={() => action[key].fnc(item, indexItem)}
                                    >
                                        {renderIconAction(key)}
                                    </IconButton>
                                </Tooltip>
                            ))}
                    </Box>
                );
            case 'homework':
                return (
                    <Box>
                        <TypographyCustom variant='body1' fontWeight='medium'>
                            {getValue(item, columnKey) ? `HW${getValue(item, columnKey)}` : ''}
                        </TypographyCustom>
                    </Box>
                );
            default:
                return (
                    <Box>
                        <TypographyCustom
                            variant='body1'
                            fontWeight='medium'
                            sx={{
                                display: '-webkit-box',
                                WebkitLineClamp: 3,
                                WebkitBoxOrient: 'vertical',
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                whiteSpace: 'normal'
                            }}
                        >
                            {getValue(item, columnKey)}
                        </TypographyCustom>
                    </Box>
                );
        }
    };

    const renderTable = (items, columnsLevel, level = 0) => {
        const { cols: columns, childrenKey, rowComponent } = columnsLevel[level];
        return (
            <Box
                sx={{
                    ml: level > 0 ? 4 : 0,
                    mt: level > 0 ? 2 : 0,
                    mb: level > 0 ? 2 : 0,
                    borderLeft: level > 0 ? '2px solid' : 'none',
                    borderColor: level > 0 ? LevelColors[level] : 'transparent',
                    pl: level > 0 ? 3 : 0
                }}
            >
                {level != -1 && (
                    <>
                        <TableContainer
                            component={Paper}
                            elevation={0}
                            sx={{ border: '1px solid', borderColor: 'divider' }}
                        >
                            <Table sx={{ tableLayout: level != 0 ? 'fixed' : 'auto' }}>
                                <TableHead>
                                    <TableRow sx={{ bgcolor: 'grey.50' }}>
                                        {columns.map(({ width, header, field }, index) => (
                                            <TableCellCustom
                                                key={field}
                                                id={field}
                                                sx={{ width: width, fontWeight: 'medium' }}
                                            >
                                                {header}
                                            </TableCellCustom>
                                        ))}
                                    </TableRow>
                                </TableHead>
                                <TableBody>
                                    {items.length > 0 ? (
                                        items.map((item, index) => {
                                            const hasNested = childrenKey && item[childrenKey]?.length > 0;
                                            const children = childrenKey ? item[childrenKey] : null;
                                            return (
                                                <React.Fragment key={item.id}>
                                                    <TableRow hover sx={{ '&:hover': { bgcolor: 'grey.50' } }}>
                                                        {columns.map(({ width, header, field, action }, indexColumn) => (
                                                            <TableCellCustom key={field + level}>
                                                                <Box
                                                                    sx={{
                                                                        display: 'flex',
                                                                        alignItems: 'center',
                                                                        gap: 2
                                                                    }}
                                                                >
                                                                    {indexColumn == 1 && hasNested && (
                                                                        <IconButton
                                                                            size='small'
                                                                            onClick={() => toggleRow(item.id, level)}
                                                                        >
                                                                          {expandedRows[`${level}-${item.id}`] ?
                                                                            <KeyboardArrowUp fontSize='small' color='action' />
                                                                            : <KeyboardArrowDown fontSize='small' color='action' />}
                                                                        </IconButton>
                                                                    )}
                                                                    {renderCellContent(item, field, action,  level === 0 ? (page - 1) * pageSize + (index + 1) : index + 1) }
                                                                </Box>
                                                            </TableCellCustom>
                                                        ))}
                                                    </TableRow>
                                                    <TableRow>
                                                        <TableCellCustom
                                                            colSpan={columns.length}
                                                            sx={{
                                                                p: 0,
                                                                borderBottom: 0,
                                                                width: '100%',
                                                                display: 'table-cell'
                                                            }}
                                                        >
                                                            <Collapse
                                                                in={expandedRows[`${level}-${item.id}`] || false}
                                                                timeout='auto'
                                                                unmountOnExit
                                                                sx={{
                                                                    width: '100%',
                                                                    '& .MuiCollapse-wrapper': {
                                                                        width: '100%'
                                                                    },
                                                                    '& .MuiCollapse-wrapperInner': {
                                                                        width: '100%'
                                                                    }
                                                                }}
                                                            >
                                                                {hasNested && (
                                                                    <>
                                                                        <Box style={{
                                                                                backgroundColor: 'grey.50',
                                                                                p: 0
                                                                            }}
                                                                        >
                                                                            {renderTable(
                                                                                children,
                                                                                columnsLevel,
                                                                                level + 1
                                                                            )}
                                                                        </Box>
                                                                    </>
                                                                )}
                                                                {rowComponent && (
                                                                    <Table>
                                                                        <TableBody>
                                                                            <TableRow sx={{ width: '100%' }}>
                                                                                <TableCellCustom
                                                                                    colSpan={columns.length}
                                                                                    sx={{
                                                                                        p: 0,
                                                                                        height: '57px',
                                                                                        backgroundColor: 'grey.50',
                                                                                        width: '100%'
                                                                                    }}
                                                                                    align='center'
                                                                                >
                                                                                    <Tooltip
                                                                                        title={rowComponent.title || ''}
                                                                                    >
                                                                                        <IconButton
                                                                                            color={
                                                                                                rowComponent.color || ''
                                                                                            }
                                                                                            onClick={() =>
                                                                                                rowComponent.fnc(
                                                                                                    item,
                                                                                                    index
                                                                                                )
                                                                                            }
                                                                                        >
                                                                                            {renderIconAction(
                                                                                                rowComponent.icon
                                                                                            )}
                                                                                        </IconButton>
                                                                                    </Tooltip>
                                                                                </TableCellCustom>
                                                                            </TableRow>
                                                                        </TableBody>
                                                                    </Table>
                                                                )}
                                                            </Collapse>
                                                        </TableCellCustom>
                                                    </TableRow>
                                                </React.Fragment>
                                            );
                                        })
                                    ) : (
                                        <TableRow>
                                            <TableCellCustom colSpan={columns.length} align='center'>
                                                <Typography sx={{ p: 2 }} align='center'>
                                                    No data available.
                                                </Typography>
                                            </TableCellCustom>
                                        </TableRow>
                                        // <></>
                                    )}
                                </TableBody>
                            </Table>
                        </TableContainer>
                    </>
                )}
            </Box>
        );
    };

    return <>{renderTable(data, columns)}</>;
};

export default function RealDataNestedTable({ data, columns, level = 0, page = 1, pageSize = 10 }) {
    return <NestedTable data={data} columns={columns} level={level} page={page} pageSize={pageSize} className='max-w-7xl mx-auto' />;
}
