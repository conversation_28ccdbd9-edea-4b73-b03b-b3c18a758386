import {
  Autocomplete,
  Box,
  Button,
  Grid
} from '@mui/material';

import { CustomTextField } from '../vocabulary/components/VocaForm';

import { config } from '../vocabulary/utils';

const SearchFilter = ({ filters, onSearch, onReset, setParams, params }) => {
  const handleChange = (name, value) => {
    setParams(prev => ({ ...prev, [name]: value }));
  };
  const handleSearch = () => {
    onSearch();
  };

    const handleReset = () => {
        onReset();
    };
    return (
        <div>
            <Grid container spacing={2.75} paddingY={3}>

                {filters.map((item) => {
                    switch (item.type) {
                      case 'Autocomplete':
                        let labelKey = 'title';
                        if (item.labelKey === 'name') {
                          labelKey = 'name';
                        }
                        return (
                          <Grid size={2} key={item.name}>
                            <Autocomplete
                              value={params[item.name] || null}
                              disablePortal
                              options={item.options}
                              filterSelectedOptions
                              getOptionLabel={(option) => option[labelKey] || ''}
                              isOptionEqualToValue={(opt, val) => opt.id === val.id}
                              renderInput={(inputParams) => (
                                <CustomTextField
                                  {...inputParams}
                                  label={item.label}
                                  size="small"
                                />
                              )}
                              onChange={(e, value) => {
                                handleChange(item.name, value);
                              }}
                            />
                          </Grid>
                        );
                      case 'TextField':
                            return (
                                <Grid size={2} key={item.name}>
                                    <CustomTextField
                                        value={params[item.name] || ''}
                                        onChange={e => handleChange(item.name, e.target.value)}
                                        label={item.label}
                                        variant='outlined'
                                        size='small'
                                        fullWidth
                                        onKeyDown={(e) => {
                                          if (e.key === "Enter") {
                                            e.preventDefault(); 
                                            handleSearch();
                                          }
                                        }}
                                    />
                                </Grid>
                            );
                    }
                })}
                <Grid size={12 - 3 * filters.length}>
                    <Box>
                 {onSearch &&        <Button
                            variant='contained'
                            sx={{
                                bgcolor: config.colors.primary,
                                fontSize: '0.9375rem',
                                textTransform: 'capitalize',
                                mr: 1
                            }}
                            className='btn btn-primary'
                            onClick={handleSearch}
                        >
                            Search
                        </Button>}
                       {
                            onReset &&  <Button
                            variant='contained'
                            sx={{ mr: 2.5, bgcolor: '#8592A3', fontSize: '0.9375rem', textTransform: 'capitalize' }}
                            className='btn btn-primary'
                            onClick={handleReset}
                        >
                            Reset
                        </Button>
                       }
                    </Box>
                </Grid>
            </Grid>
        </div>
    );
};

export default SearchFilter;
