import { useEffect, useState } from 'react';

import {
  Box,
  Button,
  Pagination,
  PaginationItem,
  Stack,
  styled,
  IconButton,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  TextField
} from '@mui/material';
import { config } from './utils';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import EditIcon from '@mui/icons-material/Edit';
import VisibilityIcon from '@mui/icons-material/Visibility';
import DeleteIcon from '@mui/icons-material/Delete';
import { useNavigate, useSearchParams } from 'react-router-dom';
import KeyboardDoubleArrowRightIcon from '@mui/icons-material/KeyboardDoubleArrowRight';
import KeyboardDoubleArrowLeftIcon from '@mui/icons-material/KeyboardDoubleArrowLeft';
import Header from './components/Header';
import { deleteVocabulary, getListWord } from '../../../services/admin/tag-vocabularies';
import { CustomTextField } from './components/VocaForm';
import AlertDialog from '../../../components/dialogs/AlertDialog';
import CustomSnackbar from '../../../components/snackbar';
import SearchFilter from '../components/SearchFilter';

const HeadCell = styled(TableCell)(({ theme }) => ({
  color: config.colors.headingColor,
  fontWeight: '500',
  fontSize: '0.75rem',
  textTransform: 'uppercase',
  width: '20%'
}));

const TagVocabulary = () => {
  const navigate = useNavigate();
  const [words, setWords] = useState([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [total, setTotal] = useState(0);
  const [currentId, setCurrentId] = useState(false);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [message, setMessage] = useState();
  const [meta, setMeta] = useState(null);

  const [searchParams, setSearchParams] = useSearchParams(); 
  const [inputValue, setInputValue] = useState({keyword:  searchParams.get("keyword") || ""});
  const [params, setParams] = useState({
    page: 1,
    keyword: '',
    sort: {
      field: 'word',
      order: 'desc'
    }
  });

  useEffect(() => {
    const fetchDataWords = async () => {
      const {
        data: { data, meta }
      } = await getListWord({ ...params, ...inputValue });
      setTotal(meta.last_page);
      setMeta(meta);
      setWords(data);
    };
    fetchDataWords();
  }, [params]);

  const handleChangePagination = (event, value) => {
    setParams({ ...params, page: value });
  };

  const handleClickOpen = id => {
    setCurrentId(id);
    setOpenDialog(true);
  };
  const handleSearch = () => {
    setParams({ ...params, page: 1, keyword: inputValue.keyword });
    setSearchParams(inputValue);
  };

  const handleReset = () => {
    setInputValue({keyword: ''});
    setSearchParams({keyword: ''});
    setParams({
      page: 1,
      keyword: '',
      sort: {
        field: 'word',
        order: 'desc'
      }
    });
  };

  const handleAction = async () => {
    if (!currentId) return;
    await deleteVocabulary(currentId);
    setWords(prev => prev.filter(item => item.id !== currentId));
    setOpenDialog(false);
    setMessage('Vocabulary deleted successfully');
    setSnackbarOpen(true);
  };
  const filters = [
    {
      type: 'TextField',
      name: 'keyword',
      label: 'Search by title'
    }
  ];
  return (
    <Box padding={2.5}>
      <Header
        title='Tag vocabulary'
        onClick={() => navigate('/tag-vocabularies/create')}
        labelButton='Create Tag Vocabulary'
      />
      <Box>
        <SearchFilter
          filters={filters}
          onSearch={handleSearch}
          onReset={handleReset}
          params={inputValue}
          setParams={setInputValue}
        />
      </Box>
      <Box sx={{ width: '100%' }}>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <HeadCell sx={{ width: '5%' }}>ID</HeadCell>
                <HeadCell sx={{ width: '40%' }}>Title</HeadCell>
                <HeadCell sx={{ width: '20%' }}>Class</HeadCell>
                <HeadCell sx={{ width: '20%' }}>Session</HeadCell>
                <HeadCell sx={{ textAlign: 'center' }}>Action</HeadCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {words.map(({ id, title, get_class, session }, index) => (
                <TableRow key={id} hover>
                  <TableCell sx={{ width: '5%' }}>{(meta.current_page - 1) * meta.per_page + index + 1}</TableCell>
                  <TableCell sx={{ width: '40%' }}>{title}</TableCell>
                  <TableCell sx={{ width: '20%' }}>{get_class?.name}</TableCell>
                  <TableCell sx={{ width: '20%' }}>{session}</TableCell>
                  <TableCell sx={{ gap: 2, textAlign: 'center'  }}>
                    <IconButton size='small' onClick={() => navigate(`/tag-vocabularies/${id}`)}>
                      <VisibilityIcon variant='outlined' color='primary' />
                    </IconButton>
                    <IconButton
                      size='small'
                      onClick={() => navigate(`/tag-vocabularies/${id}/edit`)}
                    >
                      <EditIcon variant='contained' color='secondary' />
                    </IconButton>
                    <IconButton size='small' onClick={() => handleClickOpen(id)}>
                      <DeleteIcon variant='outlined' color='error' />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
              {words.length === 0 && (
                <TableRow>
                  <TableCell colSpan={5} align='center'>
                    No data available
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </Box>
      <Stack spacing={2} my={2.5} alignItems={'center'}>
        {total && total > 1 && (
          <Pagination
            count={total}
            page={params.page}
            onChange={handleChangePagination}
            variant='outlined'
            shape='rounded'
            renderItem={item => (
              <PaginationItem
                {...item}
                sx={{
                  '&.MuiPaginationItem-root': {
                    color: config.colors.primary,
                    backgroundColor: '#f0f2f4',
                    border: 'none'
                  },
                  '&.Mui-selected': {
                    backgroundColor: config.colors.primary,
                    color: 'white'
                  },
                  '&:hover': {
                    backgroundColor: '#e1e4e8'
                  },
                  '& .MuiSvgIcon-root': {
                    color: config.colors.bodyColor
                  }
                }}
                slots={{
                  previous: KeyboardDoubleArrowLeftIcon,
                  next: KeyboardDoubleArrowRightIcon
                }}
              />
            )}
          />
        )}

      </Stack>
      <AlertDialog open={openDialog} setOpen={setOpenDialog} handleAction={handleAction} />
      <CustomSnackbar open={snackbarOpen} setOpen={setSnackbarOpen} message={message} />
    </Box>
  );
};

export default TagVocabulary;
