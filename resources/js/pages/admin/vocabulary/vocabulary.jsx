import { useEffect, useState, useRef } from 'react';
import {
  Box, Button, Pagination, PaginationItem, Stack, styled, IconButton,
  Dialog, DialogTitle, DialogContent, DialogActions, Typography,
  List, ListItem, ListItemText, Alert,
  Grid,
  Autocomplete,
} from '@mui/material';
import { config, VocabularyType } from './utils';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import TableSortLabel from '@mui/material/TableSortLabel';
import EditIcon from '@mui/icons-material/Edit';
import VisibilityIcon from '@mui/icons-material/Visibility';
import DeleteIcon from '@mui/icons-material/Delete';
import UploadFileIcon from '@mui/icons-material/UploadFile';
import DownloadIcon from '@mui/icons-material/Download';
import { useNavigate, useSearchParams } from 'react-router-dom';
import KeyboardDoubleArrowRightIcon from '@mui/icons-material/KeyboardDoubleArrowRight';
import KeyboardDoubleArrowLeftIcon from '@mui/icons-material/KeyboardDoubleArrowLeft';
import { deleteVocabulary, getListWord, importCsv, getLevels } from '../../../services/admin/vocabularies';
import AlertDialog from '../../../components/dialogs/AlertDialog';
import CustomSnackbar from '../../../components/snackbar';
import { formatHtml } from '../../../components/common/FormatHtml';
import { Controller, useForm } from 'react-hook-form';
import { getList } from '../../../services/admin/classes';
import { CustomTextField } from './components/VocaForm';

const HeadCell = styled(TableCell)(({ theme }) => ({
  color: config.colors.headingColor,
  fontWeight: '500',
  fontSize: '0.75rem',
  textTransform: 'uppercase',
  width: '20%'
}));

export const importVocabulariesCSV = async (formData) => {
  try {
    const response = await importCsv(formData)
    return response;
  } catch (error) {
    console.error('Import vocabularies error:', error);
    throw error;
  }
};

const Vocabulary = () => {
  const navigate = useNavigate();
  const fileInputRef = useRef(null);

  const [words, setWords] = useState([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [total, setTotal] = useState(0);
  const [currentId, setCurrentId] = useState(false);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [message, setMessage] = useState();
  const [meta, setMeta] = useState(null);
  const [classes, setClasses] = useState([]);
  const [importing, setImporting] = useState(false);
  const [importResults, setImportResults] = useState(null);
  const [showImportDialog, setShowImportDialog] = useState(false);
  const [levels, setLevels] = useState([]);

  const [searchParams, setSearchParams] = useSearchParams();
  const [inputValue, setInputValue] = useState({word:  searchParams.get("word") || "", type: searchParams.get("type") || ""});

  const [params, setParams] = useState({
    page: 1,
    word: '',
    type: '',
    class_id: '',
    session: '',
    sort: {
      field: 'id',
      order: 'desc'
    }
  });

  const onSubmit = async () => {
    const finalParams = { ...params, ...inputValue };
    setSearchParams(inputValue);
    setParams(finalParams);
    await fetchDataWords();
  };

  const fetchDataWords = async () => {
    const finalParams = { ...params, ...inputValue };
    const { data: { data, meta } } = await getListWord(finalParams);
    setTotal(meta.last_page);
    setMeta(meta);
    setWords(data);
  };

  const fetchDataClasses = async () => {
    const { data: classList } = await getList();
    setClasses(classList);
  };

  useEffect(() => {
    fetchDataWords();
  }, [params]);

  useEffect(() => {
    const fetchLevels = async () => {
      try {
        const { data: { data } } = await getLevels();
        setLevels(data);
      } catch (error) {
        console.error('Error fetching levels:', error);
      }
    };
    fetchLevels();
    fetchDataClasses();
  }, []);

  const handleChangePagination = (event, value) => {
    setParams({ ...params, page: value });
  };

  const handleClickOpen = id => {
    setCurrentId(id);
    setOpenDialog(true);
  };


  const handleReset = () => {
    const newInput =  {word: '', type: ''}
    setInputValue(newInput);
    setSearchParams(newInput);
    setParams({
      page: 1,
      word: '',
      type: '',
      class_id: '',
      session: '',
      sort: {
        field: 'word',
        order: 'desc'
      }
    });
  };

  const handleAction = async () => {
    if (!currentId) return;
    await deleteVocabulary(currentId);
    setWords(prev => prev.filter(item => item.id !== currentId));
    setOpenDialog(false);
    setMessage('Vocabulary deleted successfully');
    setSnackbarOpen(true);
  };

  const handleImport = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = async (event) => {
    const file = event.target.files[0];

    if (!file) return;

    if (!file.name.toLowerCase().endsWith('.csv')) {
      setMessage('Please select a CSV file');
      setSnackbarOpen(true);
      return;
    }

    if (file.size > 100 * 1024 * 1024) {
      setMessage('File size must be less than 5MB');
      setSnackbarOpen(true);
      return;
    }

    await uploadCSVFile(file);

    event.target.value = '';
  };

  const uploadCSVFile = async (file) => {
    const formData = new FormData();
    formData.append('csv_file', file);

    setImporting(true);

    try {
      const { data: { data: result } } = await importVocabulariesCSV(formData);
      setImportResults(result);
      setShowImportDialog(true);
      const { total, error_count, error_details } = result;
      const messages = [
        `Import completed: ${total} vocabularies imported`,
        error_count ? `${error_count} errors occurred` : '',
        error_details.length ? `${error_details.slice(0, 10).map(error => `Row ${error.row}: ${error.message}`).join(', ')}` : ''
      ]
      setMessage(messages.join(', '));
      setSnackbarOpen(true);

      const { data: { data, meta } } = await getListWord(params);
      setTotal(meta.last_page);
      setMeta(meta);
      setWords(data);

    } catch (error) {
      setMessage(error.response?.data?.message || 'Import failed. Please try again.');
      setSnackbarOpen(true);
    } finally {
      setImporting(false);
    }
  };

  const ExampleDisplay = ({ example }) => {
    if (!example) return '-';
    try {
      const examples = typeof example === 'string' ? JSON.parse(example) : example;
      if (Array.isArray(examples)) {
        return (
          <Box>
            {examples.map((item, index) => (
              <Typography
                key={index}
                variant="body2"
                sx={{
                display: '-webkit-box',
                WebkitLineClamp: 1,
                WebkitBoxOrient: 'vertical',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'normal'
          }}
              >
                {index + 1}. {formatHtml(item).slice(0, 100)}
              </Typography>
            ))}
            {examples.length === 0 && (
              <Typography variant="body2" sx={{ fontSize: '0.75rem', color: 'text.secondary' }}>
                No examples
              </Typography>
            )}
          </Box>
        );
      } else {
        return (
          <Typography variant="body2"
            sx={{fontSize: '0.75rem' ,
                display: '-webkit-box',
                WebkitLineClamp: 1,
                WebkitBoxOrient: 'vertical',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'normal'
            }}>
            {formatHtml(example).slice(0, 100)}...
          </Typography>
        );
      }
    } catch (error) {
      return (
        <Typography variant="body2"
          sx={{fontSize: '0.75rem' ,
              display: '-webkit-box',
              WebkitLineClamp: 1,
              WebkitBoxOrient: 'vertical',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'normal'
          }}>
          {formatHtml(example).slice(0, 100)}
        </Typography>
      );
    }
  };

  const getLevelName = (level) => {
    return levels.find(({ value }) => value == level)?.label;
  }

  const downloadTemplate = () => {
    const headers = [
      'Word', 'Type', 'Definition', 'Description',
      'Sample1', 'Sample2', 'Sample3', 'Session', 'Class', 'Level'
    ];

    const sampleData = [
      [
        'hello',
        'noun',
        'A greeting',
        'Used to greet someone',
        'Hello, how are you?',
        'Hello there!',
        'Hello everyone!',
        '1',
        'Class A',
        '1'
      ],
      [
        'beautiful',
        'adjective',
        'Having beauty',
        'Pleasing to look at',
        'She is beautiful',
        'The sunset is beautiful',
        'What a beautiful day!',
        '1',
        'Class A',
        '2'
      ],
      [
        'run',
        'verb',
        'Move quickly on foot',
        'Physical activity',
        'I run every morning',
        'She runs fast',
        'Let\'s run together',
        '2',
        'Class B',
        '3'
      ],
      [
        'study',
        'verb',
        'Learn about something',
        'Educational activity',
        'I study English daily',
        'Students study hard',
        'We study together',
        '3',
        'Class C',
        '1'
      ]
    ];

    const csvRows = [
      headers.join(','),
      ...sampleData.map(row =>
        row.map(field =>
          field.includes(',') || field.includes('"') || field.includes('\n')
            ? `"${field.replace(/"/g, '""')}"`
            : field
        ).join(',')
      )
    ];
    const csvContent = csvRows.join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'vocabulary_template.csv';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  };

  const CustomHeader = () => (
    <Box sx={{
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      mb: 2,
    }}>
      <Typography variant="h6" component="h1">
        Vocabularies
      </Typography>

      <Box sx={{ display: 'flex', gap: 1 }}>
        <Button
          variant='outlined'
          size='small'
          sx={{
            fontSize: '0.875rem',
            textTransform: 'capitalize',
            minWidth: '120px'
          }}
          onClick={handleImport}
          disabled={importing}
          startIcon={<UploadFileIcon />}
        >
          Import CSV
        </Button>

        <Button
          variant='outlined'
          size='small'
          sx={{ fontSize: '0.875rem', textTransform: 'capitalize' }}
          onClick={downloadTemplate}
          startIcon={<DownloadIcon />}
        >
          Template
        </Button>

        <Button
          variant='contained'
          size='small'
          onClick={() => navigate('/vocabularies/create')}
          sx={{
            bgcolor: config.colors.primary,
            fontSize: '0.875rem',
            textTransform: 'capitalize'
          }}
        >
          Create Vocabulary
        </Button>

        <input
          ref={fileInputRef}
          type="file"
          accept=".csv"
          onChange={handleFileChange}
          style={{ display: 'none' }}
        />
      </Box>
    </Box>
  );

  return (
    <Box sx={{ p: 2.5 }}>
      <CustomHeader />
      <Box>
        <Grid container spacing={2.75} paddingY={3}>
          <Grid item size={2}>
            <CustomTextField
              label="Input Word to Search"
              variant='outlined'
              size='small'
              fullWidth
              value={inputValue.word}
              onChange={(e) =>
                setInputValue({ ...inputValue, word: e.target.value })
              }
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  e.preventDefault();
                  onSubmit();
                }
              }}
            />
          </Grid>
          <Grid item size={2}>
            <Autocomplete
              options={Object.entries(VocabularyType).map(([value, name]) => ({ value: parseInt(value), name }))}
              getOptionLabel={(option) => option?.name || ''}
              value={Object.entries(VocabularyType).map(([value, name]) => ({ value: parseInt(value), name })).find(v => v.value === parseInt(inputValue.type)) || null}
              onChange={(_, value)  => setInputValue({...inputValue, type: value?.value || '' })}
              renderInput={(inputValue) => (
                <CustomTextField {...inputValue} label="Type" size="small" />
              )}
            />
          </Grid>

          {/* <Grid item size={2}>
            <Autocomplete
              options={classes}
              getOptionLabel={(option) => option?.name || ''}
              value={classes.find(c => c.id === params.class_id) || null}
              onChange={(_, value) => setParams({ ...params, class_id: value?.id || '' })}
              renderInput={(params) => (
                <CustomTextField {...params} label="Class" size="small" />
              )}
            />
          </Grid>

          <Grid item size={2}>
            <Autocomplete
              options={sessions}
              getOptionLabel={(option) => option.label}
              value={sessions.find(c => c.id === params.session) || null}
              onChange={(_, value) => setParams({ ...params, session: value.id || '' })}
              renderInput={(params) => (
                <CustomTextField {...params} label="Session" size="small" />
              )}
            />
          </Grid> */}
          <Grid item size={4}>
            <Box>
              <Button
                variant='contained'
                sx={{
                  bgcolor: config.colors.primary,
                  fontSize: '0.9375rem',
                  textTransform: 'capitalize',
                  mr: 1
                }}
                onClick={onSubmit}
              >
                Search
              </Button>

              <Button
                variant='contained'
                sx={{ mr: 2.5, bgcolor: '#8592A3', fontSize: '0.9375rem', textTransform: 'capitalize' }}
                onClick={handleReset}
              >
                Reset
              </Button>
            </Box>
          </Grid>
        </Grid>
      </Box>

      <Box sx={{ width: '100%' }}>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <HeadCell sx={{ width: '5%' }}>
                  ID
                </HeadCell>
                <HeadCell sx={{ width: '10%' }}>
                  <TableSortLabel
                    direction={params.sort.order === 'asc' ? 'asc' : 'desc'}
                    onClick={() =>
                      setParams({
                        ...params,
                        sort: {
                          field: 'word',
                          order: params.sort.order === 'asc' ? 'desc' : 'asc'
                        }
                      })
                    }
                  >
                    Word
                  </TableSortLabel>
                </HeadCell>
                <HeadCell sx={{ width: '5%' }}>
                  <TableSortLabel
                    direction={params.sort.order === 'asc' ? 'asc' : 'desc'}
                    onClick={() =>
                      setParams({
                        ...params,
                        sort: {
                          field: 'type',
                          order: params.sort.order === 'asc' ? 'desc' : 'asc'
                        }
                      })
                    }
                  >
                    Type
                  </TableSortLabel>
                </HeadCell>
                <HeadCell sx={{ width: '5%' }}>
                  <TableSortLabel
                    direction={params.sort.order === 'asc' ? 'asc' : 'desc'}
                    onClick={() =>
                      setParams({
                        ...params,
                        sort: {
                          field: 'level',
                          order: params.sort.order === 'asc' ? 'desc' : 'asc'
                        }
                      })
                    }
                  >
                    Level
                  </TableSortLabel>
                </HeadCell>
                <HeadCell sx={{ width: '20%' }}>
                  <TableSortLabel
                    direction={params.sort.order === 'asc' ? 'asc' : 'desc'}
                    onClick={() =>
                      setParams({
                        ...params,
                        sort: {
                          field: 'definition',
                          order: params.sort.order === 'asc' ? 'desc' : 'asc'
                        }
                      })
                    }
                  >
                    Definition
                  </TableSortLabel>
                </HeadCell>
                <HeadCell sx={{ width: '20%' }}>Sample</HeadCell>
                <HeadCell sx={{ width: '10%' }}>Description</HeadCell>
                <HeadCell sx={{ width: '10%' }} align='right'>
                  Action
                </HeadCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {words.map(({ id, word, type, level, definition, description, example }, index) => (
                <TableRow key={id} hover>
                  <TableCell>{(meta.current_page - 1) * meta.per_page + index + 1}</TableCell>
                  <TableCell>{word}</TableCell>
                  <TableCell>{VocabularyType[type]}</TableCell>
                  <TableCell>{getLevelName(level)}</TableCell>
                  <TableCell>{definition}</TableCell>
                  <TableCell>
                    <ExampleDisplay example={example} />
                  </TableCell>
                  <TableCell>{formatHtml(description).slice(0, 100)}</TableCell>
                  <TableCell align='right' sx={{ gap: 2 }}>
                    <IconButton size='small' onClick={() => navigate(`/vocabularies/${id}`)}>
                      <VisibilityIcon variant='outlined' color='primary' />
                    </IconButton>
                    <IconButton size='small' onClick={() => navigate(`/vocabularies/${id}/edit`)}>
                      <EditIcon variant='contained' color='secondary' />
                    </IconButton>
                    <IconButton size='small' onClick={() => handleClickOpen(id)}>
                      <DeleteIcon variant='outlined' color='error' />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
              {words.length === 0 && (
                <TableRow>
                  <TableCell colSpan={12} align='center'>
                    No data available
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </Box>

      <Stack spacing={2} alignItems={'center'} pt={4.5} pb={2.5}>
        {total > 0 && total > 1 && (
          <Pagination
            count={total}
            page={params.page}
            onChange={handleChangePagination}
            variant='outlined'
            shape='rounded'
            renderItem={item => (
              <PaginationItem
                {...item}
                sx={{
                  '&.MuiPaginationItem-root': {
                    color: config.colors.primary,
                    backgroundColor: '#f0f2f4',
                    border: 'none'
                  },
                  '&.Mui-selected': {
                    backgroundColor: config.colors.primary,
                    color: 'white'
                  },
                  '&:hover': {
                    backgroundColor: '#e1e4e8'
                  },
                  '& .MuiSvgIcon-root': {
                    color: config.colors.bodyColor
                  }
                }}
                slots={{
                  previous: KeyboardDoubleArrowLeftIcon,
                  next: KeyboardDoubleArrowRightIcon
                }}
              />
            )}
          />
        )}

      </Stack>

      <Dialog open={showImportDialog} onClose={() => setShowImportDialog(false)} maxWidth="md">
        <DialogTitle>Import Results</DialogTitle>
        <DialogContent>
          {importResults && (
            <Box sx={{ pt: 1 }}>
              <Alert severity="success" sx={{ mb: 2 }}>
                <Typography variant="body2">
                  ✅ Successfully imported: <strong>{importResults.total}</strong> vocabularies
                </Typography>
              </Alert>

              {importResults.error_count > 0 && (
                <Alert severity="error" sx={{ mb: 2 }}>
                  <Typography variant="body2">
                    ❌ Failed to import: <strong>{importResults.error_count}</strong> rows
                  </Typography>
                </Alert>
              )}

              {importResults.error_details && importResults.error_details.length > 0 && (
                <Alert severity="error" sx={{ mb: 2 }}>
                  <Box>
                    <Typography variant="subtitle2" gutterBottom>
                      Error Details:
                    </Typography>
                    <List dense sx={{ maxHeight: 200, overflow: 'auto' }}>
                      {importResults.error_details.slice(0, 10).map((error, index) => (
                        <ListItem key={index} sx={{ py: 0.5 }}>
                          <ListItemText
                            primary={error}
                          />
                        </ListItem>
                      ))}
                    </List>
                  </Box>
                </Alert>
              )}

              {importResults.duplicates && importResults.duplicates.length > 0 && (
                <Alert severity="warning" sx={{ mt: 2 }}>
                  <Typography variant="body2">
                    ⚠️ Found {importResults.duplicates.length} duplicate words (skipped)
                  </Typography>
                </Alert>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowImportDialog(false)} variant="contained">
            Close
          </Button>
        </DialogActions>
      </Dialog>

      <AlertDialog open={openDialog} setOpen={setOpenDialog} handleAction={handleAction} />
      <CustomSnackbar open={snackbarOpen} setOpen={setSnackbarOpen} message={message} />
    </Box>
  );
};

export default Vocabulary;
