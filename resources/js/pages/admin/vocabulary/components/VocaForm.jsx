import React, { useEffect, useMemo, useState } from 'react';
import { useForm, Controller } from 'react-hook-form';
import {
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  FormHelperText,
  Button,
  Grid,
  Box,
  styled,
  Typography,
  Card,
  CardContent, Chip
} from '@mui/material';
import { config, TypeForm } from '../utils';
import { useNavigate, useParams } from 'react-router-dom';
import { createVocabulary, getVocabulary, updateVocabulary, getLevels } from '../../../../services/admin/vocabularies';
import { VocabularyType } from '../utils';
import CustomSnackbar from '../../../../components/snackbar';
import { Editor } from '@tinymce/tinymce-react';
import { tinymceInit } from '../../../../config/tinymce-init';

export const CustomTextField = styled(TextField)(({ theme }) => ({
  '& .MuiOutlinedInput-root': {
    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
      borderColor: config.colors.primary,
      borderWidth: '1px'
    }
  },
  '& .MuiInputLabel-root': {
    fontSize: '14px'
  }
}));

const VocabularyForm = ({ typeForm }) => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [message, setMessage] = useState();
  const [tags, setTags] = useState([]);

  const [levels, setLevels] = useState([]);

  const {
    handleSubmit,
    setValue,
    control,
    setError,
    formState: { errors }
  } = useForm({
    mode: 'onChange',
    defaultValues: {
      word: '',
      definition: '',
      type: 1,
      level: 1,
      example1: '',
      example2: '',
      example3: '',
      description: ''
    }
  });

  const isPreview = typeForm === TypeForm.VIEW;

  const fetchDetailWord = async id => {
    try {
      const { data: { data: { word, definition, type, level, example, description, tags} } } = await getVocabulary(id);
      setValue('word', word);
      setValue('definition', definition);
      setValue('type', Number(type));
      setValue('level', Number(level));
      setValue('description', description);
      setTags(tags);
      if (example) {
        try {
          const parsedExamples = typeof example === 'string' ? JSON.parse(example) : example;
          if (Array.isArray(parsedExamples)) {
            setValue('example1', parsedExamples[0] || '');
            setValue('example2', parsedExamples[1] || '');
            setValue('example3', parsedExamples[2] || '');
          } else if (typeof parsedExamples === 'string') {
            setValue('example1', parsedExamples);
            setValue('example2', '');
            setValue('example3', '');
          }
        } catch (e) {
          setValue('example1', example);
          setValue('example2', '');
          setValue('example3', '');
        }
      }
    } catch (error) {
      console.error('Error fetching vocabulary:', error);
    }
  };

  useEffect(() => {
    const fetchLevels = async () => {
      try {
        const { data: { data } } = await getLevels();
        setLevels(data);
      } catch (error) {
        console.error('Error fetching levels:', error);
      }
    };
    fetchLevels();
    id && fetchDetailWord(id);
  }, []);

  const onSubmit = async formValues => {
    const { example1, example2, example3, word, definition, type, description, level } = formValues
    try {
      const examples = [
        example1?.trim(),
        example2?.trim(),
        example3?.trim()
      ].filter(ex => ex && ex !== '');

      const processedData = {
        word,
        definition,
        type,
        description,
        level,
        example: examples
      };

      if (id) {
        await updateVocabulary(id, processedData);
        navigate(`/vocabularies/${id}/edit`);
        setMessage('Vocabulary updated successfully');
        setSnackbarOpen(true);
      } else {
        await createVocabulary(processedData);
        navigate('/vocabularies');
      }
    } catch (error) {
      if (error?.response?.status === 422 && error?.response?.data?.word) {
        setError('word', {
          type: 'manual',
          message: error.response.data.word
        });
        return;
      }
      setMessage('Error saving vocabulary. Please try again.');
      setSnackbarOpen(true);
    }
  };

  const title = useMemo(() => {
    return id ? 'Edit Vocabulary' : 'Create Vocabulary';
  }, [id]);

  return (
    <Box padding={2.5}>
      {/*<Header title={title} onClick={() => navigate('/vocabularies')} labelButton='Vocabulary List' />*/}
      <Box component='form' onSubmit={handleSubmit(onSubmit)} sx={{ flexGrow: 1 }}>
        <Grid container spacing={2}>
          <Grid size={6}>
            <Controller
              name='word'
              control={control}
              rules={{
                required: 'This field is required.',
                maxLength: { value: 191, message: 'Word must be less than 191 characters.' },
              }}
              render={({ field }) => (
                <CustomTextField
                  {...field}
                  fullWidth
                  label='Word'
                  variant='outlined'
                  size='small'
                  error={!!errors.word}
                  helperText={errors.word?.message}
                  disabled={isPreview}
                />
              )}
            />
          </Grid>

          <Grid size={6}>
            <Controller
              name='definition'
              control={control}
              rules={{ required: 'This field is required.' }}
              render={({ field }) => (
                <CustomTextField
                  {...field}
                  fullWidth
                  label='Definition'
                  variant='outlined'
                  size='small'
                  error={!!errors.definition}
                  helperText={errors.definition?.message}
                  disabled={isPreview}
                />
              )}
            />
          </Grid>

          <Grid size={6}>
            <Controller
              name='type'
              control={control}
              rules={{ required: 'This field is required.' }}
              render={({ field }) => (
                <FormControl fullWidth error={!!errors.type} size='small'>
                  <InputLabel id='type-id'>Type</InputLabel>
                  <Select
                    labelId='type-id'
                    id='type'
                    label='Type'
                    {...field}
                    value={field.value || 1}
                    disabled={isPreview}
                  >
                    {Object.entries(VocabularyType).map(([key, value]) => (
                      <MenuItem key={key} value={key}>
                        {value}
                      </MenuItem>
                    ))}
                  </Select>
                  <FormHelperText>{errors.type?.message}</FormHelperText>
                </FormControl>
              )}
            />
          </Grid>
          <Grid size={6}>
            <Controller
              name='level'
              control={control}
              rules={{ required: 'This field is required.' }}
              render={({ field }) => (
                <FormControl fullWidth error={!!errors.level} size='small'>
                  <InputLabel id='level'>Level</InputLabel>
                  <Select
                    labelId='level'
                    id='level'
                    label='Level'
                    {...field}
                    value={field.value}
                    disabled={isPreview}
                  >
                    {levels.map(({ value, label }) => (
                      <MenuItem key={value} value={value}>
                        {label}
                      </MenuItem>
                    ))}
                  </Select>
                  <FormHelperText>{errors.level?.message}</FormHelperText>
                </FormControl>
              )}
            />
          </Grid>
        </Grid>

        <Grid container spacing={2} sx={{ mt: 2 }}>
          <Grid size={6}>
            <Card sx={{ mb: 2, bgcolor: '#fafafa' }}>
              <CardContent sx={{ py: 2 }}>
                <Typography variant="body2" sx={{ mb: 1, color: 'text.secondary' }}>
                  Sample 1
                </Typography>
                <Controller
                  name='example1'
                  control={control}
                  rules={{ required: 'At least one sample is required.' }}
                  render={({ field }) => (
                    <Editor
                      tinymceScriptSrc="/tinymce/tinymce.min.js"
                      init={{
                        ...tinymceInit,
                        placeholder: 'Enter sample 1...',
                        content_style: isPreview ?`
                          body {
                            font-family: Arial, sans-serif;
                            font-size: 14px;
                            color:rgba(0, 0, 0, 0.38);
                          }

                        `:""
                      }}
                      value={field.value}
                      onEditorChange={(value) => field.onChange(value)}
                      disabled={isPreview}
                    />
                  )}
                />
                {errors.example1 && (
                  <Typography color="error" variant="caption" sx={{ mt: 1, display: 'block' }}>
                    {errors.example1.message}
                  </Typography>
                )}
              </CardContent>
            </Card>
          </Grid>
          <Grid size={6}>
            <Card sx={{ mb: 2, bgcolor: '#fafafa' }}>
              <CardContent sx={{ py: 2 }}>
                <Typography variant="body2" sx={{ mb: 1, color: 'text.secondary' }}>
                  Sample 2 (Optional)
                </Typography>
                <Controller
                  name='example2'
                  control={control}
                  render={({ field }) => (
                    <Editor
                      tinymceScriptSrc="/tinymce/tinymce.min.js"
                      init={{
                        ...tinymceInit,
                        placeholder: 'Enter sample 2...',
                        content_style: isPreview ?`
                          body {
                            font-family: Arial, sans-serif;
                            font-size: 14px;
                            color:rgba(0, 0, 0, 0.38);
                          }

                        `:""
                      }}
                      value={field.value}
                      onEditorChange={(value) => field.onChange(value)}
                      disabled={isPreview}
                    />
                  )}
                />
                {errors.example2 && (
                  <Typography color="error" variant="caption" sx={{ mt: 1, display: 'block' }}>
                    {errors.example2.message}
                  </Typography>
                )}
              </CardContent>
            </Card>
          </Grid>
          <Grid size={6}>
            <Card sx={{ mb: 2, bgcolor: '#fafafa' }}>
              <CardContent sx={{ py: 2 }}>
                <Typography variant="body2" sx={{ mb: 1, color: 'text.secondary' }}>
                  Sample 3 (Optional)
                </Typography>
                <Controller
                  name='example3'
                  control={control}
                  render={({ field }) => (
                    <Editor
                      tinymceScriptSrc="/tinymce/tinymce.min.js"
                      init={{
                        ...tinymceInit,
                        placeholder: 'Enter sample 3...',
                        content_style: isPreview ?`
                          body {
                            font-family: Arial, sans-serif;
                            font-size: 14px;
                            color:rgba(0, 0, 0, 0.38);
                          }

                        `:""
                      }}
                      value={field.value}
                      onEditorChange={(value) => field.onChange(value)}
                      disabled={isPreview}
                    />
                  )}
                />
                {errors.example3 && (
                  <Typography color="error" variant="caption" sx={{ mt: 1, display: 'block' }}>
                    {errors.example3.message}
                  </Typography>
                )}
              </CardContent>
            </Card>
          </Grid>
          <Grid size={6}>
            <Card sx={{ mb: 2, bgcolor: '#fafafa' }}>
              <CardContent sx={{ py: 2 }}>
                <Typography variant="body2" sx={{ mb: 1, color: 'text.secondary' }}>
                  Description
                </Typography>
                <Controller
                  name='description'
                  control={control}
                  render={({ field }) => (
                    <Box>
                      <Editor
                        tinymceScriptSrc="/tinymce/tinymce.min.js"
                        init={{
                          ...tinymceInit,
                          placeholder: 'Enter description...',
                          content_style: isPreview ?`
                          body {
                            font-family: Arial, sans-serif;
                            font-size: 14px;
                            color:rgba(0, 0, 0, 0.38);
                          }

                        `:""
                        }}
                        value={field.value}
                        onEditorChange={(value) => field.onChange(value)}
                        disabled={isPreview}
                      />
                    </Box>
                  )}
                />
                {errors.description && (
                  <Typography color="error" variant="caption" sx={{ mt: 1, display: 'block' }}>
                    {errors.description.message}
                  </Typography>
                )}
              </CardContent>
            </Card>
          </Grid>
          {tags.length > 0 && (
            <>
              <Typography variant="body2" sx={{ mb: 1, color: 'text.secondary' }}>
                List tag vocabulary:
              </Typography>
              <Box sx={{ display: "flex", flexWrap: "wrap", gap: 1 }}>
                {tags.map((tag) => (
                  <Chip
                    key={tag.id}
                    label={tag.title}
                    component="a"
                    target="_blank"
                    href={`/admin/tag-vocabularies/${tag.id}`}
                    clickable
                    color="primary"
                    variant="outlined"
                  />
                ))}
              </Box>
            </>
          )}
        </Grid>

        <Box sx={{ mt: 3 }} display={'flex'} justifyContent='flex-end'>
          {isPreview ? (
            <Button
              variant='contained'
              sx={{
                mr: 1,
                bgcolor: config.colors.primary,
                fontSize: '0.9375rem',
                textTransform: 'capitalize'
              }}
              component="a"
              href={`/admin/vocabularies/${id}/edit`}
            >
              Edit
            </Button>
          ) : (
            <Button
              variant='contained'
              sx={{
                bgcolor: config.colors.primary,
                fontSize: '0.9375rem',
                textTransform: 'capitalize'
              }}
              type='submit'
            >
              Save
            </Button>
          )}
        </Box>
      </Box>

      <CustomSnackbar
        open={snackbarOpen}
        setOpen={setSnackbarOpen}
        message={message}
      />
    </Box>
  );
};

export default VocabularyForm;
