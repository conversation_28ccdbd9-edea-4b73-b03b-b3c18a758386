// resources/js/exam.js
import React from 'react';
import { createRoot } from 'react-dom/client';
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import ExamInfo from '../../components/ExamInfo';
import { ToastContainer } from 'react-toastify';

const rootEl = document.getElementById('root-exam');
if (rootEl) {
  createRoot(rootEl).render(
    <React.StrictMode>
      <BrowserRouter>
        <ToastContainer position="top-right" autoClose={3000} />
        <Routes>
          <Route path='admin/homeworks/create' element={<ExamInfo />} />
          <Route path='admin/homeworks/:id/edit' element={<ExamInfo />} />
        </Routes>
      </BrowserRouter>
    </React.StrictMode>
  );
}
