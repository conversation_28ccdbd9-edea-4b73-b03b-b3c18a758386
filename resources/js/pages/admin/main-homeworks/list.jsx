import { createRoot } from 'react-dom/client';
import { <PERSON><PERSON><PERSON><PERSON>outer} from 'react-router-dom';
import React from 'react';
import {
  Card,
} from '@mui/material';
import List from '../../../components/main-homework/List';
import { ToastContainer } from 'react-toastify';

const rootEl = document.getElementById('root-main-homework');
if (!rootEl) {
  throw new Error('Root element not found');
}

createRoot(rootEl).render(
    <React.StrictMode>
      <BrowserRouter basename='/admin/main-homeworks'>
        <>
          <ToastContainer position="top-right" autoClose={3000} />
          <Card elevation={1} sx={{ p: 2, display: 'flex', flexDirection: 'column', gap: 2 }}>
            <List></List>
          </Card>
        </>
      </BrowserRouter>
    </React.StrictMode>
  
  
);
