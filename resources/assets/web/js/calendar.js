var calendar;
var dataSchedule;

function escapeHtml(text) {
  var map = {
    '&': '&amp;',
    '<': '&lt;',
    '>': '&gt;',
    '"': '&quot;',
    "'": '&#039;'
  };

  return text.replace(/[&<>"']/g, function(m) { return map[m]; });
}

function eventDataPush (events_data, key, val){
  events_data.push({
    title: escapeHtml(val.class),
    start: val.date+'T'+val.start+':00',
    color: val.type_class == 'Regular' ? (val.type == 'Reading' ? '#E7F6FD' : '#E7FDF7') : '#fff8ea',
    textColor: val.type_class == 'Regular' ? (val.type == 'Reading' ? '#1890FF' : '#10C285') : '#ffc145',
    status: (new Date() < new Date(val.date)) ? null : (val.attendances ? val.attendances : 'N/A'),
    imageurl: val.type_class == 'Regular' ? (val.type == 'Reading' ? '/assets/web/images/book-open.svg':'/assets/web/images/pencil.svg') : '/assets/web/images/user-circle.svg',
    description: escapeHtml(val.class)+' '+val.start+' '+'N/A', //title + time + status
    className: val.type_class == 'Regular' ? (val.type == 'Reading' ? 'item__reading' : 'item__writing') : "item__private",
  });
}
function genCalender(events,date){
  const calendarEl = document.getElementById('calendar');
  if (calendar) {
    calendar.destroy();
  }
  calendar = new FullCalendar.Calendar(calendarEl, {
    plugins: ['interaction', 'dayGrid', 'timeGrid', 'list'],
    timeZone: 'UTC',
    header: false,
    showNonCurrentDates: false,
    aspectRatio: 2,
    contentHeight: 'auto',
    events: events,
    eventRender: function (info) {
      if (info.event.extendedProps.imageurl) {
        let elem = info.el,
          img = document.createElement('img');
        divImage = document.createElement('div');
        divImage.className = "icon__rw";
        img.src = info.event.extendedProps.imageurl;
        elem.querySelector('.fc-title').appendChild(divImage).appendChild(img);
      }

      if (info.event.extendedProps.status) {
        let elem = info.el,
          divStatus = document.createElement('div');
        divStatus.className = "icon__status";
        if(info.event.extendedProps.status == 'O'){
          divStatus.className = "icon__status sto";
        }else if(info.event.extendedProps.status == 'X'){
          divStatus.className = "icon__status stx";
        }else if(info.event.extendedProps.status == 'Late'){
          divStatus.className = "icon__status stl";
        }else if(info.event.extendedProps.status == 'N/A'){
          divStatus.className = "icon__status stn";
        }
        divStatus.html = info.event.extendedProps.status;
        // console.log(divStatus.html)
        elem.querySelector('.fc-content').appendChild(divStatus).textContent = divStatus.html;
      }

      // var tooltip = new Tooltip(info.el, {
      //   title: info.event.extendedProps.description,
      //   placement: 'top',
      //   trigger: 'hover',
      //   container: '#calendar'
      // });
    },

    eventTimeFormat: { // like '14:30'
      hour: 'numeric',
      minute: '2-digit',
      meridiem: 'long'
    },
  });
  calendar.gotoDate(date);
  calendar.render();
}
$(function (){
  $( document ).ready(function() {
    ajaxgetSchedule($('#student-id').val());
  });

  $('#students').on('change', function() {
    ajaxgetSchedule($(this).val());
  });

  $('#months').on('change', function() {
    const scheMonth = dataSchedule.schedule[$('#years').val()+'-'+$(this).val()];
    const events_data = []
    $.each(scheMonth, function (key, val) {
      eventDataPush(events_data, key, val)
    });
    genCalender(events_data, $('#years').val()+'-'+$(this).val()+'-01');
    appendListSchedule(scheMonth);
  });

  $('#years').on('change', function() {
    const scheMonth = dataSchedule.schedule[$(this).val()+'-'+$('#months').val()];
    const events_data = []
    $.each(scheMonth, function (key, val) {
      eventDataPush(events_data, key, val)
    });
    genCalender(events_data, $(this).val()+'-'+$('#months').val()+'-01');
    appendListSchedule(scheMonth);
  });

  function ajaxgetSchedule(student_id){
    fetch(window.location.origin + '/ajax-get-schedule?student_id='+student_id)
      .then(response => response.json())
      .then(data => {
        dataSchedule = data;
        const d = new Date();
        const month = d.getMonth()+1;
        const viewM = ((''+month).length<2 ? '0' : '') + month;
        $('#months').val(viewM).change();
        $('#years').val(d.getFullYear()).change();
        const day = d.getDate();
        const currentMonth = d.getFullYear() + '-' + viewM;
        const scheMonth = data.schedule[currentMonth];
        const events_data = []
        $.each(scheMonth, function (key, val) {
          eventDataPush(events_data, key, val)
        });
        genCalender(events_data, currentMonth+'-01');
        appendListSchedule(scheMonth);
      })
      .catch(error => {
        console.error('Error fetching cities:', error);
      });
  }
  function appendListSchedule(schedule){
    $(".list-schedule").html('');
    $.each(schedule, function (key, val) {
      if(val.type_class == 'Regular') {
        if (val.type == 'Reading') {
          $(".list-schedule").append("<div>\n" +
            "                <div class=\"text-14-20 font-semibold text-neutral-700 mb-4\">" + val.date + "</div>\n" +
            "                <div class=\"flex items-center gap-[10px]\">\n" +
            "                  <div class=\"flex items-center justify-center bg-blueLight py-4 px-3 rounded-lg text-blue ml-[13px] relative\n" +
            "                before:block before:bg-blue before:w-[3px] before:absolute before:-left-[13px] before:h-full\">\n" +
            "                    " + tConvert(val.start) + "\n" +
            "                  </div>\n" +
            "                  <div>\n" +
            "                    <div class=\"text-14-20 font-medium text-neutral-800\">Reading Class " + escapeHtml(val.class) + "</div>\n" +
            "                  </div>\n" +
            "                </div>\n" +
            "              </div>");
        } else {
          $(".list-schedule").append("<div>\n" +
            "                <div class=\"text-14-20 font-semibold text-neutral-700 mb-4\">" + val.date + "</div>\n" +
            "                <div class=\"flex items-center gap-[10px]\">\n" +
            "                  <div class=\"flex items-center justify-center bg-greenLight py-4 px-3 rounded-lg text-green ml-[13px] relative\n" +
            "                before:block before:bg-green before:w-[3px] before:absolute before:-left-[13px] before:h-full\">\n" +
            "                    " + tConvert(val.start) + "\n" +
            "                  </div>\n" +
            "                  <div>\n" +
            "                    <div class=\"text-14-20 font-medium text-neutral-800\">Writing Class " + escapeHtml(val.class) + "</div>\n" +
            "                  </div>\n" +
            "                </div>\n" +
            "              </div>");
        }
      }else{
        $(".list-schedule").append("<div>\n" +
          "                <div class=\"text-14-20 font-semibold text-neutral-700 mb-4\">" + val.date + "</div>\n" +
          "                <div class=\"flex items-center gap-[10px]\">\n" +
          "                  <div style='    background-color: #fff8ea;\n" +
          "    border-color: #fff8ea;\n" +
          "    color: #ffc145;' class=\"flex items-center justify-center py-4 px-3 rounded-lg ml-[13px] relative\n" +
          "                before:block before:bg-private before:w-[3px] before:absolute before:-left-[13px] before:h-full\">\n" +
          "                    " + tConvert(val.start) + "\n" +
          "                  </div>\n" +
          "                  <div>\n" +
          "                    <div class=\"text-14-20 font-medium text-neutral-800\">Private Class " + escapeHtml(val.class) + "</div>\n" +
          "                  </div>\n" +
          "                </div>\n" +
          "              </div>");
      }
    });
  }

  function tConvert (time) {
    // Check correct time format and split into components
    time = time.toString ().match (/^([01]\d|2[0-3])(:)([0-5]\d)(:[0-5]\d)?$/) || [time];

    if (time.length > 1) { // If time format correct
      time = time.slice (1);  // Remove full string match value
      time[5] = +time[0] < 12 ? 'AM' : 'PM'; // Set AM/PM
      time[0] = +time[0] % 12 || 12; // Adjust hours
    }
    return time.join (''); // return adjusted time or original string
  }

})


