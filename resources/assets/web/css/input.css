@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Manrope', sans-serif;
    scroll-behavior: smooth;
  }

  .container {
    max-width: unset !important;
  }

  body {
    font-family: "Poppins", sans-serif;
    font-weight: 400;
    color: #252931;
    overflow-x: hidden;
  }

  .text-57-64 {
    @apply text-[57px] leading-[64px];
  }

  .text-36-44 {
    @apply text-[64px] leading-[44px];
  }

  .text-45-52 {
    @apply text-[57px] leading-[52px];
  }

  .text-32-40 {
    @apply text-[32px] leading-[44px];
  }

  .text-28-36 {
    @apply text-[28px] leading-9
  }

  .text-24-32 {
    @apply text-2xl;
  }
  .text-20-26 {
    @apply text-[20px] leading-[26px];
  }
  .text-16-24 {
    @apply text-base;
  }
  .text-14-20 {
    @apply text-sm;
  }
  .text-12-16 {
    @apply text-xs;
  }
  .text-11-16 {
    @apply text-[11px] leading-4;
  }
  .text-10-16 {
    @apply text-[10px] leading-4;
  }
  .text-dot {
    @apply before:block before:w-1 before:h-1 before:bg-black before:rounded-full before:absolute before:top-2 before:-left-[14px];
  }
}

.overflow-hidden {
  overflow: hidden;
}

svg {
  width: 100%;
  height: 100%;
}

.shadow-424D5B14 {
  box-shadow: 4px 0 30px 0 #424D5B14;
}

.shadow-323F6D0D {
  box-shadow: 0 4px 20px 0 #323F6D0D;
}

.shadow-00046212 {
  box-shadow: 0 10px 20px 0 #00046212;
}

.shadow-00000005 {
  box-shadow: 0 16px 24px 0 #00000005;
}

.rotate-180 {
  --tw-rotate: 180deg;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.box__select {
  -webkit-appearance: none;
  -moz-appearance: none;
}

.box__select option {
  width: 100%;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
  display: none;
}

.page-message .select2-container--default .selection .select2-selection--single::after {
  content: '';
  position: absolute;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 4px solid #ABB3BF;
  width: 0;
  height: 0;
  top: 20px;
  right: 8px;
}

.select2-results__option {
  padding: 12px 16px;
  color: #788393;
}

.select2-container--default .select2-results__option--highlighted[aria-selected],
.select2-container--default .select2-results__option[aria-selected=true] {
    background-color: #E8F9F3;
    color: #10C285;
    font-weight: 500;
}

.select2-results__option[aria-selected] {
  font-size: 14px;
  line-height: 20px;
}

.select2-results {
    box-shadow: 0px 10px 20px 0px rgba(0, 4, 98, 0.07);
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
}

.select2-container--default.select2-container--open.select2-container--below .select2-selection--single {
  border: 1px solid transparent;
  box-shadow: 0px 10px 20px 0px #00046212;
}

.select2-container--default .select2-selection--single {
  padding: 0.5rem 1rem;
  --tw-bg-opacity: 1;
  border: 1px solid #E2E4EB;
  border-radius : 8px;
  height: fit-content;
}

@media (min-width: 375px) {
  .select2-container .select2-selection--single .select2-selection__rendered {
    padding: 0;
    min-width: 75px;
  }
}

.select2-container--open .select2-dropdown--below {
  border: none;
  top: -2px;
}

.select2-container--default .select2-results__option--highlighted[aria-selected]:last-child,
.select2-container--default .select2-results__option[aria-selected=true]:last-child {
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}

.select2-container--default .select2-selection--single {
  position: relative;
}

.select2-container--default .select2-selection--single::after {
  content: '';
  position: absolute;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 4px solid #ABB3BF;
  width: 0;
  height: 0;
  top: 17px;
  right: 8px;
}

.invoice__reason .select2-container--default .select2-selection--single::after {
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid #ABB3BF;
  right: 18px;
}

.refund .select2-container--default .select2-selection--single::after {
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid #ABB3BF;
  top: 21px;
  right: 17px;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
  font-size: 0.875rem;
  line-height: 1.25rem;
  color: #252931;
  max-width: 250px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.page-message .select2-container--default .select2-selection--single,
.invoice-select .select2-container--default .select2-selection--single {
  border: 1px solid transparent;
}

.page-message .select2-container .select2-selection--single .select2-selection__rendered {
  font-weight: 600;
  color: #0F1A30;
  font-size: 16px;
  line-height: 24px;
  max-width: max-content;
}

.page-invoice .select2-container .select2-selection--single .select2-selection__rendered {
  max-width: max-content;
}

.page-message .select2-results__option {
  padding: 12px 16px;
}

.page-message .select2-container--default .select2-results>.select2-results__options {
  border-radius: 8px;
  background-color: white;
}

.page-message .select2-container,
.invoice__reason .select2-container,
.page-parent .page-feedback .select2-container,
.page-parent .page-grade .select2-container,
.page-parent .page-schedule .select2-container,
.invoice-select .select2-container {
  width: 100% !important;
}

.page-message .select2-container--default.select2-container--open.select2-container--below .select2-selection--single {
  border: 1px solid transparent;
  box-shadow: none;
  padding: 10px 16px;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}

.invoice-select .select2-container--default.select2-container--open.select2-container--below .select2-selection--single {
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}

.page-message .select2-container--default .select2-selection--single {
  padding: 10px 16px;
}

.select2-container--open .select2-dropdown--below.message__select {
  top: 3px;
  border-radius: 8px;
}

.message__select .select2-results__option--highlighted[aria-selected]:first-of-type,
.message__select .select2-results__option[aria-selected=true]:first-of-type {
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

.message__select .select2-results__option[aria-selected] {
  border-bottom: 1px solid #F7F7FA;
}

.message__select .select2-results__option[aria-selected]:last-of-type {
  border-bottom: none;
}

.page-message .select2-container--default .select2-selection--single::after,
.invoice-select .select2-container--default .select2-selection--single::after {
  content: none;
}

input {
  outline: none;
}

/*radio custom*/
.radio__custom {
  position: relative;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Hide the browser's default radio button */
.radio__custom input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}

/* Create a custom radio button */
.radio__custom .checkmark {
  position: absolute;
  top: 8px;
  left: 8px;
  height: 16px;
  width: 16px;
  background-color: #fff;
  border: 1px solid #E2E4EB;
  border-radius: 50%;
}

/* On mouse-over, add a grey background color */
.radio__custom:hover input ~ .checkmark {
  background-color: #ccc;
}

/* When the radio button is checked, add a blue background */
.radio__custom input:checked ~ .checkmark {
  background-color: #fff;
  border: 1px solid #10C285;
}

/* Create the indicator (the dot/circle - hidden when not checked) */
.radio__custom .checkmark:after {
  content: "";
  position: absolute;
  display: none;
}

/* Show the indicator (dot/circle) when checked */
.radio__custom input:checked ~ .checkmark:after {
  display: block;
}

/* Style the indicator (dot/circle) */
.radio__custom .checkmark:after {
  top: 3px;
  left: 3px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #10C285;
}

.radio__custom.active {
  border-color: #10C285;
  background: #E8F9F3;
}

.radio__custom.active .text-neutral-400 {
  color: #373E49;
}
/*end radio custom*/


/* Demo */
.switch {
  position: relative;
  display: inline-block;
  width: 48px;
  height: 24px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  -webkit-transition: .4s;
  transition: .4s;
}

.slider.round {
  border-radius: 34px;
}

input:checked + .slider {
  background-color: #10C285;
}

.slider:before {
  position: absolute;
  content: "";
  height: 20px;
  width: 20px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  -webkit-transition: .4s;
  transition: .4s;
}

.slider.round:before {
  border-radius: 50%;
}

input:checked + .slider:before {
  -webkit-transform: translateX(24px);
  -ms-transform: translateX(24px);
  transform: translateX(24px);
}

@media (min-width: 992px) {
  aside {
    width: 200px;
  }

  aside.aside-small {
    width: 72px;
  }

  .box__content--main {
    width: calc(100% - 200px);
    transform: translateX(200px);
  }

  .box__content--main.aside-small {
    width: calc(100% - 72px);
    transform: translateX(72px);
  }

  .header-fixed {
    left: 200px;
  }

  .header-fixed.aside-small {
    left: 72px;
  }

  .select2-container--default .select2-selection--single {
      padding: 10px 36px 10px 16px;
      width: 170px;
  }

  .select2-container .select2-selection--single .select2-selection__rendered {
      min-width: 108px;
  }

  .select2-container--default .select2-selection--single::after {
      right: 21px;
  }
}

@media (min-width: 1800px) {
  aside {
    width: 240px;
  }

  .box__content--main {
    width: calc(100% - 240px);
    transform: translateX(240px);
  }

  .header-fixed {
    left: 240px;
  }
}

/*custom fullcalendar*/
.calendar__schedule .fc-view * {
  font-family: "Inter", sans-serif;
}

.calendar__schedule .fc-view td,
.calendar__schedule .fc-view th {
  border: 1px solid #F7F7FA;
  background: #fff;
}

.calendar__schedule .fc-view .fc-day-header {
  text-align: left;
  padding: 4px 8px 16px 8px;
  font-size: 10px;
  font-weight: 700;
  line-height: 12px;
  color: #71717A;
  text-transform: uppercase;
}

.calendar__schedule .fc-view .fc-title {
  white-space: normal;
}

.calendar__schedule.fc-ltr .fc-dayGrid-view .fc-day-top .fc-day-number {
  font-size: 12px;
  font-weight: 500;
  line-height: 16px;
  color: #ABB3BF;
  float: unset;
  padding: 0;
}

.calendar__schedule.fc .fc-row .fc-content-skeleton thead td {
  padding: 4px 8px;
  line-height: 1;
  text-align: left;
}

.calendar__schedule .fc-event-container {
  position: relative;
}

.calendar__schedule .icon__status {
  position: absolute;
  color: red;
  bottom: -2px;
  right: -2px;
  border: 2px solid #fff;
  padding: 4px 8px 4px 8px;
  border-radius: 4px 0 0 0;
  background: #E7F6FD;
  font-size: 10px;
  font-weight: 600;
  line-height: 16px;
}

.calendar__schedule .fc-content {
  display: flex;
  flex-direction: column-reverse;
  padding: 12px 6px 12px 9px;
}

.calendar__schedule .fc-day-grid-event .fc-time {
  color: #373E49;
  font-size: 12px;
  font-weight: 600;
  line-height: 16px;
  text-align: left;
}

.calendar__schedule .fc-title {
  display: flex;
  align-items: center;
  justify-content: start;
  flex-direction: row-reverse;
  font-size: 12px;
  font-weight: 400;
  line-height: 16px;
}

.calendar__schedule .fc-event {
  border-radius: 0;
  margin: 0;
}

.calendar__schedule tr:first-child>td>.fc-day-grid-event {
  margin-top: 5px;
}

.calendar__schedule .fc-content .icon__rw {
  width: 12px;
  height: 12px;
  flex-shrink: 0;
  border-radius: 50%;
  margin-right: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.calendar__schedule .item__writing .icon__rw {
  background: #10C285;
}

.calendar__schedule .item__reading .icon__rw {
  background: #1890FF;
}

@media (max-width: 991.98px) {
  .calendar__schedule .fc-content .icon__rw {
    display: none;
  }

  .calendar__schedule .icon__status {
    display: none;
  }
  .calendar__schedule .fc-view .fc-title {
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  .calendar__schedule .fc-content {
    padding: 6px;
  }
}

.calendar__schedule .fc-day-grid-event {
  position: relative;
  width: calc(100% - 6px);
  margin: auto;
}

.calendar__schedule .fc-day-grid-event:before {
  content: '';
  display: block;
  width: 3px;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.calendar__schedule .item__writing:before {
  background: #10C285;
}
.calendar__schedule .item__reading:before {
  background: #1890FF;
}

.calendar__schedule.fc-unthemed .fc-disabled-day{
  background: #EBEDF2;
}

.calendar__schedule .fc-view td.fc-today, .fc-view th.fc-today {
  background: transparent;
}

.calendar__schedule.fc-ltr .fc-dayGrid-view .fc-today .fc-day-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #10C285;
  color: #fff;
  padding: 2px;
}

.calendar__schedule.fc-unthemed td.fc-today {
  position: relative;
}

.calendar__schedule.fc-unthemed td.fc-today:before {
  content: '';
  display: block;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  border: 1px solid #10C285;
}

.calendar__schedule.fc-unthemed .fc-content-skeleton td.fc-today:before {
  border-top: 0;
}

.calendar__schedule.fc-unthemed thead td.fc-today:before {
  border-bottom: 0;
}

.calendar__schedule .fc-view {
  overflow: auto;
}

/*end custom fullcalendar*/
/*tooltip*/
.popper,
.tooltip {
  position: absolute;
  z-index: 9999;
  background: #0F1A30;
  color: #fff;
  width: 150px;
  border-radius: 3px;
  box-shadow: 0 0 2px rgba(0,0,0,0.5);
  padding: 10px;
  text-align: center;
}
.style5 .tooltip {
  background: #1E252B;
  color: #FFFFFF;
  max-width: 200px;
  width: auto;
  font-size: .8rem;
  padding: .5em 1em;
}
.popper .popper__arrow,
.tooltip .tooltip-arrow {
  width: 0;
  height: 0;
  border-style: solid;
  position: absolute;
  margin: 5px;
}

.tooltip .tooltip-arrow,
.popper .popper__arrow {
  border-color: #0F1A30;
}
.style5 .tooltip .tooltip-arrow {
  border-color: #1E252B;
}
.popper[x-placement^="top"],
.tooltip[x-placement^="top"] {
  margin-bottom: 5px;
}
.popper[x-placement^="top"] .popper__arrow,
.tooltip[x-placement^="top"] .tooltip-arrow {
  border-width: 5px 5px 0 5px;
  border-left-color: transparent;
  border-right-color: transparent;
  border-bottom-color: transparent;
  bottom: -5px;
  left: calc(50% - 5px);
  margin-top: 0;
  margin-bottom: 0;
}
.popper[x-placement^="bottom"],
.tooltip[x-placement^="bottom"] {
  margin-top: 5px;
}
.tooltip[x-placement^="bottom"] .tooltip-arrow,
.popper[x-placement^="bottom"] .popper__arrow {
  border-width: 0 5px 5px 5px;
  border-left-color: transparent;
  border-right-color: transparent;
  border-top-color: transparent;
  top: -5px;
  left: calc(50% - 5px);
  margin-top: 0;
  margin-bottom: 0;
}
.tooltip[x-placement^="right"],
.popper[x-placement^="right"] {
  margin-left: 5px;
}
.popper[x-placement^="right"] .popper__arrow,
.tooltip[x-placement^="right"] .tooltip-arrow {
  border-width: 5px 5px 5px 0;
  border-left-color: transparent;
  border-top-color: transparent;
  border-bottom-color: transparent;
  left: -5px;
  top: calc(50% - 5px);
  margin-left: 0;
  margin-right: 0;
}
.popper[x-placement^="left"],
.tooltip[x-placement^="left"] {
  margin-right: 5px;
}
.popper[x-placement^="left"] .popper__arrow,
.tooltip[x-placement^="left"] .tooltip-arrow {
  border-width: 5px 0 5px 5px;
  border-top-color: transparent;
  border-right-color: transparent;
  border-bottom-color: transparent;
  right: -5px;
  top: calc(50% - 5px);
  margin-left: 0;
  margin-right: 0;
}
.tooltip-inner {
  font-size: 14px;
  line-height: 20px;
}
@media (min-width: 992px) {
  .calendar__schedule .tooltip {
    display: none;
  }
}

/*calendar dashboard*/
.calendar__dashboard .fc-toolbar h2 {
  font-size: 14px;
  font-weight: 600;
  line-height: 20px;
  color: #252931;
}

.fc td, .fc th {
  border: 0;
  vertical-align: middle;
}

.fc-button-primary {
  border: 0;
  padding: 0;
}

.fc-icon-chevron-left:before,
.fc-icon-chevron-right:before {
  content: unset;
}

.fc-header-toolbar .fc-button {
  background: url("/q1-academy-spr/html/src/images/keyboard_arrow_right.svg") no-repeat center;
  background-size: contain;
  outline: 0;
}

.fc-header-toolbar .fc-prev-button {
  transform: rotate(180deg);
}

.fc-button-primary:not(:disabled).fc-button-active, .fc-button-primary:not(:disabled):active {
  background-color: #fff;
  border: 0;
}
.fc th {
  font-size: 10px;
  font-weight: 600;
  line-height: 16px;
  color: #788393;
}

.fc td {
  font-size: 10px;
  font-weight: 400;
  line-height: 16px;
  color: #252931;
  text-align: center;
}

.fc-ltr .fc-dayGrid-view .fc-day-top .fc-day-number {
  float: unset;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  width: 20px;
  height: 20px;
}

.fc-unthemed td.fc-today {
  background: #fff;
}

.fc-dayGrid-view .fc-today span {
  background-color: #0F1A30;
  color: #fff;
  padding: 6px;
  border-radius: 50%;
}

.fc-head-container .fc-row {
  min-height: 4em;
  display: flex;
  align-items: center;
}

.fc-dayGrid-view .fc-body .fc-row {
  display: flex;
  align-items: center;
}

.tabs li.active {
  font-weight: 600;
  color: #0F1A30;
}

.page-message .tabs li.active {
  font-weight: 400;
  background-color: #E8F9F3;
}

textarea {
  resize: none;
}

.refund .select2-container--default .select2-selection--single {
  border-radius: 8px;
  border: 1px solid #E2E4EB;
  padding: 14px 40px 14px 16px;
}

.part2 {
  border-left: 1px solid #D4D9DF;
  border-right: 1px solid #D4D9DF;
}

/* CSS */
.options {
  display: none;
}

.option {
  padding: 10px;
  cursor: pointer;
}

.option:hover {
  background: #f0f0f0;
}

@media (min-width: 768px) {
  .page-parent .select2-container--default .select2-selection--single .select2-selection__rendered {
    max-width: 100%;
  }
}

.border-neutral-300 {
  border-color: #D4D9DF;
}

/*radio custom*/
.checkbox__custom {
  display: flex;
  align-items: center;
  position: relative;
  padding-left: 24px;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Hide the browser's default checkbox */
.checkbox__custom input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

/* Create a custom checkbox */
.checkbox__custom .checkmark {
  position: absolute;
  top: 2px;
  left: 0;
  height: 16px;
  width: 16px;
  border-radius: 2px;
  background-color: #fff;
  border: 1px solid #E2E4EB;
}

/* On mouse-over, add a grey background color */
.checkbox__custom:hover input ~ .checkmark {
  background-color: #fff;
}

/* When the checkbox is checked, add a blue background */
.checkbox__custom input:checked ~ .checkmark {
  background-color: #10C285;
  border-color: #10C285;
}

/* Create the checkmark/indicator (hidden when not checked) */
.checkbox__custom .checkmark:after {
  content: "";
  position: absolute;
  display: none;
}

/* Show the checkmark when checked */
.checkbox__custom input:checked ~ .checkmark:after {
  display: block;
}

/* Style the checkmark/indicator */
.checkbox__custom .checkmark:after {
  left: 4px;
  top: 1px;
  width: 6px;
  height: 10px;
  border: solid white;
  border-width: 0 3px 3px 0;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
}
/*end radio custom*/

.page-invoice .tabs li.active a::before,
.page-invoice .tabs li:hover a::before {
  content: '';
  position: absolute;
  bottom: 1px;
  width: 100%;
  height: 2px;
  background-color: #0F1A30;
}

.swiper {
  height: 100%;
}

.swiper-slide {
  text-align: center;
  font-size: 18px;
  display: flex;
  justify-content: center;
  align-items: center;
  height: auto;
}

.swiper-button-prev:after,
.swiper-rtl .swiper-button-next:after,
.swiper-button-next:after,
.swiper-rtl .swiper-button-prev:after {
  font-size: 16px;
  color: #10C285;
  border-radius: 50%;
  padding: 7px 11px;
  border: 1px solid #EBEDF2;
  background-color: #FFFFFF;
}

.swiper-button-next,
.swiper-button-prev {
  z-index: 99;
}

.swiper-button-prev {
  left: -15px;
}

.swiper-button-next {
  right: -15px;
}

.calendar-event-dot .fc-event {
  background-color: transparent;
  border: none;
  top: -8px
}

.calendar-event-dot .fc-day-grid-event .fc-content {
  display: none;
}

.calendar-event-dot .fc-day-grid-event::after {
  content: '';
  display: block;
  width: 4px;
  height: 4px;
  background-color: #10C285;
  border-radius: 50%;
  position: absolute;
  left: 46%;
}

@media (max-width: 1024px) {
  .swiper-button-prev:after,
  .swiper-rtl .swiper-button-next:after,
  .swiper-button-next:after,
  .swiper-rtl .swiper-button-prev:after {
    font-size: 12px;
    padding: 5px 8px;
  }

  .swiper-button-prev {
    left: -12px;
  }
  
  .swiper-button-next {
    right: -12px;
  }
}

.text-edited h1 {
  font-size: 57px;
  line-height: 64px;
}

.text-edited h2 {
  font-size: 45px;
  line-height: 52px;
}

.text-edited h3 {
  font-size: 36px;
  line-height: 44px;
}

.text-edited h4 {
  font-size: 32px;
  line-height: 40px;
}

.text-edited h5 {
  font-size: 28px;
  line-height: 36px;
}

.text-edited h6 {
  font-size: 24px;
  line-height: 32px;
}

.text-edited h1,
.text-edited h2,
.text-edited h3,
.text-edited h4,
.text-edited h5,
.text-edited h6,
.text-edited p,
.text-edited li {
  margin-bottom: 16px;
}

.text-edited p,
.text-edited li {
  font-size: 14px;
  line-height: 20px;
  position: relative;
}

.text-edited a {
  font-size: 12px;
  line-height: 16px;
  color: #10C285;
}

.text-edited table {
  width: 100%;
  max-width: 100%;
  margin-bottom: 1rem;
  border: 1px solid #EBEDF2;
}

.text-edited .table th {
  text-align: inherit;
}

.text-edited .table td, 
.text-edited .table th {
  padding: .75rem;
  vertical-align: top;
  border-top: 1px solid #EBEDF2;
  border-left: 1px solid #EBEDF2;
}

.text-edited .table thead th {
  vertical-align: bottom;
  border-bottom: 1px solid #EBEDF2;
  border-left: 1px solid #EBEDF2;
}

.text-edited .table tbody tr:nth-of-type(odd) {
  background-color: #F7F7FA;
}

.text-edited figure.table {
  width: 100%;
}

.text-edited ul {
  list-style: disc;
}

.text-edited ol {
  list-style: auto;
}

.text-edited ul, 
.text-edited ol {
  margin-left: 1.25rem;
}

.text-edited ol > ul {
  margin-left: 0;
}

/* .show_student::after {
  content: '';
  position: absolute;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid #10C285;
  width: 0;
  height: 0;
  top: 28px;
  right: 50px;
} */
