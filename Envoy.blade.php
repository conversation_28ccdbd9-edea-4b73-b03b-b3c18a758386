@setup
require __DIR__.'/vendor/autoload.php';

$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);

try {
$dotenv->load();
$dotenv->required(['DEPLOY_USER', 'DEPLOY_SERVER', 'DEPLOY_BASE_DIR', 'DEPLOY_REPO'])->notEmpty();
} catch ( Exception $e )  {
echo $e->getMessage();
}

$user = env('DEPLOY_USER');
$repo = env('DEPLOY_REPO');

if (!isset($baseDir)) {
$baseDir = env('DEPLOY_BASE_DIR');
}

if (!isset($branch)) {
$branch = 'master';
}

$releaseDir = $baseDir . '/releases';
$currentDir = $baseDir . '/current';
$release = date('YmdHis');
$currentReleaseDir = $releaseDir . '/' . $release;

function logMessage($message) {
return "echo '\033[32m" .$message. "\033[0m';\n";
}
@endsetup

@servers([
'localhost' => '127.0.0.1',
'prod' => env('DEPLOY_USER').'@'.env('DEPLOY_SERVER'),
'staging' => env('STAGING_DEPLOY_USER').'@'.env('STAGING_DEPLOY_SERVER'),
])

@task('rollback', ['on' => 'prod', 'confirm' => true])
{{ logMessage("Rolling back...") }}
cd {{ $releaseDir }}
ln -nfs {{ $releaseDir }}/$(find . -maxdepth 1 -name "20*" | sort  | tail -n 2 | head -n1) {{ $baseDir }}/current
{{ logMessage("Rolled back!") }}

{{ logMessage("Rebuilding cache") }}
php {{ $currentDir }}/artisan route:cache

php {{ $currentDir }}/artisan config:cache

php {{ $currentDir }}/artisan view:cache
{{ logMessage("Rebuilding cache completed") }}

echo "Rolled back to $(find . -maxdepth 1 -name "20*" | sort  | tail -n 2 | head -n1)"
@endtask

@task('init', ['on' => 'prod', 'confirm' => true])
if [ ! -d {{ $baseDir }}/current ]; then
cd {{ $baseDir }}

git clone {{ $repo }} --branch={{ $branch }} --depth=1 -q {{ $release }}
{{ logMessage("Repository cloned") }}

mv {{ $release }}/storage {{ $baseDir }}/storage
ln -nfs {{ $baseDir }}/storage {{ $release }}/storage
ln -nfs {{ $baseDir }}/storage/public {{ $release }}/public/storage
{{ logMessage("Storage directory set up") }}

cp {{ $release }}/.env.example {{ $baseDir }}/.env
ln -nfs {{ $baseDir }}/.env {{ $release }}/.env
{{ logMessage("Environment file set up") }}

sudo chown -R {{ $user }}:www-data {{ $baseDir }}/storage
sudo chmod -R ug+rwx {{ $baseDir }}/storage

rm -rf {{ $release }}
{{ logMessage("Deployment path initialised. Run 'envoy run deploy' now.") }}
else
{{ logMessage("Deployment path already initialised (current symlink exists)!") }}
fi
@endtask

@story('deploy', ['on' => 'prod'])
git
composer
npm_install
update_symlinks
migrate_release
set_permissions
reload_services
cache
npm_run_prod
clean_old_releases
@endstory

@task('git')
{{ logMessage("Cloning repository") }}

git clone {{ $repo }} --branch={{ $branch }} --depth=1 -q {{ $currentReleaseDir }}
@endtask

@task('composer')
{{ logMessage("Running composer") }}

cd {{ $currentReleaseDir }}

composer install --no-interaction --quiet --prefer-dist --optimize-autoloader
@endtask

@task('npm_install')
{{ logMessage("NPM install") }}

cd {{ $currentReleaseDir }}
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"  # This loads nvm
[ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"  # This loads nvm bash_completion

npm install --silent --no-progress > /dev/null
@endtask

@task('npm_run_prod')
{{ logMessage("NPM run prod") }}

cd {{ $currentReleaseDir }}
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"  # This loads nvm
[ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"  # This loads nvm bash_completion

NODE_OPTIONS="--max-old-space-size=4096" npm run production

{{ logMessage("Deleting node_modules folder") }}
rm -rf node_modules
@endtask

@task('update_symlinks')
{{ logMessage("Updating symlinks") }}

# Remove the storage directory and replace with persistent data
{{ logMessage("Linking storage directory") }}
mkdir -p {{ $baseDir }}/storage
cp -n -r {{ $currentReleaseDir }}/storage/* {{ $baseDir }}/storage
rm -rf {{ $currentReleaseDir }}/storage;
cd {{ $currentReleaseDir }};
mkdir -p {{ $baseDir }}/storage/app/public
ln -nfs {{ $baseDir }}/storage {{ $currentReleaseDir }}/storage;
ln -nfs {{ $baseDir }}/storage/app/public {{ $currentReleaseDir }}/public/storage

# Remove the public uploads directory and replace with persistent data
#    {{ logMessage("Linking uploads directory") }}
#    rm -rf {{ $currentReleaseDir }}/public/uploads
#    cd {{ $currentReleaseDir }}/public
#    ln -nfs {{ $baseDir }}/uploads {{ $currentReleaseDir }}/uploads;

# Import the environment config
{{ logMessage("Linking .env file") }}
cp -n {{ $currentReleaseDir }}/.env.example {{ $baseDir }}/.env
cd {{ $currentReleaseDir }};
ln -nfs {{ $baseDir }}/.env .env;

# Symlink the latest release to the current directory
{{ logMessage("Linking current release") }}
ln -nfs {{ $currentReleaseDir }} {{ $currentDir }};
@endtask

@task('set_permissions')
# Set dir permissions
{{ logMessage("Set permissions") }}

sudo chown -R {{ $user }}:www-data {{ $baseDir }}
sudo chmod -R ug+rwx {{ $baseDir }}/storage
cd {{ $baseDir }}
sudo chown -R {{ $user }}:www-data current
sudo chmod -R ug+rwx current/storage current/bootstrap/cache
sudo chown -R {{ $user }}:www-data {{ $currentReleaseDir }}
@endtask

@task('cache')
{{ logMessage("Clearing cache") }}

php {{ $currentDir }}/artisan route:clear

php {{ $currentDir }}/artisan config:clear

php {{ $currentDir }}/artisan view:clear

php {{ $currentDir }}/artisan cache:clear

php {{ $currentDir }}/artisan route:cache

php {{ $currentDir }}/artisan config:cache

php {{ $currentDir }}/artisan view:cache
@endtask

@task('clean_old_releases')
# Delete all but the 5 most recent releases
{{ logMessage("Cleaning old releases") }}
cd {{ $releaseDir }}
ls -dt {{ $releaseDir }}/* | tail -n +6 | xargs -d "\n" rm -rf;
@endtask

@task('migrate_release', ['on' => 'prod', 'confirm' => false])
{{ logMessage("Running migrations") }}

php {{ $currentReleaseDir }}/artisan migrate --force
@endtask

@task('migrate', ['on' => 'prod', 'confirm' => true])
{{ logMessage("Running migrations") }}

php {{ $currentDir }}/artisan migrate --force
@endtask

@task('migrate_rollback', ['on' => 'prod', 'confirm' => true])
{{ logMessage("Rolling back migrations") }}

php {{ $currentDir }}/artisan migrate:rollback --force
@endtask

@task('migrate_status', ['on' => 'prod'])
php {{ $currentDir }}/artisan migrate:status
@endtask

@task('reload_services', ['on' => 'prod'])
# Reload Services
{{ logMessage("Restarting service supervisor") }}
sudo supervisorctl restart all

{{ logMessage("Reloading php") }}
sudo systemctl reload apache2
@endtask


@finished
echo "Envoy deployment script finished.\r\n";
@endfinished



@setup
$stagingRemoteDir = env('STAGING_DEPLOY_BASE_DIR', '/public_html');
$remote = env('STAGING_DEPLOY_USER')."@". env('STAGING_DEPLOY_SERVER');
$localDir = __DIR__;
@endsetup

@task('rsync_staging', ['on' => 'localhost'])
echo "🔄 Deploy lên staging bằng rsync siêu tốc!"
echo "Local: {{ $localDir }}"
echo "Remote: {{ $remote }}:{{ $stagingRemoteDir }}"
rsync -az --exclude=".git" --exclude="node_modules" --exclude="storage" --exclude=".env" {{ $localDir }}/ {{ $remote }}:{{ $stagingRemoteDir }}
@endtask

@task('post_rsync_staging', ['on' => 'staging'])
echo "🔧 Update symlink, migrate, cache các kiểu trên staging"
cd {{ $stagingRemoteDir }}
php artisan migrate --force
php artisan config:cache
php artisan route:cache
php artisan view:cache
@endtask

@macro('deploy_staging_rsync')
rsync_staging
post_rsync_staging
@endmacro
