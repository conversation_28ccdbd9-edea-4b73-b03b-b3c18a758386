<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('grades', function (Blueprint $table) {
            $table->id();
            $table->integer('class_id');
            $table->integer('student_id');
            $table->integer('session');
            $table->string('type');
            $table->enum('attendance',['X','O','Late','N/A'])->nullable();
            $table->enum('participation',['A+','A','A-','B+','B','B-','C+','C','Fp','N/A'])->nullable();
            $table->string('score')->nullable();
            $table->string('score1')->nullable();
            $table->string('score2')->nullable();
            $table->string('vocab')->nullable();
            $table->string('vocab1')->nullable();
            $table->string('vocab2')->nullable();
            $table->string('concept')->nullable();
            $table->string('concept1')->nullable();
            $table->string('concept2')->nullable();
            $table->string('skill')->nullable();
            $table->string('skill1')->nullable();
            $table->string('skill2')->nullable();
            $table->string('sentence')->nullable();
            $table->string('sentence1')->nullable();
            $table->string('sentence2')->nullable();
            $table->string('vocabulary_maxtrix_1')->nullable();
            $table->string('vocabulary_maxtrix_11')->nullable();
            $table->string('vocabulary_maxtrix_12')->nullable();
            $table->string('vocabulary_maxtrix_2')->nullable();
            $table->string('vocabulary_maxtrix_21')->nullable();
            $table->string('vocabulary_maxtrix_22')->nullable();
            $table->string('vocab_hw')->nullable();
            $table->string('vocab_hw1')->nullable();
            $table->string('vocab_hw2')->nullable();
            $table->enum('short_response',['A+','A','A-','B+','B','B-','C+','C','Fp','Fm','N/A'])->nullable();
            $table->text('description')->nullable();
            $table->enum('status',['Active','Inactive'])->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('grades');
    }
};
