<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  public function up(): void
  {
    Schema::create('student_vocabularies', function (Blueprint $table) {
      $table->id();
      $table->unsignedBigInteger('account_id');
      $table->unsignedBigInteger('vocabulary_id');
      $table->unsignedTinyInteger('bucket')->default(1);
      $table->timestamp('last_reviewed_at')->nullable();
      $table->timestamp('next_review_at')->nullable();
      $table->unsignedTinyInteger('last_homework_score')->nullable();
      $table->unsignedBigInteger('class_id')->nullable();
      $table->unsignedBigInteger('session')->nullable();
      $table->timestamps();

      $table->foreign('account_id')->references('id')->on('accounts')->onDelete('cascade');
      $table->foreign('vocabulary_id')->references('id')->on('vocabularies')->onDelete('cascade');
      $table->foreign('class_id')->references('id')->on('classes')->onDelete('set null');
    });
  }

  public function down(): void
  {
    Schema::dropIfExists('student_vocabularies');
  }
};
