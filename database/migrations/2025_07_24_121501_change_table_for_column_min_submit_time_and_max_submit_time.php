<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    Schema::table('account_homework', function (Blueprint $table) {
      $table->dropColumn(['min_submit_time', 'max_submit_time']);
    });
    Schema::table('task_homework', function (Blueprint $table) {
      $table->integer('min_submit_time');
      $table->integer('max_submit_time');
    });
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    Schema::table('account_homework', function (Blueprint $table) {
      $table->integer('min_submit_time');
      $table->integer('max_submit_time');
    });
    Schema::table('task_homework', function (Blueprint $table) {
      $table->dropColumn(['min_submit_time', 'max_submit_time']);
    });
  }
};
