<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    Schema::table('account_task_answers', function (Blueprint $table) {
      $table
        ->text('answer')
        ->nullable()
        ->change();
    });
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    Schema::table('account_task_answers', function (Blueprint $table) {
      $table
        ->string('answer', 255)
        ->nullable()
        ->change();
    });
  }
};
