<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('homeworks', function (Blueprint $table) {
            $table->id(); 
            $table->string('title'); 
            $table->string('description');
            $table->unsignedBigInteger('main_homework_id'); 

            $table->integer('type');
            $table->integer('class');
            $table->integer('session');
            $table->integer('order');

            $table->timestamps();

            $table->foreign('main_homework_id')
                ->references('id')
                ->on('main_homeworks')
                ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('homeworks');
    }
};

