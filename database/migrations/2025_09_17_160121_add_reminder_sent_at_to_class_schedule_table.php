<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('class_schedule', function (Blueprint $table) {
            $table->timestamp('first_reminder_sent_at')->nullable()->after('date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('class_schedule', function (Blueprint $table) {
            $table->dropColumn('first_reminder_sent_at');
        });
    }
};
