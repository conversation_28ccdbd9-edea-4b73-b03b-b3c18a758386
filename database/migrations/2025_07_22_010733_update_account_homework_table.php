
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    Schema::table('account_homework', function (Blueprint $table) {
        $table->unsignedBigInteger('account_main_homework_id');
        $table->foreign('account_main_homework_id')->references('id')->on('account_main_homework')->onDelete('cascade');
    });
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    Schema::table('account_homework', function (Blueprint $table) {
        $table->dropForeign(['account_main_homework_id']);
        $table->dropColumn('account_main_homework_id');
    });
  }
};
