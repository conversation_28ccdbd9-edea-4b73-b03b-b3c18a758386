// dangerfile.mjs — Full checklist (pass + fail), only changed files, split notes by GitLab API
import fs from "fs";
import path from "path";
import yamlPkg from "js-yaml";
const yaml = yamlPkg;

// Danger API có sẵn trên globalThis
const { danger, fail, warn } = globalThis;

// ===== Load checklist
const checklistPath = path.join(process.cwd(), ".danger", "checklist.yml");
const doc = yaml.load(fs.readFileSync(checklistPath, "utf8")) || {};
const title = doc?.meta?.title || "Checklist";
const failOn = new Set(doc?.meta?.fail_on || ["must"]);

// ===== Only CHANGED files (no repo fallback)
const changedFiles = Array.from(
  new Set([...(danger.git.modified_files || []), ...(danger.git.created_files || [])])
);

// ===== Limits (để tránh quá dài nhưng vẫn đủ thông tin)
const MAX_HITS_PER_RULE = Number(process.env.DANGER_MAX_HITS_PER_RULE || 10);
const MAX_SNIPPET_LEN = Number(process.env.DANGER_MAX_SNIPPET_LEN || 160);
const MAX_NOTE_CHARS = Number(process.env.DANGER_MAX_NOTE_CHARS || 30000); // ~30k / note

// ===== Regex helpers: hỗ trợ (?i)(?m)(?s)(?u) đầu pattern
const VALID_FLAGS = new Set(["g", "i", "m", "s", "u", "y"]);
const parseInlineFlags = (p) => {
  const m = p?.match?.(/^\(\?([imsuy]+)\)/);
  return m ? { src: p.slice(m[0].length), flags: m[1] } : { src: p, flags: "" };
};
const uniqFlags = (f) => Array.from(new Set(f.split("").filter((c) => VALID_FLAGS.has(c)))).join("");
const makeRegs = (patterns = []) =>
  patterns.map((p) => {
    const { src, flags } = parseInlineFlags(p);
    return new RegExp(src, uniqFlags(flags || ""));
  });
const makeRegsForIncludes = (patterns = []) =>
  patterns.map((p) => {
    const { src, flags } = parseInlineFlags(p);
    const keep = flags.replace(/[^i]/g, ""); // file path chỉ cần i
    return new RegExp(src, uniqFlags(keep));
  });

// ===== Utils
const filterByInclude = (files, include) => {
  if (!include || include.length === 0) return files;
  const regs = makeRegsForIncludes(include);
  return files.filter((f) => regs.some((r) => r.test(f)));
};

function truncate(s, max) {
  if (!s) return s;
  return s.length <= max ? s : s.slice(0, max - 1) + "…";
}

function grepInGivenFiles(files, patterns) {
  const regs = makeRegs(patterns);
  const hits = [];
  for (const f of files) {
    try {
      const lines = fs.readFileSync(f, "utf8").split("\n");
      for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        if (regs.some((r) => r.test(line))) {
          hits.push({ file: f, line: i + 1, snippet: truncate(line.trim(), MAX_SNIPPET_LEN) });
          if (hits.length >= MAX_HITS_PER_RULE) return hits;
        }
      }
    } catch { /* file có thể bị xóa / binary */ }
  }
  return hits;
}

function anyFileMatches(files, patterns) {
  const regs = makeRegsForIncludes(patterns);
  return files.some((f) => regs.some((r) => r.test(f)));
}

// ===== Evaluate all rules (ONLY changed files)
const mr = danger.gitlab?.mr || {};
const results = [];

for (const r of doc.rules || []) {
  let ok = true;
  let note = "";

  const filesInScope = filterByInclude(changedFiles, r.include);

  switch (r.type) {
    case "mr_description_required": {
      ok = !!mr.description && mr.description.trim().length >= 20;
      if (!ok) note = "MR description thiếu hoặc quá ngắn (≥ 20 ký tự).";
      break;
    }
    case "files_changed_require": {
      // Chỉ check trong files thay đổi của MR
      ok = anyFileMatches(filesInScope, r.patterns_any || []);
      if (!ok) note = "Không tìm thấy tệp bắt buộc trong các file thay đổi.";
      break;
    }
    case "forbid_pattern": {
      const hits = grepInGivenFiles(filesInScope, r.patterns || []);
      ok = hits.length === 0;
      if (!ok) {
        note =
          hits.map((h) => `• ${h.file}:${h.line} → \`${h.snippet}\``).join("\n") +
          (hits.length >= MAX_HITS_PER_RULE ? `\n…(đã rút gọn hiển thị)` : "");
      }
      break;
    }
    case "require_pattern": {
      // Chính sách fail khi không tìm thấy mẫu yêu cầu
      // Ưu tiên theo rule: r.require_fail_policy ∈ "fail" | "warn" | "ignore"
      // Hoặc meta mặc định: doc.meta?.default_require_fail_policy
      const policy =
        (r.require_fail_policy || doc?.meta?.default_require_fail_policy || "fail").toLowerCase();

      // Chỉ check trong file thay đổi thuộc include
      const filesInScope = filterByInclude(changedFiles, r.include);

      // Nếu MR không chạm file thuộc phạm vi include → BỎ QUA (Pass)
      if (!filesInScope || filesInScope.length === 0) {
        ok = true;
        note = "Bỏ qua vì MR không thay đổi file trong phạm vi kiểm tra.";
        break;
      }

      const hits = grepInGivenFiles(filesInScope, r.patterns || []);
      ok = hits.length > 0;

      if (!ok) {
        // Không tìm thấy mẫu yêu cầu trong file đã đổi
        note = "Không tìm thấy mẫu yêu cầu trong các file thay đổi.";

        if (policy === "ignore") {
          ok = true; // pass
        } else if (policy === "warn") {
          // pass nhưng thêm cảnh báo nhẹ bằng cách gắn level 'should' tạm thời
          // (không đổi r.level thật, chỉ ảnh hưởng gate ở cuối)
          results.push({
            id: `${r.id || "require_pattern"}__policy_warn_shadow`,
            level: "should",
            ok: false,
            message: `[WARN] ${r.message || "Thiếu mẫu yêu cầu"}`,
            note,
          });
          ok = true; // rule chính vẫn pass
          note += " (đã chuyển thành cảnh báo theo policy=warn)";
        }
      } else {
        // Có match → ghi chi tiết vị trí (rút gọn)
        note =
          hits
            .map((h) => `• ${h.file}:${h.line} → \`${h.snippet}\``)
            .join("\n") + (hits.length >= MAX_HITS_PER_RULE ? `\n…(đã rút gọn hiển thị)` : "");
      }
      break;
    }
    default:
      // type không hỗ trợ → coi như pass để tránh âm tính giả
      ok = true;
  }

  results.push({
    id: r.id,
    level: r.level || "should",
    ok,
    message: r.message || r.id || "",
    note
  });
}

// ===== Grouping
const failedMust = results.filter((x) => x.level === "must" && !x.ok);
const failedShould = results.filter((x) => x.level === "should" && !x.ok);
const passed = results.filter((x) => x.ok);

// ===== Compose markdown blocks
const summary =
  `**Tổng kết (chỉ quét file thay đổi):** ` +
  `MUST fail: **${failedMust.length}**, SHOULD cảnh báo: **${failedShould.length}**, ` +
  `Passed: **${passed.length}**.\n`;

const bullet = (x) =>
  `- ❌ **${x.message}** _(mức: ${x.level})_` + (x.note ? `\n  \n  ${x.note}` : "");

const secMust =
  failedMust.length
    ? `#### ❌ Lỗi bắt buộc (MUST)\n${failedMust.map(bullet).join("\n")}\n`
    : `#### ❌ Lỗi bắt buộc (MUST)\n- ✅ Không có mục nào.\n`;

const secShould =
  failedShould.length
    ? `#### ⚠️ Cảnh báo khuyến nghị (SHOULD)\n${failedShould.map(bullet).join("\n")}\n`
    : `#### ⚠️ Cảnh báo khuyến nghị (SHOULD)\n- ✅ Không có mục nào.\n`;

// Bảng FULL checklist (gồm cả Pass)
const tblHdr = `| Nội dung kiểm tra | Kết quả | Mức | Ghi chú |
|---|---|---|---|`;
const row = (x) =>
  `| ${x.message} | ${x.ok ? "✅ Pass" : "❌ Fail"} | ${x.level} | ${x.note || ""} |`;
const fullTable = [tblHdr, ...results.map(row)].join("\n");

// ===== Split into multiple notes via GitLab API
const projectId = process.env.CI_PROJECT_ID; // GitLab CI set sẵn (string or number)
const mrIid = danger.gitlab?.mr?.iid;        // internal iid của MR
const api = danger.gitlab?.api;

async function postNote(body) {
  if (!api || !projectId || !mrIid) {
    // Fallback: nếu vì lý do nào đó không có API, dùng markdown (sẽ dồn vào 1 note)
    return globalThis.markdown?.(body);
  }

  // Ưu tiên post discussion nếu bật cờ (cho gọn threads)
  if (process.env.DANGER_USE_DISCUSSION === "1" && api.MergeRequestDiscussions?.create) {
    return api.MergeRequestDiscussions.create(projectId, mrIid, { body });
  }

  // GitBeaker "đời mới": MergeRequestNotes.create(projectId, mrIid, body)
  if (api.MergeRequestNotes?.create) {
    return api.MergeRequestNotes.create(projectId, mrIid, body);
  }

  // Một số bản cũ: MergeRequests.createNote(projectId, mrIid, body)
  if (api.MergeRequests?.createNote) {
    return api.MergeRequests.createNote(projectId, mrIid, body);
  }

  // Nếu không có các service trên, fallback về markdown()
  return globalThis.markdown?.(body);
}

function chunkBySize(text, max) {
  const chunks = [];
  let i = 0;
  while (i < text.length) {
    chunks.push(text.slice(i, i + max));
    i += max;
  }
  return chunks;
}

// 1) Summary (ngắn)
await postNote(`### ${title}\n\n${summary}`);

// 2) MUST (chia nhỏ nếu dài)
for (const chunk of chunkBySize(secMust, MAX_NOTE_CHARS)) {
  await postNote(chunk);
}

// 3) SHOULD (chia nhỏ nếu dài)
for (const chunk of chunkBySize(secShould, MAX_NOTE_CHARS)) {
  await postNote(chunk);
}

// 4) FULL TABLE (tách nhỏ an toàn)
const tableHeader = `### Toàn bộ Checklist (Pass/Fail)\n\n`;
const tableChunks = chunkBySize(fullTable, MAX_NOTE_CHARS - tableHeader.length);
if (tableChunks.length > 0) {
  // note đầu có header
  await postNote(tableHeader + tableChunks[0]);
  for (let i = 1; i < tableChunks.length; i++) {
    await postNote(tableChunks[i]);
  }
}

// ===== Gate theo policy
if (failOn.has("must") && failedMust.length) {
  fail(`Có ${failedMust.length} mục **MUST** bị fail.`);
}
if (failOn.has("should") && failedShould.length) {
  warn(`Có ${failedShould.length} mục **SHOULD** chưa đạt.`);
}
