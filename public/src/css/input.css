@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Manrope', sans-serif;
    scroll-behavior: smooth;
  }

  .container {
    max-width: unset !important;
  }

  body {
    font-family: "Poppins", sans-serif;
    font-weight: 400;
    color: #252931;
    overflow-x: hidden;
  }

  .text-57-64 {
    @apply text-[57px] leading-[64px];
  }

  .text-36-44 {
    @apply text-[64px] leading-[44px];
  }

  .text-45-52 {
    @apply text-[57px] leading-[52px];
  }

  .text-32-40 {
    @apply text-[32px] leading-[44px];
  }

  .text-28-36 {
    @apply text-[28px] leading-9
  }

  .text-24-32 {
    @apply text-2xl;
  }
  .text-20-26 {
    @apply text-[20px] leading-[26px];
  }
  .text-16-24 {
    @apply text-base;
  }
  .text-14-20 {
    @apply text-sm;
  }
  .text-12-16 {
    @apply text-xs;
  }
  .text-11-16 {
    @apply text-[11px] leading-4;
  }
}

svg {
  width: 100%;
  height: 100%;
}

.shadow-424D5B14 {
  box-shadow: 4px 0 30px 0 #424D5B14;
}

.shadow-323F6D0D {
  box-shadow: 0 4px 20px 0 #323F6D0D;
}

.rotate-180 {
  --tw-rotate: 180deg;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.box__select {
  -webkit-appearance: none;
  -moz-appearance: none;
}

.box__select option {
  width: 100%;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
  display: none;
}

.select2-results__option {
  padding: 0.5rem 1rem;
  color: #788393;
}

.select2-container--default .select2-results__option--highlighted[aria-selected],
.select2-container--default .select2-results__option[aria-selected=true] {
  background-color: #E8F9F3;
  color: #10C285;
  font-weight: 500;
}

.select2-results__option[aria-selected] {
  font-size: 14px;
  line-height: 20px;
}

.select2-results {
  box-shadow: 0px 10px 20px 0px rgba(0, 4, 98, 0.07);
}

.select2-container--default.select2-container--open.select2-container--below .select2-selection--single {
  border: 1px solid transparent;
  box-shadow: 0px 10px 20px 0px #00046212;
}

.select2-container--default .select2-selection--single {
  padding: 0.5rem 1rem;
  --tw-bg-opacity: 1;
  border: 1px solid #E2E4EB;
  border-radius : 8px;
  height: fit-content;
}

.select2-container .select2-selection--single .select2-selection__rendered {
  padding: 0;
  min-width: 75px;
}

.select2-container--open .select2-dropdown--below {
  border: none;
  top: -2px;
}

.select2-container--default .select2-results__option--highlighted[aria-selected]:last-child,
.select2-container--default .select2-results__option[aria-selected=true]:last-child {
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}

.feedback .select2-container--default .select2-selection--single {
  position: relative;
}

.feedback .select2-container--default .select2-selection--single::after {
  content: '';
  position: absolute;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 4px solid #ABB3BF;
  width: 0;
  height: 0;
  top: 17px;
  right: 21px;
}

.feedback .select2-container--default .select2-selection--single .select2-selection__rendered {
  font-size: 0.875rem;
  line-height: 1.25rem;
  color: #252931;
}

.message .select2-container--default .select2-selection--single {
  border: none;
}

.message .select2-container .select2-selection--single .select2-selection__rendered {
  font-weight: 600;
  color: #0F1A30;
  font-size: 16px;
  line-height: 24px;
}

.message .select2-results__option {
  padding: 12px 16px;
}

.message .select2-container--default .select2-results>.select2-results__options {
  border-radius: 8px;
  background-color: white;
}

.message .select2-container {
  width: 100% !important;
}

.text-dot::after {
  content: '';
  position: absolute;
  top: 8px;
  left: -14px;
  width: 4px;
  height: 4px;
  background-color: #000;
  border-radius: 100%;
}

.message .select2-container--default.select2-container--open.select2-container--below .select2-selection--single {
  border: none;
  box-shadow: none;
}

.message .select2-container--default .select2-selection--single {
  padding: 0;
}

.select2-container--open .select2-dropdown--below.message__select {
  top: 24px;
  border-radius: 8px;
}

.message__select .select2-results__option--highlighted[aria-selected]:first-of-type,
.message__select .select2-results__option[aria-selected=true]:first-of-type {
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

.message__text--ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 208px;
}

.note-massage {
  list-style: auto;
}

.note-massage li {
  word-break: break-all;
}

.ndot-note {
  list-style: auto;
}
@media (min-width: 992px) {
  aside {
    width: 200px;
  }

  aside.aside-small {
    width: 72px;
  }

  .box__content--main {
    width: calc(100% - 200px);
    transform: translateX(200px);
  }

  .box__content--main.aside-small {
    width: calc(100% - 72px);
    transform: translateX(72px);
  }

  .feedback .select2-container--default .select2-selection--single {
    padding: 9px 35px 9px 15px;
  }

  .feedback .select2-container .select2-selection--single .select2-selection__rendered {
    min-width: 108px;
  }
}

@media (min-width: 1800px) {
  aside {
    width: 240px;
  }

  .box__content--main {
    width: calc(100% - 240px);
    transform: translateX(240px);
  }
}

input {
  outline: none;
}

/*common slide*/
.swiper {
  width: 100%;
  height: 100%;
}

.swiper-slide {
  text-align: center;
  font-size: 18px;
  background: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
}

.swiper-slide img {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: cover;
}
/*end common slide*/

/*radio custom*/
.radio__custom {
  position: relative;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Hide the browser's default radio button */
.radio__custom input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}

/* Create a custom radio button */
.radio__custom .checkmark {
  position: absolute;
  top: 8px;
  left: 8px;
  height: 16px;
  width: 16px;
  background-color: #fff;
  border: 1px solid #E2E4EB;
  border-radius: 50%;
}

/* On mouse-over, add a grey background color */
.radio__custom:hover input ~ .checkmark {
  background-color: #ccc;
}

/* When the radio button is checked, add a blue background */
.radio__custom input:checked ~ .checkmark {
  background-color: #fff;
  border: 1px solid #10C285;
}

/* Create the indicator (the dot/circle - hidden when not checked) */
.radio__custom .checkmark:after {
  content: "";
  position: absolute;
  display: none;
}

/* Show the indicator (dot/circle) when checked */
.radio__custom input:checked ~ .checkmark:after {
  display: block;
}

/* Style the indicator (dot/circle) */
.radio__custom .checkmark:after {
  top: 3px;
  left: 3px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #10C285;
}

.radio__custom.active {
  border-color: #10C285;
  background: #E8F9F3;
}

.radio__custom.active .text-neutral-400 {
  color: #373E49;
}
/*end radio custom*/
