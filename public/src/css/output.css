/*
! tailwindcss v3.4.1 | MIT License | https://tailwindcss.com
*/

/*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box;
  /* 1 */
  border-width: 0;
  /* 2 */
  border-style: solid;
  /* 2 */
  border-color: currentColor;
  /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5;
  /* 1 */
  -webkit-text-size-adjust: 100%;
  /* 2 */
  -moz-tab-size: 4;
  /* 3 */
  -o-tab-size: 4;
  tab-size: 4;
  /* 3 */
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  /* 4 */
  -webkit-font-feature-settings: normal;
  font-feature-settings: normal;
  /* 5 */
  font-variation-settings: normal;
  /* 6 */
  -webkit-tap-highlight-color: transparent;
  /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0;
  /* 1 */
  line-height: inherit;
  /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0;
  /* 1 */
  color: inherit;
  /* 2 */
  border-top-width: 1px;
  /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
  text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  /* 1 */
  -webkit-font-feature-settings: normal;
  font-feature-settings: normal;
  /* 2 */
  font-variation-settings: normal;
  /* 3 */
  font-size: 1em;
  /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0;
  /* 1 */
  border-color: inherit;
  /* 2 */
  border-collapse: collapse;
  /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit;
  /* 1 */
  -webkit-font-feature-settings: inherit;
  font-feature-settings: inherit;
  /* 1 */
  font-variation-settings: inherit;
  /* 1 */
  font-size: 100%;
  /* 1 */
  font-weight: inherit;
  /* 1 */
  line-height: inherit;
  /* 1 */
  color: inherit;
  /* 1 */
  margin: 0;
  /* 2 */
  padding: 0;
  /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
[type='button'],
[type='reset'],
[type='submit'] {
  -webkit-appearance: button;
  /* 1 */
  background-color: transparent;
  /* 2 */
  background-image: none;
  /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield;
  /* 1 */
  outline-offset: -2px;
  /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button;
  /* 1 */
  font: inherit;
  /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/

dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-webkit-input-placeholder, textarea::-webkit-input-placeholder {
  opacity: 1;
  /* 1 */
  color: #9ca3af;
  /* 2 */
}

input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1;
  /* 1 */
  color: #9ca3af;
  /* 2 */
}

input:-ms-input-placeholder, textarea:-ms-input-placeholder {
  opacity: 1;
  /* 1 */
  color: #9ca3af;
  /* 2 */
}

input::-ms-input-placeholder, textarea::-ms-input-placeholder {
  opacity: 1;
  /* 1 */
  color: #9ca3af;
  /* 2 */
}

input::placeholder,
textarea::placeholder {
  opacity: 1;
  /* 1 */
  color: #9ca3af;
  /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/

:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block;
  /* 1 */
  vertical-align: middle;
  /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */

[hidden] {
  display: none;
}

html {
  font-family: 'Manrope', sans-serif;
  scroll-behavior: smooth;
}

.container {
  max-width: unset !important;
}

body {
  font-family: "Poppins", sans-serif;
  font-weight: 400;
  color: #252931;
  overflow-x: hidden;
}

.text-24-32 {
  font-size: 1.5rem;
  line-height: 2rem;
}

.text-20-26 {
  font-size: 20px;
  line-height: 26px;
}

.text-16-24 {
  font-size: 1rem;
  line-height: 1.5rem;
}

.text-14-20 {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.text-12-16 {
  font-size: 0.75rem;
  line-height: 1rem;
}

*, ::before, ::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
}

::-webkit-backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
}

.container {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: 16px;
  padding-left: 16px;
}

@media (min-width: 640px) {
  .container {
    max-width: 640px;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  .container {
    max-width: 1280px;
    padding-right: 60px;
    padding-left: 60px;
  }
}

@media (min-width: 1536px) {
  .container {
    max-width: 1536px;
  }
}

.invisible {
  visibility: hidden;
}

.fixed {
  position: fixed;
}

.absolute {
  position: absolute;
}

.relative {
  position: relative;
}

.inset-0 {
  inset: 0px;
}

.-right-4 {
  right: -1rem;
}

.bottom-0 {
  bottom: 0px;
}

.bottom-\[-19px\] {
  bottom: -19px;
}

.left-0 {
  left: 0px;
}

.right-0 {
  right: 0px;
}

.top-0 {
  top: 0px;
}

.top-2 {
  top: 0.5rem;
}

.top-\[38px\] {
  top: 38px;
}

.z-40 {
  z-index: 40;
}

.z-50 {
  z-index: 50;
}

.z-\[51\] {
  z-index: 51;
}

.z-\[52\] {
  z-index: 52;
}

.m-auto {
  margin: auto;
}

.mx-2 {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.my-2 {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

.my-2\.5 {
  margin-top: 0.625rem;
  margin-bottom: 0.625rem;
}

.mb-1 {
  margin-bottom: 0.25rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mb-8 {
  margin-bottom: 2rem;
}

.ml-5 {
  margin-left: 1.25rem;
}

.ml-6 {
  margin-left: 1.5rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.block {
  display: block;
}

.flex {
  display: -ms-flexbox;
  display: flex;
}

.inline-flex {
  display: -ms-inline-flexbox;
  display: inline-flex;
}

.hidden {
  display: none;
}

.size-8 {
  width: 2rem;
  height: 2rem;
}

.h-1 {
  height: 0.25rem;
}

.h-10 {
  height: 2.5rem;
}

.h-4 {
  height: 1rem;
}

.h-6 {
  height: 1.5rem;
}

.h-7 {
  height: 1.75rem;
}

.h-\[2px\] {
  height: 2px;
}

.h-\[37px\] {
  height: 37px;
}

.h-\[56px\] {
  height: 56px;
}

.h-full {
  height: 100%;
}

.h-screen {
  height: 100vh;
}

.min-h-\[calc\(100vh-69px\)\] {
  min-height: calc(100vh - 69px);
}

.min-h-screen {
  min-height: 100vh;
}

.w-1 {
  width: 0.25rem;
}

.w-10 {
  width: 2.5rem;
}

.w-4 {
  width: 1rem;
}

.w-6 {
  width: 1.5rem;
}

.w-\[122px\] {
  width: 122px;
}

.w-\[56px\] {
  width: 56px;
}

.w-full {
  width: 100%;
}

.max-w-\[1120px\] {
  max-width: 1120px;
}

.max-w-\[1432px\] {
  max-width: 1432px;
}

.max-w-\[47\.5rem\] {
  max-width: 47.5rem;
}

.max-w-max {
  max-width: -webkit-max-content;
  max-width: -moz-max-content;
  max-width: max-content;
}

.flex-1 {
  -ms-flex: 1 1 0%;
  flex: 1 1 0%;
}

.flex-shrink-0 {
  -ms-flex-negative: 0;
  flex-shrink: 0;
}

.-translate-x-full {
  --tw-translate-x: -100%;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.transform {
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.cursor-pointer {
  cursor: pointer;
}

.flex-col {
  -ms-flex-direction: column;
  flex-direction: column;
}

.items-start {
  -ms-flex-align: start;
  align-items: flex-start;
}

.items-center {
  -ms-flex-align: center;
  align-items: center;
}

.justify-start {
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.justify-end {
  -ms-flex-pack: end;
  justify-content: flex-end;
}

.justify-center {
  -ms-flex-pack: center;
  justify-content: center;
}

.justify-between {
  -ms-flex-pack: justify;
  justify-content: space-between;
}

.gap-1 {
  gap: 0.25rem;
}

.gap-3 {
  gap: 0.75rem;
}

.gap-4 {
  gap: 1rem;
}

.gap-\[8px\] {
  gap: 8px;
}

.gap-x-2 {
  -webkit-column-gap: 0.5rem;
  -moz-column-gap: 0.5rem;
  column-gap: 0.5rem;
}

.gap-x-\[10px\] {
  -webkit-column-gap: 10px;
  -moz-column-gap: 10px;
  column-gap: 10px;
}

.gap-y-2 {
  row-gap: 0.5rem;
}

.gap-y-4 {
  row-gap: 1rem;
}

.gap-y-6 {
  row-gap: 1.5rem;
}

.gap-y-\[10px\] {
  row-gap: 10px;
}

.overflow-y-auto {
  overflow-y: auto;
}

.break-all {
  word-break: break-all;
}

.rounded {
  border-radius: 0.25rem;
}

.rounded-full {
  border-radius: 9999px;
}

.rounded-lg {
  border-radius: 0.5rem;
}

.rounded-b-lg {
  border-bottom-right-radius: 0.5rem;
  border-bottom-left-radius: 0.5rem;
}

.rounded-t-lg {
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
}

.border {
  border-width: 1px;
}

.border-b {
  border-bottom-width: 1px;
}

.border-l {
  border-left-width: 1px;
}

.border-l-4 {
  border-left-width: 4px;
}

.border-r {
  border-right-width: 1px;
}

.border-t {
  border-top-width: 1px;
}

.border-\[\#203459\] {
  --tw-border-opacity: 1;
  border-color: rgb(32 52 89 / var(--tw-border-opacity));
}

.border-\[\#EEF0F6\] {
  --tw-border-opacity: 1;
  border-color: rgb(238 240 246 / var(--tw-border-opacity));
}

.border-green {
  --tw-border-opacity: 1;
  border-color: rgb(16 194 133 / var(--tw-border-opacity));
}

.border-greenLight {
  --tw-border-opacity: 1;
  border-color: rgb(232 249 243 / var(--tw-border-opacity));
}

.border-neutral-100 {
  --tw-border-opacity: 1;
  border-color: rgb(235 237 242 / var(--tw-border-opacity));
}

.border-neutral-200 {
  --tw-border-opacity: 1;
  border-color: rgb(226 228 235 / var(--tw-border-opacity));
}

.border-b-neutral-100 {
  --tw-border-opacity: 1;
  border-bottom-color: rgb(235 237 242 / var(--tw-border-opacity));
}

.border-r-neutral-100 {
  --tw-border-opacity: 1;
  border-right-color: rgb(235 237 242 / var(--tw-border-opacity));
}

.bg-\[\#203459\] {
  --tw-bg-opacity: 1;
  background-color: rgb(32 52 89 / var(--tw-bg-opacity));
}

.bg-black {
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity));
}

.bg-black\/\[0\.7\] {
  background-color: rgb(0 0 0 / 0.7);
}

.bg-dark {
  --tw-bg-opacity: 1;
  background-color: rgb(15 26 48 / var(--tw-bg-opacity));
}

.bg-green {
  --tw-bg-opacity: 1;
  background-color: rgb(16 194 133 / var(--tw-bg-opacity));
}

.bg-greenLight {
  --tw-bg-opacity: 1;
  background-color: rgb(232 249 243 / var(--tw-bg-opacity));
}

.bg-neutral-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(235 237 242 / var(--tw-bg-opacity));
}

.bg-neutral-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(226 228 235 / var(--tw-bg-opacity));
}

.bg-neutral-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(247 247 250 / var(--tw-bg-opacity));
}

.bg-neutral-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(37 41 49 / var(--tw-bg-opacity));
}

.bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.bg-\[url\(\'\/q1-academy-spr\/html\/src\/images\/image-bg-login\.jpg\'\)\] {
  background-image: url('/q1-academy-spr/html/src/images/image-bg-login.jpg');
}

.bg-cover {
  background-size: cover;
}

.object-cover {
  -o-object-fit: cover;
  object-fit: cover;
}

.p-2 {
  padding: 0.5rem;
}

.p-3 {
  padding: 0.75rem;
}

.p-4 {
  padding: 1rem;
}

.p-6 {
  padding: 1.5rem;
}

.px-1 {
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}

.px-1\.5 {
  padding-left: 0.375rem;
  padding-right: 0.375rem;
}

.px-10 {
  padding-left: 2.5rem;
  padding-right: 2.5rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.px-\[19px\] {
  padding-left: 19px;
  padding-right: 19px;
}

.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.py-1\.5 {
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}

.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.py-2\.5 {
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
}

.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}

.py-\[14px\] {
  padding-top: 14px;
  padding-bottom: 14px;
}

.py-\[22px\] {
  padding-top: 22px;
  padding-bottom: 22px;
}

.pb-10 {
  padding-bottom: 2.5rem;
}

.pb-2 {
  padding-bottom: 0.5rem;
}

.pb-4 {
  padding-bottom: 1rem;
}

.pb-5 {
  padding-bottom: 1.25rem;
}

.pl-6 {
  padding-left: 1.5rem;
}

.pt-\[57px\] {
  padding-top: 57px;
}

.pt-\[69px\] {
  padding-top: 69px;
}

.pt-\[80px\] {
  padding-top: 80px;
}

.text-center {
  text-align: center;
}

.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}

.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

.font-medium {
  font-weight: 500;
}

.font-normal {
  font-weight: 400;
}

.font-semibold {
  font-weight: 600;
}

.text-\[\#242834\] {
  --tw-text-opacity: 1;
  color: rgb(36 40 52 / var(--tw-text-opacity));
}

.text-\[\#494F5B\] {
  --tw-text-opacity: 1;
  color: rgb(73 79 91 / var(--tw-text-opacity));
}

.text-\[0F1A30\] {
  color: 0F1A30;
}

.text-dark {
  --tw-text-opacity: 1;
  color: rgb(15 26 48 / var(--tw-text-opacity));
}

.text-green {
  --tw-text-opacity: 1;
  color: rgb(16 194 133 / var(--tw-text-opacity));
}

.text-neutral-300 {
  --tw-text-opacity: 1;
  color: rgb(212 217 223 / var(--tw-text-opacity));
}

.text-neutral-400 {
  --tw-text-opacity: 1;
  color: rgb(171 179 191 / var(--tw-text-opacity));
}

.text-neutral-500 {
  --tw-text-opacity: 1;
  color: rgb(120 131 147 / var(--tw-text-opacity));
}

.text-neutral-600 {
  --tw-text-opacity: 1;
  color: rgb(87 95 107 / var(--tw-text-opacity));
}

.text-neutral-700 {
  --tw-text-opacity: 1;
  color: rgb(55 62 73 / var(--tw-text-opacity));
}

.text-neutral-800 {
  --tw-text-opacity: 1;
  color: rgb(37 41 49 / var(--tw-text-opacity));
}

.text-red {
  --tw-text-opacity: 1;
  color: rgb(237 25 65 / var(--tw-text-opacity));
}

.text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.opacity-0 {
  opacity: 0;
}

.transition {
  transition-property: color, background-color, border-color, fill, stroke, opacity, box-shadow, -webkit-text-decoration-color, -webkit-transform, -webkit-filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-text-decoration-color, -webkit-transform, -webkit-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

svg {
  width: 100%;
  height: 100%;
}

.shadow-424D5B14 {
  box-shadow: 4px 0 30px 0 #424D5B14;
}

.shadow-323F6D0D {
  box-shadow: 0 4px 20px 0 #323F6D0D;
}

.rotate-180 {
  --tw-rotate: 180deg;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.box__select {
  -webkit-appearance: none;
  -moz-appearance: none;
}

.box__select option {
  width: 100%;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
  display: none;
}

.select2-results__option {
  padding: 0.5rem 1rem;
  color: #788393;
}

.select2-container--default .select2-results__option--highlighted[aria-selected],
.select2-container--default .select2-results__option[aria-selected=true] {
  background-color: #E8F9F3;
  color: #10C285;
  font-weight: 500;
}

.select2-results__option[aria-selected] {
  font-size: 14px;
  line-height: 20px;
}

.select2-results {
  box-shadow: 0px 10px 20px 0px rgba(0, 4, 98, 0.07);
}

.select2-container--default.select2-container--open.select2-container--below .select2-selection--single {
  border: 1px solid transparent;
  box-shadow: 0px 10px 20px 0px #00046212;
}

.select2-container--default .select2-selection--single {
  padding: 0.5rem 1rem;
  --tw-bg-opacity: 1;
  border: 1px solid #E2E4EB;
  border-radius : 8px;
  height: -webkit-fit-content;
  height: -moz-fit-content;
  height: fit-content;
}

.select2-container .select2-selection--single .select2-selection__rendered {
  padding: 0;
  min-width: 75px;
}

.select2-container--open .select2-dropdown--below {
  border: none;
  top: -2px;
}

.select2-container--default .select2-results__option--highlighted[aria-selected]:last-child,
.select2-container--default .select2-results__option[aria-selected=true]:last-child {
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}

.feedback .select2-container--default .select2-selection--single {
  position: relative;
}

.feedback .select2-container--default .select2-selection--single::after {
  content: '';
  position: absolute;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 4px solid #ABB3BF;
  width: 0;
  height: 0;
  top: 17px;
  right: 21px;
}

.feedback .select2-container--default .select2-selection--single .select2-selection__rendered {
  font-size: 0.875rem;
  line-height: 1.25rem;
  color: #252931;
}

.message .select2-container--default .select2-selection--single {
  border: none;
}

.message .select2-container .select2-selection--single .select2-selection__rendered {
  font-weight: 600;
  color: #0F1A30;
  font-size: 16px;
  line-height: 24px;
}

.message .select2-results__option {
  padding: 12px 16px;
}

.message .select2-container--default .select2-results>.select2-results__options {
  border-radius: 8px;
  background-color: white;
}

.message .select2-container {
  width: 100% !important;
}

.text-dot::after {
  content: '';
  position: absolute;
  top: 8px;
  left: -14px;
  width: 4px;
  height: 4px;
  background-color: #000;
  border-radius: 100%;
}

.message .select2-container--default.select2-container--open.select2-container--below .select2-selection--single {
  border: none;
  box-shadow: none;
}

.message .select2-container--default .select2-selection--single {
  padding: 0;
}

.select2-container--open .select2-dropdown--below.message__select {
  top: 24px;
  border-radius: 8px;
}

.message__select .select2-results__option--highlighted[aria-selected]:first-of-type,
.message__select .select2-results__option[aria-selected=true]:first-of-type {
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

.message__text--ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 208px;
}

.note-massage {
  list-style: auto;
}

.note-massage li {
  word-break: break-all;
}

.ndot-note {
  list-style: auto;
}

@media (min-width: 992px) {
  aside {
    width: 200px;
  }

  aside.aside-small {
    width: 72px;
  }

  .box__content--main {
    width: calc(100% - 200px);
    -webkit-transform: translateX(200px);
    transform: translateX(200px);
  }

  .box__content--main.aside-small {
    width: calc(100% - 72px);
    -webkit-transform: translateX(72px);
    transform: translateX(72px);
  }

  .feedback .select2-container--default .select2-selection--single {
    padding: 9px 35px 9px 15px;
  }

  .feedback .select2-container .select2-selection--single .select2-selection__rendered {
    min-width: 108px;
  }
}

@media (min-width: 1800px) {
  aside {
    width: 240px;
  }

  .box__content--main {
    width: calc(100% - 240px);
    -webkit-transform: translateX(240px);
    transform: translateX(240px);
  }
}

input {
  outline: none;
}

/*common slide*/

.swiper {
  width: 100%;
  height: 100%;
}

.swiper-slide {
  text-align: center;
  font-size: 18px;
  background: #fff;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: center;
  justify-content: center;
  -ms-flex-align: center;
  align-items: center;
}

.swiper-slide img {
  display: block;
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
  object-fit: cover;
}

/*end common slide*/

/*radio custom*/

.radio__custom {
  position: relative;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Hide the browser's default radio button */

.radio__custom input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}

/* Create a custom radio button */

.radio__custom .checkmark {
  position: absolute;
  top: 8px;
  left: 8px;
  height: 16px;
  width: 16px;
  background-color: #fff;
  border: 1px solid #E2E4EB;
  border-radius: 50%;
}

/* On mouse-over, add a grey background color */

.radio__custom:hover input ~ .checkmark {
  background-color: #ccc;
}

/* When the radio button is checked, add a blue background */

.radio__custom input:checked ~ .checkmark {
  background-color: #fff;
  border: 1px solid #10C285;
}

/* Create the indicator (the dot/circle - hidden when not checked) */

.radio__custom .checkmark:after {
  content: "";
  position: absolute;
  display: none;
}

/* Show the indicator (dot/circle) when checked */

.radio__custom input:checked ~ .checkmark:after {
  display: block;
}

/* Style the indicator (dot/circle) */

.radio__custom .checkmark:after {
  top: 3px;
  left: 3px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #10C285;
}

.radio__custom.active {
  border-color: #10C285;
  background: #E8F9F3;
}

.radio__custom.active .text-neutral-400 {
  color: #373E49;
}

/*end radio custom*/

.placeholder\:text-neutral-400::-webkit-input-placeholder {
  --tw-text-opacity: 1;
  color: rgb(171 179 191 / var(--tw-text-opacity));
}

.placeholder\:text-neutral-400::-moz-placeholder {
  --tw-text-opacity: 1;
  color: rgb(171 179 191 / var(--tw-text-opacity));
}

.placeholder\:text-neutral-400:-ms-input-placeholder {
  --tw-text-opacity: 1;
  color: rgb(171 179 191 / var(--tw-text-opacity));
}

.placeholder\:text-neutral-400::-ms-input-placeholder {
  --tw-text-opacity: 1;
  color: rgb(171 179 191 / var(--tw-text-opacity));
}

.placeholder\:text-neutral-400::placeholder {
  --tw-text-opacity: 1;
  color: rgb(171 179 191 / var(--tw-text-opacity));
}

.before\:absolute::before {
  content: var(--tw-content);
  position: absolute;
}

.before\:left-0::before {
  content: var(--tw-content);
  left: 0px;
}

.before\:top-0::before {
  content: var(--tw-content);
  top: 0px;
}

.before\:block::before {
  content: var(--tw-content);
  display: block;
}

.before\:h-full::before {
  content: var(--tw-content);
  height: 100%;
}

.before\:w-\[1px\]::before {
  content: var(--tw-content);
  width: 1px;
}

.before\:bg-neutral-200::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(226 228 235 / var(--tw-bg-opacity));
}

.last\:mb-0:last-child {
  margin-bottom: 0px;
}

.hover\:bg-green:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(16 194 133 / var(--tw-bg-opacity));
}

.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.group:hover .group-hover\:fill-white {
  fill: #fff;
}

.group:hover .group-hover\:font-semibold {
  font-weight: 600;
}

.group:hover .group-hover\:text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

@media (min-width: 1024px) {
  .lg\:mx-0 {
    margin-left: 0px;
    margin-right: 0px;
  }

  .lg\:mb-\[10px\] {
    margin-bottom: 10px;
  }

  .lg\:block {
    display: block;
  }

  .lg\:flex {
    display: -ms-flexbox;
    display: flex;
  }

  .lg\:hidden {
    display: none;
  }

  .lg\:w-1\/2 {
    width: 50%;
  }

  .lg\:w-\[360px\] {
    width: 360px;
  }

  .lg\:w-\[400px\] {
    width: 400px;
  }

  .lg\:w-full {
    width: 100%;
  }

  .lg\:flex-1 {
    -ms-flex: 1 1 0%;
    flex: 1 1 0%;
  }

  .lg\:flex-row {
    -ms-flex-direction: row;
    flex-direction: row;
  }

  .lg\:flex-col {
    -ms-flex-direction: column;
    flex-direction: column;
  }

  .lg\:items-start {
    -ms-flex-align: start;
    align-items: flex-start;
  }

  .lg\:items-center {
    -ms-flex-align: center;
    align-items: center;
  }

  .lg\:justify-start {
    -ms-flex-pack: start;
    justify-content: flex-start;
  }

  .lg\:justify-center {
    -ms-flex-pack: center;
    justify-content: center;
  }

  .lg\:justify-between {
    -ms-flex-pack: justify;
    justify-content: space-between;
  }

  .lg\:gap-6 {
    gap: 1.5rem;
  }

  .lg\:gap-x-4 {
    -webkit-column-gap: 1rem;
    -moz-column-gap: 1rem;
    column-gap: 1rem;
  }

  .lg\:gap-y-8 {
    row-gap: 2rem;
  }

  .lg\:px-0 {
    padding-left: 0px;
    padding-right: 0px;
  }

  .lg\:px-\[60px\] {
    padding-left: 60px;
    padding-right: 60px;
  }

  .lg\:py-3 {
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
  }

  .lg\:py-6 {
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
  }

  .lg\:py-8 {
    padding-top: 2rem;
    padding-bottom: 2rem;
  }

  .lg\:pt-0 {
    padding-top: 0px;
  }
}

@media (min-width: 1688px) {
  .min-\[1688px\]\:px-\[120px\] {
    padding-left: 120px;
    padding-right: 120px;
  }
}
