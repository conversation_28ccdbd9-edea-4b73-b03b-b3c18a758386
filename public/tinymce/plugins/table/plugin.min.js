!function(){"use strict";var e=tinymce.util.Tools.resolve("tinymce.PluginManager");const t=e=>t=>(e=>{const t=typeof e;return null===e?"null":"object"===t&&Array.isArray(e)?"array":"object"===t&&(o=l=e,(n=String).prototype.isPrototypeOf(o)||(null===(r=l.constructor)||void 0===r?void 0:r.name)===n.name)?"string":t;var o,l,n,r})(t)===e,o=e=>t=>typeof t===e,l=t("string"),n=t("array"),r=o("boolean"),a=e=>undefined===e;const s=e=>!(e=>null==e)(e),c=o("function"),i=o("number"),m=()=>{},d=e=>()=>e,u=e=>e,p=(e,t)=>e===t;function b(e,...t){return(...o)=>{const l=t.concat(o);return e.apply(null,l)}}const g=e=>{e()},h=d(!1),f=d(!0);class y{constructor(e,t){this.tag=e,this.value=t}static some(e){return new y(!0,e)}static none(){return y.singletonNone}fold(e,t){return this.tag?t(this.value):e()}isSome(){return this.tag}isNone(){return!this.tag}map(e){return this.tag?y.some(e(this.value)):y.none()}bind(e){return this.tag?e(this.value):y.none()}exists(e){return this.tag&&e(this.value)}forall(e){return!this.tag||e(this.value)}filter(e){return!this.tag||e(this.value)?this:y.none()}getOr(e){return this.tag?this.value:e}or(e){return this.tag?this:e}getOrThunk(e){return this.tag?this.value:e()}orThunk(e){return this.tag?this:e()}getOrDie(e){if(this.tag)return this.value;throw new Error(null!=e?e:"Called getOrDie on None")}static from(e){return s(e)?y.some(e):y.none()}getOrNull(){return this.tag?this.value:null}getOrUndefined(){return this.value}each(e){this.tag&&e(this.value)}toArray(){return this.tag?[this.value]:[]}toString(){return this.tag?`some(${this.value})`:"none()"}}y.singletonNone=new y(!1),Array.prototype.slice;const w=Array.prototype.indexOf,S=Array.prototype.push,C=(e,t)=>((e,t)=>w.call(e,t))(e,t)>-1,v=(e,t)=>{for(let o=0,l=e.length;o<l;o++)if(t(e[o],o))return!0;return!1},T=(e,t)=>{const o=[];for(let l=0;l<e;l++)o.push(t(l));return o},x=(e,t)=>{const o=e.length,l=new Array(o);for(let n=0;n<o;n++){const o=e[n];l[n]=t(o,n)}return l},A=(e,t)=>{for(let o=0,l=e.length;o<l;o++)t(e[o],o)},R=(e,t)=>{const o=[];for(let l=0,n=e.length;l<n;l++){const n=e[l];t(n,l)&&o.push(n)}return o},O=(e,t,o)=>(A(e,((e,l)=>{o=t(o,e,l)})),o),_=(e,t)=>((e,t,o)=>{for(let l=0,n=e.length;l<n;l++){const n=e[l];if(t(n,l))return y.some(n);if(o(n,l))break}return y.none()})(e,t,h),D=(e,t)=>(e=>{const t=[];for(let o=0,l=e.length;o<l;++o){if(!n(e[o]))throw new Error("Arr.flatten item "+o+" was not an array, input: "+e);S.apply(t,e[o])}return t})(x(e,t)),I=(e,t)=>{for(let o=0,l=e.length;o<l;++o)if(!0!==t(e[o],o))return!1;return!0},N=(e,t)=>t>=0&&t<e.length?y.some(e[t]):y.none();c(Array.from)&&Array.from;const M=(e,t)=>{for(let o=0;o<e.length;o++){const l=t(e[o],o);if(l.isSome())return l}return y.none()},P=Object.keys,k=Object.hasOwnProperty,B=(e,t)=>{const o=P(e);for(let l=0,n=o.length;l<n;l++){const n=o[l];t(e[n],n)}},E=(e,t)=>{const o={};var l;return((e,t,o,l)=>{B(e,((e,n)=>{(t(e,n)?o:l)(e,n)}))})(e,t,(l=o,(e,t)=>{l[t]=e}),m),o},F=e=>P(e).length,q=(e,t)=>L(e,t)?y.from(e[t]):y.none(),L=(e,t)=>k.call(e,t),H=(e,t)=>L(e,t)&&void 0!==e[t]&&null!==e[t],V=e=>{let t=e;return{get:()=>t,set:e=>{t=e}}},$=(e,t,o=p)=>e.exists((e=>o(e,t))),j=(e,t,o)=>e.isSome()&&t.isSome()?y.some(o(e.getOrDie(),t.getOrDie())):y.none(),z=(e,t)=>((e,t)=>""===t||e.length>=t.length&&e.substr(0,0+t.length)===t)(e,t),U=(W=/^\s+|\s+$/g,e=>e.replace(W,""));var W;const G=e=>e.length>0,K=(e,t=10)=>{const o=parseInt(e,t);return isNaN(o)?y.none():y.some(o)},J=e=>{if(null==e)throw new Error("Node cannot be null or undefined");return{dom:e}},Q={fromHtml:(e,t)=>{const o=(t||document).createElement("div");if(o.innerHTML=e,!o.hasChildNodes()||o.childNodes.length>1){const t="HTML does not have a single root node";throw console.error(t,e),new Error(t)}return J(o.childNodes[0])},fromTag:(e,t)=>{const o=(t||document).createElement(e);return J(o)},fromText:(e,t)=>{const o=(t||document).createTextNode(e);return J(o)},fromDom:J,fromPoint:(e,t,o)=>y.from(e.dom.elementFromPoint(t,o)).map(J)},X=(e,t)=>{const o=e.dom;if(1!==o.nodeType)return!1;{const e=o;if(void 0!==e.matches)return e.matches(t);if(void 0!==e.msMatchesSelector)return e.msMatchesSelector(t);if(void 0!==e.webkitMatchesSelector)return e.webkitMatchesSelector(t);if(void 0!==e.mozMatchesSelector)return e.mozMatchesSelector(t);throw new Error("Browser lacks native selectors")}},Y=e=>1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType||0===e.childElementCount,Z=(e,t)=>e.dom===t.dom,ee=X,te=e=>e.dom.nodeName.toLowerCase(),oe=e=>e.dom.nodeType,le=e=>t=>oe(t)===e,ne=le(1),re=le(3),ae=le(9),se=le(11),ce=e=>t=>ne(t)&&te(t)===e,ie=e=>y.from(e.dom.parentNode).map(Q.fromDom),me=e=>y.from(e.dom.nextSibling).map(Q.fromDom),de=e=>x(e.dom.childNodes,Q.fromDom),ue=e=>Q.fromDom(e.dom.host),pe=(e,t)=>{ie(e).each((o=>{o.dom.insertBefore(t.dom,e.dom)}))},be=(e,t)=>{me(e).fold((()=>{ie(e).each((e=>{ge(e,t)}))}),(e=>{pe(e,t)}))},ge=(e,t)=>{e.dom.appendChild(t.dom)},he=(e,t)=>{A(t,((o,l)=>{const n=0===l?e:t[l-1];be(n,o)}))},fe=(e,t,o)=>{if(!(l(o)||r(o)||i(o)))throw console.error("Invalid call to Attribute.set. Key ",t,":: Value ",o,":: Element ",e),new Error("Attribute value was not simple");e.setAttribute(t,o+"")},ye=(e,t)=>{const o=e.dom.getAttribute(t);return null===o?void 0:o},we=(e,t)=>y.from(ye(e,t)),Se=(e,t)=>{e.dom.removeAttribute(t)},Ce=e=>{const t=e.dom;null!==t.parentNode&&t.parentNode.removeChild(t)},ve=e=>x(e,Q.fromDom),Te=e=>void 0!==e.style&&c(e.style.getPropertyValue),xe=e=>{const t=re(e)?e.dom.parentNode:e.dom;if(null==t||null===t.ownerDocument)return!1;const o=t.ownerDocument;return(e=>{const t=(e=>Q.fromDom(e.dom.getRootNode()))(e);return se(o=t)&&s(o.dom.host)?y.some(t):y.none();var o})(Q.fromDom(t)).fold((()=>o.body.contains(t)),(l=xe,n=ue,e=>l(n(e))));var l,n},Ae=(e,t)=>{const o=e.dom,l=window.getComputedStyle(o).getPropertyValue(t);return""!==l||xe(e)?l:Re(o,t)},Re=(e,t)=>Te(e)?e.style.getPropertyValue(t):"",Oe=(e,t)=>{const o=e.dom,l=Re(o,t);return y.from(l).filter((e=>e.length>0))},_e=(e,t,o)=>((e,t)=>(e=>{const t=parseFloat(e);return isNaN(t)?y.none():y.some(t)})(e).getOr(t))(Ae(e,t),o),De=e=>((e,t)=>{const o=e.dom,l=o.getBoundingClientRect().width||o.offsetWidth;return"border-box"===t?l:((e,t,o,l)=>t-_e(e,`padding-${o}`,0)-_e(e,`padding-${l}`,0)-_e(e,`border-${o}-width`,0)-_e(e,`border-${l}-width`,0))(e,l,"left","right")})(e,"content-box"),Ie=(e=>{const t=t=>e(t)?y.from(t.dom.nodeValue):y.none();return{get:o=>{if(!e(o))throw new Error("Can only get text value of a text node");return t(o).getOr("")},getOption:t,set:(t,o)=>{if(!e(t))throw new Error("Can only set raw text value of a text node");t.dom.nodeValue=o}}})(re);var Ne=(e,t,o,l,n)=>e(o,l)?y.some(o):c(n)&&n(o)?y.none():t(o,l,n);const Me=(e,t,o)=>{let l=e.dom;const n=c(o)?o:h;for(;l.parentNode;){l=l.parentNode;const e=Q.fromDom(l);if(t(e))return y.some(e);if(n(e))break}return y.none()},Pe=(e,t,o)=>Me(e,(e=>X(e,t)),o),ke=(e,t)=>(e=>_(e.dom.childNodes,(e=>{return o=Q.fromDom(e),X(o,t);var o})).map(Q.fromDom))(e),Be=(e,t)=>((e,t)=>{const o=void 0===t?document:t.dom;return Y(o)?y.none():y.from(o.querySelector(e)).map(Q.fromDom)})(t,e),Ee=(e,t,o)=>Ne(((e,t)=>X(e,t)),Pe,e,t,o),Fe=(e,t=!1)=>{return xe(e)?e.dom.isContentEditable:(o=e,Ee(o,"[contenteditable]")).fold(d(t),(e=>"true"===qe(e)));var o},qe=e=>e.dom.contentEditable,Le=(e,t)=>{let o=[];return A(de(e),(e=>{t(e)&&(o=o.concat([e])),o=o.concat(Le(e,t))})),o},He=(e,t)=>(e=>R(de(e),(e=>X(e,t))))(e),Ve=(e,t)=>((e,t)=>{const o=void 0===t?document:t.dom;return Y(o)?[]:x(o.querySelectorAll(e),Q.fromDom)})(t,e),$e=e=>t=>Z(t,(e=>Q.fromDom(e.getBody()))(e)),je=e=>/^\d+(\.\d+)?$/.test(e)?e+"px":e,ze=e=>Q.fromDom(e.selection.getStart()),Ue=["tfoot","thead","tbody","colgroup"],We=(e,t,o)=>({element:e,rowspan:t,colspan:o}),Ge=(e,t,o)=>({element:e,cells:t,section:o}),Ke=(e,t,o=0)=>we(e,t).map((e=>parseInt(e,10))).getOr(o),Je=(e,t)=>Qe(e,t,f),Qe=(e,t,o)=>D(de(e),(e=>X(e,t)?o(e)?[e]:[]:Qe(e,t,o))),Xe=(e,t)=>Ee(e,"table",t),Ye=e=>Je(e,"tr"),Ze=e=>Xe(e).fold(d([]),(e=>He(e,"colgroup"))),et=ce("th"),tt=(e,t)=>e&&t?"sectionCells":e?"section":"cells",ot=e=>{const t=x(e,(e=>(e=>{const t="thead"===e.section,o=$((e=>{const t=R(e,(e=>et(e.element)));return 0===t.length?y.some("td"):t.length===e.length?y.some("th"):y.none()})(e.cells),"th");return"tfoot"===e.section?{type:"footer"}:t||o?{type:"header",subType:tt(t,o)}:{type:"body"}})(e).type)),o=C(t,"header"),l=C(t,"footer");if(o||l){const e=C(t,"body");return!o||e||l?o||e||!l?y.none():y.some("footer"):y.some("header")}return y.some("body")},lt=e=>ie(e).map((e=>{const t=te(e);return(e=>C(Ue,e))(t)?t:"tbody"})).getOr("tbody"),nt=e=>we(e,"data-snooker-locked-cols").bind((e=>y.from(e.match(/\d+/g)))).map((e=>((e,t)=>{const o={};for(let l=0,n=e.length;l<n;l++){const n=e[l];o[String(n)]=t(n,l)}return o})(e,f))),rt=(e,t)=>e+","+t,at=e=>{const t={},o=[];var l;const n=(l=e,N(l,0)).map((e=>e.element)).bind(Xe).bind(nt).getOr({});let r=0,a=0,s=0;const{pass:c,fail:i}=(e=>{const t=[],o=[];for(let n=0,r=e.length;n<r;n++){const r=e[n];(l=r,"colgroup"===l.section?t:o).push(r)}var l;return{pass:t,fail:o}})(e);A(i,(e=>{const l=[];A(e.cells,(e=>{let o=0;for(;void 0!==t[rt(s,o)];)o++;const r=H(n,o.toString()),c=((e,t,o,l,n,r)=>({element:e,rowspan:t,colspan:o,row:l,column:n,isLocked:r}))(e.element,e.rowspan,e.colspan,s,o,r);for(let l=0;l<e.colspan;l++)for(let n=0;n<e.rowspan;n++){const e=o+l,r=rt(s+n,e);t[r]=c,a=Math.max(a,e+1)}l.push(c)})),r++,o.push(Ge(e.element,l,e.section)),s++}));const{columns:m,colgroups:d}=(e=>N(e,e.length-1))(c).map((e=>{const t=(e=>{const t={};let o=0;return A(e.cells,(e=>{const l=e.colspan;T(l,(n=>{const r=o+n;t[r]=((e,t,o)=>({element:e,colspan:t,column:o}))(e.element,l,r)})),o+=l})),t})(e),o=((e,t)=>({element:e,columns:t}))(e.element,((e,t)=>{const o=[];return B(e,((e,l)=>{o.push(t(e,l))})),o})(t,u));return{colgroups:[o],columns:t}})).getOrThunk((()=>({colgroups:[],columns:{}}))),p=((e,t)=>({rows:e,columns:t}))(r,a);return{grid:p,access:t,all:o,columns:m,colgroups:d}},st=e=>{const t=(e=>{const t=Ye(e);return((e,t)=>x(e,(e=>{if("colgroup"===te(e)){const t=x((e=>X(e,"colgroup")?He(e,"col"):D(Ze(e),(e=>He(e,"col"))))(e),(e=>{const t=Ke(e,"span",1);return We(e,1,t)}));return Ge(e,t,"colgroup")}{const o=x((e=>Je(e,"th,td"))(e),(e=>{const t=Ke(e,"rowspan",1),o=Ke(e,"colspan",1);return We(e,t,o)}));return Ge(e,o,t(e))}})))([...Ze(e),...t],lt)})(e);return at(t)},ct=(e,t,o)=>y.from(e.access[rt(t,o)]),it=(e,t,o)=>{const l=((e,t)=>{const o=D(e.all,(e=>e.cells));return R(o,t)})(e,(e=>o(t,e.element)));return l.length>0?y.some(l[0]):y.none()},mt=(e,t)=>y.from(e.columns[t]),dt=(e,t)=>M(e.all,(e=>_(e.cells,(e=>Z(t,e.element))))),ut=(e,t,o)=>{const l=(e=>{const t=[],o=e=>{t.push(e)};for(let t=0;t<e.length;t++)e[t].each(o);return t})(x(t.selection,(t=>{return(l=t,((e,t,o=h)=>o(t)?y.none():C(e,te(t))?y.some(t):Pe(t,e.join(","),(e=>X(e,"table")||o(e))))(["td","th"],l,n)).bind((t=>dt(e,t))).filter(o);var l,n})));return n=l,l.length>0?y.some(n):y.none();var n},pt=(e,t)=>ut(e,t,f),bt=(e,t)=>I(t,(t=>((e,t)=>dt(e,t).exists((e=>!e.isLocked)))(e,t))),gt=(e,t)=>((e,t)=>t.mergable)(0,t).filter((t=>bt(e,t.cells))),ht=(e,t)=>((e,t)=>t.unmergable)(0,t).filter((t=>bt(e,t))),ft=ce("col"),yt=ce("colgroup"),wt=e=>t=>{const o=[],l=l=>{const n="td"===e?{scope:null}:{},r=t.replace(l,e,n);return o.push({item:l,sub:r}),r};return{replaceOrInit:(e,t)=>{if((e=>"tr"===te(e)||yt(e))(e)||ft(e))return e;{const n=e;return((e,t)=>_(o,(o=>t(o.item,e))))(n,t).fold((()=>l(n)),(o=>t(e,o.item)?o.sub:l(n)))}}}};var St=["body","p","div","article","aside","figcaption","figure","footer","header","nav","section","ol","ul","li","table","thead","tbody","tfoot","caption","tr","td","th","h1","h2","h3","h4","h5","h6","blockquote","pre","address"];const Ct=(e,t,o,l)=>{const n=t(e,o);return r=(o,l)=>{const n=t(e,l);return vt(e,o,n)},a=n,((e,t)=>{for(let o=e.length-1;o>=0;o--)t(e[o],o)})(l,((e,t)=>{a=r(a,e)})),a;var r,a},vt=(e,t,o)=>t.bind((t=>o.filter(b(e.eq,t)))),Tt={up:d({selector:Pe,closest:Ee,predicate:Me,all:(e,t)=>{const o=c(t)?t:h;let l=e.dom;const n=[];for(;null!==l.parentNode&&void 0!==l.parentNode;){const e=l.parentNode,t=Q.fromDom(e);if(n.push(t),!0===o(t))break;l=e}return n}}),down:d({selector:Ve,predicate:Le}),styles:d({get:Ae,getRaw:Oe,set:(e,t,o)=>{((e,t,o)=>{if(!l(o))throw console.error("Invalid call to CSS.set. Property ",t,":: Value ",o,":: Element ",e),new Error("CSS value must be a string: "+o);Te(e)&&e.style.setProperty(t,o)})(e.dom,t,o)},remove:(e,t)=>{((e,t)=>{Te(e)&&e.style.removeProperty(t)})(e.dom,t),$(we(e,"style").map(U),"")&&Se(e,"style")}}),attrs:d({get:ye,set:(e,t,o)=>{fe(e.dom,t,o)},remove:Se,copyTo:(e,t)=>{((e,t)=>{const o=e.dom;B(t,((e,t)=>{fe(o,t,e)}))})(t,O(e.dom.attributes,((e,t)=>(e[t.name]=t.value,e)),{}))}}),insert:d({before:pe,after:be,afterAll:he,append:ge,appendAll:(e,t)=>{A(t,(t=>{ge(e,t)}))},prepend:(e,t)=>{(e=>(e=>{const t=e.dom.childNodes;return y.from(t[0]).map(Q.fromDom)})(e))(e).fold((()=>{ge(e,t)}),(o=>{e.dom.insertBefore(t.dom,o.dom)}))},wrap:(e,t)=>{pe(e,t),ge(t,e)}}),remove:d({unwrap:e=>{const t=de(e);t.length>0&&he(e,t),Ce(e)},remove:Ce}),create:d({nu:Q.fromTag,clone:e=>Q.fromDom(e.dom.cloneNode(!1)),text:Q.fromText}),query:d({comparePosition:(e,t)=>e.dom.compareDocumentPosition(t.dom),prevSibling:e=>y.from(e.dom.previousSibling).map(Q.fromDom),nextSibling:me}),property:d({children:de,name:te,parent:ie,document:e=>{return(t=e,ae(t)?t:Q.fromDom(t.dom.ownerDocument)).dom;var t},isText:re,isComment:e=>8===oe(e)||"#comment"===te(e),isElement:ne,isSpecial:e=>{const t=te(e);return C(["script","noscript","iframe","noframes","noembed","title","style","textarea","xmp"],t)},getLanguage:e=>ne(e)?we(e,"lang"):y.none(),getText:e=>Ie.get(e),setText:(e,t)=>Ie.set(e,t),isBoundary:e=>!!ne(e)&&("body"===te(e)||C(St,te(e))),isEmptyTag:e=>!!ne(e)&&C(["br","img","hr","input"],te(e)),isNonEditable:e=>ne(e)&&"false"===ye(e,"contenteditable")}),eq:Z,is:ee};wt("th"),wt("td");const xt=(e,t)=>t.column>=e.startCol&&t.column+t.colspan-1<=e.finishCol&&t.row>=e.startRow&&t.row+t.rowspan-1<=e.finishRow,At=(e,t,o)=>((e,t,o)=>{const l=it(e,t,Z),n=it(e,o,Z);return l.bind((e=>n.map((t=>{return o=e,l=t,{startRow:Math.min(o.row,l.row),startCol:Math.min(o.column,l.column),finishRow:Math.max(o.row+o.rowspan-1,l.row+l.rowspan-1),finishCol:Math.max(o.column+o.colspan-1,l.column+l.colspan-1)};var o,l}))))})(e,t,o).bind((t=>((e,t)=>{let o=!0;const l=b(xt,t);for(let n=t.startRow;n<=t.finishRow;n++)for(let r=t.startCol;r<=t.finishCol;r++)o=o&&ct(e,n,r).exists(l);return o?y.some(t):y.none()})(e,t))),Rt=st;var Ot=tinymce.util.Tools.resolve("tinymce.util.Tools");const _t=(e,t,o)=>{const l=e.select("td,th",t);let n;for(let t=0;t<l.length;t++){const r=e.getStyle(l[t],o);if(a(n)&&(n=r),n!==r)return""}return n},Dt=(e,t,o)=>{Ot.each("left center right".split(" "),(l=>{l!==o&&e.formatter.remove("align"+l,{},t)})),o&&e.formatter.apply("align"+o,{},t)},It=(e,t,o)=>{e.dispatch("TableModified",{...o,table:t})};var Nt=tinymce.util.Tools.resolve("tinymce.Env");const Mt=T(5,(e=>{const t=`${e+1}px`;return{title:t,value:t}})),Pt=x(["Solid","Dotted","Dashed","Double","Groove","Ridge","Inset","Outset","None","Hidden"],(e=>({title:e,value:e.toLowerCase()}))),kt="100%",Bt=e=>{var t;const o=e.dom,l=null!==(t=o.getParent(e.selection.getStart(),o.isBlock))&&void 0!==t?t:e.getBody();return De(Q.fromDom(l))+"px"},Et=e=>t=>t.options.get(e),Ft=Et("table_sizing_mode"),qt=Et("table_border_widths"),Lt=Et("table_border_styles"),Ht=Et("table_cell_advtab"),Vt=Et("table_row_advtab"),$t=Et("table_advtab"),jt=Et("table_appearance_options"),zt=Et("table_grid"),Ut=Et("table_style_by_css"),Wt=Et("table_cell_class_list"),Gt=Et("table_row_class_list"),Kt=Et("table_class_list"),Jt=Et("table_toolbar"),Qt=Et("table_background_color_map"),Xt=Et("table_border_color_map"),Yt=e=>"fixed"===Ft(e),Zt=e=>"responsive"===Ft(e),eo=e=>{const t=e.options,o=t.get("table_default_styles");return t.isSet("table_default_styles")?o:((e,t)=>Zt(e)||!Ut(e)?t:Yt(e)?{...t,width:Bt(e)}:{...t,width:kt})(e,o)},to=e=>{const t=e.options,o=t.get("table_default_attributes");return t.isSet("table_default_attributes")?o:((e,t)=>Zt(e)||Ut(e)?t:Yt(e)?{...t,width:Bt(e)}:{...t,width:kt})(e,o)},oo=e=>Pe(e,"table"),lo=(e,t,o)=>Be(e,t).bind((t=>Be(e,o).bind((e=>{return(o=oo,l=[t,e],((e,t,o)=>o.length>0?((e,t,o,l)=>l(e,t,o[0],o.slice(1)))(e,t,o,Ct):y.none())(Tt,((e,t)=>o(t)),l)).map((o=>({first:t,last:e,table:o})));var o,l})))),no=u,ro=e=>{const t=(e,t)=>we(e,t).exists((e=>parseInt(e,10)>1));return e.length>0&&I(e,(e=>t(e,"rowspan")||t(e,"colspan")))?y.some(e):y.none()},ao=(e,t,o)=>{return t.length<=1?y.none():(l=e,n=o.firstSelectedSelector,r=o.lastSelectedSelector,lo(l,n,r).bind((e=>{const t=e=>Z(l,e),o="thead,tfoot,tbody,table",n=Pe(e.first,o,t),r=Pe(e.last,o,t);return n.bind((t=>r.bind((o=>Z(t,o)?((e,t,o)=>{const l=Rt(e);return At(l,t,o)})(e.table,e.first,e.last):y.none()))))}))).map((e=>({bounds:e,cells:t})));var l,n,r},so="data-mce-selected",co="data-mce-first-selected",io="data-mce-last-selected",mo={selected:so,selectedSelector:"td["+so+"],th["+so+"]",firstSelected:co,firstSelectedSelector:"td["+co+"],th["+co+"]",lastSelected:io,lastSelectedSelector:"td["+io+"],th["+io+"]"},uo=e=>(t,o)=>{const l=te(t),n="col"===l||"colgroup"===l?Xe(r=t).bind((e=>((e,t)=>((e,t)=>{const o=Ve(e,t);return o.length>0?y.some(o):y.none()})(e,t))(e,mo.firstSelectedSelector))).fold(d(r),(e=>e[0])):t;var r;return Ee(n,e,o)},po=uo("th,td,caption"),bo=uo("th,td"),go=e=>ve(e.model.table.getSelectedCells()),ho=(e,t)=>{const o=bo(e),l=o.bind((e=>Xe(e))).map((e=>Ye(e)));return j(o,l,((e,o)=>R(o,(o=>v(ve(o.dom.cells),(o=>"1"===ye(o,t)||Z(o,e))))))).getOr([])},fo=[{text:"None",value:""},{text:"Top",value:"top"},{text:"Middle",value:"middle"},{text:"Bottom",value:"bottom"}],yo=/^#?([a-f\d])([a-f\d])([a-f\d])$/i,wo=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i,So=e=>{return(t=e,z(t,"#")?(e=>e.substring(1))(t):t).toUpperCase();var t},Co=e=>{const t=e.toString(16);return(1===t.length?"0"+t:t).toUpperCase()},vo=e=>{return t=Co(e.red)+Co(e.green)+Co(e.blue),{value:So(t)};var t},To=/^\s*rgb\s*\(\s*(\d+)\s*[,\s]\s*(\d+)\s*[,\s]\s*(\d+)\s*\)\s*$/i,xo=/^\s*rgba\s*\(\s*(\d+)\s*[,\s]\s*(\d+)\s*[,\s]\s*(\d+)\s*[,\s]\s*((?:\d?\.\d+|\d+)%?)\s*\)\s*$/i,Ao=(e,t,o,l)=>({red:e,green:t,blue:o,alpha:l}),Ro=(e,t,o,l)=>{const n=parseInt(e,10),r=parseInt(t,10),a=parseInt(o,10),s=parseFloat(l);return Ao(n,r,a,s)},Oo=e=>{const t=To.exec(e);if(null!==t)return y.some(Ro(t[1],t[2],t[3],"1"));const o=xo.exec(e);return null!==o?y.some(Ro(o[1],o[2],o[3],o[4])):y.none()},_o=(e,t,o)=>l=>{const n=(e=>{const t=V(y.none()),o=()=>t.get().each(e);return{clear:()=>{o(),t.set(y.none())},isSet:()=>t.get().isSome(),get:()=>t.get(),set:e=>{o(),t.set(y.some(e))}}})((e=>e.unbind())),r=!G(o),a=()=>{const a=go(e),s=l=>e.formatter.match(t,{value:o},l.dom,r);r?(l.setActive(!v(a,s)),n.set(e.formatter.formatChanged(t,(e=>l.setActive(!e)),!0))):(l.setActive(I(a,s)),n.set(e.formatter.formatChanged(t,l.setActive,!1,{value:o})))};return e.initialized?a():e.on("init",a),n.clear},Do=e=>H(e,"menu"),Io=e=>x(e,(e=>{const t=e.text||e.title||"";return Do(e)?{text:t,items:Io(e.menu)}:{text:t,value:e.value}})),No=e=>e.length?y.some(Io([{text:"Select...",value:"mce-no-match"},...e])):y.none(),Mo=(e,t,o,l)=>x(t,(t=>{const n=t.text||t.title;return Do(t)?{type:"nestedmenuitem",text:n,getSubmenuItems:()=>Mo(e,t.menu,o,l)}:{text:n,type:"togglemenuitem",onAction:()=>l(t.value),onSetup:_o(e,o,t.value)}})),Po=(e,t)=>o=>{e.execCommand("mceTableApplyCellStyle",!1,{[t]:o})},ko=e=>D(e,(e=>Do(e)?[{...e,menu:ko(e.menu)}]:G(e.value)?[e]:[])),Bo=(e,t,o,l)=>n=>n(Mo(e,t,o,l)),Eo=(e,t,o)=>{const l=x(t,(e=>{return{text:e.title,value:"#"+(o=e.value,(t=o,(e=>yo.test(e)||wo.test(e))(t)?y.some({value:So(t)}):y.none()).orThunk((()=>Oo(o).map(vo))).getOrThunk((()=>{const e=document.createElement("canvas");e.height=1,e.width=1;const t=e.getContext("2d");t.clearRect(0,0,e.width,e.height),t.fillStyle="#FFFFFF",t.fillStyle=o,t.fillRect(0,0,1,1);const l=t.getImageData(0,0,1,1).data,n=l[0],r=l[1],a=l[2],s=l[3];return vo(Ao(n,r,a,s))}))).value,type:"choiceitem"};var t,o}));return[{type:"fancymenuitem",fancytype:"colorswatch",initData:{colors:l.length>0?l:void 0,allowCustomColors:!1},onAction:t=>{const l="remove"===t.value?"":t.value;e.execCommand("mceTableApplyCellStyle",!1,{[o]:l})}}]},Fo=e=>()=>{const t="header"===e.queryCommandValue("mceTableRowType")?"body":"header";e.execCommand("mceTableRowType",!1,{type:t})},qo=e=>()=>{const t="th"===e.queryCommandValue("mceTableColType")?"td":"th";e.execCommand("mceTableColType",!1,{type:t})},Lo=[{name:"width",type:"input",label:"Width"},{name:"celltype",type:"listbox",label:"Cell type",items:[{text:"Cell",value:"td"},{text:"Header cell",value:"th"}]},{name:"scope",type:"listbox",label:"Scope",items:[{text:"None",value:""},{text:"Row",value:"row"},{text:"Column",value:"col"},{text:"Row group",value:"rowgroup"},{text:"Column group",value:"colgroup"}]},{name:"halign",type:"listbox",label:"Horizontal align",items:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]},{name:"valign",type:"listbox",label:"Vertical align",items:fo}],Ho=e=>Lo.concat((e=>No(Wt(e)).map((e=>({name:"class",type:"listbox",label:"Class",items:e}))))(e).toArray()),Vo=(e,t)=>{const o=[{name:"borderstyle",type:"listbox",label:"Border style",items:[{text:"Select...",value:""}].concat(Io(Lt(e)))},{name:"bordercolor",type:"colorinput",label:"Border color"},{name:"backgroundcolor",type:"colorinput",label:"Background color"}];return{title:"Advanced",name:"advanced",items:"cell"===t?[{name:"borderwidth",type:"input",label:"Border width"}].concat(o):o}},$o=(e,t)=>{const o=e.dom;return{setAttrib:(e,l)=>{o.setAttrib(t,e,l)},setStyle:(e,l)=>{o.setStyle(t,e,l)},setFormat:(o,l)=>{""===l?e.formatter.remove(o,{value:null},t,!0):e.formatter.apply(o,{value:l},t)}}},jo=e=>{return z(e,"rgb")?Oo(t=e).map(vo).map((e=>"#"+e.value)).getOr(t):e;var t},zo=e=>{const t=Q.fromDom(e);return{borderwidth:Oe(t,"border-width").getOr(""),borderstyle:Oe(t,"border-style").getOr(""),bordercolor:Oe(t,"border-color").map(jo).getOr(""),backgroundcolor:Oe(t,"background-color").map(jo).getOr("")}},Uo=e=>{const t=e[0],o=e.slice(1);return A(o,(e=>{A(P(t),(o=>{B(e,((e,l)=>{const n=t[o];""!==n&&o===l&&n!==e&&(t[o]="class"===o?"mce-no-match":"")}))}))})),t},Wo=(e,t,o,l)=>_(e,(e=>!a(o.formatter.matchNode(l,t+e)))).getOr(""),Go=b(Wo,["left","center","right"],"align"),Ko=b(Wo,["top","middle","bottom"],"valign"),Jo=e=>Xe(Q.fromDom(e)).map((t=>((e,t)=>{const o=st(e);return pt(o,t).bind((e=>{const t=e[e.length-1],l=e[0].row,n=t.row+t.rowspan,r=o.all.slice(l,n);return ot(r)})).getOr("")})(t,{selection:ve(e.cells)}))).getOr(""),Qo=(e,t)=>{const o=st(e),l=(e=>D(e.all,(e=>e.cells)))(o),n=R(l,(e=>v(t,(t=>Z(e.element,t)))));return x(n,(e=>({element:e.element.dom,column:mt(o,e.column).map((e=>e.element.dom))})))},Xo=(e,t,o,l)=>{const n=l.getData();l.close(),e.undoManager.transact((()=>{((e,t,o,l)=>{const n=E(l,((e,t)=>o[t]!==e));F(n)>0&&t.length>=1&&Xe(t[0]).each((o=>{const r=Qo(o,t),a=F(E(n,((e,t)=>"scope"!==t&&"celltype"!==t)))>0,s=L(n,"celltype");(a||L(n,"scope"))&&((e,t,o,l)=>{const n=1===t.length;A(t,(t=>{const r=t.element,a=n?f:l,s=$o(e,r);((e,t,o,l)=>{l("scope")&&e.setAttrib("scope",o.scope),l("class")&&"mce-no-match"!==o.class&&e.setAttrib("class",o.class),l("width")&&t.setStyle("width",je(o.width))})(s,t.column.map((t=>$o(e,t))).getOr(s),o,a),Ht(e)&&((e,t,o)=>{o("backgroundcolor")&&e.setFormat("tablecellbackgroundcolor",t.backgroundcolor),o("bordercolor")&&e.setFormat("tablecellbordercolor",t.bordercolor),o("borderstyle")&&e.setFormat("tablecellborderstyle",t.borderstyle),o("borderwidth")&&e.setFormat("tablecellborderwidth",je(t.borderwidth))})(s,o,a),l("halign")&&Dt(e,r,o.halign),l("valign")&&((e,t,o)=>{Ot.each("top middle bottom".split(" "),(l=>{l!==o&&e.formatter.remove("valign"+l,{},t)})),o&&e.formatter.apply("valign"+o,{},t)})(e,r,o.valign)}))})(e,r,l,b(L,n)),s&&((e,t)=>{e.execCommand("mceTableCellType",!1,{type:t.celltype,no_events:!0})})(e,l),It(e,o.dom,{structure:s,style:a})}))})(e,t,o,n),e.focus()}))},Yo=e=>{const t=go(e);if(0===t.length)return;const o=((e,t)=>{const o=Xe(t[0]).map((o=>x(Qo(o,t),(t=>((e,t,o,l)=>{const n=e.dom;return{width:(a=l.getOr(t),s="width",n.getStyle(a,s)||n.getAttrib(a,s)),scope:n.getAttrib(t,"scope"),celltype:(r=t,r.nodeName.toLowerCase()),class:n.getAttrib(t,"class",""),halign:Go(e,t),valign:Ko(e,t),...o?zo(t):{}};var r,a,s})(e,t.element,Ht(e),t.column)))));return Uo(o.getOrDie())})(e,t),l={type:"tabpanel",tabs:[{title:"General",name:"general",items:Ho(e)},Vo(e,"cell")]},n={type:"panel",items:[{type:"grid",columns:2,items:Ho(e)}]};e.windowManager.open({title:"Cell Properties",size:"normal",body:Ht(e)?l:n,buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:o,onSubmit:b(Xo,e,t,o)})},Zo=[{type:"listbox",name:"type",label:"Row type",items:[{text:"Header",value:"header"},{text:"Body",value:"body"},{text:"Footer",value:"footer"}]},{type:"listbox",name:"align",label:"Alignment",items:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]},{label:"Height",name:"height",type:"input"}],el=e=>Zo.concat((e=>No(Gt(e)).map((e=>({name:"class",type:"listbox",label:"Class",items:e}))))(e).toArray()),tl=(e,t,o,l)=>{const n=l.getData();l.close(),e.undoManager.transact((()=>{((e,t,o,l)=>{const n=E(l,((e,t)=>o[t]!==e));if(F(n)>0){const o=L(n,"type"),r=!o||F(n)>1;r&&((e,t,o,l)=>{const n=1===t.length?f:l;A(t,(t=>{const r=He(Q.fromDom(t),"td,th"),a=$o(e,t);((e,t,o)=>{o("class")&&"mce-no-match"!==t.class&&e.setAttrib("class",t.class),o("height")&&e.setStyle("height",je(t.height))})(a,o,n),Vt(e)&&((e,t,o)=>{o("backgroundcolor")&&e.setStyle("background-color",t.backgroundcolor),o("bordercolor")&&e.setStyle("border-color",t.bordercolor),o("borderstyle")&&e.setStyle("border-style",t.borderstyle)})(a,o,n),l("height")&&A(r,(t=>{e.dom.setStyle(t.dom,"height",null)})),l("align")&&Dt(e,t,o.align)}))})(e,t,l,b(L,n)),o&&((e,t)=>{e.execCommand("mceTableRowType",!1,{type:t.type,no_events:!0})})(e,l),Xe(Q.fromDom(t[0])).each((t=>It(e,t.dom,{structure:o,style:r})))}})(e,t,o,n),e.focus()}))},ol=e=>{const t=ho(ze(e),mo.selected);if(0===t.length)return;const o=x(t,(t=>((e,t,o)=>{const l=e.dom;return{height:l.getStyle(t,"height")||l.getAttrib(t,"height"),class:l.getAttrib(t,"class",""),type:Jo(t),align:Go(e,t),...o?zo(t):{}}})(e,t.dom,Vt(e)))),l=Uo(o),n={type:"tabpanel",tabs:[{title:"General",name:"general",items:el(e)},Vo(e,"row")]},r={type:"panel",items:[{type:"grid",columns:2,items:el(e)}]};e.windowManager.open({title:"Row Properties",size:"normal",body:Vt(e)?n:r,buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:l,onSubmit:b(tl,e,x(t,(e=>e.dom)),l)})},ll=(e,t,o)=>{const l=o?[{type:"input",name:"cols",label:"Cols",inputMode:"numeric"},{type:"input",name:"rows",label:"Rows",inputMode:"numeric"}]:[],n=jt(e)?[{type:"input",name:"cellspacing",label:"Cell spacing",inputMode:"numeric"},{type:"input",name:"cellpadding",label:"Cell padding",inputMode:"numeric"},{type:"input",name:"border",label:"Border width"},{type:"label",label:"Caption",items:[{type:"checkbox",name:"caption",label:"Show caption"}]}]:[],r=t.length>0?[{name:"class",type:"listbox",label:"Class",items:t}]:[];return l.concat([{type:"input",name:"width",label:"Width"},{type:"input",name:"height",label:"Height"}]).concat(n).concat([{type:"listbox",name:"align",label:"Alignment",items:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]}]).concat(r)},nl=(e,t,o,n)=>{if("TD"===t.tagName||"TH"===t.tagName)l(o)&&s(n)?e.setStyle(t,o,n):e.setStyles(t,o);else if(t.children)for(let l=0;l<t.children.length;l++)nl(e,t.children[l],o,n)},rl=(e,t,o,l)=>{const n=e.dom,r=l.getData(),s=E(r,((e,t)=>o[t]!==e));l.close(),e.undoManager.transact((()=>{if(!t){const o=K(r.cols).getOr(1),l=K(r.rows).getOr(1);e.execCommand("mceInsertTable",!1,{rows:l,columns:o}),t=bo(ze(e),$e(e)).bind((t=>Xe(t,$e(e)))).map((e=>e.dom)).getOrDie()}if(F(s)>0){const o={border:L(s,"border"),bordercolor:L(s,"bordercolor"),cellpadding:L(s,"cellpadding")};((e,t,o,l)=>{const n=e.dom,r={},s={},c=Ut(e),i=$t(e),m=0===parseFloat(o.border);if(a(o.class)||"mce-no-match"===o.class||(r.class=o.class),s.height=je(o.height),c?s.width=je(o.width):n.getAttrib(t,"width")&&(r.width=(e=>e?e.replace(/px$/,""):"")(o.width)),c?(m?(r.border=0,s["border-width"]=""):(s["border-width"]=je(o.border),r.border=1),s["border-spacing"]=je(o.cellspacing)):(r.border=m?0:o.border,r.cellpadding=o.cellpadding,r.cellspacing=o.cellspacing),c&&t.children){const e={};if(m?e["border-width"]="":l.border&&(e["border-width"]=je(o.border)),l.cellpadding&&(e.padding=je(o.cellpadding)),i&&l.bordercolor&&(e["border-color"]=o.bordercolor),!(e=>{for(const t in e)if(k.call(e,t))return!1;return!0})(e))for(let o=0;o<t.children.length;o++)nl(n,t.children[o],e)}if(i){const e=o;s["background-color"]=e.backgroundcolor,s["border-color"]=e.bordercolor,s["border-style"]=e.borderstyle}n.setStyles(t,{...eo(e),...s}),n.setAttribs(t,{...to(e),...r})})(e,t,r,o);const l=n.select("caption",t)[0];(l&&!r.caption||!l&&r.caption)&&e.execCommand("mceTableToggleCaption"),Dt(e,t,r.align)}if(e.focus(),e.addVisual(),F(s)>0){const o=L(s,"caption"),l=!o||F(s)>1;It(e,t,{structure:o,style:l})}}))},al=(e,t)=>{const o=e.dom;let l,n=((e,t)=>{const o=eo(e),l=to(e),n=t?{borderstyle:q(o,"border-style").getOr(""),bordercolor:jo(q(o,"border-color").getOr("")),backgroundcolor:jo(q(o,"background-color").getOr(""))}:{};return{height:"",width:"100%",cellspacing:"",cellpadding:"",caption:!1,class:"",align:"",border:"",...o,...l,...n,...(()=>{const t=o["border-width"];return Ut(e)&&t?{border:t}:q(l,"border").fold((()=>({})),(e=>({border:e})))})(),...{...q(o,"border-spacing").or(q(l,"cellspacing")).fold((()=>({})),(e=>({cellspacing:e}))),...q(o,"border-padding").or(q(l,"cellpadding")).fold((()=>({})),(e=>({cellpadding:e})))}}})(e,$t(e));t?(n.cols="1",n.rows="1",$t(e)&&(n.borderstyle="",n.bordercolor="",n.backgroundcolor="")):(l=o.getParent(e.selection.getStart(),"table",e.getBody()),l?n=((e,t,o)=>{const l=e.dom,n=Ut(e)?l.getStyle(t,"border-spacing")||l.getAttrib(t,"cellspacing"):l.getAttrib(t,"cellspacing")||l.getStyle(t,"border-spacing"),r=Ut(e)?_t(l,t,"padding")||l.getAttrib(t,"cellpadding"):l.getAttrib(t,"cellpadding")||_t(l,t,"padding");return{width:l.getStyle(t,"width")||l.getAttrib(t,"width"),height:l.getStyle(t,"height")||l.getAttrib(t,"height"),cellspacing:null!=n?n:"",cellpadding:null!=r?r:"",border:((t,o)=>{const l=Oe(Q.fromDom(o),"border-width");return Ut(e)&&l.isSome()?l.getOr(""):t.getAttrib(o,"border")||_t(e.dom,o,"border-width")||_t(e.dom,o,"border")||""})(l,t),caption:!!l.select("caption",t)[0],class:l.getAttrib(t,"class",""),align:Go(e,t),...o?zo(t):{}}})(e,l,$t(e)):$t(e)&&(n.borderstyle="",n.bordercolor="",n.backgroundcolor=""));const r=No(Kt(e));r.isSome()&&n.class&&(n.class=n.class.replace(/\s*mce\-item\-table\s*/g,""));const a={type:"grid",columns:2,items:ll(e,r.getOr([]),t)},s=$t(e)?{type:"tabpanel",tabs:[{title:"General",name:"general",items:[a]},Vo(e,"table")]}:{type:"panel",items:[a]};e.windowManager.open({title:"Table Properties",size:"normal",body:s,onSubmit:b(rl,e,l,n),buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:n})},sl=e=>{B({mceTableProps:b(al,e,!1),mceTableRowProps:b(ol,e),mceTableCellProps:b(Yo,e),mceInsertTableDialog:b(al,e,!0)},((t,o)=>e.addCommand(o,(()=>{return o=t,void((e=>{return(t=e,o=ce("table"),Ne(((e,t)=>t(e)),Me,t,o,void 0)).forall(Fe);var t,o})(ze(e))&&o());var o}))))},cl=e=>{const t=V(y.none()),o=V([]);let l=y.none();const n=ce("caption"),r=e=>l.forall((t=>!t[e])),a=()=>po(ze(e),$e(e)).bind((t=>{return o=j(Xe(t),po((e=>Q.fromDom(e.selection.getEnd()))(e),$e(e)).bind(Xe),((o,l)=>Z(o,l)?n(t)?y.some((e=>({element:e,mergable:y.none(),unmergable:y.none(),selection:[e]}))(t)):y.some(((e,t,o)=>({element:o,mergable:ao(t,e,mo),unmergable:ro(e),selection:no(e)}))(go(e),o,t)):y.none())),o.bind(u);var o})),s=e=>Xe(e.element).map((t=>{const o=st(t),l=pt(o,e).getOr([]),n=O(l,((e,t)=>(t.isLocked&&(e.onAny=!0,0===t.column?e.onFirst=!0:t.column+t.colspan>=o.grid.columns&&(e.onLast=!0)),e)),{onAny:!1,onFirst:!1,onLast:!1});return{mergeable:gt(o,e).isSome(),unmergeable:ht(o,e).isSome(),locked:n}})),c=()=>{t.set((e=>{let t,o=!1;return(...l)=>(o||(o=!0,t=e.apply(null,l)),t)})(a)()),l=t.get().bind(s),A(o.get(),g)},i=e=>(e(),o.set(o.get().concat([e])),()=>{o.set(R(o.get(),(t=>t!==e)))}),m=(o,l)=>i((()=>t.get().fold((()=>{o.setEnabled(!1)}),(t=>{o.setEnabled(!l(t)&&e.selection.isEditable())})))),d=(o,l,n)=>i((()=>t.get().fold((()=>{o.setEnabled(!1),o.setActive(!1)}),(t=>{o.setEnabled(!l(t)&&e.selection.isEditable()),o.setActive(n(t))})))),p=e=>l.exists((t=>t.locked[e])),b=(t,o)=>l=>d(l,(e=>n(e.element)),(()=>e.queryCommandValue(t)===o)),f=b("mceTableRowType","header"),w=b("mceTableColType","th");return e.on("NodeChange ExecCommand TableSelectorChange",c),{onSetupTable:e=>m(e,(e=>!1)),onSetupCellOrRow:e=>m(e,(e=>n(e.element))),onSetupColumn:e=>t=>m(t,(t=>n(t.element)||p(e))),onSetupPasteable:e=>t=>m(t,(t=>n(t.element)||e().isNone())),onSetupPasteableColumn:(e,t)=>o=>m(o,(o=>n(o.element)||e().isNone()||p(t))),onSetupMergeable:e=>m(e,(e=>r("mergeable"))),onSetupUnmergeable:e=>m(e,(e=>r("unmergeable"))),resetTargets:c,onSetupTableWithCaption:t=>d(t,h,(t=>Xe(t.element,$e(e)).exists((e=>ke(e,"caption").isSome())))),onSetupTableRowHeaders:f,onSetupTableColumnHeaders:w,targets:t.get}};var il=tinymce.util.Tools.resolve("tinymce.FakeClipboard");const ml="x-tinymce/dom-table-",dl=ml+"rows",ul=ml+"columns",pl=e=>{var t;const o=null!==(t=il.read())&&void 0!==t?t:[];return M(o,(t=>y.from(t.getType(e))))},bl=()=>pl(dl),gl=()=>pl(ul),hl=e=>t=>{const o=()=>{t.setEnabled(e.selection.isEditable())};return e.on("NodeChange",o),o(),()=>{e.off("NodeChange",o)}},fl=e=>t=>{const o=()=>{t.setEnabled(e.selection.isEditable())};return e.on("NodeChange",o),o(),()=>{e.off("NodeChange",o)}};e.add("table",(e=>{const t=cl(e);(e=>{const t=e.options.register;t("table_border_widths",{processor:"object[]",default:Mt}),t("table_border_styles",{processor:"object[]",default:Pt}),t("table_cell_advtab",{processor:"boolean",default:!0}),t("table_row_advtab",{processor:"boolean",default:!0}),t("table_advtab",{processor:"boolean",default:!0}),t("table_appearance_options",{processor:"boolean",default:!0}),t("table_grid",{processor:"boolean",default:!Nt.deviceType.isTouch()}),t("table_cell_class_list",{processor:"object[]",default:[]}),t("table_row_class_list",{processor:"object[]",default:[]}),t("table_class_list",{processor:"object[]",default:[]}),t("table_toolbar",{processor:"string",default:"tableprops tabledelete | tableinsertrowbefore tableinsertrowafter tabledeleterow | tableinsertcolbefore tableinsertcolafter tabledeletecol"}),t("table_background_color_map",{processor:"object[]",default:[]}),t("table_border_color_map",{processor:"object[]",default:[]})})(e),sl(e),((e,t)=>{const o=t=>()=>e.execCommand(t),l=(t,l)=>!!e.queryCommandSupported(l.command)&&(e.ui.registry.addMenuItem(t,{...l,onAction:c(l.onAction)?l.onAction:o(l.command)}),!0),n=(t,l)=>{e.queryCommandSupported(l.command)&&e.ui.registry.addToggleMenuItem(t,{...l,onAction:c(l.onAction)?l.onAction:o(l.command)})},r=t=>{e.execCommand("mceInsertTable",!1,{rows:t.numRows,columns:t.numColumns})},a=[l("tableinsertrowbefore",{text:"Insert row before",icon:"table-insert-row-above",command:"mceTableInsertRowBefore",onSetup:t.onSetupCellOrRow}),l("tableinsertrowafter",{text:"Insert row after",icon:"table-insert-row-after",command:"mceTableInsertRowAfter",onSetup:t.onSetupCellOrRow}),l("tabledeleterow",{text:"Delete row",icon:"table-delete-row",command:"mceTableDeleteRow",onSetup:t.onSetupCellOrRow}),l("tablerowprops",{text:"Row properties",icon:"table-row-properties",command:"mceTableRowProps",onSetup:t.onSetupCellOrRow}),l("tablecutrow",{text:"Cut row",icon:"cut-row",command:"mceTableCutRow",onSetup:t.onSetupCellOrRow}),l("tablecopyrow",{text:"Copy row",icon:"duplicate-row",command:"mceTableCopyRow",onSetup:t.onSetupCellOrRow}),l("tablepasterowbefore",{text:"Paste row before",icon:"paste-row-before",command:"mceTablePasteRowBefore",onSetup:t.onSetupPasteable(bl)}),l("tablepasterowafter",{text:"Paste row after",icon:"paste-row-after",command:"mceTablePasteRowAfter",onSetup:t.onSetupPasteable(bl)})],s=[l("tableinsertcolumnbefore",{text:"Insert column before",icon:"table-insert-column-before",command:"mceTableInsertColBefore",onSetup:t.onSetupColumn("onFirst")}),l("tableinsertcolumnafter",{text:"Insert column after",icon:"table-insert-column-after",command:"mceTableInsertColAfter",onSetup:t.onSetupColumn("onLast")}),l("tabledeletecolumn",{text:"Delete column",icon:"table-delete-column",command:"mceTableDeleteCol",onSetup:t.onSetupColumn("onAny")}),l("tablecutcolumn",{text:"Cut column",icon:"cut-column",command:"mceTableCutCol",onSetup:t.onSetupColumn("onAny")}),l("tablecopycolumn",{text:"Copy column",icon:"duplicate-column",command:"mceTableCopyCol",onSetup:t.onSetupColumn("onAny")}),l("tablepastecolumnbefore",{text:"Paste column before",icon:"paste-column-before",command:"mceTablePasteColBefore",onSetup:t.onSetupPasteableColumn(gl,"onFirst")}),l("tablepastecolumnafter",{text:"Paste column after",icon:"paste-column-after",command:"mceTablePasteColAfter",onSetup:t.onSetupPasteableColumn(gl,"onLast")})],i=[l("tablecellprops",{text:"Cell properties",icon:"table-cell-properties",command:"mceTableCellProps",onSetup:t.onSetupCellOrRow}),l("tablemergecells",{text:"Merge cells",icon:"table-merge-cells",command:"mceTableMergeCells",onSetup:t.onSetupMergeable}),l("tablesplitcells",{text:"Split cell",icon:"table-split-cells",command:"mceTableSplitCells",onSetup:t.onSetupUnmergeable})];zt(e)?e.ui.registry.addNestedMenuItem("inserttable",{text:"Table",icon:"table",getSubmenuItems:()=>[{type:"fancymenuitem",fancytype:"inserttable",onAction:r}],onSetup:fl(e)}):e.ui.registry.addMenuItem("inserttable",{text:"Table",icon:"table",onAction:o("mceInsertTableDialog"),onSetup:fl(e)}),e.ui.registry.addMenuItem("inserttabledialog",{text:"Insert table",icon:"table",onAction:o("mceInsertTableDialog"),onSetup:fl(e)}),l("tableprops",{text:"Table properties",onSetup:t.onSetupTable,command:"mceTableProps"}),l("deletetable",{text:"Delete table",icon:"table-delete-table",onSetup:t.onSetupTable,command:"mceTableDelete"}),C(a,!0)&&e.ui.registry.addNestedMenuItem("row",{type:"nestedmenuitem",text:"Row",getSubmenuItems:d("tableinsertrowbefore tableinsertrowafter tabledeleterow tablerowprops | tablecutrow tablecopyrow tablepasterowbefore tablepasterowafter")}),C(s,!0)&&e.ui.registry.addNestedMenuItem("column",{type:"nestedmenuitem",text:"Column",getSubmenuItems:d("tableinsertcolumnbefore tableinsertcolumnafter tabledeletecolumn | tablecutcolumn tablecopycolumn tablepastecolumnbefore tablepastecolumnafter")}),C(i,!0)&&e.ui.registry.addNestedMenuItem("cell",{type:"nestedmenuitem",text:"Cell",getSubmenuItems:d("tablecellprops tablemergecells tablesplitcells")}),e.ui.registry.addContextMenu("table",{update:()=>(t.resetTargets(),t.targets().fold(d(""),(e=>"caption"===te(e.element)?"tableprops deletetable":"cell row column | advtablesort | tableprops deletetable")))});const m=ko(Kt(e));0!==m.length&&e.queryCommandSupported("mceTableToggleClass")&&e.ui.registry.addNestedMenuItem("tableclass",{icon:"table-classes",text:"Table styles",getSubmenuItems:()=>Mo(e,m,"tableclass",(t=>e.execCommand("mceTableToggleClass",!1,t))),onSetup:t.onSetupTable});const u=ko(Wt(e));0!==u.length&&e.queryCommandSupported("mceTableCellToggleClass")&&e.ui.registry.addNestedMenuItem("tablecellclass",{icon:"table-cell-classes",text:"Cell styles",getSubmenuItems:()=>Mo(e,u,"tablecellclass",(t=>e.execCommand("mceTableCellToggleClass",!1,t))),onSetup:t.onSetupCellOrRow}),e.queryCommandSupported("mceTableApplyCellStyle")&&(e.ui.registry.addNestedMenuItem("tablecellvalign",{icon:"vertical-align",text:"Vertical align",getSubmenuItems:()=>Mo(e,fo,"tablecellverticalalign",Po(e,"vertical-align")),onSetup:t.onSetupCellOrRow}),e.ui.registry.addNestedMenuItem("tablecellborderwidth",{icon:"border-width",text:"Border width",getSubmenuItems:()=>Mo(e,qt(e),"tablecellborderwidth",Po(e,"border-width")),onSetup:t.onSetupCellOrRow}),e.ui.registry.addNestedMenuItem("tablecellborderstyle",{icon:"border-style",text:"Border style",getSubmenuItems:()=>Mo(e,Lt(e),"tablecellborderstyle",Po(e,"border-style")),onSetup:t.onSetupCellOrRow}),e.ui.registry.addNestedMenuItem("tablecellbackgroundcolor",{icon:"cell-background-color",text:"Background color",getSubmenuItems:()=>Eo(e,Qt(e),"background-color"),onSetup:t.onSetupCellOrRow}),e.ui.registry.addNestedMenuItem("tablecellbordercolor",{icon:"cell-border-color",text:"Border color",getSubmenuItems:()=>Eo(e,Xt(e),"border-color"),onSetup:t.onSetupCellOrRow})),n("tablecaption",{icon:"table-caption",text:"Table caption",command:"mceTableToggleCaption",onSetup:t.onSetupTableWithCaption}),n("tablerowheader",{text:"Row header",icon:"table-top-header",command:"mceTableRowType",onAction:Fo(e),onSetup:t.onSetupTableRowHeaders}),n("tablecolheader",{text:"Column header",icon:"table-left-header",command:"mceTableColType",onAction:qo(e),onSetup:t.onSetupTableRowHeaders})})(e,t),((e,t)=>{e.ui.registry.addMenuButton("table",{tooltip:"Table",icon:"table",onSetup:hl(e),fetch:e=>e("inserttable | cell row column | advtablesort | tableprops deletetable")});const o=t=>()=>e.execCommand(t),l=(t,l)=>{e.queryCommandSupported(l.command)&&e.ui.registry.addButton(t,{...l,onAction:c(l.onAction)?l.onAction:o(l.command)})},n=(t,l)=>{e.queryCommandSupported(l.command)&&e.ui.registry.addToggleButton(t,{...l,onAction:c(l.onAction)?l.onAction:o(l.command)})};l("tableprops",{tooltip:"Table properties",command:"mceTableProps",icon:"table",onSetup:t.onSetupTable}),l("tabledelete",{tooltip:"Delete table",command:"mceTableDelete",icon:"table-delete-table",onSetup:t.onSetupTable}),l("tablecellprops",{tooltip:"Cell properties",command:"mceTableCellProps",icon:"table-cell-properties",onSetup:t.onSetupCellOrRow}),l("tablemergecells",{tooltip:"Merge cells",command:"mceTableMergeCells",icon:"table-merge-cells",onSetup:t.onSetupMergeable}),l("tablesplitcells",{tooltip:"Split cell",command:"mceTableSplitCells",icon:"table-split-cells",onSetup:t.onSetupUnmergeable}),l("tableinsertrowbefore",{tooltip:"Insert row before",command:"mceTableInsertRowBefore",icon:"table-insert-row-above",onSetup:t.onSetupCellOrRow}),l("tableinsertrowafter",{tooltip:"Insert row after",command:"mceTableInsertRowAfter",icon:"table-insert-row-after",onSetup:t.onSetupCellOrRow}),l("tabledeleterow",{tooltip:"Delete row",command:"mceTableDeleteRow",icon:"table-delete-row",onSetup:t.onSetupCellOrRow}),l("tablerowprops",{tooltip:"Row properties",command:"mceTableRowProps",icon:"table-row-properties",onSetup:t.onSetupCellOrRow}),l("tableinsertcolbefore",{tooltip:"Insert column before",command:"mceTableInsertColBefore",icon:"table-insert-column-before",onSetup:t.onSetupColumn("onFirst")}),l("tableinsertcolafter",{tooltip:"Insert column after",command:"mceTableInsertColAfter",icon:"table-insert-column-after",onSetup:t.onSetupColumn("onLast")}),l("tabledeletecol",{tooltip:"Delete column",command:"mceTableDeleteCol",icon:"table-delete-column",onSetup:t.onSetupColumn("onAny")}),l("tablecutrow",{tooltip:"Cut row",command:"mceTableCutRow",icon:"cut-row",onSetup:t.onSetupCellOrRow}),l("tablecopyrow",{tooltip:"Copy row",command:"mceTableCopyRow",icon:"duplicate-row",onSetup:t.onSetupCellOrRow}),l("tablepasterowbefore",{tooltip:"Paste row before",command:"mceTablePasteRowBefore",icon:"paste-row-before",onSetup:t.onSetupPasteable(bl)}),l("tablepasterowafter",{tooltip:"Paste row after",command:"mceTablePasteRowAfter",icon:"paste-row-after",onSetup:t.onSetupPasteable(bl)}),l("tablecutcol",{tooltip:"Cut column",command:"mceTableCutCol",icon:"cut-column",onSetup:t.onSetupColumn("onAny")}),l("tablecopycol",{tooltip:"Copy column",command:"mceTableCopyCol",icon:"duplicate-column",onSetup:t.onSetupColumn("onAny")}),l("tablepastecolbefore",{tooltip:"Paste column before",command:"mceTablePasteColBefore",icon:"paste-column-before",onSetup:t.onSetupPasteableColumn(gl,"onFirst")}),l("tablepastecolafter",{tooltip:"Paste column after",command:"mceTablePasteColAfter",icon:"paste-column-after",onSetup:t.onSetupPasteableColumn(gl,"onLast")}),l("tableinsertdialog",{tooltip:"Insert table",command:"mceInsertTableDialog",icon:"table",onSetup:hl(e)});const r=ko(Kt(e));0!==r.length&&e.queryCommandSupported("mceTableToggleClass")&&e.ui.registry.addMenuButton("tableclass",{icon:"table-classes",tooltip:"Table styles",fetch:Bo(e,r,"tableclass",(t=>e.execCommand("mceTableToggleClass",!1,t))),onSetup:t.onSetupTable});const a=ko(Wt(e));0!==a.length&&e.queryCommandSupported("mceTableCellToggleClass")&&e.ui.registry.addMenuButton("tablecellclass",{icon:"table-cell-classes",tooltip:"Cell styles",fetch:Bo(e,a,"tablecellclass",(t=>e.execCommand("mceTableCellToggleClass",!1,t))),onSetup:t.onSetupCellOrRow}),e.queryCommandSupported("mceTableApplyCellStyle")&&(e.ui.registry.addMenuButton("tablecellvalign",{icon:"vertical-align",tooltip:"Vertical align",fetch:Bo(e,fo,"tablecellverticalalign",Po(e,"vertical-align")),onSetup:t.onSetupCellOrRow}),e.ui.registry.addMenuButton("tablecellborderwidth",{icon:"border-width",tooltip:"Border width",fetch:Bo(e,qt(e),"tablecellborderwidth",Po(e,"border-width")),onSetup:t.onSetupCellOrRow}),e.ui.registry.addMenuButton("tablecellborderstyle",{icon:"border-style",tooltip:"Border style",fetch:Bo(e,Lt(e),"tablecellborderstyle",Po(e,"border-style")),onSetup:t.onSetupCellOrRow}),e.ui.registry.addMenuButton("tablecellbackgroundcolor",{icon:"cell-background-color",tooltip:"Background color",fetch:t=>t(Eo(e,Qt(e),"background-color")),onSetup:t.onSetupCellOrRow}),e.ui.registry.addMenuButton("tablecellbordercolor",{icon:"cell-border-color",tooltip:"Border color",fetch:t=>t(Eo(e,Xt(e),"border-color")),onSetup:t.onSetupCellOrRow})),n("tablecaption",{tooltip:"Table caption",icon:"table-caption",command:"mceTableToggleCaption",onSetup:t.onSetupTableWithCaption}),n("tablerowheader",{tooltip:"Row header",icon:"table-top-header",command:"mceTableRowType",onAction:Fo(e),onSetup:t.onSetupTableRowHeaders}),n("tablecolheader",{tooltip:"Column header",icon:"table-left-header",command:"mceTableColType",onAction:qo(e),onSetup:t.onSetupTableColumnHeaders})})(e,t),(e=>{const t=Jt(e);t.length>0&&e.ui.registry.addContextToolbar("table",{predicate:t=>e.dom.is(t,"table")&&e.getBody().contains(t)&&e.dom.isEditable(t.parentNode),items:t,scope:"node",position:"node"})})(e)}))}();