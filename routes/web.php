<?php

use Illuminate\Support\Facades\Route;
use \App\Http\Controllers\Admin\AdminAuthController;
use App\Http\Controllers\Api\Admin\KakaoController;

/*
 * Frontend Routes
 */
Route::group(['as' => 'web.'], function () {
  includeRouteFiles(__DIR__ . '/web/');
});

/*
 * Backend Routes
 *
 * These routes can only be accessed by users with type `admin`
 */
Route::group(['prefix' => 'admin', 'namespace' => 'Admin'], function () {
  Route::group(['middleware' => ['guest','admin_check_login']], function () {
    Route::get('/login', [AdminAuthController::class, 'getLogin'])->name('adminLogin');
    Route::post('/login', [AdminAuthController::class, 'postLogin'])->name('adminLoginPost');
    Route::get('/forgot-password', [AdminAuthController::class, 'forgotPassword'])->name('forgot-password');
    Route::post('/forgot-password', [AdminAuthController::class, 'forgotPasswordPost'])->name('forgot-passwordPost');
    Route::get('/password/reset/{token}', [AdminAuthController::class, 'resetPassword'])->name('reset-password');
    Route::post('/reset-password/{token}', [AdminAuthController::class, 'resetPasswordPost'])->name('reset-passwordPost');
  });
});
Route::get('admin/api/grades/{account_homework_id}', [\App\Http\Controllers\Api\Admin\GradeController::class, 'show']);
Route::get('admin/api/vocabulary-grades/{accountId}/{homeworkId}', [\App\Http\Controllers\Api\Web\VocabularyGradeController::class, 'listGrades']);
Route::get('api/getCities/{id}',[\App\Http\Controllers\Admin\StudentController::class,'getCities']);
Route::group(['prefix' => 'admin', 'as' => 'admin.', 'middleware' => 'admin'], function () {
  includeRouteFiles(__DIR__ . '/admin/');
});

//kakao
Route::prefix('kakao')->group(function () {
    Route::get('/login', function () {
          $clientId = env('KAKAO_REST_API_KEY');
          $redirectUri = urlencode(route('kakao.callback'));
          $scope = 'friends,talk_message';
          $url = "https://kauth.kakao.com/oauth/authorize?client_id={$clientId}&response_type=code&redirect_uri={$redirectUri}&scope={$scope}";

          return redirect($url);
      });

    Route::get('/callback', [KakaoController::class, 'callback'])->name('kakao.callback');
    Route::group(['middleware' => 'super_admin'], function () {
      Route::get('/friends', [KakaoController::class, 'friends']);
      Route::get('/', [KakaoController::class, 'index'])->name('admin.kakao');
      Route::get('/check-token', [KakaoController::class, 'checkToken']);
    });
});
