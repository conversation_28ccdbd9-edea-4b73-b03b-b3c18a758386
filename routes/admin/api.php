<?php

use App\Http\Controllers\Api\Admin\AnkiFlashcardController;
use App\Http\Controllers\Api\Admin\ClassController;
use App\Http\Controllers\Api\Admin\GradeController;
use App\Http\Controllers\Api\Admin\HomeworkController;
use App\Http\Controllers\Api\Admin\QuestionController;
use App\Http\Controllers\Api\Admin\TaskController;
use App\Http\Controllers\Api\Admin\TaskHomeworkController;
use App\Http\Controllers\Api\Admin\MainHomeWorkController;
use App\Http\Controllers\Api\Admin\QuestionEvaluationCriteriaController;
use App\Http\Controllers\Api\Admin\RequestUnlockController;
use App\Http\Controllers\Api\Admin\VocabularyController;
use App\Http\Controllers\Api\Admin\TagVocabularyController;
use App\Http\Controllers\Api\Admin\VocabularyGradeController;
use Illuminate\Support\Facades\Route;

Route::group(['prefix' => 'api', 'middleware' => 'teacher_check', 'as' => 'api.'], function () {
  Route::resource('tasks', TaskController::class);
  Route::get('tasks/import/questions', [TaskController::class, 'listTaskImport']);
  Route::get('classes', [ClassController::class, 'index']);
  Route::get('class-detail', [ClassController::class, 'getDetail']);
  Route::get('session-class', [ClassController::class, 'sessions']);
  Route::resource('homeworks', HomeworkController::class);
  Route::get('homeworks-student', [HomeworkController::class, 'homeworkStudent']);
  Route::get('homeworks-student-list', [HomeworkController::class, 'homeworkStudentList']);
  Route::get('homeworks-student-detail', [HomeworkController::class, 'homeworkStudentDetail']);
  Route::post('homeworks-student-detail', [HomeworkController::class, 'updateHomeworkStatus']);
  Route::resource('task-homework', TaskHomeworkController::class);
  Route::post('task-homework/sort', [TaskHomeworkController::class, 'sort']);
  Route::post('task-homework/import', [TaskHomeworkController::class, 'import']);
  Route::resource('questions', QuestionController::class);
  Route::post('questions-upload-image', [QuestionController::class, 'uploadImage']);
  Route::resource('vocabularies', VocabularyController::class);
  Route::get('/grades', [GradeController::class, 'index']);
  Route::post('/grades/', [GradeController::class, 'save']);
  Route::post('/grades/total-score', [GradeController::class, 'updateTotalScore']);
  Route::get('/vocabularies/homework/list', [VocabularyController::class, 'vocabularies']);
  Route::get('vocabularies/class/session', [VocabularyController::class, 'getVocabularyClassSession']);
  Route::resource('main-homeworks', MainHomeWorkController::class)->except(['show']);
  Route::post('main-homeworks/{id}/duplicate',[MainHomeWorkController::class,'duplicate']);
  Route::get('main-homeworks/get-homeworks',[MainHomeWorkController::class,'getHomeworks']);
  Route::get('main-homeworks/get-main-homeworks-by-class',[MainHomeWorkController::class,'getMainHomeworksByClass']);
  Route::resource('tag-vocabularies', TagVocabularyController::class);
  Route::get('/tag-vocabularies-get-all', [TagVocabularyController::class, 'getAll']);
  Route::prefix('anki-flashcard')
    ->controller(AnkiFlashcardController::class)->group(function () {
      Route::get('/', 'index');
      Route::post('/review', 'review');
      Route::post('/homework-score', 'homeworkScore');
    });
  Route::prefix('request-unlocks')
    ->controller(RequestUnlockController::class)->group(function () {
      Route::get('/', 'index');
      Route::put('/{id}', 'update');
    });
    Route::get('/question-evaluation-criteria', [QuestionEvaluationCriteriaController::class, 'index']);
    Route::put('/vocabulary-grades', [VocabularyGradeController::class, 'updateGrades']);
    Route::post('/vocabularies/import', [VocabularyController::class, 'importCsv']);
    Route::get('/vocabularies/get/levels', [VocabularyController::class, 'getLevels']);
    Route::post('/invoices/batch', [\App\Http\Controllers\Api\Admin\InvoiceController::class, 'batch']);
});
