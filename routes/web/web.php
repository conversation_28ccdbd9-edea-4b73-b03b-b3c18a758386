<?php

use App\Http\Controllers\Web\RequestUnlockController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Web\AuthController;
use App\Http\Controllers\Web\HomeController;
use App\Http\Controllers\Web\ProductController;
use App\Http\Controllers\Web\CartController;
use App\Http\Controllers\Web\ScheduleController;
use App\Http\Controllers\Web\MessageController;
use App\Http\Controllers\Web\ReportController;
use App\Http\Controllers\UserHomeworkController;


Route::group(['middleware' => ['guest','admin_check_login']], function () {
  Route::get('/login', [AuthController::class, 'getLogin'])->name('login');
  Route::get('/forgot-password', [AuthController::class, 'forgotPassword'])->name('forgot');
  Route::post('/forgot-password', [AuthController::class, 'forgotPost'])->name('forgotPost');
  Route::get('/password/reset/{token}', [AuthController::class, 'resetPassword'])->name('reset-password');
  Route::post('/password/reset/{token}', [AuthController::class, 'resetPasswordPost'])->name('resetPasswordPost');
  Route::post('/login', [AuthController::class, 'postLogin'])->name('loginPost');
  Route::put('/login', [AuthController::class, 'chooseAccount'])->name('choose.account');
});

Route::group(['middleware' => 'user_check',], function () {
  Route::get('',[HomeController::class,'index'])->name('index');
  Route::get('/test',[HomeController::class,'test'])->name('test');
  Route::post('/logout',[AuthController::class,'webLogout'])->name('logout');
  Route::get('/products',[ProductController::class,'index'])->name('products.index');
  Route::get('/carts',[CartController::class,'index'])->name('carts.index');
  Route::post('/carts/{id}',[CartController::class,'store'])->name('carts.store');
  Route::post('/carts/update/{id}',[CartController::class,'update'])->name('carts.update');
  Route::delete('/carts',[CartController::class,'destroy'])->name('carts.destroy');
  Route::post('/checkout',[CartController::class,'checkout'])->name('carts.checkout');
  Route::group(['middleware' => 'student_check',], function () {
    Route::get('student',[HomeController::class,'student'])->name('student');
  });
  Route::group(['middleware' => 'parent_check',], function () {
    Route::get('parents',[HomeController::class,'parents'])->name('parents');
    Route::get('/reset-password',[AuthController::class,'reset'])->name('reset');
    Route::post('/reset-password',[AuthController::class,'resetPost'])->name('resetPost');
    Route::post('/ajax-post-address',[\App\Http\Controllers\Web\ProfileController::class,'updateAddress'])->name('address.update');
    Route::post('/ajax-refund-invoice',[\App\Http\Controllers\Web\InvoiceController::class,'refundInvoice'])->name('invoice.refund');
    Route::get('/ajax-get-student/{id}',[\App\Http\Controllers\Web\ProfileController::class,'getStudent'])->name('profile.students');
    Route::get('invoices',[\App\Http\Controllers\Web\InvoiceController::class,'invoices'])->name('invoices');
  });
  Route::get('/schedule',[ScheduleController::class,'index'])->name('schedule.index');
  Route::get('/ajax-get-schedule',[ScheduleController::class,'ajaxGetSchedule'])->name('schedule.ajax-get');
  Route::get('/messages',[MessageController::class,'index'])->name('messages.index');
  Route::get('/messages/{id}',[MessageController::class,'show'])->name('messages.show');
  Route::get('/grades',[\App\Http\Controllers\Web\GradeController::class,'index'])->name('grades.index');
  Route::get('/grades-lc/regular',[\App\Http\Controllers\Web\GradeLearningCenterController::class,'index'])->name('grades-lc.regular');
  Route::get('/grades-lc/private',[\App\Http\Controllers\Web\GradeLearningCenterController::class,'privateClass'])->name('grades-lc.private');
  Route::get('/ajax-get-term/{id}',[\App\Http\Controllers\Web\GradeController::class,'getTerm'])->name('grades.term');
  Route::get('/ajax-get-term-by-id/{id}',[\App\Http\Controllers\Web\GradeController::class,'getTermByStudent']);
  Route::get('/feedback-and-report',[ReportController::class,'index'])->name('reports.regular');
  Route::get('/feedback-and-report-private',[ReportController::class,'privateClass'])->name('reports.private');
  Route::get('/profile',[\App\Http\Controllers\Web\ProfileController::class,'index'])->name('profile');
  Route::get('/get-all-schedule',[\App\Http\Controllers\Web\HomeController::class,'schedules']);

  Route::prefix('homeworks')->name('homeworks.')->group(function () {
    Route::get('/', [UserHomeworkController::class, 'index'])->name('index');
    Route::get('/{homeworkId}', [UserHomeworkController::class, 'homework'])->name('detail');
  });
  Route::prefix('classwork')->name('classwork.')->group(function () {
    Route::get('/', [UserHomeworkController::class, 'classworkIndex'])->name('index');
  });
  Route::prefix('homeworks-result')->name('homeworks-result.')->group(function () {
    Route::get('/', [UserHomeworkController::class, 'indexResult'])->name('index');
    Route::get('/{homeworkId}', [UserHomeworkController::class, 'homeworkResult'])->name('detail');
  });
  Route::get('/request-unlocks', [RequestUnlockController::class, 'index'])->name('request-unlocks.index');

   Route::prefix('statistical-vocab')->name('statistical-vocab.')->group(function () {
    Route::get('/', [UserHomeworkController::class, 'indexStatisticalVocab'])->name('index');
  });
});
