<?php

use App\Http\Controllers\Admin\HomeController;
use App\Http\Controllers\Api\Web\VocabularyGradeController;
use App\Http\Controllers\Api\Web\RequestUnlockController;
use App\Http\Controllers\UserHomeworkController;
use Illuminate\Support\Facades\Route;

Route::group(['prefix' => 'api', 'middleware' => 'user_check', 'as' => 'api.'], function () {
    Route::group(['prefix' => 'homeworks'], function () {
        Route::get('/', [UserHomeworkController::class, 'listMainHomeworksForAccount']);
        Route::get('/{homeworkId}/{taskId}', [UserHomeworkController::class, 'taskDetail'])->name('task');
        Route::post('/submit-answers', [UserHomeworkController::class, 'submitAnswers']);
        Route::post('/auto-unlock', [UserHomeworkController::class, 'autoUnlockHomeworkByAccount']);
    });

    Route::group(['prefix' => 'homework-results'], function () {
        Route::get('/', [UserHomeworkController::class, 'listMainHomeworksResultForAccount']);
    });

    Route::group(['prefix' => 'classwork'], function () {
        Route::get('/', [UserHomeworkController::class, 'listMainClassworkForAccount']);
    });

    Route::group(['prefix' => 'statistical-vocab'], function () {
        Route::get('/', [UserHomeworkController::class, 'showVocabularyStats']);
    });

    Route::resource('request-unlocks', RequestUnlockController::class);
    Route::get('/vocabulary/grades/{homeworkId}', [VocabularyGradeController::class, 'listGrades']);
    Route::get('/unlock-request-status/task', [UserHomeworkController::class, 'checkStatusTaskRequest']);
    Route::post('/vocabularies/count', [UserHomeworkController::class, 'updateVocabularyCount']);

    Route::post('/setup-time', [HomeController::class, 'setupKakaoTime'])
            ->name('setupTime');
    Route::post('/request-makeup', [HomeController::class, 'requestMakeup'])
        ->name('requestMakeup');

});
