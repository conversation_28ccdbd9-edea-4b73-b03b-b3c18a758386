<?php

use App\Http\Controllers\Api\Admin\KakaoController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

//Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
//    return $request->user();
//});

Route::get('parent/{id}',[\App\Http\Controllers\Admin\StudentController::class,'getParentByID']);
Route::get('prod/{id}',[\App\Http\Controllers\Admin\InvoiceController::class,'getProdByID']);
Route::get('syncwp',[\App\Http\Controllers\Admin\InvoiceController::class,'syncWp']);
Route::get('student',[\App\Http\Controllers\Admin\StudentController::class,'getStudentByClassId']);
Route::post('/kakao/send', [KakaoController::class, 'sendMessage']);
Route::post('/kakao/delete', [KakaoController::class, 'destroy']);
