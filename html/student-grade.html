<!doctype html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Q1 Academy - student grade</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:slnt,wght@-10..0,100..900&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css" integrity="sha512-nMNlpuaDPrqlEls3IX/Q56H36qvBASwb3ipuo3MxeWbsQB1881ox0cRv7UPTgBlriqoynt35KjEwgGUeUXIPnw==" crossorigin="anonymous" referrerpolicy="no-referrer" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" integrity="sha512-1ycn6IcaQQ40/MKBW2W4Rhis/DbILU74C1vSrLJxCq57o941Ym01SwNsOMqvEBFlcgUa6xLiPY/NS5R+E6ztJQ==" crossorigin="anonymous" referrerpolicy="no-referrer" />
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fullcalendar/core@4.4.0/main.min.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fullcalendar/daygrid@4.4.0/main.min.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fullcalendar/timegrid@4.4.0/main.min.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fullcalendar/list@4.4.0/main.min.css">
  <link href="/html/src/css/output.css" rel="stylesheet">
</head>
<body>
<aside class="fixed z-50 h-screen hidden lg:flex flex-col transition">
  <div class="relative bg-neutral-800 py-[14px]">
    <a href="#" class="block h-7">
      <svg class="mx-auto logo-full" width="122" height="28" viewBox="0 0 122 28" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g clip-path="url(#clip0_175_1342)">
          <path d="M37.4784 17.8957L36.373 19.1272L35.1974 18.0778C34.7784 18.3666 34.3208 18.5933 33.8266 18.756C33.3324 18.9188 32.7954 19.0011 32.2178 19.0011C31.4623 19.0011 30.7718 18.8679 30.1474 18.6025C29.522 18.3371 28.986 17.9751 28.5376 17.5185C28.0901 17.0619 27.7403 16.5249 27.4881 15.9097C27.2369 15.2944 27.1108 14.6416 27.1108 13.9511C27.1108 13.2606 27.2369 12.6077 27.4881 11.9925C27.7403 11.3772 28.0942 10.8383 28.5518 10.3766C29.0084 9.91491 29.5494 9.54881 30.1748 9.27831C30.7992 9.00781 31.4897 8.87256 32.2453 8.87256C33.0009 8.87256 33.6903 9.00578 34.3157 9.27119C34.9401 9.53661 35.4771 9.89864 35.9245 10.3552C36.372 10.8118 36.7218 11.3488 36.974 11.964C37.2262 12.5792 37.3523 13.2321 37.3523 13.9226C37.3523 14.4819 37.2658 15.0189 37.093 15.5314C36.9201 16.0449 36.675 16.5158 36.3588 16.9439L37.4784 17.8957ZM32.2463 15.5171L33.3507 14.2582L35.0429 15.7968C35.2117 15.5354 35.3367 15.2487 35.4211 14.9365C35.5045 14.6243 35.5472 14.2958 35.5472 13.9501C35.5472 13.4741 35.4649 13.0247 35.3022 12.5996C35.1384 12.1755 34.9106 11.8044 34.6168 11.4871C34.3229 11.1708 33.971 10.9186 33.5602 10.7315C33.1504 10.5454 32.7019 10.4518 32.2168 10.4518C31.7317 10.4518 31.2863 10.5423 30.8806 10.7244C30.4748 10.9064 30.127 11.1535 29.8382 11.4657C29.5494 11.7789 29.3226 12.1471 29.1599 12.5711C28.9962 12.9952 28.9149 13.4457 28.9149 13.9206C28.9149 14.3955 28.9962 14.846 29.1599 15.27C29.3226 15.6941 29.5515 16.0653 29.8454 16.3825C30.1392 16.6998 30.4911 16.952 30.9009 17.1381C31.3107 17.3242 31.7592 17.4178 32.2443 17.4178C32.5707 17.4178 32.8738 17.3761 33.1534 17.2917C33.4331 17.2083 33.6893 17.0913 33.9222 16.9419L32.2443 15.5151L32.2463 15.5171Z" fill="white"/>
          <path d="M41.1713 8.97119H42.3743V18.8343H40.6679V10.7203L38.9045 11.2094L38.5415 9.81015L41.1713 8.97119Z" fill="white"/>
          <path d="M58.5456 18.8334H56.7263L55.7328 16.469H51.1017L50.0939 18.8334H48.3306L52.6403 8.97021H54.2348L58.5446 18.8334H58.5456ZM53.4111 11.0264L51.7332 14.9436H55.1043L53.4121 11.0264H53.4111Z" fill="white"/>
          <path d="M67.3528 17.9872C67.0772 18.1967 66.7812 18.3787 66.465 18.5333C66.1477 18.6868 65.7999 18.8038 65.4226 18.8831C65.0454 18.9624 64.6274 19.0021 64.1708 19.0021C63.4529 19.0021 62.7898 18.8709 62.1847 18.6105C61.5787 18.3492 61.0539 17.9933 60.6105 17.5407C60.1672 17.0882 59.8204 16.5543 59.5682 15.9391C59.317 15.3238 59.1909 14.6608 59.1909 13.953C59.1909 13.2453 59.314 12.5965 59.5621 11.9802C59.8092 11.365 60.157 10.826 60.6044 10.3643C61.0519 9.90262 61.5837 9.53856 62.199 9.27315C62.8142 9.00773 63.4956 8.87451 64.241 8.87451C64.6884 8.87451 65.0962 8.91214 65.4653 8.98637C65.8335 9.06061 66.1721 9.16637 66.4792 9.30162C66.7873 9.43687 67.0711 9.59755 67.3334 9.78466C67.5948 9.97076 67.8419 10.1762 68.0748 10.3999L66.9694 11.6731C66.5779 11.309 66.163 11.0151 65.7247 10.7914C65.2864 10.5677 64.7871 10.4558 64.2277 10.4558C63.761 10.4558 63.3298 10.5463 62.9332 10.7284C62.5366 10.9104 62.1939 11.1575 61.9051 11.4697C61.6163 11.7829 61.3915 12.148 61.2329 12.568C61.0743 12.988 60.9949 13.4405 60.9949 13.9246C60.9949 14.4086 61.0743 14.8642 61.2329 15.2883C61.3915 15.7133 61.6153 16.0835 61.9051 16.4008C62.1939 16.7181 62.5366 16.9672 62.9332 17.1492C63.3288 17.3313 63.761 17.4218 64.2277 17.4218C64.8247 17.4218 65.3372 17.3079 65.7664 17.0791C66.1955 16.8513 66.6195 16.5401 67.0385 16.1486L68.1439 17.2682C67.8927 17.5387 67.6283 17.7787 67.3538 17.9882L67.3528 17.9872Z" fill="white"/>
          <path d="M79.321 18.8334H77.5017L76.5082 16.469H71.8771L70.8693 18.8334H69.106L73.4157 8.97021H75.0102L79.3199 18.8334H79.321ZM74.1865 11.0264L72.5086 14.9436H75.8797L74.1875 11.0264H74.1865Z" fill="white"/>
          <path d="M89.4766 15.861C89.2254 16.463 88.8675 16.9827 88.4068 17.421C87.9451 17.8593 87.3949 18.204 86.7563 18.4562C86.1177 18.7084 85.415 18.8345 84.6513 18.8345H80.9995V9.0415H84.6513C85.416 9.0415 86.1177 9.16557 86.7563 9.41268C87.3949 9.65979 87.9451 10.0025 88.4068 10.4408C88.8685 10.8791 89.2254 11.3967 89.4766 11.9936C89.7278 12.5906 89.8539 13.2343 89.8539 13.9248C89.8539 14.6153 89.7278 15.261 89.4766 15.862V15.861ZM87.8048 12.6099C87.641 12.2021 87.4122 11.8492 87.1173 11.5492C86.8224 11.2503 86.4645 11.0154 86.0445 10.8465C85.6235 10.6777 85.1567 10.5933 84.6421 10.5933H82.7069V17.2806H84.6421C85.1567 17.2806 85.6245 17.1993 86.0445 17.0345C86.4655 16.8708 86.8234 16.641 87.1173 16.3461C87.4112 16.0512 87.64 15.6993 87.8048 15.2925C87.9685 14.8848 88.0499 14.4383 88.0499 13.9512C88.0499 13.4641 87.9675 13.0167 87.8048 12.6099Z" fill="white"/>
          <path d="M99.1856 10.5934H93.6454V13.1113H98.5561V14.6642H93.6454V17.2797H99.2557V18.8325H91.939V9.03955H99.1856V10.5924V10.5934Z" fill="white"/>
          <path d="M106.139 16.4269H106.083L103.019 11.8386V18.834H101.313V9.04102H103.145L106.125 13.6721L109.105 9.04102H110.937V18.834H109.231V11.8111L106.139 16.428V16.4269Z" fill="white"/>
          <path d="M118.072 18.833H116.337V14.9718L112.449 9.04004H114.478L117.219 13.3772L119.989 9.04004H121.961L118.072 14.9301V18.833Z" fill="white"/>
          <path d="M18.4142 5.74863C18.0288 6.03134 17.5966 6.29777 17.1309 6.53878C16.9255 6.64658 16.7119 6.74624 16.4984 6.84386C15.7641 7.16826 14.9628 7.43673 14.1116 7.6452V2.82701H12.8669C12.8669 3.23378 12.5252 3.64462 11.9273 3.95478C11.3619 4.24969 10.6317 4.41138 9.87208 4.41138C9.11244 4.41138 8.38229 4.24867 7.81485 3.95376C7.21791 3.64462 6.87623 3.23378 6.87623 2.82701C6.87623 2.26872 7.85756 1.24468 9.46023 1.24468C10.5107 1.24468 10.9907 1.54772 11.0853 1.81314C11.0965 1.84364 11.1026 1.87517 11.1026 1.90771C11.1026 2.22194 10.5768 2.57278 9.8731 2.57278V3.81749C11.2602 3.81749 12.3473 2.97853 12.3473 1.90771C12.3473 1.72873 12.3158 1.55179 12.2568 1.39111C12.1083 0.973156 11.5398 0.000976562 9.46125 0.000976562C7.26978 0.000976562 5.63253 1.49382 5.63253 2.82803C5.63253 3.7158 6.22031 4.52934 7.24232 5.05916C7.98162 5.44457 8.91618 5.65609 9.87208 5.65609C10.828 5.65609 11.7625 5.44356 12.5008 5.05916C12.63 4.99204 12.753 4.92086 12.8679 4.84459V7.90553V12.7634C12.4937 12.9088 12.1083 13.0268 11.7148 13.1142C11.6883 13.1203 11.6629 13.1254 11.6364 13.1305C11.1626 12.682 10.528 12.4237 9.87412 12.4237C9.22024 12.4237 8.58262 12.681 8.10874 13.1305C8.0823 13.1244 8.05586 13.1203 8.0284 13.1132C7.63587 13.0257 7.25147 12.9078 6.87724 12.7624V6.72081C6.73894 6.66285 6.60572 6.59878 6.47556 6.53268C6.17048 6.37506 5.88777 6.19405 5.63253 5.99473V7.64418C4.78136 7.43673 3.98003 7.16724 3.24581 6.84284C3.03225 6.74624 2.8187 6.64556 2.61328 6.53777C2.15058 6.29981 1.71839 6.03032 1.32687 5.74457L0.337402 5.02255V17.1443C0.337402 21.1947 3.14005 24.8282 5.91523 26.6332C6.23658 26.8407 6.55793 27.0298 6.87724 27.1905C7.92264 27.7223 8.94262 28 9.87412 28C10.8056 28 11.8256 27.7223 12.8679 27.1905C13.1873 27.0298 13.5086 26.8417 13.83 26.6332C16.6051 24.8282 19.4047 21.1967 19.4047 17.1443V5.01747L18.4153 5.74965L18.4142 5.74863ZM12.8669 19.5066L11.8195 16.6267C11.8968 16.5382 11.9639 16.4436 12.0269 16.347C12.0269 16.347 12.0269 16.346 12.028 16.345C12.3127 16.2738 12.5934 16.1894 12.8669 16.0908V19.5076V19.5066ZM6.87623 16.0897C7.14978 16.1884 7.42943 16.2738 7.71519 16.344C7.71519 16.344 7.71519 16.345 7.7162 16.346C7.78027 16.4457 7.84942 16.5402 7.92162 16.6287L6.87623 19.5066V16.0897ZM8.3345 17.0172C8.34873 17.0284 8.36195 17.0395 8.37924 17.0507C8.40466 17.0701 8.43212 17.0863 8.45958 17.1067C8.50941 17.1423 8.56229 17.1728 8.6172 17.2033C8.6172 17.2063 8.62025 17.2063 8.62025 17.2063C8.68127 17.2399 8.74228 17.2724 8.80635 17.3009C8.91414 17.3507 9.02804 17.3955 9.14498 17.428C9.23142 17.4534 9.32193 17.4748 9.41345 17.4921C9.42159 17.4951 9.43277 17.4951 9.44091 17.4972L8.74228 19.4151L8.3406 18.752L7.61146 19.0012L8.3345 17.0162V17.0172ZM10.3246 17.4911C10.4517 17.4687 10.5768 17.4382 10.6988 17.3965C10.7324 17.3853 10.7629 17.3711 10.7934 17.3609C10.8321 17.3467 10.8707 17.3304 10.9073 17.3141C10.9826 17.2806 11.0538 17.245 11.1239 17.2033C11.1961 17.1646 11.2622 17.1199 11.3293 17.0762C11.3324 17.0731 11.3344 17.0711 11.3344 17.0711C11.3598 17.0548 11.3843 17.0375 11.4097 17.0182L12.1327 19.0032L11.4005 18.7541L11.0009 19.4141L10.3022 17.4982C10.3104 17.4982 10.3165 17.4951 10.3246 17.4931V17.4911ZM12.8669 15.5355C12.6849 15.6057 12.5008 15.6708 12.3127 15.7277C12.388 15.4867 12.4266 15.2345 12.4266 14.9793C12.4266 14.4749 12.2802 13.9908 12.0036 13.5779C12.2964 13.5068 12.5852 13.4183 12.8679 13.3166V15.5355H12.8669ZM9.8731 12.9424C10.4416 12.9424 10.9704 13.1722 11.3588 13.5881C11.7137 13.9705 11.908 14.4637 11.908 14.9793C11.908 15.3342 11.8134 15.683 11.6364 15.9911C11.5531 16.1375 11.4534 16.2708 11.3375 16.3908C10.9663 16.7762 10.47 16.9948 9.93717 17.0121L9.88124 17.0151H9.86192L9.80598 17.0121C9.47345 17.0009 9.15414 16.9124 8.87143 16.7518C8.75753 16.6877 8.64974 16.6135 8.55008 16.527C8.50025 16.4853 8.45347 16.4385 8.40568 16.3908V16.3877C8.28873 16.2718 8.18907 16.1386 8.1067 15.9911C7.92976 15.683 7.83519 15.3342 7.83519 14.9793C7.83519 14.4637 8.03247 13.9705 8.38432 13.5901C8.77279 13.1712 9.30159 12.9413 9.8731 12.9413V12.9424ZM7.74061 13.5779C7.46401 13.9908 7.31757 14.4749 7.31757 14.9793C7.31757 15.2345 7.35621 15.4867 7.43452 15.7277C7.24537 15.6708 7.06029 15.6057 6.87724 15.5355V13.3166C7.15995 13.4193 7.44875 13.5068 7.74163 13.5779H7.74061ZM5.63151 24.8841C3.4919 23.1269 1.5811 20.2663 1.5811 17.1443V7.39198C1.73059 7.48046 1.88618 7.56384 2.04177 7.64418C2.26854 7.76113 2.50447 7.87401 2.74039 7.97977C3.6302 8.37332 4.60035 8.68958 5.63151 8.92246V24.8841ZM9.8731 26.7552C8.98838 26.7552 7.93586 26.4064 6.87623 25.7688V19.8005L8.11281 19.3795L8.85821 20.6049L9.87005 17.8277L10.8849 20.6079L11.6283 19.3795L12.8669 19.8005V25.7688C11.8083 26.4064 10.7548 26.7552 9.8731 26.7552ZM18.159 17.1443C18.159 20.2652 16.2523 23.1269 14.1116 24.8841V8.92246C15.1428 8.68958 16.1129 8.37332 17.0028 7.97977C17.2387 7.87401 17.4736 7.76113 17.7044 7.64418C17.86 7.56384 18.0126 7.48046 18.159 7.39504V17.1443Z" fill="white"/>
          <path d="M5.15215 4.04492H3.66846V5.52861H5.15215V4.04492Z" fill="white"/>
          <path d="M3.35887 2.66504H2.30127V3.72264H3.35887V2.66504Z" fill="white"/>
        </g>
        <defs>
          <clipPath id="clip0_175_1342">
            <rect width="121.624" height="28" fill="white" transform="translate(0.337402)"/>
          </clipPath>
        </defs>
      </svg>
      <svg class="mx-auto hidden logo-small" width="20" height="29" viewBox="0 0 20 29" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M18.5432 6.06358C18.1578 6.34628 17.7256 6.61272 17.2598 6.85373C17.0544 6.96152 16.8409 7.06118 16.6273 7.1588C15.8931 7.4832 15.0917 7.75167 14.2406 7.96014V3.14195H12.9959C12.9959 3.54872 12.6542 3.95956 12.0562 4.26972C11.4908 4.56463 10.7607 4.72632 10.001 4.72632C9.24138 4.72632 8.51123 4.56361 7.94378 4.2687C7.34685 3.95956 7.00516 3.54872 7.00516 3.14195C7.00516 2.58366 7.98649 1.55962 9.58917 1.55962C10.6396 1.55962 11.1196 1.86266 11.2142 2.12808C11.2254 2.15859 11.2315 2.19011 11.2315 2.22265C11.2315 2.53688 10.7057 2.88772 10.002 2.88772V4.13244C11.3891 4.13244 12.4762 3.29347 12.4762 2.22265C12.4762 2.04367 12.4447 1.86673 12.3857 1.70605C12.2372 1.2881 11.6688 0.315918 9.59018 0.315918C7.39871 0.315918 5.76146 1.80876 5.76146 3.14297C5.76146 4.03074 6.34925 4.84428 7.37126 5.3741C8.11056 5.75952 9.04511 5.97104 10.001 5.97104C10.9569 5.97104 11.8915 5.7585 12.6298 5.3741C12.7589 5.30698 12.882 5.2358 12.9969 5.15953V8.22047V13.0783C12.6227 13.2237 12.2372 13.3417 11.8437 13.4292C11.8172 13.4353 11.7918 13.4403 11.7654 13.4454C11.2915 12.997 10.6569 12.7387 10.0031 12.7387C9.34917 12.7387 8.71156 12.996 8.23767 13.4454C8.21123 13.4393 8.18479 13.4353 8.15734 13.4281C7.7648 13.3407 7.38041 13.2227 7.00618 13.0773V7.03576C6.86788 6.97779 6.73466 6.91372 6.60449 6.84762C6.29942 6.69 6.01671 6.50899 5.76146 6.30967V7.95912C4.9103 7.75167 4.10896 7.48219 3.37474 7.15779C3.16119 7.06118 2.94763 6.9605 2.74222 6.85271C2.27952 6.61475 1.84732 6.34526 1.45581 6.05951L0.466339 5.33749V17.4592C0.466339 21.5096 3.26898 25.1431 6.04417 26.9482C6.36552 27.1556 6.68686 27.3448 7.00618 27.5054C8.05158 28.0373 9.07155 28.3149 10.0031 28.3149C10.9346 28.3149 11.9545 28.0373 12.9969 27.5054C13.3162 27.3448 13.6375 27.1566 13.9589 26.9482C16.7341 25.1431 19.5337 21.5117 19.5337 17.4592V5.33241L18.5442 6.06459L18.5432 6.06358ZM12.9959 19.8216L11.9484 16.9416C12.0257 16.8531 12.0928 16.7586 12.1559 16.662C12.1559 16.662 12.1559 16.6609 12.1569 16.6599C12.4416 16.5887 12.7223 16.5043 12.9959 16.4057V19.8226V19.8216ZM7.00516 16.4047C7.27872 16.5033 7.55837 16.5887 7.84413 16.6589C7.84413 16.6589 7.84412 16.6599 7.84514 16.6609C7.90921 16.7606 7.97836 16.8552 8.05056 16.9437L7.00516 19.8216V16.4047ZM8.46343 17.3321C8.47767 17.3433 8.49089 17.3545 8.50818 17.3657C8.5336 17.385 8.56106 17.4013 8.58851 17.4216C8.63834 17.4572 8.69122 17.4877 8.74614 17.5182C8.74614 17.5213 8.74919 17.5213 8.74919 17.5213C8.8102 17.5548 8.87122 17.5874 8.93528 17.6158C9.04308 17.6657 9.15697 17.7104 9.27392 17.743C9.36036 17.7684 9.45087 17.7897 9.54239 17.807C9.55052 17.8101 9.56171 17.8101 9.56985 17.8121L8.87122 19.73L8.46953 19.067L7.7404 19.3161L8.46343 17.3311V17.3321ZM10.4536 17.806C10.5807 17.7836 10.7057 17.7531 10.8278 17.7114C10.8613 17.7002 10.8918 17.686 10.9224 17.6758C10.961 17.6616 10.9996 17.6453 11.0363 17.6291C11.1115 17.5955 11.1827 17.5599 11.2529 17.5182C11.3251 17.4796 11.3912 17.4348 11.4583 17.3911C11.4613 17.388 11.4634 17.386 11.4634 17.386C11.4888 17.3697 11.5132 17.3525 11.5386 17.3331L12.2616 19.3182L11.5295 19.069L11.1298 19.729L10.4312 17.8131C10.4393 17.8131 10.4454 17.8101 10.4536 17.808V17.806ZM12.9959 15.8505C12.8138 15.9206 12.6298 15.9857 12.4416 16.0427C12.5169 15.8016 12.5555 15.5494 12.5555 15.2942C12.5555 14.7898 12.4091 14.3058 12.1325 13.8929C12.4254 13.8217 12.7142 13.7332 12.9969 13.6315V15.8505H12.9959ZM10.002 13.2573C10.5705 13.2573 11.0993 13.4871 11.4878 13.903C11.8427 14.2854 12.0369 14.7786 12.0369 15.2942C12.0369 15.6491 11.9423 15.9979 11.7654 16.306C11.682 16.4525 11.5823 16.5857 11.4664 16.7057C11.0952 17.0911 10.599 17.3097 10.0661 17.327L10.0102 17.3301H9.99085L9.93492 17.327C9.60239 17.3158 9.28307 17.2274 9.00037 17.0667C8.88647 17.0026 8.77868 16.9284 8.67902 16.842C8.62919 16.8003 8.58241 16.7535 8.53462 16.7057V16.7026C8.41767 16.5867 8.31801 16.4535 8.23564 16.306C8.0587 15.9979 7.96412 15.6491 7.96412 15.2942C7.96412 14.7786 8.16141 14.2854 8.51326 13.9051C8.90173 13.4861 9.43053 13.2563 10.002 13.2563V13.2573ZM7.86955 13.8929C7.59295 14.3058 7.44651 14.7898 7.44651 15.2942C7.44651 15.5494 7.48515 15.8016 7.56345 16.0427C7.37431 15.9857 7.18923 15.9206 7.00618 15.8505V13.6315C7.28888 13.7342 7.57769 13.8217 7.87056 13.8929H7.86955ZM5.76045 25.199C3.62084 23.4418 1.71004 20.5812 1.71004 17.4592V7.70693C1.85953 7.7954 2.01511 7.87879 2.1707 7.95912C2.39748 8.07607 2.6334 8.18895 2.86933 8.29471C3.75914 8.68826 4.72929 9.00452 5.76045 9.2374V25.199ZM10.002 27.0702C9.11731 27.0702 8.0648 26.7214 7.00516 26.0838V20.1154L8.24174 19.6944L8.98715 20.9198L9.99899 18.1426L11.0139 20.9229L11.7572 19.6944L12.9959 20.1154V26.0838C11.9372 26.7214 10.8837 27.0702 10.002 27.0702ZM18.2879 17.4592C18.2879 20.5802 16.3812 23.4418 14.2406 25.199V9.2374C15.2717 9.00452 16.2419 8.68826 17.1317 8.29471C17.3676 8.18895 17.6025 8.07607 17.8334 7.95912C17.989 7.87879 18.1415 7.7954 18.2879 7.70998V17.4592Z" fill="white"/>
        <path d="M5.28144 4.35986H3.79774V5.84356H5.28144V4.35986Z" fill="white"/>
        <path d="M3.48762 2.97998H2.43002V4.03758H3.48762V2.97998Z" fill="white"/>
      </svg>
    </a>
    <div class="icon-arrow-aside flex items-center justify-center size-8 p-2 rounded-full border border-neutral-100 bg-white
                absolute top-[38px] -right-4 cursor-pointer">
      <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g clip-path="url(#clip0_175_1402)">
          <path d="M6.04942 9.03803L11.1159 14.1045L10.643 14.5774L4.7499 8.68429L10.6375 2.79076L11.1149 3.26547L6.04942 8.33092L5.69586 8.68447L6.04942 9.03803Z" stroke="#10C285"/>
        </g>
        <defs>
          <clipPath id="clip0_175_1402">
            <rect width="16" height="16" fill="white" transform="translate(0.149414 0.68457)"/>
          </clipPath>
        </defs>
      </svg>
    </div>
  </div>
  <div class="shadow-424D5B14 bg-dark flex-1 py-6 px-4">
    <ul>
      <li class="mb-2 last:mb-0">
        <a href="./student-dashboard.html" class="flex items-center gap-3 p-3 rounded hover:bg-green group">
          <!--        khi ở trạng thái active thêm class  bg-green-->
          <div class="w-4 h-4 flex items-center justify-center">
            <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
              <g clip-path="url(#clip0_175_7829)">
                <path class="group-hover:fill-white" d="M3.33333 11.1488H8.33333C8.79167 11.1488 9.16667 10.7738 9.16667 10.3154V3.64876C9.16667 3.19043 8.79167 2.81543 8.33333 2.81543H3.33333C2.875 2.81543 2.5 3.19043 2.5 3.64876V10.3154C2.5 10.7738 2.875 11.1488 3.33333 11.1488ZM3.33333 17.8154H8.33333C8.79167 17.8154 9.16667 17.4404 9.16667 16.9821V13.6488C9.16667 13.1904 8.79167 12.8154 8.33333 12.8154H3.33333C2.875 12.8154 2.5 13.1904 2.5 13.6488V16.9821C2.5 17.4404 2.875 17.8154 3.33333 17.8154ZM11.6667 17.8154H16.6667C17.125 17.8154 17.5 17.4404 17.5 16.9821V10.3154C17.5 9.8571 17.125 9.4821 16.6667 9.4821H11.6667C11.2083 9.4821 10.8333 9.8571 10.8333 10.3154V16.9821C10.8333 17.4404 11.2083 17.8154 11.6667 17.8154ZM10.8333 3.64876V6.9821C10.8333 7.44043 11.2083 7.81543 11.6667 7.81543H16.6667C17.125 7.81543 17.5 7.44043 17.5 6.9821V3.64876C17.5 3.19043 17.125 2.81543 16.6667 2.81543H11.6667C11.2083 2.81543 10.8333 3.19043 10.8333 3.64876Z" fill="#ABB3BF"/>
                <!-- khi ở trạng thái active fill của path đổi sang màu #fff -->
              </g>
              <defs>
                <clipPath id="clip0_175_7829">
                  <rect width="20" height="20" fill="white" transform="translate(0 0.31543)"/>
                </clipPath>
              </defs>
            </svg>
          </div>
          <div class="menu__text text-12-16 font-semibold text-neutral-400 group-hover:text-white group-hover:font-semibold">
            <!--        khi ở trạng thái active đổi class text-neutral-400 thành text-white-->
            Dashboard
          </div>
        </a>
      </li>
      <li class="mb-2 last:mb-0">
        <a href="./student-message.html" class="flex items-center gap-3 p-3 rounded hover:bg-green group">
          <div class="w-4 h-4 flex items-center justify-center">
            <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
              <g clip-path="url(#clip0_175_5101)">
                <path class="group-hover:fill-white" d="M16.6667 1.98193H3.33333C2.41666 1.98193 1.675 2.73193 1.675 3.6486L1.66666 18.6486L5 15.3153H16.6667C17.5833 15.3153 18.3333 14.5653 18.3333 13.6486V3.6486C18.3333 2.73193 17.5833 1.98193 16.6667 1.98193ZM5.83333 7.81527H14.1667C14.625 7.81527 15 8.19027 15 8.6486C15 9.10693 14.625 9.48193 14.1667 9.48193H5.83333C5.375 9.48193 5 9.10693 5 8.6486C5 8.19027 5.375 7.81527 5.83333 7.81527ZM10.8333 11.9819H5.83333C5.375 11.9819 5 11.6069 5 11.1486C5 10.6903 5.375 10.3153 5.83333 10.3153H10.8333C11.2917 10.3153 11.6667 10.6903 11.6667 11.1486C11.6667 11.6069 11.2917 11.9819 10.8333 11.9819ZM14.1667 6.98193H5.83333C5.375 6.98193 5 6.60693 5 6.1486C5 5.69027 5.375 5.31527 5.83333 5.31527H14.1667C14.625 5.31527 15 5.69027 15 6.1486C15 6.60693 14.625 6.98193 14.1667 6.98193Z" fill="#ABB3BF" />
              </g>
              <defs>
                <clipPath id="clip0_175_5101">
                  <rect width="20" height="20" fill="white" transform="translate(0 0.31543)"/>
                </clipPath>
              </defs>
            </svg>
          </div>
          <div class="menu__text text-12-16 text-neutral-400 group-hover:text-white group-hover:font-semibold">
            Message
          </div>
        </a>
      </li>
      <li class="mb-2 last:mb-0">
        <a href="./student-schedule.html" class="flex items-center gap-3 p-3 rounded hover:bg-green group">
          <div class="w-4 h-4 flex items-center justify-center">
            <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path class="group-hover:fill-white" d="M14.1667 2.81559H17.5C17.721 2.81559 17.933 2.90339 18.0893 3.05967C18.2455 3.21595 18.3333 3.42791 18.3333 3.64893V16.9823C18.3333 17.2033 18.2455 17.4152 18.0893 17.5715C17.933 17.7278 17.721 17.8156 17.5 17.8156H2.5C2.27898 17.8156 2.06702 17.7278 1.91074 17.5715C1.75446 17.4152 1.66666 17.2033 1.66666 16.9823V3.64893C1.66666 3.42791 1.75446 3.21595 1.91074 3.05967C2.06702 2.90339 2.27898 2.81559 2.5 2.81559H5.83333V1.14893H7.5V2.81559H12.5V1.14893H14.1667V2.81559ZM3.33333 7.81559V16.1489H16.6667V7.81559H3.33333ZM5 11.1489H9.16666V14.4823H5V11.1489Z" fill="#ABB3BF"/>
            </svg>
          </div>
          <div class="menu__text text-12-16 text-neutral-400 group-hover:text-white group-hover:font-semibold">
            Schedule
          </div>
        </a>
      </li>
      <li class="mb-2 last:mb-0">
        <a href="./student-grade.html" class="flex items-center gap-3 p-3 rounded hover:bg-green group bg-green">
          <div class="w-4 h-4 flex items-center justify-center">
            <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M10 14.7072L14.3083 17.3072C14.625 17.4989 15.0167 17.2155 14.9333 16.8572L13.7917 11.9572L17.5917 8.66553C17.8667 8.42386 17.725 7.96553 17.35 7.93219L12.3417 7.50719L10.3833 2.89053C10.2417 2.54886 9.75833 2.54886 9.61666 2.89053L7.65833 7.50719L2.65 7.93219C2.28333 7.96553 2.13333 8.42386 2.41666 8.66553L6.21667 11.9572L5.075 16.8572C4.99167 17.2155 5.38333 17.4989 5.7 17.3072L10 14.7072Z" fill="white"/>
            </svg>
          </div>
          <div class="menu__text text-12-16 text-white">
            Grade
          </div>
        </a>
      </li>
      <li class="mb-2 last:mb-0">
        <a href="./student-feedback-report.html" class="flex items-center gap-3 p-3 rounded hover:bg-green group">
          <div class="w-4 h-4 flex items-center justify-center">
            <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path class="group-hover:fill-white" d="M15.8333 18.6486H4.16666C3.50362 18.6486 2.86774 18.3852 2.3989 17.9164C1.93006 17.4475 1.66666 16.8116 1.66666 16.1486V2.81527C1.66666 2.59425 1.75446 2.38229 1.91074 2.22601C2.06702 2.06973 2.27898 1.98193 2.5 1.98193H14.1667C14.3877 1.98193 14.5996 2.06973 14.7559 2.22601C14.9122 2.38229 15 2.59425 15 2.81527V12.8153H18.3333V16.1486C18.3333 16.8116 18.0699 17.4475 17.6011 17.9164C17.1323 18.3852 16.4964 18.6486 15.8333 18.6486ZM15 14.4819V16.1486C15 16.3696 15.0878 16.5816 15.2441 16.7379C15.4004 16.8941 15.6123 16.9819 15.8333 16.9819C16.0543 16.9819 16.2663 16.8941 16.4226 16.7379C16.5789 16.5816 16.6667 16.3696 16.6667 16.1486V14.4819H15ZM5 6.1486V7.81527H11.6667V6.1486H5ZM5 9.48193V11.1486H11.6667V9.48193H5ZM5 12.8153V14.4819H9.16666V12.8153H5Z" fill="#ABB3BF"/>
            </svg>
          </div>
          <div class="menu__text text-12-16 text-neutral-400 group-hover:text-white group-hover:font-semibold">
            Feedback Report
          </div>
        </a>
      </li>
      <li class="mb-2 last:mb-0">
        <a href="./parent-invoice.html" class="flex items-center gap-3 p-3 rounded hover:bg-green group">
          <div class="w-4 h-4 flex items-center justify-center">
            <svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path class="group-hover:fill-white" d="M15.3333 5.6486V12.3153C15.3333 13.0486 14.7333 13.6486 14 13.6486H3.33332C2.96666 13.6486 2.66666 13.3486 2.66666 12.9819C2.66666 12.6153 2.96666 12.3153 3.33332 12.3153H14V5.6486C14 5.28193 14.3 4.98193 14.6667 4.98193C15.0333 4.98193 15.3333 5.28193 15.3333 5.6486ZM2.66666 10.9819C1.55999 10.9819 0.666656 10.0886 0.666656 8.98193V4.98193C0.666656 3.87527 1.55999 2.98193 2.66666 2.98193H10.6667C11.7733 2.98193 12.6667 3.87527 12.6667 4.98193V9.6486C12.6667 10.3819 12.0667 10.9819 11.3333 10.9819H2.66666ZM4.66666 6.98193C4.66666 8.0886 5.55999 8.98193 6.66666 8.98193C7.77332 8.98193 8.66666 8.0886 8.66666 6.98193C8.66666 5.87527 7.77332 4.98193 6.66666 4.98193C5.55999 4.98193 4.66666 5.87527 4.66666 6.98193Z" fill="#ABB3BF"/>
            </svg>
          </div>
          <div class="menu__text text-12-16 text-neutral-400 group-hover:text-white group-hover:font-semibold">
            Invoice
          </div>
        </a>
      </li>
      <li class="mb-2 last:mb-0">
        <a href="./student-profile.html" class="flex items-center gap-3 p-3 rounded hover:bg-green group">
          <div class="w-4 h-4 flex items-center justify-center">
            <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path class="group-hover:fill-white" d="M10 1.98193C5.4 1.98193 1.66666 5.71527 1.66666 10.3153C1.66666 14.9153 5.4 18.6486 10 18.6486C14.6 18.6486 18.3333 14.9153 18.3333 10.3153C18.3333 5.71527 14.6 1.98193 10 1.98193ZM10 4.48193C11.3833 4.48193 12.5 5.5986 12.5 6.98193C12.5 8.36527 11.3833 9.48193 10 9.48193C8.61666 9.48193 7.5 8.36527 7.5 6.98193C7.5 5.5986 8.61666 4.48193 10 4.48193ZM10 16.3153C7.91666 16.3153 6.075 15.2486 5 13.6319C5.025 11.9736 8.33333 11.0653 10 11.0653C11.6583 11.0653 14.975 11.9736 15 13.6319C13.925 15.2486 12.0833 16.3153 10 16.3153Z" fill="#ABB3BF"/>
            </svg>
          </div>
          <div class="menu__text text-12-16 text-neutral-400 group-hover:text-white group-hover:font-semibold">
            Profile
          </div>
        </a>
      </li>
    </ul>
  </div>
</aside>
<div class="side-bar w-[264px] flex flex-col transition fixed z-[52] top-0 bottom-0 -translate-x-full">
  <div class="bg-neutral-800 py-[14px] px-4">
    <a href="#">
      <div class="w-[122px] h-7">
        <svg class="logo-full" width="122" height="28" viewBox="0 0 122 28" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g clip-path="url(#clip0_175_1342)">
            <path d="M37.4784 17.8957L36.373 19.1272L35.1974 18.0778C34.7784 18.3666 34.3208 18.5933 33.8266 18.756C33.3324 18.9188 32.7954 19.0011 32.2178 19.0011C31.4623 19.0011 30.7718 18.8679 30.1474 18.6025C29.522 18.3371 28.986 17.9751 28.5376 17.5185C28.0901 17.0619 27.7403 16.5249 27.4881 15.9097C27.2369 15.2944 27.1108 14.6416 27.1108 13.9511C27.1108 13.2606 27.2369 12.6077 27.4881 11.9925C27.7403 11.3772 28.0942 10.8383 28.5518 10.3766C29.0084 9.91491 29.5494 9.54881 30.1748 9.27831C30.7992 9.00781 31.4897 8.87256 32.2453 8.87256C33.0009 8.87256 33.6903 9.00578 34.3157 9.27119C34.9401 9.53661 35.4771 9.89864 35.9245 10.3552C36.372 10.8118 36.7218 11.3488 36.974 11.964C37.2262 12.5792 37.3523 13.2321 37.3523 13.9226C37.3523 14.4819 37.2658 15.0189 37.093 15.5314C36.9201 16.0449 36.675 16.5158 36.3588 16.9439L37.4784 17.8957ZM32.2463 15.5171L33.3507 14.2582L35.0429 15.7968C35.2117 15.5354 35.3367 15.2487 35.4211 14.9365C35.5045 14.6243 35.5472 14.2958 35.5472 13.9501C35.5472 13.4741 35.4649 13.0247 35.3022 12.5996C35.1384 12.1755 34.9106 11.8044 34.6168 11.4871C34.3229 11.1708 33.971 10.9186 33.5602 10.7315C33.1504 10.5454 32.7019 10.4518 32.2168 10.4518C31.7317 10.4518 31.2863 10.5423 30.8806 10.7244C30.4748 10.9064 30.127 11.1535 29.8382 11.4657C29.5494 11.7789 29.3226 12.1471 29.1599 12.5711C28.9962 12.9952 28.9149 13.4457 28.9149 13.9206C28.9149 14.3955 28.9962 14.846 29.1599 15.27C29.3226 15.6941 29.5515 16.0653 29.8454 16.3825C30.1392 16.6998 30.4911 16.952 30.9009 17.1381C31.3107 17.3242 31.7592 17.4178 32.2443 17.4178C32.5707 17.4178 32.8738 17.3761 33.1534 17.2917C33.4331 17.2083 33.6893 17.0913 33.9222 16.9419L32.2443 15.5151L32.2463 15.5171Z" fill="white"/>
            <path d="M41.1713 8.97119H42.3743V18.8343H40.6679V10.7203L38.9045 11.2094L38.5415 9.81015L41.1713 8.97119Z" fill="white"/>
            <path d="M58.5456 18.8334H56.7263L55.7328 16.469H51.1017L50.0939 18.8334H48.3306L52.6403 8.97021H54.2348L58.5446 18.8334H58.5456ZM53.4111 11.0264L51.7332 14.9436H55.1043L53.4121 11.0264H53.4111Z" fill="white"/>
            <path d="M67.3528 17.9872C67.0772 18.1967 66.7812 18.3787 66.465 18.5333C66.1477 18.6868 65.7999 18.8038 65.4226 18.8831C65.0454 18.9624 64.6274 19.0021 64.1708 19.0021C63.4529 19.0021 62.7898 18.8709 62.1847 18.6105C61.5787 18.3492 61.0539 17.9933 60.6105 17.5407C60.1672 17.0882 59.8204 16.5543 59.5682 15.9391C59.317 15.3238 59.1909 14.6608 59.1909 13.953C59.1909 13.2453 59.314 12.5965 59.5621 11.9802C59.8092 11.365 60.157 10.826 60.6044 10.3643C61.0519 9.90262 61.5837 9.53856 62.199 9.27315C62.8142 9.00773 63.4956 8.87451 64.241 8.87451C64.6884 8.87451 65.0962 8.91214 65.4653 8.98637C65.8335 9.06061 66.1721 9.16637 66.4792 9.30162C66.7873 9.43687 67.0711 9.59755 67.3334 9.78466C67.5948 9.97076 67.8419 10.1762 68.0748 10.3999L66.9694 11.6731C66.5779 11.309 66.163 11.0151 65.7247 10.7914C65.2864 10.5677 64.7871 10.4558 64.2277 10.4558C63.761 10.4558 63.3298 10.5463 62.9332 10.7284C62.5366 10.9104 62.1939 11.1575 61.9051 11.4697C61.6163 11.7829 61.3915 12.148 61.2329 12.568C61.0743 12.988 60.9949 13.4405 60.9949 13.9246C60.9949 14.4086 61.0743 14.8642 61.2329 15.2883C61.3915 15.7133 61.6153 16.0835 61.9051 16.4008C62.1939 16.7181 62.5366 16.9672 62.9332 17.1492C63.3288 17.3313 63.761 17.4218 64.2277 17.4218C64.8247 17.4218 65.3372 17.3079 65.7664 17.0791C66.1955 16.8513 66.6195 16.5401 67.0385 16.1486L68.1439 17.2682C67.8927 17.5387 67.6283 17.7787 67.3538 17.9882L67.3528 17.9872Z" fill="white"/>
            <path d="M79.321 18.8334H77.5017L76.5082 16.469H71.8771L70.8693 18.8334H69.106L73.4157 8.97021H75.0102L79.3199 18.8334H79.321ZM74.1865 11.0264L72.5086 14.9436H75.8797L74.1875 11.0264H74.1865Z" fill="white"/>
            <path d="M89.4766 15.861C89.2254 16.463 88.8675 16.9827 88.4068 17.421C87.9451 17.8593 87.3949 18.204 86.7563 18.4562C86.1177 18.7084 85.415 18.8345 84.6513 18.8345H80.9995V9.0415H84.6513C85.416 9.0415 86.1177 9.16557 86.7563 9.41268C87.3949 9.65979 87.9451 10.0025 88.4068 10.4408C88.8685 10.8791 89.2254 11.3967 89.4766 11.9936C89.7278 12.5906 89.8539 13.2343 89.8539 13.9248C89.8539 14.6153 89.7278 15.261 89.4766 15.862V15.861ZM87.8048 12.6099C87.641 12.2021 87.4122 11.8492 87.1173 11.5492C86.8224 11.2503 86.4645 11.0154 86.0445 10.8465C85.6235 10.6777 85.1567 10.5933 84.6421 10.5933H82.7069V17.2806H84.6421C85.1567 17.2806 85.6245 17.1993 86.0445 17.0345C86.4655 16.8708 86.8234 16.641 87.1173 16.3461C87.4112 16.0512 87.64 15.6993 87.8048 15.2925C87.9685 14.8848 88.0499 14.4383 88.0499 13.9512C88.0499 13.4641 87.9675 13.0167 87.8048 12.6099Z" fill="white"/>
            <path d="M99.1856 10.5934H93.6454V13.1113H98.5561V14.6642H93.6454V17.2797H99.2557V18.8325H91.939V9.03955H99.1856V10.5924V10.5934Z" fill="white"/>
            <path d="M106.139 16.4269H106.083L103.019 11.8386V18.834H101.313V9.04102H103.145L106.125 13.6721L109.105 9.04102H110.937V18.834H109.231V11.8111L106.139 16.428V16.4269Z" fill="white"/>
            <path d="M118.072 18.833H116.337V14.9718L112.449 9.04004H114.478L117.219 13.3772L119.989 9.04004H121.961L118.072 14.9301V18.833Z" fill="white"/>
            <path d="M18.4142 5.74863C18.0288 6.03134 17.5966 6.29777 17.1309 6.53878C16.9255 6.64658 16.7119 6.74624 16.4984 6.84386C15.7641 7.16826 14.9628 7.43673 14.1116 7.6452V2.82701H12.8669C12.8669 3.23378 12.5252 3.64462 11.9273 3.95478C11.3619 4.24969 10.6317 4.41138 9.87208 4.41138C9.11244 4.41138 8.38229 4.24867 7.81485 3.95376C7.21791 3.64462 6.87623 3.23378 6.87623 2.82701C6.87623 2.26872 7.85756 1.24468 9.46023 1.24468C10.5107 1.24468 10.9907 1.54772 11.0853 1.81314C11.0965 1.84364 11.1026 1.87517 11.1026 1.90771C11.1026 2.22194 10.5768 2.57278 9.8731 2.57278V3.81749C11.2602 3.81749 12.3473 2.97853 12.3473 1.90771C12.3473 1.72873 12.3158 1.55179 12.2568 1.39111C12.1083 0.973156 11.5398 0.000976562 9.46125 0.000976562C7.26978 0.000976562 5.63253 1.49382 5.63253 2.82803C5.63253 3.7158 6.22031 4.52934 7.24232 5.05916C7.98162 5.44457 8.91618 5.65609 9.87208 5.65609C10.828 5.65609 11.7625 5.44356 12.5008 5.05916C12.63 4.99204 12.753 4.92086 12.8679 4.84459V7.90553V12.7634C12.4937 12.9088 12.1083 13.0268 11.7148 13.1142C11.6883 13.1203 11.6629 13.1254 11.6364 13.1305C11.1626 12.682 10.528 12.4237 9.87412 12.4237C9.22024 12.4237 8.58262 12.681 8.10874 13.1305C8.0823 13.1244 8.05586 13.1203 8.0284 13.1132C7.63587 13.0257 7.25147 12.9078 6.87724 12.7624V6.72081C6.73894 6.66285 6.60572 6.59878 6.47556 6.53268C6.17048 6.37506 5.88777 6.19405 5.63253 5.99473V7.64418C4.78136 7.43673 3.98003 7.16724 3.24581 6.84284C3.03225 6.74624 2.8187 6.64556 2.61328 6.53777C2.15058 6.29981 1.71839 6.03032 1.32687 5.74457L0.337402 5.02255V17.1443C0.337402 21.1947 3.14005 24.8282 5.91523 26.6332C6.23658 26.8407 6.55793 27.0298 6.87724 27.1905C7.92264 27.7223 8.94262 28 9.87412 28C10.8056 28 11.8256 27.7223 12.8679 27.1905C13.1873 27.0298 13.5086 26.8417 13.83 26.6332C16.6051 24.8282 19.4047 21.1967 19.4047 17.1443V5.01747L18.4153 5.74965L18.4142 5.74863ZM12.8669 19.5066L11.8195 16.6267C11.8968 16.5382 11.9639 16.4436 12.0269 16.347C12.0269 16.347 12.0269 16.346 12.028 16.345C12.3127 16.2738 12.5934 16.1894 12.8669 16.0908V19.5076V19.5066ZM6.87623 16.0897C7.14978 16.1884 7.42943 16.2738 7.71519 16.344C7.71519 16.344 7.71519 16.345 7.7162 16.346C7.78027 16.4457 7.84942 16.5402 7.92162 16.6287L6.87623 19.5066V16.0897ZM8.3345 17.0172C8.34873 17.0284 8.36195 17.0395 8.37924 17.0507C8.40466 17.0701 8.43212 17.0863 8.45958 17.1067C8.50941 17.1423 8.56229 17.1728 8.6172 17.2033C8.6172 17.2063 8.62025 17.2063 8.62025 17.2063C8.68127 17.2399 8.74228 17.2724 8.80635 17.3009C8.91414 17.3507 9.02804 17.3955 9.14498 17.428C9.23142 17.4534 9.32193 17.4748 9.41345 17.4921C9.42159 17.4951 9.43277 17.4951 9.44091 17.4972L8.74228 19.4151L8.3406 18.752L7.61146 19.0012L8.3345 17.0162V17.0172ZM10.3246 17.4911C10.4517 17.4687 10.5768 17.4382 10.6988 17.3965C10.7324 17.3853 10.7629 17.3711 10.7934 17.3609C10.8321 17.3467 10.8707 17.3304 10.9073 17.3141C10.9826 17.2806 11.0538 17.245 11.1239 17.2033C11.1961 17.1646 11.2622 17.1199 11.3293 17.0762C11.3324 17.0731 11.3344 17.0711 11.3344 17.0711C11.3598 17.0548 11.3843 17.0375 11.4097 17.0182L12.1327 19.0032L11.4005 18.7541L11.0009 19.4141L10.3022 17.4982C10.3104 17.4982 10.3165 17.4951 10.3246 17.4931V17.4911ZM12.8669 15.5355C12.6849 15.6057 12.5008 15.6708 12.3127 15.7277C12.388 15.4867 12.4266 15.2345 12.4266 14.9793C12.4266 14.4749 12.2802 13.9908 12.0036 13.5779C12.2964 13.5068 12.5852 13.4183 12.8679 13.3166V15.5355H12.8669ZM9.8731 12.9424C10.4416 12.9424 10.9704 13.1722 11.3588 13.5881C11.7137 13.9705 11.908 14.4637 11.908 14.9793C11.908 15.3342 11.8134 15.683 11.6364 15.9911C11.5531 16.1375 11.4534 16.2708 11.3375 16.3908C10.9663 16.7762 10.47 16.9948 9.93717 17.0121L9.88124 17.0151H9.86192L9.80598 17.0121C9.47345 17.0009 9.15414 16.9124 8.87143 16.7518C8.75753 16.6877 8.64974 16.6135 8.55008 16.527C8.50025 16.4853 8.45347 16.4385 8.40568 16.3908V16.3877C8.28873 16.2718 8.18907 16.1386 8.1067 15.9911C7.92976 15.683 7.83519 15.3342 7.83519 14.9793C7.83519 14.4637 8.03247 13.9705 8.38432 13.5901C8.77279 13.1712 9.30159 12.9413 9.8731 12.9413V12.9424ZM7.74061 13.5779C7.46401 13.9908 7.31757 14.4749 7.31757 14.9793C7.31757 15.2345 7.35621 15.4867 7.43452 15.7277C7.24537 15.6708 7.06029 15.6057 6.87724 15.5355V13.3166C7.15995 13.4193 7.44875 13.5068 7.74163 13.5779H7.74061ZM5.63151 24.8841C3.4919 23.1269 1.5811 20.2663 1.5811 17.1443V7.39198C1.73059 7.48046 1.88618 7.56384 2.04177 7.64418C2.26854 7.76113 2.50447 7.87401 2.74039 7.97977C3.6302 8.37332 4.60035 8.68958 5.63151 8.92246V24.8841ZM9.8731 26.7552C8.98838 26.7552 7.93586 26.4064 6.87623 25.7688V19.8005L8.11281 19.3795L8.85821 20.6049L9.87005 17.8277L10.8849 20.6079L11.6283 19.3795L12.8669 19.8005V25.7688C11.8083 26.4064 10.7548 26.7552 9.8731 26.7552ZM18.159 17.1443C18.159 20.2652 16.2523 23.1269 14.1116 24.8841V8.92246C15.1428 8.68958 16.1129 8.37332 17.0028 7.97977C17.2387 7.87401 17.4736 7.76113 17.7044 7.64418C17.86 7.56384 18.0126 7.48046 18.159 7.39504V17.1443Z" fill="white"/>
            <path d="M5.15215 4.04492H3.66846V5.52861H5.15215V4.04492Z" fill="white"/>
            <path d="M3.35887 2.66504H2.30127V3.72264H3.35887V2.66504Z" fill="white"/>
          </g>
          <defs>
            <clipPath id="clip0_175_1342">
              <rect width="121.624" height="28" fill="white" transform="translate(0.337402)"/>
            </clipPath>
          </defs>
        </svg>
      </div>
    </a>
  </div>
  <div class="shadow-424D5B14 bg-dark flex-1 py-6 px-4 overflow-y-auto">
    <ul>
      <li class="mb-2 last:mb-0">
        <a href="./student-dashboard.html" class="flex items-center gap-3 p-3 rounded hover:bg-green group">
          <!--        khi ở trạng thái active thêm class  bg-green-->
          <div class="w-4 h-4 flex items-center justify-center">
            <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
              <g clip-path="url(#clip0_175_7829)">
                <path d="M3.33333 11.1488H8.33333C8.79167 11.1488 9.16667 10.7738 9.16667 10.3154V3.64876C9.16667 3.19043 8.79167 2.81543 8.33333 2.81543H3.33333C2.875 2.81543 2.5 3.19043 2.5 3.64876V10.3154C2.5 10.7738 2.875 11.1488 3.33333 11.1488ZM3.33333 17.8154H8.33333C8.79167 17.8154 9.16667 17.4404 9.16667 16.9821V13.6488C9.16667 13.1904 8.79167 12.8154 8.33333 12.8154H3.33333C2.875 12.8154 2.5 13.1904 2.5 13.6488V16.9821C2.5 17.4404 2.875 17.8154 3.33333 17.8154ZM11.6667 17.8154H16.6667C17.125 17.8154 17.5 17.4404 17.5 16.9821V10.3154C17.5 9.8571 17.125 9.4821 16.6667 9.4821H11.6667C11.2083 9.4821 10.8333 9.8571 10.8333 10.3154V16.9821C10.8333 17.4404 11.2083 17.8154 11.6667 17.8154ZM10.8333 3.64876V6.9821C10.8333 7.44043 11.2083 7.81543 11.6667 7.81543H16.6667C17.125 7.81543 17.5 7.44043 17.5 6.9821V3.64876C17.5 3.19043 17.125 2.81543 16.6667 2.81543H11.6667C11.2083 2.81543 10.8333 3.19043 10.8333 3.64876Z" fill="#ABB3BF"/>
                <!-- khi ở trạng thái active fill của path đổi sang màu #fff -->
              </g>
              <defs>
                <clipPath id="clip0_175_7829">
                  <rect width="20" height="20" fill="white" transform="translate(0 0.31543)"/>
                </clipPath>
              </defs>
            </svg>
          </div>
          <div class="menu__text text-12-16 font-semibold text-neutral-400 group-hover:text-white group-hover:font-semibold">
            <!--        khi ở trạng thái active đổi class text-neutral-400 thành text-white-->
            Dashboard
          </div>
        </a>
      </li>
      <li class="mb-2 last:mb-0">
        <a href="./student-message.html" class="flex items-center gap-3 p-3 rounded hover:bg-green group">
          <div class="w-4 h-4 flex items-center justify-center">
            <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
              <g clip-path="url(#clip0_175_5101)">
                <path class="group-hover:fill-white" d="M16.6667 1.98193H3.33333C2.41666 1.98193 1.675 2.73193 1.675 3.6486L1.66666 18.6486L5 15.3153H16.6667C17.5833 15.3153 18.3333 14.5653 18.3333 13.6486V3.6486C18.3333 2.73193 17.5833 1.98193 16.6667 1.98193ZM5.83333 7.81527H14.1667C14.625 7.81527 15 8.19027 15 8.6486C15 9.10693 14.625 9.48193 14.1667 9.48193H5.83333C5.375 9.48193 5 9.10693 5 8.6486C5 8.19027 5.375 7.81527 5.83333 7.81527ZM10.8333 11.9819H5.83333C5.375 11.9819 5 11.6069 5 11.1486C5 10.6903 5.375 10.3153 5.83333 10.3153H10.8333C11.2917 10.3153 11.6667 10.6903 11.6667 11.1486C11.6667 11.6069 11.2917 11.9819 10.8333 11.9819ZM14.1667 6.98193H5.83333C5.375 6.98193 5 6.60693 5 6.1486C5 5.69027 5.375 5.31527 5.83333 5.31527H14.1667C14.625 5.31527 15 5.69027 15 6.1486C15 6.60693 14.625 6.98193 14.1667 6.98193Z" fill="#ABB3BF" />
              </g>
              <defs>
                <clipPath id="clip0_175_5101">
                  <rect width="20" height="20" fill="white" transform="translate(0 0.31543)"/>
                </clipPath>
              </defs>
            </svg>
          </div>
          <div class="menu__text text-12-16 text-neutral-400 group-hover:text-white group-hover:font-semibold">
            Message
          </div>
        </a>
      </li>
      <li class="mb-2 last:mb-0">
        <a href="./student-schedule.html" class="flex items-center gap-3 p-3 rounded hover:bg-green group">
          <div class="w-4 h-4 flex items-center justify-center">
            <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path class="group-hover:fill-white" d="M14.1667 2.81559H17.5C17.721 2.81559 17.933 2.90339 18.0893 3.05967C18.2455 3.21595 18.3333 3.42791 18.3333 3.64893V16.9823C18.3333 17.2033 18.2455 17.4152 18.0893 17.5715C17.933 17.7278 17.721 17.8156 17.5 17.8156H2.5C2.27898 17.8156 2.06702 17.7278 1.91074 17.5715C1.75446 17.4152 1.66666 17.2033 1.66666 16.9823V3.64893C1.66666 3.42791 1.75446 3.21595 1.91074 3.05967C2.06702 2.90339 2.27898 2.81559 2.5 2.81559H5.83333V1.14893H7.5V2.81559H12.5V1.14893H14.1667V2.81559ZM3.33333 7.81559V16.1489H16.6667V7.81559H3.33333ZM5 11.1489H9.16666V14.4823H5V11.1489Z" fill="#ABB3BF"/>
            </svg>
          </div>
          <div class="menu__text text-12-16 text-neutral-400 group-hover:text-white group-hover:font-semibold">
            Schedule
          </div>
        </a>
      </li>
      <li class="mb-2 last:mb-0">
        <a href="./student-grade.html" class="flex items-center gap-3 p-3 rounded hover:bg-green group bg-green">
          <div class="w-4 h-4 flex items-center justify-center">
            <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M10 14.7072L14.3083 17.3072C14.625 17.4989 15.0167 17.2155 14.9333 16.8572L13.7917 11.9572L17.5917 8.66553C17.8667 8.42386 17.725 7.96553 17.35 7.93219L12.3417 7.50719L10.3833 2.89053C10.2417 2.54886 9.75833 2.54886 9.61666 2.89053L7.65833 7.50719L2.65 7.93219C2.28333 7.96553 2.13333 8.42386 2.41666 8.66553L6.21667 11.9572L5.075 16.8572C4.99167 17.2155 5.38333 17.4989 5.7 17.3072L10 14.7072Z" fill="white"/>
            </svg>
          </div>
          <div class="menu__text text-12-16 text-white">
            Grade
          </div>
        </a>
      </li>
      <li class="mb-2 last:mb-0">
        <a href="./student-feedback-report.html" class="flex items-center gap-3 p-3 rounded hover:bg-green group">
          <div class="w-4 h-4 flex items-center justify-center">
            <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path class="group-hover:fill-white" d="M15.8333 18.6486H4.16666C3.50362 18.6486 2.86774 18.3852 2.3989 17.9164C1.93006 17.4475 1.66666 16.8116 1.66666 16.1486V2.81527C1.66666 2.59425 1.75446 2.38229 1.91074 2.22601C2.06702 2.06973 2.27898 1.98193 2.5 1.98193H14.1667C14.3877 1.98193 14.5996 2.06973 14.7559 2.22601C14.9122 2.38229 15 2.59425 15 2.81527V12.8153H18.3333V16.1486C18.3333 16.8116 18.0699 17.4475 17.6011 17.9164C17.1323 18.3852 16.4964 18.6486 15.8333 18.6486ZM15 14.4819V16.1486C15 16.3696 15.0878 16.5816 15.2441 16.7379C15.4004 16.8941 15.6123 16.9819 15.8333 16.9819C16.0543 16.9819 16.2663 16.8941 16.4226 16.7379C16.5789 16.5816 16.6667 16.3696 16.6667 16.1486V14.4819H15ZM5 6.1486V7.81527H11.6667V6.1486H5ZM5 9.48193V11.1486H11.6667V9.48193H5ZM5 12.8153V14.4819H9.16666V12.8153H5Z" fill="#ABB3BF"/>
            </svg>
          </div>
          <div class="menu__text text-12-16 text-neutral-400 group-hover:text-white group-hover:font-semibold">
            Feedback Report
          </div>
        </a>
      </li>
      <li class="mb-2 last:mb-0">
        <a href="./parent-invoice.html" class="flex items-center gap-3 p-3 rounded hover:bg-green group">
          <div class="w-4 h-4 flex items-center justify-center">
            <svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path class="group-hover:fill-white" d="M15.3333 5.6486V12.3153C15.3333 13.0486 14.7333 13.6486 14 13.6486H3.33332C2.96666 13.6486 2.66666 13.3486 2.66666 12.9819C2.66666 12.6153 2.96666 12.3153 3.33332 12.3153H14V5.6486C14 5.28193 14.3 4.98193 14.6667 4.98193C15.0333 4.98193 15.3333 5.28193 15.3333 5.6486ZM2.66666 10.9819C1.55999 10.9819 0.666656 10.0886 0.666656 8.98193V4.98193C0.666656 3.87527 1.55999 2.98193 2.66666 2.98193H10.6667C11.7733 2.98193 12.6667 3.87527 12.6667 4.98193V9.6486C12.6667 10.3819 12.0667 10.9819 11.3333 10.9819H2.66666ZM4.66666 6.98193C4.66666 8.0886 5.55999 8.98193 6.66666 8.98193C7.77332 8.98193 8.66666 8.0886 8.66666 6.98193C8.66666 5.87527 7.77332 4.98193 6.66666 4.98193C5.55999 4.98193 4.66666 5.87527 4.66666 6.98193Z" fill="#ABB3BF"/>
            </svg>
          </div>
          <div class="menu__text text-12-16 text-neutral-400 group-hover:text-white group-hover:font-semibold">
            Invoice
          </div>
        </a>
      </li>
      <li class="mb-2 last:mb-0">
        <a href="./student-profile.html" class="flex items-center gap-3 p-3 rounded hover:bg-green group">
          <div class="w-4 h-4 flex items-center justify-center">
            <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path class="group-hover:fill-white" d="M10 1.98193C5.4 1.98193 1.66666 5.71527 1.66666 10.3153C1.66666 14.9153 5.4 18.6486 10 18.6486C14.6 18.6486 18.3333 14.9153 18.3333 10.3153C18.3333 5.71527 14.6 1.98193 10 1.98193ZM10 4.48193C11.3833 4.48193 12.5 5.5986 12.5 6.98193C12.5 8.36527 11.3833 9.48193 10 9.48193C8.61666 9.48193 7.5 8.36527 7.5 6.98193C7.5 5.5986 8.61666 4.48193 10 4.48193ZM10 16.3153C7.91666 16.3153 6.075 15.2486 5 13.6319C5.025 11.9736 8.33333 11.0653 10 11.0653C11.6583 11.0653 14.975 11.9736 15 13.6319C13.925 15.2486 12.0833 16.3153 10 16.3153Z" fill="#ABB3BF"/>
            </svg>
          </div>
          <div class="menu__text text-12-16 text-neutral-400 group-hover:text-white group-hover:font-semibold">
            Profile
          </div>
        </a>
      </li>
    </ul>
  </div>
</div>
<header class="header-fixed bg-white fixed top-0 left-0 right-0 z-40 transition-all lg:py-3 border-b border-neutral-100">
  <div class="flex items-center">
    <div class="icon__hamburger border-r border-neutral-100 w-[56px] h-[56px] flex-shrink-0 py-[22px] px-[19px] lg:hidden">
      <span class="block w-full h-[2px] rounded-lg bg-black mb-1"></span>
      <span class="block w-full h-[2px] rounded-lg bg-black mb-1"></span>
      <span class="block w-full h-[2px] rounded-lg bg-black"></span>
    </div>
    <div class="container">
      <div class="flex items-center">
        <div class="flex-1">
          <h1 class="text-2xl font-semibold">Grade</h1>
        </div>
        <div class="hidden lg:flex lg:items-center relative">
          <div class="setting__acc flex items-center gap-1">
            <div class="text-12-16">Jay Jang (장이준)</div>
            <div class="w-6 h-6">
              <svg class="transition-all rotate-180" width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g clip-path="url(#clip0_312_18726)">
                  <path d="M7.14941 10L12.1494 15L17.1494 10H7.14941Z" fill="#10C285"/>
                </g>
                <defs>
                  <clipPath id="clip0_312_18726">
                    <rect width="24" height="24" fill="white" transform="translate(0.149414)"/>
                  </clipPath>
                </defs>
              </svg>
            </div>
          </div>
          <div class="setting__acc--child hidden absolute top-[30px] right-0 shadow-00046212 border border-neutral-100 p-2 rounded-lg bg-white min-w-[237px]">
            <a href="./update-password.html" class="flex items-center gap-1 py-3 px-2 rounded-lg hover:bg-neutral-50">
              <div class="w-6 h-6">
                <svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12.1494 12H12.1544M17.1494 12H17.1544M7.14941 12H7.15441M5.34941 7H18.9494C20.0695 7 20.6296 7 21.0574 7.21799C21.4337 7.40973 21.7397 7.71569 21.9314 8.09202C22.1494 8.51984 22.1494 9.0799 22.1494 10.2V13.8C22.1494 14.9201 22.1494 15.4802 21.9314 15.908C21.7397 16.2843 21.4337 16.5903 21.0574 16.782C20.6296 17 20.0695 17 18.9494 17H5.34941C4.22931 17 3.66926 17 3.24143 16.782C2.86511 16.5903 2.55915 16.2843 2.3674 15.908C2.14941 15.4802 2.14941 14.9201 2.14941 13.8V10.2C2.14941 9.0799 2.14941 8.51984 2.3674 8.09202C2.55915 7.71569 2.86511 7.40973 3.24143 7.21799C3.66926 7 4.22931 7 5.34941 7ZM12.3994 12C12.3994 12.1381 12.2875 12.25 12.1494 12.25C12.0113 12.25 11.8994 12.1381 11.8994 12C11.8994 11.8619 12.0113 11.75 12.1494 11.75C12.2875 11.75 12.3994 11.8619 12.3994 12ZM17.3994 12C17.3994 12.1381 17.2875 12.25 17.1494 12.25C17.0113 12.25 16.8994 12.1381 16.8994 12C16.8994 11.8619 17.0113 11.75 17.1494 11.75C17.2875 11.75 17.3994 11.8619 17.3994 12ZM7.39941 12C7.39941 12.1381 7.28749 12.25 7.14941 12.25C7.01134 12.25 6.89941 12.1381 6.89941 12C6.89941 11.8619 7.01134 11.75 7.14941 11.75C7.28749 11.75 7.39941 11.8619 7.39941 12Z" stroke="#10C285" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </div>
              <div class="text-12-16">Change Password</div>
            </a>
            <a href="./login.html" class="flex items-center gap-1 py-3 px-2 rounded-lg hover:bg-neutral-50">
              <div class="w-6 h-6">
                <svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <g clip-path="url(#clip0_175_1874)">
                    <path d="M10.4494 7.7C10.0594 8.09 10.0594 8.71 10.4494 9.1L12.3494 11H3.14941C2.59941 11 2.14941 11.45 2.14941 12C2.14941 12.55 2.59941 13 3.14941 13H12.3494L10.4494 14.9C10.0594 15.29 10.0594 15.91 10.4494 16.3C10.8394 16.69 11.4594 16.69 11.8494 16.3L15.4394 12.71C15.8294 12.32 15.8294 11.69 15.4394 11.3L11.8494 7.7C11.4594 7.31 10.8394 7.31 10.4494 7.7ZM20.1494 19H13.1494C12.5994 19 12.1494 19.45 12.1494 20C12.1494 20.55 12.5994 21 13.1494 21H20.1494C21.2494 21 22.1494 20.1 22.1494 19V5C22.1494 3.9 21.2494 3 20.1494 3H13.1494C12.5994 3 12.1494 3.45 12.1494 4C12.1494 4.55 12.5994 5 13.1494 5H20.1494V19Z" fill="#10C285"/>
                  </g>
                  <defs>
                    <clipPath id="clip0_175_1874">
                      <rect width="24" height="24" fill="white" transform="translate(0.149414)"/>
                    </clipPath>
                  </defs>
                </svg>
              </div>
              <div class="text-12-16">Log Out</div>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</header>
  <div class="box__content--main transition page-student">
    <main class="pt-[57px] min-h-screen bg-neutral-50 page-grade">
      <div class="bg-neutral-100">
        <div class="container py-2">
          <div class="flex gap-[8px] justify-end">
            <select name="session" class="box__select px-4 py-2 rounded-lg bg-white focus:outline-none cursor-pointer" single="single">
              <option value="">Session 1</option>
              <option value="1">Session 2</option>
              <option value="2">Session 3</option>
            </select>
            <select name="session" class="box__select px-4 py-2 rounded-lg bg-white focus:outline-none cursor-pointer" single="single">
              <option value="">Tearm 1-2024</option>
              <option value="1">Tearm 2-2024</option>
              <option value="2">Tearm 3-2024</option>
            </select>
            <select name="session" class="box__select px-4 py-2 rounded-lg bg-white focus:outline-none cursor-pointer" single="single">
              <option value="">2024</option>
              <option value="1">2025</option>
              <option value="2">2026</option>
            </select>
          </div>
        </div>
      </div>
      <div class="w-full max-w-[1320px] mx-auto p-4 lg:py-8 lg:px-[60px]">
        <div class="grid-cols-1 grid-rows-1 lg:grid-cols-2 gap-4 lg:gap-[34px] grid">
          <div class="bg-white p-4 rounded-lg">
            <div class="flex items-center justify-between border-l-4 border-green px-4 py-2 mb-6">
              <div class="text-14-20 font-semibold text-[0F1A30]">Class Thru830-QR5</div>
              <div class="text-green font-semibold text-14-20">Reading</div>
            </div>
            <div class="mb-4 rounded-lg border border-[#EEF0F6]">
              <div>
                <div class="text-neutral-200 font-semibold rounded-t-lg text-12-16 bg-dark py-3 text-center">Class Participation</div>
                <div class="flex max-w-full overflow-auto w-full flex-nowrap">
                  <div class="flex w-full items-center flex-shrink-0 xl:flex-1 xl:w-full">
                    <div class="px-2 py-2.5 w-1/3 text-14-20 text-neutral-500 text-center border-l border-neutral-100">O</div>
                    <div class="px-2 py-2.5 w-2/3 text-14-20 font-semibold text-green text-center border-l border-neutral-100">A-</div>
                  </div>
                </div>
                <div class="text-neutral-200 font-semibold text-12-16 bg-dark py-3 text-center">Class Work</div>
                <div class="flex max-w-full overflow-auto w-full">
                  <div class="flex flex-col w-1/3 flex-shrink-0 xl:flex-1 xl:w-full">
                    <div class="px-2 py-3 flex-1 text-12-16 font-semibold text-neutral-500 text-center bg-neutral-100">Score</div>
                    <div class="p-2 flex-1 text-14-20 text-neutral-700 text-center border-r border-neutral-100">76/100</div>
                  </div>
                  <div class="flex flex-col w-1/3 flex-shrink-0 xl:flex-1 xl:w-full">
                    <div class="px-2 py-3 flex-1 text-12-16 font-semibold text-neutral-500 text-center bg-neutral-100">%</div>
                    <div class="p-2 flex-1 text-14-20 text-neutral-700 text-center border-r border-neutral-100">76%</div>
                  </div>
                  <div class="flex flex-col w-1/3 flex-shrink-0 xl:flex-1 xl:w-full">
                    <div class="px-2 py-3 flex-1 text-12-16 font-semibold text-neutral-500 text-center bg-neutral-100">Grade</div>
                    <div class="p-2 flex-1 text-14-20 font-semibold text-green text-center border-r border-neutral-100">B+</div>
                  </div>
                  <div class="flex flex-col w-1/3 flex-shrink-0 xl:flex-1 xl:w-full">
                    <div class="px-2 py-3 flex-1 text-12-16 font-semibold text-neutral-500 text-center bg-neutral-100">Grade</div>
                    <div class="p-2 flex-1 text-14-20 font-semibold text-green text-center">B+</div>
                  </div>
                </div>
                <div class="text-neutral-200 font-semibold text-12-16 bg-dark py-3 text-center">Home Work</div>
                <div class="rounded-b-lg relative flex flex-col max-w-full overflow-auto w-full bg-neutral-50">
                  <div class="flex">
                    <div class="px-2 py-3 text-12-16 font-semibold text-neutral-500 text-center w-1/3 flex-shrink-0 bg-neutral-100 xl:flex-1 xl:w-full">HW1</div>
                    <div class="px-2 py-3 text-12-16 font-semibold text-neutral-500 text-center w-1/3 flex-shrink-0 bg-neutral-100 xl:flex-1 xl:w-full">%</div>
                    <div class="px-2 py-3 text-12-16 font-semibold text-neutral-500 text-center w-1/3 flex-shrink-0 bg-neutral-100 xl:flex-1 xl:w-full">Grade</div>
                    <div class="px-2 py-3 text-12-16 font-semibold text-neutral-500 text-center w-1/3 flex-shrink-0 bg-neutral-100 xl:flex-1 xl:w-full">Grade</div>
                  </div>
                  <div class="text-neutral-500 font-semibold sticky bottom-0 left-0 text-12-16 py-3 lg:px-[92px] text-center flex-1">Vocab Quiz</div>
                  <div class="flex">
                    <div class="px-2 py-2.5 text-14-20 text-neutral-700 text-center w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full border-y border-neutral-100">25/42</div>
                    <div class="px-2 py-2.5 text-14-20 text-neutral-700 text-center border-l border-y border-neutral-100 w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full">60%</div>
                    <div class="px-2 py-2.5 text-14-20 font-semibold text-green text-center border-l border-y border-neutral-100 w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full">B</div>
                    <div class="px-2 py-2.5 text-14-20 font-semibold text-green text-center border-l border-y border-neutral-100 w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full">B</div>
                  </div>
                  <div class="text-neutral-500 font-semibold sticky bottom-0 left-0 text-12-16 py-3 lg:px-[92px] text-center flex-1 border-b border-neutral-100">Short Response</div>
                  <div class="text-green sticky bottom-0 left-0 font-semibold text-14-20 py-2.5 lg:px-[92px] xl:px-2 text-center bg-white">B-</div>
                </div>
              </div>
            </div>
            <div class="bg-neutral-50 rounded-lg border border-greenLight px-4 py-6 relative">
                <div class="text-14-20 text-green font-medium mb-4">
                  Messages and Consultations
                </div>
                <div class="text-14-20 text-neutral-700 z-[2] relative">
                    <div class="text-14-20 text-neutral-700 text-dot relative ml-5">
                      Did not meet minimum word count. Also, generic answer with low effort.
                    </div>
                </div>
                <div class="absolute top-0 right-0">
                  <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="60" height="59" viewBox="0 0 60 59" fill="none">
                    <g opacity="0.2">
                    <mask id="mask0_283_11502" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="60" height="59">
                    <rect x="15.9707" width="45.9643" height="45.9643" transform="rotate(19.8763 15.9707 0)" fill="url(#pattern0)"/>
                    </mask>
                    <g mask="url(#mask0_283_11502)">
                    <rect x="15.9707" width="45.9643" height="45.9643" transform="rotate(19.8763 15.9707 0)" fill="#10C285"/>
                    </g>
                    </g>
                    <defs>
                    <pattern id="pattern0" patternContentUnits="objectBoundingBox" width="1" height="1">
                    <use xlink:href="#image0_283_11502" transform="scale(0.00195312)"/>
                    </pattern>
                    <image id="image0_283_11502" width="512" height="512" xlink:href="data:image/png;base64,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"/>
                    </defs>
                  </svg>
                </div>
                <div class="absolute bottom-[-19px] left-0">
                  <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="83" height="83" viewBox="0 0 83 83" fill="none">
                    <g opacity="0.2">
                    <mask id="mask0_283_11505" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="11" y="11" width="61" height="61">
                    <rect x="11.1016" y="30.6682" width="45.3518" height="45.3518" transform="rotate(-25.1237 11.1016 30.6682)" fill="url(#pattern0)"/>
                    </mask>
                    <g mask="url(#mask0_283_11505)">
                    <rect x="16.1992" y="18.8953" width="47.7371" height="45.3518" fill="#10C285"/>
                    </g>
                    </g>
                    <defs>
                    <pattern id="pattern0" patternContentUnits="objectBoundingBox" width="1" height="1">
                    <use xlink:href="#image0_283_11505" transform="scale(0.00195312)"/>
                    </pattern>
                    <image id="image0_283_11505" width="512" height="512" xlink:href="data:image/png;base64,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"/>
                    </defs>
                  </svg>
                </div>
            </div>
          </div>
          <div class="bg-white p-4 rounded-lg">
            <div class="flex items-center justify-between border-l-4 border-green px-4 py-2 mb-6">
              <div class="text-14-20 font-semibold text-[0F1A30]">Class Thru830-QW3</div>
              <div class="text-green font-semibold text-14-20">Writing</div>
            </div>
            <div class="mb-4 rounded-lg border border-[#EEF0F6]">
              <div>
                <div class="text-neutral-200 font-semibold rounded-t-lg text-12-16 bg-dark py-3 text-center">Class Participation</div>
                <div class="flex max-w-full overflow-auto w-full flex-nowrap">
                  <div class="flex w-full items-center flex-shrink-0 xl:flex-1 xl:w-full">
                    <div class="px-2 py-2.5 w-1/3 text-14-20 text-neutral-500 text-center border-l border-neutral-100">O</div>
                    <div class="px-2 py-2.5 w-2/3 text-14-20 font-semibold text-green text-center border-l border-neutral-100">A-</div>
                  </div>
                </div>
                <div class="text-neutral-200 font-semibold text-12-16 bg-dark py-3 text-center">Home Work</div>
                <div class="rounded-b-lg flex flex-col max-w-full overflow-auto w-full bg-neutral-50">
                  <div class="flex">
                    <div class="px-2 py-3 text-12-16 font-semibold text-neutral-500 text-center w-1/3 flex-shrink-0 bg-neutral-100 xl:flex-1 xl:w-full">HW1</div>
                    <div class="px-2 py-3 text-12-16 font-semibold text-neutral-500 text-center w-1/3 flex-shrink-0 bg-neutral-100 xl:flex-1 xl:w-full">HW2</div>
                    <div class="px-2 py-3 text-12-16 font-semibold text-neutral-500 text-center w-1/3 flex-shrink-0 bg-neutral-100 xl:flex-1 xl:w-full">HW3</div>
                    <div class="px-2 py-3 text-12-16 font-semibold text-neutral-500 text-center w-1/3 flex-shrink-0 bg-neutral-100 xl:flex-1 xl:w-full">HW4</div>
                  </div>
                  <div class="text-neutral-500 sticky bottom-0 left-0 font-semibold text-12-16 py-3 xl:px-[92px] text-center flex-1">Transcription</div>
                  <div class="flex">
                    <div class="px-2 py-2.5 text-14-20 text-neutral-700 text-center border-y border-neutral-100 w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full">Good Efford</div>
                    <div class="px-2 py-2.5 text-14-20 text-neutral-700 text-center border-l border-y border-neutral-100 w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full">Incomplete</div>
                    <div class="px-2 py-2.5 text-14-20 font-semibold text-green text-center border-l border-y border-neutral-100 w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full">Fm</div>
                    <div class="px-2 py-2.5 text-14-20 font-semibold text-green text-center border-l border-y border-neutral-100 w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full">Fm</div>
                  </div>
                  <div class="text-neutral-500 font-semibold text-12-16 py-3 xl:px-[92px] text-center flex-1 sticky bottom-0 left-0">Technical Writing Skill</div>
                  <div class="flex">
                    <div class="px-2 py-2.5 text-14-20 text-neutral-700 text-center w-1/3 flex-shrink-0 border-y border-neutral-100 bg-white xl:flex-1 xl:w-full">3/5</div>
                    <div class="px-2 py-2.5 text-14-20 text-neutral-700 text-center border-l border-y border-neutral-100 w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full">5/5</div>
                    <div class="px-2 py-2.5 text-14-20 font-semibold text-green text-center border-l border-y border-neutral-100 w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full">Fm</div>
                    <div class="px-2 py-2.5 text-14-20 font-semibold text-green text-center border-l border-y border-neutral-100 w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full">Fm</div>
                  </div>
                  <div class="text-neutral-500 font-semibold text-12-16 py-3 xl:px-[92px] text-center flex-1 sticky bottom-0 left-0">Prewriting</div>
                  <div class="flex">
                    <div class="px-2 py-2.5 text-14-20 font-semibold text-green text-center border-y border-neutral-100 w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full">B-</div>
                    <div class="px-2 py-2.5 text-14-20 font-semibold text-green text-center border-l border-y border-neutral-100 w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full">C</div>
                    <div class="px-2 py-2.5 text-14-20 font-semibold text-green text-center border-l border-y border-neutral-100 w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full">C+</div>
                    <div class="px-2 py-2.5 text-14-20 font-semibold text-green text-center border-l border-y border-neutral-100 w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full">Fm</div>
                  </div>
                  <div class="text-neutral-500 font-semibold text-12-16 py-3 xl:px-[92px] text-center flex-1 border-b border-neutral-100 sticky bottom-0 left-0">Prewriting/Writing/Revision</div>
                  <div class="flex">
                    <div class="px-2 py-2.5 text-14-20 font-semibold text-green text-center w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full">B-</div>
                    <div class="px-2 py-2.5 text-14-20 font-semibold text-green text-center border-l border-neutral-100 w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full">C</div>
                    <div class="px-2 py-2.5 text-14-20 font-semibold text-green text-center border-l border-neutral-100 w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full">C+</div>
                    <div class="px-2 py-2.5 text-14-20 font-semibold text-green text-center border-l border-neutral-100 w-1/3 flex-shrink-0 bg-white xl:flex-1 xl:w-full">Fm</div>
                  </div>
                </div>
              </div>
            </div>
            <div class="bg-neutral-50 rounded-lg border border-greenLight px-4 py-6 relative">
                <div class="text-14-20 text-green font-medium mb-4">
                  Messages and Consultations
                </div>
                <div class="text-14-20 text-neutral-700  z-[2] relative">
                    <div class="text-14-20 text-neutral-700 text-dot relative ml-5">
                      Did not meet minimum word count. Also, generic answer with low effort.
                    </div>
                </div>
                <div class="absolute top-0 right-0">
                  <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="60" height="59" viewBox="0 0 60 59" fill="none">
                    <g opacity="0.2">
                    <mask id="mask0_283_11502" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="60" height="59">
                    <rect x="15.9707" width="45.9643" height="45.9643" transform="rotate(19.8763 15.9707 0)" fill="url(#pattern0)"/>
                    </mask>
                    <g mask="url(#mask0_283_11502)">
                    <rect x="15.9707" width="45.9643" height="45.9643" transform="rotate(19.8763 15.9707 0)" fill="#10C285"/>
                    </g>
                    </g>
                    <defs>
                    <pattern id="pattern0" patternContentUnits="objectBoundingBox" width="1" height="1">
                    <use xlink:href="#image0_283_11502" transform="scale(0.00195312)"/>
                    </pattern>
                    <image id="image0_283_11502" width="512" height="512" xlink:href="data:image/png;base64,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"/>
                    </defs>
                  </svg>
                </div>
                <div class="absolute bottom-[-19px] left-0">
                  <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="83" height="83" viewBox="0 0 83 83" fill="none">
                    <g opacity="0.2">
                    <mask id="mask0_283_11505" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="11" y="11" width="61" height="61">
                    <rect x="11.1016" y="30.6682" width="45.3518" height="45.3518" transform="rotate(-25.1237 11.1016 30.6682)" fill="url(#pattern0)"/>
                    </mask>
                    <g mask="url(#mask0_283_11505)">
                    <rect x="16.1992" y="18.8953" width="47.7371" height="45.3518" fill="#10C285"/>
                    </g>
                    </g>
                    <defs>
                    <pattern id="pattern0" patternContentUnits="objectBoundingBox" width="1" height="1">
                    <use xlink:href="#image0_283_11505" transform="scale(0.00195312)"/>
                    </pattern>
                    <image id="image0_283_11505" width="512" height="512" xlink:href="data:image/png;base64,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"/>
                    </defs>
                  </svg>
                </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
  <div class="backdrop fixed inset-0 bg-black/[0.7] z-[51] transition opacity-0 invisible"></div>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js" integrity="sha512-894YE6QWD5I59HgZOGReFYm4dnWc1Qt5NtvYSaNcOP+u1T9qYdvdihz0PPSiiqn/+/3e7Jo4EaG7TubfWGUrMQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js" integrity="sha512-2ImtlRlf2VVmiGZsjm9bEyhjGW4dU7B6TNwh/hx/iSByxNENtj3WVE6o/9Lj4TJeVXPi4bnOIMXFIJJAeufa0A==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<script src="https://cdn.jsdelivr.net/npm/@fullcalendar/core@4.4.0/main.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/@fullcalendar/interaction@4.4.0/main.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/@fullcalendar/daygrid@4.4.0/main.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/@fullcalendar/timegrid@4.4.0/main.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/@fullcalendar/list@4.4.0/main.min.js"></script>
<script src="https://unpkg.com/popper.js/dist/umd/popper.min.js"></script>
<script src="https://unpkg.com/tooltip.js/dist/umd/tooltip.min.js"></script>
<script src="/html/src/js/index.js"></script>
</body>
</html>
