<!doctype html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Q1 Academy - student message</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link
    href="https://fonts.googleapis.com/css2?family=Inter:slnt,wght@-10..0,100..900&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap"
    rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css"
        integrity="sha512-nMNlpuaDPrqlEls3IX/Q56H36qvBASwb3ipuo3MxeWbsQB1881ox0cRv7UPTgBlriqoynt35KjEwgGUeUXIPnw=="
        crossorigin="anonymous" referrerpolicy="no-referrer"/>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css"
        integrity="sha512-1ycn6IcaQQ40/MKBW2W4Rhis/DbILU74C1vSrLJxCq57o941Ym01SwNsOMqvEBFlcgUa6xLiPY/NS5R+E6ztJQ=="
        crossorigin="anonymous" referrerpolicy="no-referrer"/>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fullcalendar/core@4.4.0/main.min.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fullcalendar/daygrid@4.4.0/main.min.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fullcalendar/timegrid@4.4.0/main.min.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fullcalendar/list@4.4.0/main.min.css">
  <link href="/html/src/css/output.css" rel="stylesheet">
</head>
<body>
<aside class="fixed z-50 h-screen hidden lg:flex flex-col transition">
  <div class="relative bg-neutral-800 py-[14px]">
    <a href="#" class="block h-7">
      <svg class="mx-auto logo-full" width="122" height="28" viewBox="0 0 122 28" fill="none"
           xmlns="http://www.w3.org/2000/svg">
        <g clip-path="url(#clip0_175_1342)">
          <path
            d="M37.4784 17.8957L36.373 19.1272L35.1974 18.0778C34.7784 18.3666 34.3208 18.5933 33.8266 18.756C33.3324 18.9188 32.7954 19.0011 32.2178 19.0011C31.4623 19.0011 30.7718 18.8679 30.1474 18.6025C29.522 18.3371 28.986 17.9751 28.5376 17.5185C28.0901 17.0619 27.7403 16.5249 27.4881 15.9097C27.2369 15.2944 27.1108 14.6416 27.1108 13.9511C27.1108 13.2606 27.2369 12.6077 27.4881 11.9925C27.7403 11.3772 28.0942 10.8383 28.5518 10.3766C29.0084 9.91491 29.5494 9.54881 30.1748 9.27831C30.7992 9.00781 31.4897 8.87256 32.2453 8.87256C33.0009 8.87256 33.6903 9.00578 34.3157 9.27119C34.9401 9.53661 35.4771 9.89864 35.9245 10.3552C36.372 10.8118 36.7218 11.3488 36.974 11.964C37.2262 12.5792 37.3523 13.2321 37.3523 13.9226C37.3523 14.4819 37.2658 15.0189 37.093 15.5314C36.9201 16.0449 36.675 16.5158 36.3588 16.9439L37.4784 17.8957ZM32.2463 15.5171L33.3507 14.2582L35.0429 15.7968C35.2117 15.5354 35.3367 15.2487 35.4211 14.9365C35.5045 14.6243 35.5472 14.2958 35.5472 13.9501C35.5472 13.4741 35.4649 13.0247 35.3022 12.5996C35.1384 12.1755 34.9106 11.8044 34.6168 11.4871C34.3229 11.1708 33.971 10.9186 33.5602 10.7315C33.1504 10.5454 32.7019 10.4518 32.2168 10.4518C31.7317 10.4518 31.2863 10.5423 30.8806 10.7244C30.4748 10.9064 30.127 11.1535 29.8382 11.4657C29.5494 11.7789 29.3226 12.1471 29.1599 12.5711C28.9962 12.9952 28.9149 13.4457 28.9149 13.9206C28.9149 14.3955 28.9962 14.846 29.1599 15.27C29.3226 15.6941 29.5515 16.0653 29.8454 16.3825C30.1392 16.6998 30.4911 16.952 30.9009 17.1381C31.3107 17.3242 31.7592 17.4178 32.2443 17.4178C32.5707 17.4178 32.8738 17.3761 33.1534 17.2917C33.4331 17.2083 33.6893 17.0913 33.9222 16.9419L32.2443 15.5151L32.2463 15.5171Z"
            fill="white"/>
          <path d="M41.1713 8.97119H42.3743V18.8343H40.6679V10.7203L38.9045 11.2094L38.5415 9.81015L41.1713 8.97119Z"
                fill="white"/>
          <path
            d="M58.5456 18.8334H56.7263L55.7328 16.469H51.1017L50.0939 18.8334H48.3306L52.6403 8.97021H54.2348L58.5446 18.8334H58.5456ZM53.4111 11.0264L51.7332 14.9436H55.1043L53.4121 11.0264H53.4111Z"
            fill="white"/>
          <path
            d="M67.3528 17.9872C67.0772 18.1967 66.7812 18.3787 66.465 18.5333C66.1477 18.6868 65.7999 18.8038 65.4226 18.8831C65.0454 18.9624 64.6274 19.0021 64.1708 19.0021C63.4529 19.0021 62.7898 18.8709 62.1847 18.6105C61.5787 18.3492 61.0539 17.9933 60.6105 17.5407C60.1672 17.0882 59.8204 16.5543 59.5682 15.9391C59.317 15.3238 59.1909 14.6608 59.1909 13.953C59.1909 13.2453 59.314 12.5965 59.5621 11.9802C59.8092 11.365 60.157 10.826 60.6044 10.3643C61.0519 9.90262 61.5837 9.53856 62.199 9.27315C62.8142 9.00773 63.4956 8.87451 64.241 8.87451C64.6884 8.87451 65.0962 8.91214 65.4653 8.98637C65.8335 9.06061 66.1721 9.16637 66.4792 9.30162C66.7873 9.43687 67.0711 9.59755 67.3334 9.78466C67.5948 9.97076 67.8419 10.1762 68.0748 10.3999L66.9694 11.6731C66.5779 11.309 66.163 11.0151 65.7247 10.7914C65.2864 10.5677 64.7871 10.4558 64.2277 10.4558C63.761 10.4558 63.3298 10.5463 62.9332 10.7284C62.5366 10.9104 62.1939 11.1575 61.9051 11.4697C61.6163 11.7829 61.3915 12.148 61.2329 12.568C61.0743 12.988 60.9949 13.4405 60.9949 13.9246C60.9949 14.4086 61.0743 14.8642 61.2329 15.2883C61.3915 15.7133 61.6153 16.0835 61.9051 16.4008C62.1939 16.7181 62.5366 16.9672 62.9332 17.1492C63.3288 17.3313 63.761 17.4218 64.2277 17.4218C64.8247 17.4218 65.3372 17.3079 65.7664 17.0791C66.1955 16.8513 66.6195 16.5401 67.0385 16.1486L68.1439 17.2682C67.8927 17.5387 67.6283 17.7787 67.3538 17.9882L67.3528 17.9872Z"
            fill="white"/>
          <path
            d="M79.321 18.8334H77.5017L76.5082 16.469H71.8771L70.8693 18.8334H69.106L73.4157 8.97021H75.0102L79.3199 18.8334H79.321ZM74.1865 11.0264L72.5086 14.9436H75.8797L74.1875 11.0264H74.1865Z"
            fill="white"/>
          <path
            d="M89.4766 15.861C89.2254 16.463 88.8675 16.9827 88.4068 17.421C87.9451 17.8593 87.3949 18.204 86.7563 18.4562C86.1177 18.7084 85.415 18.8345 84.6513 18.8345H80.9995V9.0415H84.6513C85.416 9.0415 86.1177 9.16557 86.7563 9.41268C87.3949 9.65979 87.9451 10.0025 88.4068 10.4408C88.8685 10.8791 89.2254 11.3967 89.4766 11.9936C89.7278 12.5906 89.8539 13.2343 89.8539 13.9248C89.8539 14.6153 89.7278 15.261 89.4766 15.862V15.861ZM87.8048 12.6099C87.641 12.2021 87.4122 11.8492 87.1173 11.5492C86.8224 11.2503 86.4645 11.0154 86.0445 10.8465C85.6235 10.6777 85.1567 10.5933 84.6421 10.5933H82.7069V17.2806H84.6421C85.1567 17.2806 85.6245 17.1993 86.0445 17.0345C86.4655 16.8708 86.8234 16.641 87.1173 16.3461C87.4112 16.0512 87.64 15.6993 87.8048 15.2925C87.9685 14.8848 88.0499 14.4383 88.0499 13.9512C88.0499 13.4641 87.9675 13.0167 87.8048 12.6099Z"
            fill="white"/>
          <path
            d="M99.1856 10.5934H93.6454V13.1113H98.5561V14.6642H93.6454V17.2797H99.2557V18.8325H91.939V9.03955H99.1856V10.5924V10.5934Z"
            fill="white"/>
          <path
            d="M106.139 16.4269H106.083L103.019 11.8386V18.834H101.313V9.04102H103.145L106.125 13.6721L109.105 9.04102H110.937V18.834H109.231V11.8111L106.139 16.428V16.4269Z"
            fill="white"/>
          <path
            d="M118.072 18.833H116.337V14.9718L112.449 9.04004H114.478L117.219 13.3772L119.989 9.04004H121.961L118.072 14.9301V18.833Z"
            fill="white"/>
          <path
            d="M18.4142 5.74863C18.0288 6.03134 17.5966 6.29777 17.1309 6.53878C16.9255 6.64658 16.7119 6.74624 16.4984 6.84386C15.7641 7.16826 14.9628 7.43673 14.1116 7.6452V2.82701H12.8669C12.8669 3.23378 12.5252 3.64462 11.9273 3.95478C11.3619 4.24969 10.6317 4.41138 9.87208 4.41138C9.11244 4.41138 8.38229 4.24867 7.81485 3.95376C7.21791 3.64462 6.87623 3.23378 6.87623 2.82701C6.87623 2.26872 7.85756 1.24468 9.46023 1.24468C10.5107 1.24468 10.9907 1.54772 11.0853 1.81314C11.0965 1.84364 11.1026 1.87517 11.1026 1.90771C11.1026 2.22194 10.5768 2.57278 9.8731 2.57278V3.81749C11.2602 3.81749 12.3473 2.97853 12.3473 1.90771C12.3473 1.72873 12.3158 1.55179 12.2568 1.39111C12.1083 0.973156 11.5398 0.000976562 9.46125 0.000976562C7.26978 0.000976562 5.63253 1.49382 5.63253 2.82803C5.63253 3.7158 6.22031 4.52934 7.24232 5.05916C7.98162 5.44457 8.91618 5.65609 9.87208 5.65609C10.828 5.65609 11.7625 5.44356 12.5008 5.05916C12.63 4.99204 12.753 4.92086 12.8679 4.84459V7.90553V12.7634C12.4937 12.9088 12.1083 13.0268 11.7148 13.1142C11.6883 13.1203 11.6629 13.1254 11.6364 13.1305C11.1626 12.682 10.528 12.4237 9.87412 12.4237C9.22024 12.4237 8.58262 12.681 8.10874 13.1305C8.0823 13.1244 8.05586 13.1203 8.0284 13.1132C7.63587 13.0257 7.25147 12.9078 6.87724 12.7624V6.72081C6.73894 6.66285 6.60572 6.59878 6.47556 6.53268C6.17048 6.37506 5.88777 6.19405 5.63253 5.99473V7.64418C4.78136 7.43673 3.98003 7.16724 3.24581 6.84284C3.03225 6.74624 2.8187 6.64556 2.61328 6.53777C2.15058 6.29981 1.71839 6.03032 1.32687 5.74457L0.337402 5.02255V17.1443C0.337402 21.1947 3.14005 24.8282 5.91523 26.6332C6.23658 26.8407 6.55793 27.0298 6.87724 27.1905C7.92264 27.7223 8.94262 28 9.87412 28C10.8056 28 11.8256 27.7223 12.8679 27.1905C13.1873 27.0298 13.5086 26.8417 13.83 26.6332C16.6051 24.8282 19.4047 21.1967 19.4047 17.1443V5.01747L18.4153 5.74965L18.4142 5.74863ZM12.8669 19.5066L11.8195 16.6267C11.8968 16.5382 11.9639 16.4436 12.0269 16.347C12.0269 16.347 12.0269 16.346 12.028 16.345C12.3127 16.2738 12.5934 16.1894 12.8669 16.0908V19.5076V19.5066ZM6.87623 16.0897C7.14978 16.1884 7.42943 16.2738 7.71519 16.344C7.71519 16.344 7.71519 16.345 7.7162 16.346C7.78027 16.4457 7.84942 16.5402 7.92162 16.6287L6.87623 19.5066V16.0897ZM8.3345 17.0172C8.34873 17.0284 8.36195 17.0395 8.37924 17.0507C8.40466 17.0701 8.43212 17.0863 8.45958 17.1067C8.50941 17.1423 8.56229 17.1728 8.6172 17.2033C8.6172 17.2063 8.62025 17.2063 8.62025 17.2063C8.68127 17.2399 8.74228 17.2724 8.80635 17.3009C8.91414 17.3507 9.02804 17.3955 9.14498 17.428C9.23142 17.4534 9.32193 17.4748 9.41345 17.4921C9.42159 17.4951 9.43277 17.4951 9.44091 17.4972L8.74228 19.4151L8.3406 18.752L7.61146 19.0012L8.3345 17.0162V17.0172ZM10.3246 17.4911C10.4517 17.4687 10.5768 17.4382 10.6988 17.3965C10.7324 17.3853 10.7629 17.3711 10.7934 17.3609C10.8321 17.3467 10.8707 17.3304 10.9073 17.3141C10.9826 17.2806 11.0538 17.245 11.1239 17.2033C11.1961 17.1646 11.2622 17.1199 11.3293 17.0762C11.3324 17.0731 11.3344 17.0711 11.3344 17.0711C11.3598 17.0548 11.3843 17.0375 11.4097 17.0182L12.1327 19.0032L11.4005 18.7541L11.0009 19.4141L10.3022 17.4982C10.3104 17.4982 10.3165 17.4951 10.3246 17.4931V17.4911ZM12.8669 15.5355C12.6849 15.6057 12.5008 15.6708 12.3127 15.7277C12.388 15.4867 12.4266 15.2345 12.4266 14.9793C12.4266 14.4749 12.2802 13.9908 12.0036 13.5779C12.2964 13.5068 12.5852 13.4183 12.8679 13.3166V15.5355H12.8669ZM9.8731 12.9424C10.4416 12.9424 10.9704 13.1722 11.3588 13.5881C11.7137 13.9705 11.908 14.4637 11.908 14.9793C11.908 15.3342 11.8134 15.683 11.6364 15.9911C11.5531 16.1375 11.4534 16.2708 11.3375 16.3908C10.9663 16.7762 10.47 16.9948 9.93717 17.0121L9.88124 17.0151H9.86192L9.80598 17.0121C9.47345 17.0009 9.15414 16.9124 8.87143 16.7518C8.75753 16.6877 8.64974 16.6135 8.55008 16.527C8.50025 16.4853 8.45347 16.4385 8.40568 16.3908V16.3877C8.28873 16.2718 8.18907 16.1386 8.1067 15.9911C7.92976 15.683 7.83519 15.3342 7.83519 14.9793C7.83519 14.4637 8.03247 13.9705 8.38432 13.5901C8.77279 13.1712 9.30159 12.9413 9.8731 12.9413V12.9424ZM7.74061 13.5779C7.46401 13.9908 7.31757 14.4749 7.31757 14.9793C7.31757 15.2345 7.35621 15.4867 7.43452 15.7277C7.24537 15.6708 7.06029 15.6057 6.87724 15.5355V13.3166C7.15995 13.4193 7.44875 13.5068 7.74163 13.5779H7.74061ZM5.63151 24.8841C3.4919 23.1269 1.5811 20.2663 1.5811 17.1443V7.39198C1.73059 7.48046 1.88618 7.56384 2.04177 7.64418C2.26854 7.76113 2.50447 7.87401 2.74039 7.97977C3.6302 8.37332 4.60035 8.68958 5.63151 8.92246V24.8841ZM9.8731 26.7552C8.98838 26.7552 7.93586 26.4064 6.87623 25.7688V19.8005L8.11281 19.3795L8.85821 20.6049L9.87005 17.8277L10.8849 20.6079L11.6283 19.3795L12.8669 19.8005V25.7688C11.8083 26.4064 10.7548 26.7552 9.8731 26.7552ZM18.159 17.1443C18.159 20.2652 16.2523 23.1269 14.1116 24.8841V8.92246C15.1428 8.68958 16.1129 8.37332 17.0028 7.97977C17.2387 7.87401 17.4736 7.76113 17.7044 7.64418C17.86 7.56384 18.0126 7.48046 18.159 7.39504V17.1443Z"
            fill="white"/>
          <path d="M5.15215 4.04492H3.66846V5.52861H5.15215V4.04492Z" fill="white"/>
          <path d="M3.35887 2.66504H2.30127V3.72264H3.35887V2.66504Z" fill="white"/>
        </g>
        <defs>
          <clipPath id="clip0_175_1342">
            <rect width="121.624" height="28" fill="white" transform="translate(0.337402)"/>
          </clipPath>
        </defs>
      </svg>
      <svg class="mx-auto hidden logo-small" width="20" height="29" viewBox="0 0 20 29" fill="none"
           xmlns="http://www.w3.org/2000/svg">
        <path
          d="M18.5432 6.06358C18.1578 6.34628 17.7256 6.61272 17.2598 6.85373C17.0544 6.96152 16.8409 7.06118 16.6273 7.1588C15.8931 7.4832 15.0917 7.75167 14.2406 7.96014V3.14195H12.9959C12.9959 3.54872 12.6542 3.95956 12.0562 4.26972C11.4908 4.56463 10.7607 4.72632 10.001 4.72632C9.24138 4.72632 8.51123 4.56361 7.94378 4.2687C7.34685 3.95956 7.00516 3.54872 7.00516 3.14195C7.00516 2.58366 7.98649 1.55962 9.58917 1.55962C10.6396 1.55962 11.1196 1.86266 11.2142 2.12808C11.2254 2.15859 11.2315 2.19011 11.2315 2.22265C11.2315 2.53688 10.7057 2.88772 10.002 2.88772V4.13244C11.3891 4.13244 12.4762 3.29347 12.4762 2.22265C12.4762 2.04367 12.4447 1.86673 12.3857 1.70605C12.2372 1.2881 11.6688 0.315918 9.59018 0.315918C7.39871 0.315918 5.76146 1.80876 5.76146 3.14297C5.76146 4.03074 6.34925 4.84428 7.37126 5.3741C8.11056 5.75952 9.04511 5.97104 10.001 5.97104C10.9569 5.97104 11.8915 5.7585 12.6298 5.3741C12.7589 5.30698 12.882 5.2358 12.9969 5.15953V8.22047V13.0783C12.6227 13.2237 12.2372 13.3417 11.8437 13.4292C11.8172 13.4353 11.7918 13.4403 11.7654 13.4454C11.2915 12.997 10.6569 12.7387 10.0031 12.7387C9.34917 12.7387 8.71156 12.996 8.23767 13.4454C8.21123 13.4393 8.18479 13.4353 8.15734 13.4281C7.7648 13.3407 7.38041 13.2227 7.00618 13.0773V7.03576C6.86788 6.97779 6.73466 6.91372 6.60449 6.84762C6.29942 6.69 6.01671 6.50899 5.76146 6.30967V7.95912C4.9103 7.75167 4.10896 7.48219 3.37474 7.15779C3.16119 7.06118 2.94763 6.9605 2.74222 6.85271C2.27952 6.61475 1.84732 6.34526 1.45581 6.05951L0.466339 5.33749V17.4592C0.466339 21.5096 3.26898 25.1431 6.04417 26.9482C6.36552 27.1556 6.68686 27.3448 7.00618 27.5054C8.05158 28.0373 9.07155 28.3149 10.0031 28.3149C10.9346 28.3149 11.9545 28.0373 12.9969 27.5054C13.3162 27.3448 13.6375 27.1566 13.9589 26.9482C16.7341 25.1431 19.5337 21.5117 19.5337 17.4592V5.33241L18.5442 6.06459L18.5432 6.06358ZM12.9959 19.8216L11.9484 16.9416C12.0257 16.8531 12.0928 16.7586 12.1559 16.662C12.1559 16.662 12.1559 16.6609 12.1569 16.6599C12.4416 16.5887 12.7223 16.5043 12.9959 16.4057V19.8226V19.8216ZM7.00516 16.4047C7.27872 16.5033 7.55837 16.5887 7.84413 16.6589C7.84413 16.6589 7.84412 16.6599 7.84514 16.6609C7.90921 16.7606 7.97836 16.8552 8.05056 16.9437L7.00516 19.8216V16.4047ZM8.46343 17.3321C8.47767 17.3433 8.49089 17.3545 8.50818 17.3657C8.5336 17.385 8.56106 17.4013 8.58851 17.4216C8.63834 17.4572 8.69122 17.4877 8.74614 17.5182C8.74614 17.5213 8.74919 17.5213 8.74919 17.5213C8.8102 17.5548 8.87122 17.5874 8.93528 17.6158C9.04308 17.6657 9.15697 17.7104 9.27392 17.743C9.36036 17.7684 9.45087 17.7897 9.54239 17.807C9.55052 17.8101 9.56171 17.8101 9.56985 17.8121L8.87122 19.73L8.46953 19.067L7.7404 19.3161L8.46343 17.3311V17.3321ZM10.4536 17.806C10.5807 17.7836 10.7057 17.7531 10.8278 17.7114C10.8613 17.7002 10.8918 17.686 10.9224 17.6758C10.961 17.6616 10.9996 17.6453 11.0363 17.6291C11.1115 17.5955 11.1827 17.5599 11.2529 17.5182C11.3251 17.4796 11.3912 17.4348 11.4583 17.3911C11.4613 17.388 11.4634 17.386 11.4634 17.386C11.4888 17.3697 11.5132 17.3525 11.5386 17.3331L12.2616 19.3182L11.5295 19.069L11.1298 19.729L10.4312 17.8131C10.4393 17.8131 10.4454 17.8101 10.4536 17.808V17.806ZM12.9959 15.8505C12.8138 15.9206 12.6298 15.9857 12.4416 16.0427C12.5169 15.8016 12.5555 15.5494 12.5555 15.2942C12.5555 14.7898 12.4091 14.3058 12.1325 13.8929C12.4254 13.8217 12.7142 13.7332 12.9969 13.6315V15.8505H12.9959ZM10.002 13.2573C10.5705 13.2573 11.0993 13.4871 11.4878 13.903C11.8427 14.2854 12.0369 14.7786 12.0369 15.2942C12.0369 15.6491 11.9423 15.9979 11.7654 16.306C11.682 16.4525 11.5823 16.5857 11.4664 16.7057C11.0952 17.0911 10.599 17.3097 10.0661 17.327L10.0102 17.3301H9.99085L9.93492 17.327C9.60239 17.3158 9.28307 17.2274 9.00037 17.0667C8.88647 17.0026 8.77868 16.9284 8.67902 16.842C8.62919 16.8003 8.58241 16.7535 8.53462 16.7057V16.7026C8.41767 16.5867 8.31801 16.4535 8.23564 16.306C8.0587 15.9979 7.96412 15.6491 7.96412 15.2942C7.96412 14.7786 8.16141 14.2854 8.51326 13.9051C8.90173 13.4861 9.43053 13.2563 10.002 13.2563V13.2573ZM7.86955 13.8929C7.59295 14.3058 7.44651 14.7898 7.44651 15.2942C7.44651 15.5494 7.48515 15.8016 7.56345 16.0427C7.37431 15.9857 7.18923 15.9206 7.00618 15.8505V13.6315C7.28888 13.7342 7.57769 13.8217 7.87056 13.8929H7.86955ZM5.76045 25.199C3.62084 23.4418 1.71004 20.5812 1.71004 17.4592V7.70693C1.85953 7.7954 2.01511 7.87879 2.1707 7.95912C2.39748 8.07607 2.6334 8.18895 2.86933 8.29471C3.75914 8.68826 4.72929 9.00452 5.76045 9.2374V25.199ZM10.002 27.0702C9.11731 27.0702 8.0648 26.7214 7.00516 26.0838V20.1154L8.24174 19.6944L8.98715 20.9198L9.99899 18.1426L11.0139 20.9229L11.7572 19.6944L12.9959 20.1154V26.0838C11.9372 26.7214 10.8837 27.0702 10.002 27.0702ZM18.2879 17.4592C18.2879 20.5802 16.3812 23.4418 14.2406 25.199V9.2374C15.2717 9.00452 16.2419 8.68826 17.1317 8.29471C17.3676 8.18895 17.6025 8.07607 17.8334 7.95912C17.989 7.87879 18.1415 7.7954 18.2879 7.70998V17.4592Z"
          fill="white"/>
        <path d="M5.28144 4.35986H3.79774V5.84356H5.28144V4.35986Z" fill="white"/>
        <path d="M3.48762 2.97998H2.43002V4.03758H3.48762V2.97998Z" fill="white"/>
      </svg>
    </a>
    <div class="icon-arrow-aside flex items-center justify-center size-8 p-2 rounded-full border border-neutral-100 bg-white
                absolute top-[38px] -right-4 cursor-pointer">
      <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g clip-path="url(#clip0_175_1402)">
          <path
            d="M6.04942 9.03803L11.1159 14.1045L10.643 14.5774L4.7499 8.68429L10.6375 2.79076L11.1149 3.26547L6.04942 8.33092L5.69586 8.68447L6.04942 9.03803Z"
            stroke="#10C285"/>
        </g>
        <defs>
          <clipPath id="clip0_175_1402">
            <rect width="16" height="16" fill="white" transform="translate(0.149414 0.68457)"/>
          </clipPath>
        </defs>
      </svg>
    </div>
  </div>
  <div class="shadow-424D5B14 bg-dark flex-1 py-6 px-4">
    <ul>
      <li class="mb-2 last:mb-0">
        <a href="./student-dashboard.html" class="flex items-center gap-3 p-3 rounded hover:bg-green group">
          <!--        khi ở trạng thái active thêm class  bg-green-->
          <div class="w-4 h-4 flex items-center justify-center">
            <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
              <g clip-path="url(#clip0_175_7829)">
                <path class="group-hover:fill-white"
                      d="M3.33333 11.1488H8.33333C8.79167 11.1488 9.16667 10.7738 9.16667 10.3154V3.64876C9.16667 3.19043 8.79167 2.81543 8.33333 2.81543H3.33333C2.875 2.81543 2.5 3.19043 2.5 3.64876V10.3154C2.5 10.7738 2.875 11.1488 3.33333 11.1488ZM3.33333 17.8154H8.33333C8.79167 17.8154 9.16667 17.4404 9.16667 16.9821V13.6488C9.16667 13.1904 8.79167 12.8154 8.33333 12.8154H3.33333C2.875 12.8154 2.5 13.1904 2.5 13.6488V16.9821C2.5 17.4404 2.875 17.8154 3.33333 17.8154ZM11.6667 17.8154H16.6667C17.125 17.8154 17.5 17.4404 17.5 16.9821V10.3154C17.5 9.8571 17.125 9.4821 16.6667 9.4821H11.6667C11.2083 9.4821 10.8333 9.8571 10.8333 10.3154V16.9821C10.8333 17.4404 11.2083 17.8154 11.6667 17.8154ZM10.8333 3.64876V6.9821C10.8333 7.44043 11.2083 7.81543 11.6667 7.81543H16.6667C17.125 7.81543 17.5 7.44043 17.5 6.9821V3.64876C17.5 3.19043 17.125 2.81543 16.6667 2.81543H11.6667C11.2083 2.81543 10.8333 3.19043 10.8333 3.64876Z"
                      fill="#ABB3BF"/>
                <!-- khi ở trạng thái active fill của path đổi sang màu #fff -->
              </g>
              <defs>
                <clipPath id="clip0_175_7829">
                  <rect width="20" height="20" fill="white" transform="translate(0 0.31543)"/>
                </clipPath>
              </defs>
            </svg>
          </div>
          <div
            class="menu__text text-12-16 font-semibold text-neutral-400 group-hover:text-white group-hover:font-semibold">
            <!--        khi ở trạng thái active đổi class text-neutral-400 thành text-white-->
            Dashboard
          </div>
        </a>
      </li>
      <li class="mb-2 last:mb-0">
        <a href="./student-message.html" class="flex items-center gap-3 p-3 rounded hover:bg-green group bg-green">
          <div class="w-4 h-4 flex items-center justify-center">
            <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
              <g clip-path="url(#clip0_175_5101)">
                <path
                  d="M16.6667 1.98193H3.33333C2.41666 1.98193 1.675 2.73193 1.675 3.6486L1.66666 18.6486L5 15.3153H16.6667C17.5833 15.3153 18.3333 14.5653 18.3333 13.6486V3.6486C18.3333 2.73193 17.5833 1.98193 16.6667 1.98193ZM5.83333 7.81527H14.1667C14.625 7.81527 15 8.19027 15 8.6486C15 9.10693 14.625 9.48193 14.1667 9.48193H5.83333C5.375 9.48193 5 9.10693 5 8.6486C5 8.19027 5.375 7.81527 5.83333 7.81527ZM10.8333 11.9819H5.83333C5.375 11.9819 5 11.6069 5 11.1486C5 10.6903 5.375 10.3153 5.83333 10.3153H10.8333C11.2917 10.3153 11.6667 10.6903 11.6667 11.1486C11.6667 11.6069 11.2917 11.9819 10.8333 11.9819ZM14.1667 6.98193H5.83333C5.375 6.98193 5 6.60693 5 6.1486C5 5.69027 5.375 5.31527 5.83333 5.31527H14.1667C14.625 5.31527 15 5.69027 15 6.1486C15 6.60693 14.625 6.98193 14.1667 6.98193Z"
                  fill="white"/>
              </g>
              <defs>
                <clipPath id="clip0_175_5101">
                  <rect width="20" height="20" fill="white" transform="translate(0 0.31543)"/>
                </clipPath>
              </defs>
            </svg>
          </div>
          <div class="menu__text text-12-16 text-white">
            Message
          </div>
        </a>
      </li>
      <li class="mb-2 last:mb-0">
        <a href="./student-schedule.html" class="flex items-center gap-3 p-3 rounded hover:bg-green group">
          <div class="w-4 h-4 flex items-center justify-center">
            <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path class="group-hover:fill-white"
                    d="M14.1667 2.81559H17.5C17.721 2.81559 17.933 2.90339 18.0893 3.05967C18.2455 3.21595 18.3333 3.42791 18.3333 3.64893V16.9823C18.3333 17.2033 18.2455 17.4152 18.0893 17.5715C17.933 17.7278 17.721 17.8156 17.5 17.8156H2.5C2.27898 17.8156 2.06702 17.7278 1.91074 17.5715C1.75446 17.4152 1.66666 17.2033 1.66666 16.9823V3.64893C1.66666 3.42791 1.75446 3.21595 1.91074 3.05967C2.06702 2.90339 2.27898 2.81559 2.5 2.81559H5.83333V1.14893H7.5V2.81559H12.5V1.14893H14.1667V2.81559ZM3.33333 7.81559V16.1489H16.6667V7.81559H3.33333ZM5 11.1489H9.16666V14.4823H5V11.1489Z"
                    fill="#ABB3BF"/>
            </svg>
          </div>
          <div class="menu__text text-12-16 text-neutral-400 group-hover:text-white group-hover:font-semibold">
            Schedule
          </div>
        </a>
      </li>
      <li class="mb-2 last:mb-0">
        <a href="./student-grade.html" class="flex items-center gap-3 p-3 rounded hover:bg-green group">
          <div class="w-4 h-4 flex items-center justify-center">
            <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path class="group-hover:fill-white"
                    d="M10 14.7072L14.3083 17.3072C14.625 17.4989 15.0167 17.2155 14.9333 16.8572L13.7917 11.9572L17.5917 8.66553C17.8667 8.42386 17.725 7.96553 17.35 7.93219L12.3417 7.50719L10.3833 2.89053C10.2417 2.54886 9.75833 2.54886 9.61666 2.89053L7.65833 7.50719L2.65 7.93219C2.28333 7.96553 2.13333 8.42386 2.41666 8.66553L6.21667 11.9572L5.075 16.8572C4.99167 17.2155 5.38333 17.4989 5.7 17.3072L10 14.7072Z"
                    fill="#ABB3BF"/>
            </svg>
          </div>
          <div class="menu__text text-12-16 text-neutral-400 group-hover:text-white group-hover:font-semibold">
            Grade
          </div>
        </a>
      </li>
      <li class="mb-2 last:mb-0">
        <a href="./student-feedback-report.html" class="flex items-center gap-3 p-3 rounded hover:bg-green group">
          <div class="w-4 h-4 flex items-center justify-center">
            <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path class="group-hover:fill-white"
                    d="M15.8333 18.6486H4.16666C3.50362 18.6486 2.86774 18.3852 2.3989 17.9164C1.93006 17.4475 1.66666 16.8116 1.66666 16.1486V2.81527C1.66666 2.59425 1.75446 2.38229 1.91074 2.22601C2.06702 2.06973 2.27898 1.98193 2.5 1.98193H14.1667C14.3877 1.98193 14.5996 2.06973 14.7559 2.22601C14.9122 2.38229 15 2.59425 15 2.81527V12.8153H18.3333V16.1486C18.3333 16.8116 18.0699 17.4475 17.6011 17.9164C17.1323 18.3852 16.4964 18.6486 15.8333 18.6486ZM15 14.4819V16.1486C15 16.3696 15.0878 16.5816 15.2441 16.7379C15.4004 16.8941 15.6123 16.9819 15.8333 16.9819C16.0543 16.9819 16.2663 16.8941 16.4226 16.7379C16.5789 16.5816 16.6667 16.3696 16.6667 16.1486V14.4819H15ZM5 6.1486V7.81527H11.6667V6.1486H5ZM5 9.48193V11.1486H11.6667V9.48193H5ZM5 12.8153V14.4819H9.16666V12.8153H5Z"
                    fill="#ABB3BF"/>
            </svg>
          </div>
          <div class="menu__text text-12-16 text-neutral-400 group-hover:text-white group-hover:font-semibold">
            Feedback Report
          </div>
        </a>
      </li>
      <li class="mb-2 last:mb-0">
        <a href="./parent-invoice.html" class="flex items-center gap-3 p-3 rounded hover:bg-green group">
          <div class="w-4 h-4 flex items-center justify-center">
            <svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path class="group-hover:fill-white"
                    d="M15.3333 5.6486V12.3153C15.3333 13.0486 14.7333 13.6486 14 13.6486H3.33332C2.96666 13.6486 2.66666 13.3486 2.66666 12.9819C2.66666 12.6153 2.96666 12.3153 3.33332 12.3153H14V5.6486C14 5.28193 14.3 4.98193 14.6667 4.98193C15.0333 4.98193 15.3333 5.28193 15.3333 5.6486ZM2.66666 10.9819C1.55999 10.9819 0.666656 10.0886 0.666656 8.98193V4.98193C0.666656 3.87527 1.55999 2.98193 2.66666 2.98193H10.6667C11.7733 2.98193 12.6667 3.87527 12.6667 4.98193V9.6486C12.6667 10.3819 12.0667 10.9819 11.3333 10.9819H2.66666ZM4.66666 6.98193C4.66666 8.0886 5.55999 8.98193 6.66666 8.98193C7.77332 8.98193 8.66666 8.0886 8.66666 6.98193C8.66666 5.87527 7.77332 4.98193 6.66666 4.98193C5.55999 4.98193 4.66666 5.87527 4.66666 6.98193Z"
                    fill="#ABB3BF"/>
            </svg>
          </div>
          <div class="menu__text text-12-16 text-neutral-400 group-hover:text-white group-hover:font-semibold">
            Invoice
          </div>
        </a>
      </li>
      <li class="mb-2 last:mb-0">
        <a href="./student-profile.html" class="flex items-center gap-3 p-3 rounded hover:bg-green group">
          <div class="w-4 h-4 flex items-center justify-center">
            <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path class="group-hover:fill-white"
                    d="M10 1.98193C5.4 1.98193 1.66666 5.71527 1.66666 10.3153C1.66666 14.9153 5.4 18.6486 10 18.6486C14.6 18.6486 18.3333 14.9153 18.3333 10.3153C18.3333 5.71527 14.6 1.98193 10 1.98193ZM10 4.48193C11.3833 4.48193 12.5 5.5986 12.5 6.98193C12.5 8.36527 11.3833 9.48193 10 9.48193C8.61666 9.48193 7.5 8.36527 7.5 6.98193C7.5 5.5986 8.61666 4.48193 10 4.48193ZM10 16.3153C7.91666 16.3153 6.075 15.2486 5 13.6319C5.025 11.9736 8.33333 11.0653 10 11.0653C11.6583 11.0653 14.975 11.9736 15 13.6319C13.925 15.2486 12.0833 16.3153 10 16.3153Z"
                    fill="#ABB3BF"/>
            </svg>
          </div>
          <div class="menu__text text-12-16 text-neutral-400 group-hover:text-white group-hover:font-semibold">
            Profile
          </div>
        </a>
      </li>
    </ul>
  </div>
</aside>
<div class="side-bar w-[264px] flex flex-col transition fixed z-[52] top-0 bottom-0 -translate-x-full">
  <div class="bg-neutral-800 py-[14px] px-4">
    <a href="#">
      <div class="w-[122px] h-7">
        <svg class="logo-full" width="122" height="28" viewBox="0 0 122 28" fill="none"
             xmlns="http://www.w3.org/2000/svg">
          <g clip-path="url(#clip0_175_1342)">
            <path
              d="M37.4784 17.8957L36.373 19.1272L35.1974 18.0778C34.7784 18.3666 34.3208 18.5933 33.8266 18.756C33.3324 18.9188 32.7954 19.0011 32.2178 19.0011C31.4623 19.0011 30.7718 18.8679 30.1474 18.6025C29.522 18.3371 28.986 17.9751 28.5376 17.5185C28.0901 17.0619 27.7403 16.5249 27.4881 15.9097C27.2369 15.2944 27.1108 14.6416 27.1108 13.9511C27.1108 13.2606 27.2369 12.6077 27.4881 11.9925C27.7403 11.3772 28.0942 10.8383 28.5518 10.3766C29.0084 9.91491 29.5494 9.54881 30.1748 9.27831C30.7992 9.00781 31.4897 8.87256 32.2453 8.87256C33.0009 8.87256 33.6903 9.00578 34.3157 9.27119C34.9401 9.53661 35.4771 9.89864 35.9245 10.3552C36.372 10.8118 36.7218 11.3488 36.974 11.964C37.2262 12.5792 37.3523 13.2321 37.3523 13.9226C37.3523 14.4819 37.2658 15.0189 37.093 15.5314C36.9201 16.0449 36.675 16.5158 36.3588 16.9439L37.4784 17.8957ZM32.2463 15.5171L33.3507 14.2582L35.0429 15.7968C35.2117 15.5354 35.3367 15.2487 35.4211 14.9365C35.5045 14.6243 35.5472 14.2958 35.5472 13.9501C35.5472 13.4741 35.4649 13.0247 35.3022 12.5996C35.1384 12.1755 34.9106 11.8044 34.6168 11.4871C34.3229 11.1708 33.971 10.9186 33.5602 10.7315C33.1504 10.5454 32.7019 10.4518 32.2168 10.4518C31.7317 10.4518 31.2863 10.5423 30.8806 10.7244C30.4748 10.9064 30.127 11.1535 29.8382 11.4657C29.5494 11.7789 29.3226 12.1471 29.1599 12.5711C28.9962 12.9952 28.9149 13.4457 28.9149 13.9206C28.9149 14.3955 28.9962 14.846 29.1599 15.27C29.3226 15.6941 29.5515 16.0653 29.8454 16.3825C30.1392 16.6998 30.4911 16.952 30.9009 17.1381C31.3107 17.3242 31.7592 17.4178 32.2443 17.4178C32.5707 17.4178 32.8738 17.3761 33.1534 17.2917C33.4331 17.2083 33.6893 17.0913 33.9222 16.9419L32.2443 15.5151L32.2463 15.5171Z"
              fill="white"/>
            <path d="M41.1713 8.97119H42.3743V18.8343H40.6679V10.7203L38.9045 11.2094L38.5415 9.81015L41.1713 8.97119Z"
                  fill="white"/>
            <path
              d="M58.5456 18.8334H56.7263L55.7328 16.469H51.1017L50.0939 18.8334H48.3306L52.6403 8.97021H54.2348L58.5446 18.8334H58.5456ZM53.4111 11.0264L51.7332 14.9436H55.1043L53.4121 11.0264H53.4111Z"
              fill="white"/>
            <path
              d="M67.3528 17.9872C67.0772 18.1967 66.7812 18.3787 66.465 18.5333C66.1477 18.6868 65.7999 18.8038 65.4226 18.8831C65.0454 18.9624 64.6274 19.0021 64.1708 19.0021C63.4529 19.0021 62.7898 18.8709 62.1847 18.6105C61.5787 18.3492 61.0539 17.9933 60.6105 17.5407C60.1672 17.0882 59.8204 16.5543 59.5682 15.9391C59.317 15.3238 59.1909 14.6608 59.1909 13.953C59.1909 13.2453 59.314 12.5965 59.5621 11.9802C59.8092 11.365 60.157 10.826 60.6044 10.3643C61.0519 9.90262 61.5837 9.53856 62.199 9.27315C62.8142 9.00773 63.4956 8.87451 64.241 8.87451C64.6884 8.87451 65.0962 8.91214 65.4653 8.98637C65.8335 9.06061 66.1721 9.16637 66.4792 9.30162C66.7873 9.43687 67.0711 9.59755 67.3334 9.78466C67.5948 9.97076 67.8419 10.1762 68.0748 10.3999L66.9694 11.6731C66.5779 11.309 66.163 11.0151 65.7247 10.7914C65.2864 10.5677 64.7871 10.4558 64.2277 10.4558C63.761 10.4558 63.3298 10.5463 62.9332 10.7284C62.5366 10.9104 62.1939 11.1575 61.9051 11.4697C61.6163 11.7829 61.3915 12.148 61.2329 12.568C61.0743 12.988 60.9949 13.4405 60.9949 13.9246C60.9949 14.4086 61.0743 14.8642 61.2329 15.2883C61.3915 15.7133 61.6153 16.0835 61.9051 16.4008C62.1939 16.7181 62.5366 16.9672 62.9332 17.1492C63.3288 17.3313 63.761 17.4218 64.2277 17.4218C64.8247 17.4218 65.3372 17.3079 65.7664 17.0791C66.1955 16.8513 66.6195 16.5401 67.0385 16.1486L68.1439 17.2682C67.8927 17.5387 67.6283 17.7787 67.3538 17.9882L67.3528 17.9872Z"
              fill="white"/>
            <path
              d="M79.321 18.8334H77.5017L76.5082 16.469H71.8771L70.8693 18.8334H69.106L73.4157 8.97021H75.0102L79.3199 18.8334H79.321ZM74.1865 11.0264L72.5086 14.9436H75.8797L74.1875 11.0264H74.1865Z"
              fill="white"/>
            <path
              d="M89.4766 15.861C89.2254 16.463 88.8675 16.9827 88.4068 17.421C87.9451 17.8593 87.3949 18.204 86.7563 18.4562C86.1177 18.7084 85.415 18.8345 84.6513 18.8345H80.9995V9.0415H84.6513C85.416 9.0415 86.1177 9.16557 86.7563 9.41268C87.3949 9.65979 87.9451 10.0025 88.4068 10.4408C88.8685 10.8791 89.2254 11.3967 89.4766 11.9936C89.7278 12.5906 89.8539 13.2343 89.8539 13.9248C89.8539 14.6153 89.7278 15.261 89.4766 15.862V15.861ZM87.8048 12.6099C87.641 12.2021 87.4122 11.8492 87.1173 11.5492C86.8224 11.2503 86.4645 11.0154 86.0445 10.8465C85.6235 10.6777 85.1567 10.5933 84.6421 10.5933H82.7069V17.2806H84.6421C85.1567 17.2806 85.6245 17.1993 86.0445 17.0345C86.4655 16.8708 86.8234 16.641 87.1173 16.3461C87.4112 16.0512 87.64 15.6993 87.8048 15.2925C87.9685 14.8848 88.0499 14.4383 88.0499 13.9512C88.0499 13.4641 87.9675 13.0167 87.8048 12.6099Z"
              fill="white"/>
            <path
              d="M99.1856 10.5934H93.6454V13.1113H98.5561V14.6642H93.6454V17.2797H99.2557V18.8325H91.939V9.03955H99.1856V10.5924V10.5934Z"
              fill="white"/>
            <path
              d="M106.139 16.4269H106.083L103.019 11.8386V18.834H101.313V9.04102H103.145L106.125 13.6721L109.105 9.04102H110.937V18.834H109.231V11.8111L106.139 16.428V16.4269Z"
              fill="white"/>
            <path
              d="M118.072 18.833H116.337V14.9718L112.449 9.04004H114.478L117.219 13.3772L119.989 9.04004H121.961L118.072 14.9301V18.833Z"
              fill="white"/>
            <path
              d="M18.4142 5.74863C18.0288 6.03134 17.5966 6.29777 17.1309 6.53878C16.9255 6.64658 16.7119 6.74624 16.4984 6.84386C15.7641 7.16826 14.9628 7.43673 14.1116 7.6452V2.82701H12.8669C12.8669 3.23378 12.5252 3.64462 11.9273 3.95478C11.3619 4.24969 10.6317 4.41138 9.87208 4.41138C9.11244 4.41138 8.38229 4.24867 7.81485 3.95376C7.21791 3.64462 6.87623 3.23378 6.87623 2.82701C6.87623 2.26872 7.85756 1.24468 9.46023 1.24468C10.5107 1.24468 10.9907 1.54772 11.0853 1.81314C11.0965 1.84364 11.1026 1.87517 11.1026 1.90771C11.1026 2.22194 10.5768 2.57278 9.8731 2.57278V3.81749C11.2602 3.81749 12.3473 2.97853 12.3473 1.90771C12.3473 1.72873 12.3158 1.55179 12.2568 1.39111C12.1083 0.973156 11.5398 0.000976562 9.46125 0.000976562C7.26978 0.000976562 5.63253 1.49382 5.63253 2.82803C5.63253 3.7158 6.22031 4.52934 7.24232 5.05916C7.98162 5.44457 8.91618 5.65609 9.87208 5.65609C10.828 5.65609 11.7625 5.44356 12.5008 5.05916C12.63 4.99204 12.753 4.92086 12.8679 4.84459V7.90553V12.7634C12.4937 12.9088 12.1083 13.0268 11.7148 13.1142C11.6883 13.1203 11.6629 13.1254 11.6364 13.1305C11.1626 12.682 10.528 12.4237 9.87412 12.4237C9.22024 12.4237 8.58262 12.681 8.10874 13.1305C8.0823 13.1244 8.05586 13.1203 8.0284 13.1132C7.63587 13.0257 7.25147 12.9078 6.87724 12.7624V6.72081C6.73894 6.66285 6.60572 6.59878 6.47556 6.53268C6.17048 6.37506 5.88777 6.19405 5.63253 5.99473V7.64418C4.78136 7.43673 3.98003 7.16724 3.24581 6.84284C3.03225 6.74624 2.8187 6.64556 2.61328 6.53777C2.15058 6.29981 1.71839 6.03032 1.32687 5.74457L0.337402 5.02255V17.1443C0.337402 21.1947 3.14005 24.8282 5.91523 26.6332C6.23658 26.8407 6.55793 27.0298 6.87724 27.1905C7.92264 27.7223 8.94262 28 9.87412 28C10.8056 28 11.8256 27.7223 12.8679 27.1905C13.1873 27.0298 13.5086 26.8417 13.83 26.6332C16.6051 24.8282 19.4047 21.1967 19.4047 17.1443V5.01747L18.4153 5.74965L18.4142 5.74863ZM12.8669 19.5066L11.8195 16.6267C11.8968 16.5382 11.9639 16.4436 12.0269 16.347C12.0269 16.347 12.0269 16.346 12.028 16.345C12.3127 16.2738 12.5934 16.1894 12.8669 16.0908V19.5076V19.5066ZM6.87623 16.0897C7.14978 16.1884 7.42943 16.2738 7.71519 16.344C7.71519 16.344 7.71519 16.345 7.7162 16.346C7.78027 16.4457 7.84942 16.5402 7.92162 16.6287L6.87623 19.5066V16.0897ZM8.3345 17.0172C8.34873 17.0284 8.36195 17.0395 8.37924 17.0507C8.40466 17.0701 8.43212 17.0863 8.45958 17.1067C8.50941 17.1423 8.56229 17.1728 8.6172 17.2033C8.6172 17.2063 8.62025 17.2063 8.62025 17.2063C8.68127 17.2399 8.74228 17.2724 8.80635 17.3009C8.91414 17.3507 9.02804 17.3955 9.14498 17.428C9.23142 17.4534 9.32193 17.4748 9.41345 17.4921C9.42159 17.4951 9.43277 17.4951 9.44091 17.4972L8.74228 19.4151L8.3406 18.752L7.61146 19.0012L8.3345 17.0162V17.0172ZM10.3246 17.4911C10.4517 17.4687 10.5768 17.4382 10.6988 17.3965C10.7324 17.3853 10.7629 17.3711 10.7934 17.3609C10.8321 17.3467 10.8707 17.3304 10.9073 17.3141C10.9826 17.2806 11.0538 17.245 11.1239 17.2033C11.1961 17.1646 11.2622 17.1199 11.3293 17.0762C11.3324 17.0731 11.3344 17.0711 11.3344 17.0711C11.3598 17.0548 11.3843 17.0375 11.4097 17.0182L12.1327 19.0032L11.4005 18.7541L11.0009 19.4141L10.3022 17.4982C10.3104 17.4982 10.3165 17.4951 10.3246 17.4931V17.4911ZM12.8669 15.5355C12.6849 15.6057 12.5008 15.6708 12.3127 15.7277C12.388 15.4867 12.4266 15.2345 12.4266 14.9793C12.4266 14.4749 12.2802 13.9908 12.0036 13.5779C12.2964 13.5068 12.5852 13.4183 12.8679 13.3166V15.5355H12.8669ZM9.8731 12.9424C10.4416 12.9424 10.9704 13.1722 11.3588 13.5881C11.7137 13.9705 11.908 14.4637 11.908 14.9793C11.908 15.3342 11.8134 15.683 11.6364 15.9911C11.5531 16.1375 11.4534 16.2708 11.3375 16.3908C10.9663 16.7762 10.47 16.9948 9.93717 17.0121L9.88124 17.0151H9.86192L9.80598 17.0121C9.47345 17.0009 9.15414 16.9124 8.87143 16.7518C8.75753 16.6877 8.64974 16.6135 8.55008 16.527C8.50025 16.4853 8.45347 16.4385 8.40568 16.3908V16.3877C8.28873 16.2718 8.18907 16.1386 8.1067 15.9911C7.92976 15.683 7.83519 15.3342 7.83519 14.9793C7.83519 14.4637 8.03247 13.9705 8.38432 13.5901C8.77279 13.1712 9.30159 12.9413 9.8731 12.9413V12.9424ZM7.74061 13.5779C7.46401 13.9908 7.31757 14.4749 7.31757 14.9793C7.31757 15.2345 7.35621 15.4867 7.43452 15.7277C7.24537 15.6708 7.06029 15.6057 6.87724 15.5355V13.3166C7.15995 13.4193 7.44875 13.5068 7.74163 13.5779H7.74061ZM5.63151 24.8841C3.4919 23.1269 1.5811 20.2663 1.5811 17.1443V7.39198C1.73059 7.48046 1.88618 7.56384 2.04177 7.64418C2.26854 7.76113 2.50447 7.87401 2.74039 7.97977C3.6302 8.37332 4.60035 8.68958 5.63151 8.92246V24.8841ZM9.8731 26.7552C8.98838 26.7552 7.93586 26.4064 6.87623 25.7688V19.8005L8.11281 19.3795L8.85821 20.6049L9.87005 17.8277L10.8849 20.6079L11.6283 19.3795L12.8669 19.8005V25.7688C11.8083 26.4064 10.7548 26.7552 9.8731 26.7552ZM18.159 17.1443C18.159 20.2652 16.2523 23.1269 14.1116 24.8841V8.92246C15.1428 8.68958 16.1129 8.37332 17.0028 7.97977C17.2387 7.87401 17.4736 7.76113 17.7044 7.64418C17.86 7.56384 18.0126 7.48046 18.159 7.39504V17.1443Z"
              fill="white"/>
            <path d="M5.15215 4.04492H3.66846V5.52861H5.15215V4.04492Z" fill="white"/>
            <path d="M3.35887 2.66504H2.30127V3.72264H3.35887V2.66504Z" fill="white"/>
          </g>
          <defs>
            <clipPath id="clip0_175_1342">
              <rect width="121.624" height="28" fill="white" transform="translate(0.337402)"/>
            </clipPath>
          </defs>
        </svg>
      </div>
    </a>
  </div>
  <div class="shadow-424D5B14 bg-dark flex-1 py-6 px-4 overflow-y-auto">
    <ul>
      <li class="mb-2 last:mb-0">
        <a href="./student-dashboard.html" class="flex items-center gap-3 p-3 rounded hover:bg-green group">
          <!--        khi ở trạng thái active thêm class  bg-green-->
          <div class="w-4 h-4 flex items-center justify-center">
            <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
              <g clip-path="url(#clip0_175_7829)">
                <path
                  d="M3.33333 11.1488H8.33333C8.79167 11.1488 9.16667 10.7738 9.16667 10.3154V3.64876C9.16667 3.19043 8.79167 2.81543 8.33333 2.81543H3.33333C2.875 2.81543 2.5 3.19043 2.5 3.64876V10.3154C2.5 10.7738 2.875 11.1488 3.33333 11.1488ZM3.33333 17.8154H8.33333C8.79167 17.8154 9.16667 17.4404 9.16667 16.9821V13.6488C9.16667 13.1904 8.79167 12.8154 8.33333 12.8154H3.33333C2.875 12.8154 2.5 13.1904 2.5 13.6488V16.9821C2.5 17.4404 2.875 17.8154 3.33333 17.8154ZM11.6667 17.8154H16.6667C17.125 17.8154 17.5 17.4404 17.5 16.9821V10.3154C17.5 9.8571 17.125 9.4821 16.6667 9.4821H11.6667C11.2083 9.4821 10.8333 9.8571 10.8333 10.3154V16.9821C10.8333 17.4404 11.2083 17.8154 11.6667 17.8154ZM10.8333 3.64876V6.9821C10.8333 7.44043 11.2083 7.81543 11.6667 7.81543H16.6667C17.125 7.81543 17.5 7.44043 17.5 6.9821V3.64876C17.5 3.19043 17.125 2.81543 16.6667 2.81543H11.6667C11.2083 2.81543 10.8333 3.19043 10.8333 3.64876Z"
                  fill="#ABB3BF"/>
                <!-- khi ở trạng thái active fill của path đổi sang màu #fff -->
              </g>
              <defs>
                <clipPath id="clip0_175_7829">
                  <rect width="20" height="20" fill="white" transform="translate(0 0.31543)"/>
                </clipPath>
              </defs>
            </svg>
          </div>
          <div
            class="menu__text text-12-16 font-semibold text-neutral-400 group-hover:text-white group-hover:font-semibold">
            <!--        khi ở trạng thái active đổi class text-neutral-400 thành text-white-->
            Dashboard
          </div>
        </a>
      </li>
      <li class="mb-2 last:mb-0">
        <a href="./student-message.html" class="flex items-center gap-3 p-3 rounded hover:bg-green group bg-green">
          <div class="w-4 h-4 flex items-center justify-center">
            <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
              <g clip-path="url(#clip0_175_5101)">
                <path
                  d="M16.6667 1.98193H3.33333C2.41666 1.98193 1.675 2.73193 1.675 3.6486L1.66666 18.6486L5 15.3153H16.6667C17.5833 15.3153 18.3333 14.5653 18.3333 13.6486V3.6486C18.3333 2.73193 17.5833 1.98193 16.6667 1.98193ZM5.83333 7.81527H14.1667C14.625 7.81527 15 8.19027 15 8.6486C15 9.10693 14.625 9.48193 14.1667 9.48193H5.83333C5.375 9.48193 5 9.10693 5 8.6486C5 8.19027 5.375 7.81527 5.83333 7.81527ZM10.8333 11.9819H5.83333C5.375 11.9819 5 11.6069 5 11.1486C5 10.6903 5.375 10.3153 5.83333 10.3153H10.8333C11.2917 10.3153 11.6667 10.6903 11.6667 11.1486C11.6667 11.6069 11.2917 11.9819 10.8333 11.9819ZM14.1667 6.98193H5.83333C5.375 6.98193 5 6.60693 5 6.1486C5 5.69027 5.375 5.31527 5.83333 5.31527H14.1667C14.625 5.31527 15 5.69027 15 6.1486C15 6.60693 14.625 6.98193 14.1667 6.98193Z"
                  fill="white"/>
              </g>
              <defs>
                <clipPath id="clip0_175_5101">
                  <rect width="20" height="20" fill="white" transform="translate(0 0.31543)"/>
                </clipPath>
              </defs>
            </svg>
          </div>
          <div class="menu__text text-12-16 text-white">
            Message
          </div>
        </a>
      </li>
      <li class="mb-2 last:mb-0">
        <a href="./student-schedule.html" class="flex items-center gap-3 p-3 rounded hover:bg-green group">
          <div class="w-4 h-4 flex items-center justify-center">
            <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path class="group-hover:fill-white"
                    d="M14.1667 2.81559H17.5C17.721 2.81559 17.933 2.90339 18.0893 3.05967C18.2455 3.21595 18.3333 3.42791 18.3333 3.64893V16.9823C18.3333 17.2033 18.2455 17.4152 18.0893 17.5715C17.933 17.7278 17.721 17.8156 17.5 17.8156H2.5C2.27898 17.8156 2.06702 17.7278 1.91074 17.5715C1.75446 17.4152 1.66666 17.2033 1.66666 16.9823V3.64893C1.66666 3.42791 1.75446 3.21595 1.91074 3.05967C2.06702 2.90339 2.27898 2.81559 2.5 2.81559H5.83333V1.14893H7.5V2.81559H12.5V1.14893H14.1667V2.81559ZM3.33333 7.81559V16.1489H16.6667V7.81559H3.33333ZM5 11.1489H9.16666V14.4823H5V11.1489Z"
                    fill="#ABB3BF"/>
            </svg>
          </div>
          <div class="menu__text text-12-16 text-neutral-400 group-hover:text-white group-hover:font-semibold">
            Schedule
          </div>
        </a>
      </li>
      <li class="mb-2 last:mb-0">
        <a href="./student-grade.html" class="flex items-center gap-3 p-3 rounded hover:bg-green group">
          <div class="w-4 h-4 flex items-center justify-center">
            <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path class="group-hover:fill-white"
                    d="M10 14.7072L14.3083 17.3072C14.625 17.4989 15.0167 17.2155 14.9333 16.8572L13.7917 11.9572L17.5917 8.66553C17.8667 8.42386 17.725 7.96553 17.35 7.93219L12.3417 7.50719L10.3833 2.89053C10.2417 2.54886 9.75833 2.54886 9.61666 2.89053L7.65833 7.50719L2.65 7.93219C2.28333 7.96553 2.13333 8.42386 2.41666 8.66553L6.21667 11.9572L5.075 16.8572C4.99167 17.2155 5.38333 17.4989 5.7 17.3072L10 14.7072Z"
                    fill="#ABB3BF"/>
            </svg>
          </div>
          <div class="menu__text text-12-16 text-neutral-400 group-hover:text-white group-hover:font-semibold">
            Grade
          </div>
        </a>
      </li>
      <li class="mb-2 last:mb-0">
        <a href="./student-feedback-report.html" class="flex items-center gap-3 p-3 rounded hover:bg-green group ">
          <div class="w-4 h-4 flex items-center justify-center">
            <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path class="group-hover:fill-white"
                    d="M15.8333 18.6486H4.16666C3.50362 18.6486 2.86774 18.3852 2.3989 17.9164C1.93006 17.4475 1.66666 16.8116 1.66666 16.1486V2.81527C1.66666 2.59425 1.75446 2.38229 1.91074 2.22601C2.06702 2.06973 2.27898 1.98193 2.5 1.98193H14.1667C14.3877 1.98193 14.5996 2.06973 14.7559 2.22601C14.9122 2.38229 15 2.59425 15 2.81527V12.8153H18.3333V16.1486C18.3333 16.8116 18.0699 17.4475 17.6011 17.9164C17.1323 18.3852 16.4964 18.6486 15.8333 18.6486ZM15 14.4819V16.1486C15 16.3696 15.0878 16.5816 15.2441 16.7379C15.4004 16.8941 15.6123 16.9819 15.8333 16.9819C16.0543 16.9819 16.2663 16.8941 16.4226 16.7379C16.5789 16.5816 16.6667 16.3696 16.6667 16.1486V14.4819H15ZM5 6.1486V7.81527H11.6667V6.1486H5ZM5 9.48193V11.1486H11.6667V9.48193H5ZM5 12.8153V14.4819H9.16666V12.8153H5Z"
                    fill="#ABB3BF"/>
            </svg>
          </div>
          <div class="menu__text text-12-16 text-neutral-400 group-hover:text-white group-hover:font-semibold">
            Feedback Report
          </div>
        </a>
      </li>
      <li class="mb-2 last:mb-0">
        <a href="./parent-invoice.html" class="flex items-center gap-3 p-3 rounded hover:bg-green group">
          <div class="w-4 h-4 flex items-center justify-center">
            <svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path class="group-hover:fill-white"
                    d="M15.3333 5.6486V12.3153C15.3333 13.0486 14.7333 13.6486 14 13.6486H3.33332C2.96666 13.6486 2.66666 13.3486 2.66666 12.9819C2.66666 12.6153 2.96666 12.3153 3.33332 12.3153H14V5.6486C14 5.28193 14.3 4.98193 14.6667 4.98193C15.0333 4.98193 15.3333 5.28193 15.3333 5.6486ZM2.66666 10.9819C1.55999 10.9819 0.666656 10.0886 0.666656 8.98193V4.98193C0.666656 3.87527 1.55999 2.98193 2.66666 2.98193H10.6667C11.7733 2.98193 12.6667 3.87527 12.6667 4.98193V9.6486C12.6667 10.3819 12.0667 10.9819 11.3333 10.9819H2.66666ZM4.66666 6.98193C4.66666 8.0886 5.55999 8.98193 6.66666 8.98193C7.77332 8.98193 8.66666 8.0886 8.66666 6.98193C8.66666 5.87527 7.77332 4.98193 6.66666 4.98193C5.55999 4.98193 4.66666 5.87527 4.66666 6.98193Z"
                    fill="#ABB3BF"/>
            </svg>
          </div>
          <div class="menu__text text-12-16 text-neutral-400 group-hover:text-white group-hover:font-semibold">
            Invoice
          </div>
        </a>
      </li>
      <li class="mb-2 last:mb-0">
        <a href="./student-profile.html" class="flex items-center gap-3 p-3 rounded hover:bg-green group">
          <div class="w-4 h-4 flex items-center justify-center">
            <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path class="group-hover:fill-white"
                    d="M10 1.98193C5.4 1.98193 1.66666 5.71527 1.66666 10.3153C1.66666 14.9153 5.4 18.6486 10 18.6486C14.6 18.6486 18.3333 14.9153 18.3333 10.3153C18.3333 5.71527 14.6 1.98193 10 1.98193ZM10 4.48193C11.3833 4.48193 12.5 5.5986 12.5 6.98193C12.5 8.36527 11.3833 9.48193 10 9.48193C8.61666 9.48193 7.5 8.36527 7.5 6.98193C7.5 5.5986 8.61666 4.48193 10 4.48193ZM10 16.3153C7.91666 16.3153 6.075 15.2486 5 13.6319C5.025 11.9736 8.33333 11.0653 10 11.0653C11.6583 11.0653 14.975 11.9736 15 13.6319C13.925 15.2486 12.0833 16.3153 10 16.3153Z"
                    fill="#ABB3BF"/>
            </svg>
          </div>
          <div class="menu__text text-12-16 text-neutral-400 group-hover:text-white group-hover:font-semibold">
            Profile
          </div>
        </a>
      </li>
    </ul>
  </div>
</div>
<header
  class="header-fixed bg-white fixed top-0 left-0 right-0 z-40 transition-all lg:py-3 border-b border-neutral-100">
  <div class="flex items-center">
    <div
      class="icon__hamburger border-r border-neutral-100 w-[56px] h-[56px] flex-shrink-0 py-[22px] px-[19px] lg:hidden">
      <span class="block w-full h-[2px] rounded-lg bg-black mb-1"></span>
      <span class="block w-full h-[2px] rounded-lg bg-black mb-1"></span>
      <span class="block w-full h-[2px] rounded-lg bg-black"></span>
    </div>
    <div class="container">
      <div class="flex items-center">
        <div class="flex-1">
          <h1 class="text-2xl font-semibold">Message</h1>
        </div>
        <div class="hidden lg:flex lg:items-center relative">
          <div class="setting__acc flex items-center gap-1">
            <div class="text-12-16">Jay Jang (장이준)</div>
            <div class="w-6 h-6">
              <svg class="transition-all rotate-180" width="25" height="24" viewBox="0 0 25 24" fill="none"
                   xmlns="http://www.w3.org/2000/svg">
                <g clip-path="url(#clip0_312_18726)">
                  <path d="M7.14941 10L12.1494 15L17.1494 10H7.14941Z" fill="#10C285"/>
                </g>
                <defs>
                  <clipPath id="clip0_312_18726">
                    <rect width="24" height="24" fill="white" transform="translate(0.149414)"/>
                  </clipPath>
                </defs>
              </svg>
            </div>
          </div>
          <div
            class="setting__acc--child hidden absolute top-[30px] right-0 shadow-00046212 border border-neutral-100 p-2 rounded-lg bg-white min-w-[237px]">
            <a href="./update-password.html" class="flex items-center gap-1 py-3 px-2 rounded-lg hover:bg-neutral-50">
              <div class="w-6 h-6">
                <svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M12.1494 12H12.1544M17.1494 12H17.1544M7.14941 12H7.15441M5.34941 7H18.9494C20.0695 7 20.6296 7 21.0574 7.21799C21.4337 7.40973 21.7397 7.71569 21.9314 8.09202C22.1494 8.51984 22.1494 9.0799 22.1494 10.2V13.8C22.1494 14.9201 22.1494 15.4802 21.9314 15.908C21.7397 16.2843 21.4337 16.5903 21.0574 16.782C20.6296 17 20.0695 17 18.9494 17H5.34941C4.22931 17 3.66926 17 3.24143 16.782C2.86511 16.5903 2.55915 16.2843 2.3674 15.908C2.14941 15.4802 2.14941 14.9201 2.14941 13.8V10.2C2.14941 9.0799 2.14941 8.51984 2.3674 8.09202C2.55915 7.71569 2.86511 7.40973 3.24143 7.21799C3.66926 7 4.22931 7 5.34941 7ZM12.3994 12C12.3994 12.1381 12.2875 12.25 12.1494 12.25C12.0113 12.25 11.8994 12.1381 11.8994 12C11.8994 11.8619 12.0113 11.75 12.1494 11.75C12.2875 11.75 12.3994 11.8619 12.3994 12ZM17.3994 12C17.3994 12.1381 17.2875 12.25 17.1494 12.25C17.0113 12.25 16.8994 12.1381 16.8994 12C16.8994 11.8619 17.0113 11.75 17.1494 11.75C17.2875 11.75 17.3994 11.8619 17.3994 12ZM7.39941 12C7.39941 12.1381 7.28749 12.25 7.14941 12.25C7.01134 12.25 6.89941 12.1381 6.89941 12C6.89941 11.8619 7.01134 11.75 7.14941 11.75C7.28749 11.75 7.39941 11.8619 7.39941 12Z"
                    stroke="#10C285" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </div>
              <div class="text-12-16">Change Password</div>
            </a>
            <a href="./login.html" class="flex items-center gap-1 py-3 px-2 rounded-lg hover:bg-neutral-50">
              <div class="w-6 h-6">
                <svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <g clip-path="url(#clip0_175_1874)">
                    <path
                      d="M10.4494 7.7C10.0594 8.09 10.0594 8.71 10.4494 9.1L12.3494 11H3.14941C2.59941 11 2.14941 11.45 2.14941 12C2.14941 12.55 2.59941 13 3.14941 13H12.3494L10.4494 14.9C10.0594 15.29 10.0594 15.91 10.4494 16.3C10.8394 16.69 11.4594 16.69 11.8494 16.3L15.4394 12.71C15.8294 12.32 15.8294 11.69 15.4394 11.3L11.8494 7.7C11.4594 7.31 10.8394 7.31 10.4494 7.7ZM20.1494 19H13.1494C12.5994 19 12.1494 19.45 12.1494 20C12.1494 20.55 12.5994 21 13.1494 21H20.1494C21.2494 21 22.1494 20.1 22.1494 19V5C22.1494 3.9 21.2494 3 20.1494 3H13.1494C12.5994 3 12.1494 3.45 12.1494 4C12.1494 4.55 12.5994 5 13.1494 5H20.1494V19Z"
                      fill="#10C285"/>
                  </g>
                  <defs>
                    <clipPath id="clip0_175_1874">
                      <rect width="24" height="24" fill="white" transform="translate(0.149414)"/>
                    </clipPath>
                  </defs>
                </svg>
              </div>
              <div class="text-12-16">Log Out</div>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</header>
<div class="box__content--main transition page-student">
  <main class="pt-[57px] min-h-screen bg-neutral-50 page-message">
    <div class="tabs flex flex-col lg:flex-row min-h-[calc(100vh-57px)]">
      <div class="hidden lg:block border-r border-r-neutral-100 bg-white menu__tabs">
        <ul>
          <li>
            <a href="#listMessageItem">
              <div class="p-4 border-b-neutral-100 border-b">
                <p class="font-semibold text-14-20 text-[#242834] pb-2">Q1 Academy</p>
                <div class="text-12-16 text-[#494F5B] mb-2 line-clamp-1 w-[208px]">
                  Q1 Academy 공지사항 (2024년 2월. 다음 학기 (2024년 5월) 홍콩으로 이동 - 학생들이 점점 더국제적으 로 확장되면서, Q1 Academy 사업 운영을
                  한국에서홍콩으로 옮길 예 정입니다. 2024년 5월부터 결제는 국제 신용카드,카카오페이, 그리고 WeChat Pay를 통해 미국 달러로만 가능하게 될예정입니다. 학원을 더 커지기 위한
                  결정이라 양해부탁드립니다.
                </div>
                <div class="flex justify-between items-center">
                  <div class="text-12-16 text-[#494F5B]">
                    2023/05/30 10:15
                  </div>
                  <div class="text-12-16 text-white bg-green rounded-full px-1.5">1</div>
                </div>
              </div>
            </a>
          </li>
          <li>
            <a href="#listMessageItem1">
              <div class="p-4 border-b-neutral-100 border-b">
                <!-- Khi được chọn thì thêm class bg-greenLight -->
                <p class="font-semibold text-14-20 text-[#242834] pb-2">Class WR2 Fri1800</p>
                <div class="text-12-16 text-[#494F5B] mb-2 line-clamp-1 w-[208px]">
                  Q1 Academy 공지사항 (2024년 2월. 다음 학기 (2024년 5월) 홍콩으로 이동 - 학생들이 점점 더국제적으 로 확장되면서, Q1 Academy 사업 운영을
                  한국에서홍콩으로 옮길 예 정입니다. 2024년 5월부터 결제는 국제 신용카드,카카오페이, 그리고 WeChat Pay를 통해 미국 달러로만 가능하게 될예정입니다. 학원을 더 커지기 위한
                  결정이라 양해부탁드립니다.
                </div>
                <div class="flex justify-between items-center">
                  <div class="text-12-16 text-[#494F5B]">
                    2023/05/30 10:15
                  </div>
                  <div class="text-12-16 text-white bg-green rounded-full px-1.5">1</div>
                </div>
              </div>
            </a>
          </li>
          <li>
            <a href="#listMessageItem2">
              <div class="p-4 border-b-neutral-100 border-b">
                <p class="font-semibold text-14-20 text-[#242834] pb-2">Teacher Kim </p>
                <div class="text-12-16 text-[#494F5B] mb-2  line-clamp-1 w-[208px]">
                  Q1 Academy 공지사항 (2024년 2월. 다음 학기 (2024년 5월) 홍콩으로 이동 - 학생들이 점점 더국제적으 로 확장되면서, Q1 Academy 사업 운영을
                  한국에서홍콩으로 옮길 예 정입니다. 2024년 5월부터 결제는 국제 신용카드,카카오페이, 그리고 WeChat Pay를 통해 미국 달러로만 가능하게 될예정입니다. 학원을 더 커지기 위한
                  결정이라 양해부탁드립니다.
                </div>
                <div class="flex justify-between items-center">
                  <div class="text-12-16 text-[#494F5B]">
                    2023/05/30 10:15
                  </div>
                  <div class="text-12-16 text-white bg-green rounded-full px-1.5">1</div>
                </div>
              </div>
            </a>
          </li>
        </ul>
      </div>
      <div class="bg-neutral-200 py-2 px-4 lg:hidden">
        <div class="bg-white text-16-24 rounded-lg">
          <div class="relative">
            <select name="session" class="box__select focus:outline-none cursor-pointer" single="single"
                    id="listMessage">
              <option value="">Q1 Academy</option>
              <option value="1">Class WR2 Fri1800</option>
              <option value="2">Teacher Kim</option>
            </select>
            <div class="absolute top-[18px] right-[22px]">
              <svg xmlns="http://www.w3.org/2000/svg" width="10" height="5" viewBox="0 0 10 5" fill="none">
                <path d="M7.96021 0.833374L5.00065 3.79293L2.04109 0.833374H7.96021Z" fill="#ABB3BF" stroke="#ABB3BF"/>
              </svg>
            </div>
          </div>
        </div>
      </div>
      <div class="w-full message__tab-content bg-neutral-100 flex-1">
        <div class="content h-full" id="listMessageItem">
          <div class="font-semibold p-4 text-16-24 bg-white text-dark hidden lg:block">Q1 Academy</div>
          <div class="p-4 lg:py-8 xl:max-w-[780px] mx-auto">
            <div class="flex gap-y-4 flex-col lg:gap-y-8 xl:w-full">
              <div>
                <div class="flex px-4 py-2 bg-neutral-50 rounded-t-lg items-center gap-x-2">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" viewBox="0 0 16 16" fill="none"
                       style="width: 16px; height: 16px;">
                    <g clip-path="url(#clip0_78_14843)">
                      <path
                        d="M9.3334 0.666504H6.66673C6.30007 0.666504 6.00007 0.966504 6.00007 1.33317C6.00007 1.69984 6.30007 1.99984 6.66673 1.99984H9.3334C9.70007 1.99984 10.0001 1.69984 10.0001 1.33317C10.0001 0.966504 9.70007 0.666504 9.3334 0.666504ZM8.00007 9.33317C8.36673 9.33317 8.66673 9.03317 8.66673 8.6665V5.99984C8.66673 5.63317 8.36673 5.33317 8.00007 5.33317C7.6334 5.33317 7.3334 5.63317 7.3334 5.99984V8.6665C7.3334 9.03317 7.6334 9.33317 8.00007 9.33317ZM12.6867 4.9265L13.1867 4.4265C13.4401 4.17317 13.4467 3.75317 13.1867 3.49317L13.1801 3.4865C12.9201 3.2265 12.5067 3.23317 12.2467 3.4865L11.7467 3.9865C10.7134 3.15984 9.4134 2.6665 8.00007 2.6665C4.80007 2.6665 2.08007 5.3065 2.00007 8.5065C1.9134 11.8932 4.62673 14.6665 8.00007 14.6665C11.3201 14.6665 14.0001 11.9798 14.0001 8.6665C14.0001 7.25317 13.5067 5.95317 12.6867 4.9265ZM8.00007 13.3332C5.42007 13.3332 3.3334 11.2465 3.3334 8.6665C3.3334 6.0865 5.42007 3.99984 8.00007 3.99984C10.5801 3.99984 12.6667 6.0865 12.6667 8.6665C12.6667 11.2465 10.5801 13.3332 8.00007 13.3332Z"
                        fill="#575F6B"/>
                    </g>
                    <defs>
                      <clipPath id="clip0_78_14843">
                        <rect width="16" height="16" fill="white"/>
                      </clipPath>
                    </defs>
                  </svg>
                  <div class="font-medium text-12-16 text-neutral-600">2024년 01월 01일</div>
                </div>
                <div class="p-4 bg-white rounded-b-lg text-edited">
                  <div class="font-medium text-14-20 text-neutral-800 mb-5">
                    Q1 Academy <span class="font-normal">공지사항</span> (2024년 2월)
                  </div>
                  <ol class="note-massage ml-5">
                    <li class="break-all text-12-16 text-neutral-800 mb-5">
                      다음 학기 (2024년 5월) 홍콩으로 이동 - 학생들이 점점 더국제적으 로 확장되면서, Q1 Academy 사업 운영을 한국에서홍콩으로 옮길 예 정입니다. 2024년 5월부터
                      결제는 국제 신용카드,카카오페이, 그리고 WeChat Pay를 통해 미국 달러로만 가능하게 될예정입니다. 학원을 더 커지기 위한 결정이라 양해부탁드립니다.<br>
                      <br>
                      <span class="relative text-dot">(그 전까지는 한국에서 정상적으로 운영되며, 계좌이체와 신용카드를 통한 한국 원화로 가능합니다.)</span><br>
                      <a href="https://blog.naver.com/q1_academy/223312602393" target="_blank" class="text-green">https://blog.naver.com/q1_academy/223312602393</a>
                    </li>
                    <li class="break-all text-12-16 text-neutral-800 mb-5">
                      설날연휴 학원 휴무: 2월8일(목요일)~2월14일(수요일)
                    </li>
                    <li class="break-all text-12-16 text-neutral-800 mb-5">
                      무료 Officee Hour - 저희는 모든 학생들에게 일요일 오후 8시부터10 시까지(한국시간) 무료 Oce Hour를 제공합니다. 자세한 정보를보 려면 아래 블로그 게시물을
                      클릭해주세요.
                      <span><a href="https://blog.naver.com/q1_academy/223227999082" target="_blank" class="text-green">https://blog.naver.com/q1_academy/223227999082</a></span>
                    </li>
                  </ol>
                  <h2>Dưới chân Tháp Bà Ponaga, dòng sông Cái hiền hoà chảy ra biển. Hai bên bờ sông, nhà cửa lô nhô.
                    Lác đác, vài cụm dừa mọc choài ra sông, tàu lá lao xao trong gió. Giữa sông, cù lao Hải Đảo rợp bóng
                    dừa như một ốc đảo xanh lục giữa làn nước xanh lam. Cầu Bóng bắc qua sông nườm nượp xe cộ. Dưới chân
                    cầu, nơi con sông đổ ra biển</h2>
                  <h3>&nbsp;là cầu Cá. Thuyền đi biển sơn hai màu xanh đỏ, đậu san sát gần một mỏm đá nối lên như hòn
                    non bộ. Vài chiếc tàu máy chạy trên sông. Tiếng còi ô tô gay gắt lẫn tiếng ghe máy chạy ì ầm làm
                    dòng sông ồn ã lên. Nắng trưa bàng bạc lên dòng sông, mặt nước sông như dát một thứ ánh kim xanh
                    biếc màu trời. Con sông, cửa biển và bến thuyền gắn bó bao đời là một trong những cảnh đẹp của thành
                    phố Nha Trang được nhiều người biết đến.</h3>
                  <h4>Dưới chân Tháp Bà Ponaga, dòng sông Cái hiền hoà chảy ra biển. Hai bên bờ sông, nhà cửa lô nhô.
                    Lác đác, vài cụm dừa</h4>
                  <p><strong>&nbsp;mọc choài ra sông, tàu lá lao xao trong gió. Giữa sông, cù lao Hải Đảo rợp bóng dừa
                    như một ốc đảo xanh lục giữa làn nước xanh lam. Cầu Bóng bắc qua sông nườm nượp x</strong></p>
                  <p><i>e cộ. Dưới chân cầu, nơi con sông đổ ra biển là cầu Cá. Thuyền đi biển sơn hai màu xanh đỏ, đậu
                    san sát gần một mỏm đá nối lên như hòn non bộ. Vài chiếc tàu máy chạy&nbsp;</i></p>
                  <blockquote><p>trên sông. Tiếng còi ô tô gay gắt lẫn tiếng ghe máy chạy ì ầm làm dòng sông ồn ã lên.&nbsp;</p>
                  </blockquote>
                  <figure class="media">
                    <oembed url="
                        https://www.youtube.com/watch?v=CMUPG88tCDI"></oembed>
                  </figure>
                  <p><a
                    href="
                        https://www.youtube.com/watch?v=CMUPG88tCDI">https://www.youtube.com/watch?v=CMUPG88tCDI</a></p>
                  <figure
                    class="table">
                    <table>
                      <tbody>
                      <tr>
                        <td>a</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                      </tr>
                      <tr>
                        <td>&nbsp;</td>
                        <td>a</td>
                        <td>&nbsp;</td>
                      </tr>
                      <tr>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>a</td>
                      </tr>
                      <tr>
                        <td>&nbsp;</td>
                        <td>a</td>
                        <td>&nbsp;</td>
                      </tr>
                      <tr>
                        <td>&nbsp;</td>
                        <td>a</td>
                        <td>a</td>
                      </tr>
                      </tbody>
                    </table>
                  </figure>
                  <ul>
                    <li>Nắng trưa bàng bạc lên dòng sông, mặt nước sông như dát một thứ ánh kim xanh biếc màu trời.&nbsp;</li>
                  </ul>
                  <ol>
                    <ul>
                      <li>Nắng trưa bàng bạc lên dòng sông, mặt nước sông như dát một thứ ánh kim xanh biếc màu trời.&nbsp;</li>
                    </ul>
                    <li>Con sông, cửa biển và bến thuyền gắn bó bao đời là một trong những cản</li>
                    <li>h đẹp của thành phố Nha Trang được nhiều người biết đến.</li>
                    <li>&nbsp;</li>
                  </ol>
                </div>
              </div>
              <div>
                <div class="flex px-4 py-2 bg-neutral-50 rounded-t-lg items-center gap-x-2">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" viewBox="0 0 16 16" fill="none"
                       style="width: 16px; height: 16px;">
                    <g clip-path="url(#clip0_78_14843)">
                      <path
                        d="M9.3334 0.666504H6.66673C6.30007 0.666504 6.00007 0.966504 6.00007 1.33317C6.00007 1.69984 6.30007 1.99984 6.66673 1.99984H9.3334C9.70007 1.99984 10.0001 1.69984 10.0001 1.33317C10.0001 0.966504 9.70007 0.666504 9.3334 0.666504ZM8.00007 9.33317C8.36673 9.33317 8.66673 9.03317 8.66673 8.6665V5.99984C8.66673 5.63317 8.36673 5.33317 8.00007 5.33317C7.6334 5.33317 7.3334 5.63317 7.3334 5.99984V8.6665C7.3334 9.03317 7.6334 9.33317 8.00007 9.33317ZM12.6867 4.9265L13.1867 4.4265C13.4401 4.17317 13.4467 3.75317 13.1867 3.49317L13.1801 3.4865C12.9201 3.2265 12.5067 3.23317 12.2467 3.4865L11.7467 3.9865C10.7134 3.15984 9.4134 2.6665 8.00007 2.6665C4.80007 2.6665 2.08007 5.3065 2.00007 8.5065C1.9134 11.8932 4.62673 14.6665 8.00007 14.6665C11.3201 14.6665 14.0001 11.9798 14.0001 8.6665C14.0001 7.25317 13.5067 5.95317 12.6867 4.9265ZM8.00007 13.3332C5.42007 13.3332 3.3334 11.2465 3.3334 8.6665C3.3334 6.0865 5.42007 3.99984 8.00007 3.99984C10.5801 3.99984 12.6667 6.0865 12.6667 8.6665C12.6667 11.2465 10.5801 13.3332 8.00007 13.3332Z"
                        fill="#575F6B"/>
                    </g>
                    <defs>
                      <clipPath id="clip0_78_14843">
                        <rect width="16" height="16" fill="white"/>
                      </clipPath>
                    </defs>
                  </svg>
                  <div class="font-medium text-12-16 text-neutral-600">2024년 01월 01일</div>
                </div>
                <div class="p-4 bg-white rounded-b-lg">
                  <div class="font-medium text-14-20 text-neutral-800 mb-5">
                    Q1 Academy <span class="font-normal">공지사항</span> (2024년 2월)
                  </div>
                  <ol class="note-massage ml-5">
                    <li class="break-all text-12-16 text-neutral-800 mb-5">
                      다음 학기 (2024년 5월) 홍콩으로 이동 - 학생들이 점점 더국제적으 로 확장되면서, Q1 Academy 사업 운영을 한국에서홍콩으로 옮길 예 정입니다. 2024년 5월부터
                      결제는 국제 신용카드,카카오페이, 그리고 WeChat Pay를 통해 미국 달러로만 가능하게 될예정입니다. 학원을 더 커지기 위한 결정이라 양해부탁드립니다.<br>
                      <br>
                      <span class="relative text-dot">(그 전까지는 한국에서 정상적으로 운영되며, 계좌이체와 신용카드를 통한 한국 원화로 가능합니다.)</span><br>
                      <a href="https://blog.naver.com/q1_academy/223312602393" target="_blank" class="text-green">https://blog.naver.com/q1_academy/223312602393</a>
                    </li>
                    <li class="break-all text-12-16 text-neutral-800 mb-5">
                      설날연휴 학원 휴무: 2월8일(목요일)~2월14일(수요일)
                    </li>
                    <li class="break-all text-12-16 text-neutral-800 mb-5">
                      무료 Officee Hour - 저희는 모든 학생들에게 일요일 오후 8시부터10 시까지(한국시간) 무료 Oce Hour를 제공합니다. 자세한 정보를보 려면 아래 블로그 게시물을
                      클릭해주세요.
                      <span><a href="https://blog.naver.com/q1_academy/223227999082" target="_blank" class="text-green">https://blog.naver.com/q1_academy/223227999082</a></span>
                    </li>
                  </ol>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="content h-full" id="listMessageItem1">
          <div class="font-semibold p-4 text-16-24 bg-white text-dark hidden lg:block">Class WR2 Fri1800</div>
          <div class="p-4 lg:py-8 xl:max-w-[780px] mx-auto">
            <div class="flex gap-y-4 flex-col lg:gap-y-8 xl:w-full">
              <div>
                <div class="flex px-4 py-2 bg-neutral-50 rounded-t-lg items-center gap-x-2">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" viewBox="0 0 16 16" fill="none"
                       style="width: 16px; height: 16px;">
                    <g clip-path="url(#clip0_78_14843)">
                      <path
                        d="M9.3334 0.666504H6.66673C6.30007 0.666504 6.00007 0.966504 6.00007 1.33317C6.00007 1.69984 6.30007 1.99984 6.66673 1.99984H9.3334C9.70007 1.99984 10.0001 1.69984 10.0001 1.33317C10.0001 0.966504 9.70007 0.666504 9.3334 0.666504ZM8.00007 9.33317C8.36673 9.33317 8.66673 9.03317 8.66673 8.6665V5.99984C8.66673 5.63317 8.36673 5.33317 8.00007 5.33317C7.6334 5.33317 7.3334 5.63317 7.3334 5.99984V8.6665C7.3334 9.03317 7.6334 9.33317 8.00007 9.33317ZM12.6867 4.9265L13.1867 4.4265C13.4401 4.17317 13.4467 3.75317 13.1867 3.49317L13.1801 3.4865C12.9201 3.2265 12.5067 3.23317 12.2467 3.4865L11.7467 3.9865C10.7134 3.15984 9.4134 2.6665 8.00007 2.6665C4.80007 2.6665 2.08007 5.3065 2.00007 8.5065C1.9134 11.8932 4.62673 14.6665 8.00007 14.6665C11.3201 14.6665 14.0001 11.9798 14.0001 8.6665C14.0001 7.25317 13.5067 5.95317 12.6867 4.9265ZM8.00007 13.3332C5.42007 13.3332 3.3334 11.2465 3.3334 8.6665C3.3334 6.0865 5.42007 3.99984 8.00007 3.99984C10.5801 3.99984 12.6667 6.0865 12.6667 8.6665C12.6667 11.2465 10.5801 13.3332 8.00007 13.3332Z"
                        fill="#575F6B"/>
                    </g>
                    <defs>
                      <clipPath id="clip0_78_14843">
                        <rect width="16" height="16" fill="white"/>
                      </clipPath>
                    </defs>
                  </svg>
                  <div class="font-medium text-12-16 text-neutral-600">2024년 01월 01일</div>
                </div>
                <div class="p-4 bg-white rounded-b-lg">
                  <div class="font-medium text-14-20 text-neutral-800 mb-5">
                    Q1 Academy <span class="font-normal">공지사항</span> (2024년 2월)
                  </div>
                  <ol class="note-massage ml-5">
                    <li class="break-all text-12-16 text-neutral-800 mb-5">
                      다음 학기 (2024년 5월) 홍콩으로 이동 - 학생들이 점점 더국제적으 로 확장되면서, Q1 Academy 사업 운영을 한국에서홍콩으로 옮길 예 정입니다. 2024년 5월부터
                      결제는 국제 신용카드,카카오페이, 그리고 WeChat Pay를 통해 미국 달러로만 가능하게 될예정입니다. 학원을 더 커지기 위한 결정이라 양해부탁드립니다.
                    </li>
                  </ol>
                </div>
              </div>
              <div>
                <div class="flex px-4 py-2 bg-neutral-50 rounded-t-lg items-center gap-x-2">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" viewBox="0 0 16 16" fill="none"
                       style="width: 16px; height: 16px;">
                    <g clip-path="url(#clip0_78_14843)">
                      <path
                        d="M9.3334 0.666504H6.66673C6.30007 0.666504 6.00007 0.966504 6.00007 1.33317C6.00007 1.69984 6.30007 1.99984 6.66673 1.99984H9.3334C9.70007 1.99984 10.0001 1.69984 10.0001 1.33317C10.0001 0.966504 9.70007 0.666504 9.3334 0.666504ZM8.00007 9.33317C8.36673 9.33317 8.66673 9.03317 8.66673 8.6665V5.99984C8.66673 5.63317 8.36673 5.33317 8.00007 5.33317C7.6334 5.33317 7.3334 5.63317 7.3334 5.99984V8.6665C7.3334 9.03317 7.6334 9.33317 8.00007 9.33317ZM12.6867 4.9265L13.1867 4.4265C13.4401 4.17317 13.4467 3.75317 13.1867 3.49317L13.1801 3.4865C12.9201 3.2265 12.5067 3.23317 12.2467 3.4865L11.7467 3.9865C10.7134 3.15984 9.4134 2.6665 8.00007 2.6665C4.80007 2.6665 2.08007 5.3065 2.00007 8.5065C1.9134 11.8932 4.62673 14.6665 8.00007 14.6665C11.3201 14.6665 14.0001 11.9798 14.0001 8.6665C14.0001 7.25317 13.5067 5.95317 12.6867 4.9265ZM8.00007 13.3332C5.42007 13.3332 3.3334 11.2465 3.3334 8.6665C3.3334 6.0865 5.42007 3.99984 8.00007 3.99984C10.5801 3.99984 12.6667 6.0865 12.6667 8.6665C12.6667 11.2465 10.5801 13.3332 8.00007 13.3332Z"
                        fill="#575F6B"/>
                    </g>
                    <defs>
                      <clipPath id="clip0_78_14843">
                        <rect width="16" height="16" fill="white"/>
                      </clipPath>
                    </defs>
                  </svg>
                  <div class="font-medium text-12-16 text-neutral-600">2024년 01월 01일</div>
                </div>
                <div class="p-4 bg-white rounded-b-lg">
                  <div class="font-medium text-14-20 text-neutral-800 mb-5">
                    Q1 Academy <span class="font-normal">공지사항</span> (2024년 2월)
                  </div>
                  <ol class="note-massage ml-5">
                    <li class="break-all text-12-16 text-neutral-800 mb-5">
                      다음 학기 (2024년 5월) 홍콩으로 이동 - 학생들이 점점 더국제적으 로 확장되면서, Q1 Academy 사업 운영을 한국에서홍콩으로 옮길 예 정입니다. 2024년 5월부터
                      결제는 국제 신용카드,카카오페이, 그리고 WeChat Pay를 통해 미국 달러로만 가능하게 될예정입니다. 학원을 더 커지기 위한 결정이라 양해부탁드립니다.<br>
                      <br>
                      <span class="relative text-dot">(그 전까지는 한국에서 정상적으로 운영되며, 계좌이체와 신용카드를 통한 한국 원화로 가능합니다.)</span><br>
                      <a href="https://blog.naver.com/q1_academy/223312602393" target="_blank" class="text-green">https://blog.naver.com/q1_academy/223312602393</a>
                    </li>
                    <li class="break-all text-12-16 text-neutral-800 mb-5">
                      설날연휴 학원 휴무: 2월8일(목요일)~2월14일(수요일)
                    </li>
                    <li class="break-all text-12-16 text-neutral-800 mb-5">
                      무료 Officee Hour - 저희는 모든 학생들에게 일요일 오후 8시부터10 시까지(한국시간) 무료 Oce Hour를 제공합니다. 자세한 정보를보 려면 아래 블로그 게시물을
                      클릭해주세요.
                      <span><a href="https://blog.naver.com/q1_academy/223227999082" target="_blank" class="text-green">https://blog.naver.com/q1_academy/223227999082</a></span>
                    </li>
                  </ol>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="content h-full" id="listMessageItem2">
          <div class="font-semibold p-4 text-16-24 bg-white text-dark hidden lg:block">Teacher Kim</div>
          <div class="p-4 lg:py-8 xl:max-w-[780px] mx-auto">
            <div class="flex gap-y-4 flex-col lg:gap-y-8 xl:w-full">
              <div>
                <div class="flex px-4 py-2 bg-neutral-50 rounded-t-lg items-center gap-x-2">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" viewBox="0 0 16 16" fill="none"
                       style="width: 16px; height: 16px;">
                    <g clip-path="url(#clip0_78_14843)">
                      <path
                        d="M9.3334 0.666504H6.66673C6.30007 0.666504 6.00007 0.966504 6.00007 1.33317C6.00007 1.69984 6.30007 1.99984 6.66673 1.99984H9.3334C9.70007 1.99984 10.0001 1.69984 10.0001 1.33317C10.0001 0.966504 9.70007 0.666504 9.3334 0.666504ZM8.00007 9.33317C8.36673 9.33317 8.66673 9.03317 8.66673 8.6665V5.99984C8.66673 5.63317 8.36673 5.33317 8.00007 5.33317C7.6334 5.33317 7.3334 5.63317 7.3334 5.99984V8.6665C7.3334 9.03317 7.6334 9.33317 8.00007 9.33317ZM12.6867 4.9265L13.1867 4.4265C13.4401 4.17317 13.4467 3.75317 13.1867 3.49317L13.1801 3.4865C12.9201 3.2265 12.5067 3.23317 12.2467 3.4865L11.7467 3.9865C10.7134 3.15984 9.4134 2.6665 8.00007 2.6665C4.80007 2.6665 2.08007 5.3065 2.00007 8.5065C1.9134 11.8932 4.62673 14.6665 8.00007 14.6665C11.3201 14.6665 14.0001 11.9798 14.0001 8.6665C14.0001 7.25317 13.5067 5.95317 12.6867 4.9265ZM8.00007 13.3332C5.42007 13.3332 3.3334 11.2465 3.3334 8.6665C3.3334 6.0865 5.42007 3.99984 8.00007 3.99984C10.5801 3.99984 12.6667 6.0865 12.6667 8.6665C12.6667 11.2465 10.5801 13.3332 8.00007 13.3332Z"
                        fill="#575F6B"/>
                    </g>
                    <defs>
                      <clipPath id="clip0_78_14843">
                        <rect width="16" height="16" fill="white"/>
                      </clipPath>
                    </defs>
                  </svg>
                  <div class="font-medium text-12-16 text-neutral-600">2024년 01월 01일</div>
                </div>
                <div class="p-4 bg-white rounded-b-lg">
                  <div class="font-medium text-14-20 text-neutral-800 mb-5">
                    Q1 Academy <span class="font-normal">공지사항</span> (2024년 2월)
                  </div>
                  <ol class="note-massage ml-5">
                    <li class="break-all text-12-16 text-neutral-800 mb-5">
                      다음 학기 (2024년 5월) 홍콩으로 이동 - 학생들이 점점 더국제적으 로 확장되면서, Q1 Academy 사업 운영을 한국에서홍콩으로 옮길 예 정입니다. 2024년 5월부터
                      결제는 국제 신용카드,카카오페이, 그리고 WeChat Pay를 통해 미국 달러로만 가능하게 될예정입니다. 학원을 더 커지기 위한 결정이라 양해부탁드립니다.<br>
                      <br>
                      <span class="relative text-dot">(그 전까지는 한국에서 정상적으로 운영되며, 계좌이체와 신용카드를 통한 한국 원화로 가능합니다.)</span><br>
                      <a href="https://blog.naver.com/q1_academy/223312602393" target="_blank" class="text-green">https://blog.naver.com/q1_academy/223312602393</a>
                    </li>
                    <li class="break-all text-12-16 text-neutral-800 mb-5">
                      설날연휴 학원 휴무: 2월8일(목요일)~2월14일(수요일)
                    </li>
                    <li class="break-all text-12-16 text-neutral-800 mb-5">
                      무료 Officee Hour - 저희는 모든 학생들에게 일요일 오후 8시부터10 시까지(한국시간) 무료 Oce Hour를 제공합니다. 자세한 정보를보 려면 아래 블로그 게시물을
                      클릭해주세요.
                      <span><a href="https://blog.naver.com/q1_academy/223227999082" target="_blank" class="text-green">https://blog.naver.com/q1_academy/223227999082</a></span>
                    </li>
                  </ol>
                </div>
              </div>
              <div>
                <div class="flex px-4 py-2 bg-neutral-50 rounded-t-lg items-center gap-x-2">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" viewBox="0 0 16 16" fill="none"
                       style="width: 16px; height: 16px;">
                    <g clip-path="url(#clip0_78_14843)">
                      <path
                        d="M9.3334 0.666504H6.66673C6.30007 0.666504 6.00007 0.966504 6.00007 1.33317C6.00007 1.69984 6.30007 1.99984 6.66673 1.99984H9.3334C9.70007 1.99984 10.0001 1.69984 10.0001 1.33317C10.0001 0.966504 9.70007 0.666504 9.3334 0.666504ZM8.00007 9.33317C8.36673 9.33317 8.66673 9.03317 8.66673 8.6665V5.99984C8.66673 5.63317 8.36673 5.33317 8.00007 5.33317C7.6334 5.33317 7.3334 5.63317 7.3334 5.99984V8.6665C7.3334 9.03317 7.6334 9.33317 8.00007 9.33317ZM12.6867 4.9265L13.1867 4.4265C13.4401 4.17317 13.4467 3.75317 13.1867 3.49317L13.1801 3.4865C12.9201 3.2265 12.5067 3.23317 12.2467 3.4865L11.7467 3.9865C10.7134 3.15984 9.4134 2.6665 8.00007 2.6665C4.80007 2.6665 2.08007 5.3065 2.00007 8.5065C1.9134 11.8932 4.62673 14.6665 8.00007 14.6665C11.3201 14.6665 14.0001 11.9798 14.0001 8.6665C14.0001 7.25317 13.5067 5.95317 12.6867 4.9265ZM8.00007 13.3332C5.42007 13.3332 3.3334 11.2465 3.3334 8.6665C3.3334 6.0865 5.42007 3.99984 8.00007 3.99984C10.5801 3.99984 12.6667 6.0865 12.6667 8.6665C12.6667 11.2465 10.5801 13.3332 8.00007 13.3332Z"
                        fill="#575F6B"/>
                    </g>
                    <defs>
                      <clipPath id="clip0_78_14843">
                        <rect width="16" height="16" fill="white"/>
                      </clipPath>
                    </defs>
                  </svg>
                  <div class="font-medium text-12-16 text-neutral-600">2024년 01월 01일</div>
                </div>
                <div class="p-4 bg-white rounded-b-lg">
                  <div class="font-medium text-14-20 text-neutral-800 mb-5">
                    Q1 Academy <span class="font-normal">공지사항</span> (2024년 2월)
                  </div>
                  <ol class="note-massage ml-5">
                    <li class="break-all text-12-16 text-neutral-800 mb-5">
                      다음 학기 (2024년 5월) 홍콩으로 이동 - 학생들이 점점 더국제적으 로 확장되면서, Q1 Academy 사업 운영을 한국에서홍콩으로 옮길 예 정입니다. 2024년 5월부터
                      결제는 국제 신용카드,카카오페이, 그리고 WeChat Pay를 통해 미국 달러로만 가능하게 될예정입니다. 학원을 더 커지기 위한 결정이라 양해부탁드립니다.
                    </li>
                  </ol>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </main>
</div>
<div class="backdrop fixed inset-0 bg-black/[0.7] z-[51] transition opacity-0 invisible"></div>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"
        integrity="sha512-894YE6QWD5I59HgZOGReFYm4dnWc1Qt5NtvYSaNcOP+u1T9qYdvdihz0PPSiiqn/+/3e7Jo4EaG7TubfWGUrMQ=="
        crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"
        integrity="sha512-2ImtlRlf2VVmiGZsjm9bEyhjGW4dU7B6TNwh/hx/iSByxNENtj3WVE6o/9Lj4TJeVXPi4bnOIMXFIJJAeufa0A=="
        crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<script src="https://cdn.jsdelivr.net/npm/@fullcalendar/core@4.4.0/main.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/@fullcalendar/interaction@4.4.0/main.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/@fullcalendar/daygrid@4.4.0/main.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/@fullcalendar/timegrid@4.4.0/main.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/@fullcalendar/list@4.4.0/main.min.js"></script>
<script src="https://unpkg.com/popper.js/dist/umd/popper.min.js"></script>
<script src="https://unpkg.com/tooltip.js/dist/umd/tooltip.min.js"></script>
<script src="/html/src/js/index.js"></script>
</body>
</html>
