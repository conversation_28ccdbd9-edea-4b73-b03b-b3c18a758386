$(function (){
  $('.icon-arrow-aside').on('click', function (){
    $('aside .logo-full').toggleClass('hidden')
    $('aside .logo-small').toggleClass('hidden block')
    $(this).children('svg').toggleClass('rotate-180')
    $('aside').toggleClass('aside-small')
    $('.box__content--main').toggleClass('aside-small')
    $('aside .menu__text ').toggleClass('hidden')
    $('.header-fixed').toggleClass('aside-small')
    $('.child-menu').toggleClass('hidden')
  })

  $('.icon__hamburger').on('click', function (){
    $('.side-bar').toggleClass('-translate-x-full')
    $('.backdrop').toggleClass('opacity-0 invisible opacity-100 visible')
  })

  $('.backdrop, .side-bar a').on('click', function (){
    $('.backdrop').addClass('opacity-0 invisible').removeClass('opacity-100 visible');
    $('.side-bar').addClass('-translate-x-full');
    $('body').removeClass('overflow-hidden max-h-screen');
  })

  $('.radio__custom').each(function (){
    if ($(this).children('input').is(":checked")){
      $(this).addClass('active').siblings('.radio__custom').removeClass('active')
    } else {
      $(this).removeClass('active').siblings('.radio__custom').addClass('active')
    }

    if ($('#radioCheckStay').is(":checked")){
      $('.checkbox__stay').removeClass('hidden').addClass('block')
    } else {
      $('.checkbox__stay').addClass('hidden').removeClass('block')
    }

    $('.radio__custom').on('click', function (){
      if ($(this).children('input').is(":checked")){
        $(this).addClass('active').siblings('.radio__custom').removeClass('active')
      } else {
        $(this).removeClass('active').siblings('.radio__custom').addClass('active')
      }

      if ($('#radioCheckStay').is(":checked")){
        $('.checkbox__stay').removeClass('hidden').addClass('block')
      } else {
        $('.checkbox__stay').addClass('hidden').removeClass('block')
      }
    })
  })

  $('.check-level-profile').on('click', function(){
    $('.modal__level--reports').removeClass('hidden');
    $('.backdrop').toggleClass('opacity-0 invisible opacity-100 visible');
    $('body').addClass('overflow-hidden max-h-screen');
  });

  $('.edit-address-profile').on('click', function(){
    $('.modal__update--address').removeClass('hidden');
    $('.backdrop').toggleClass('opacity-0 invisible opacity-100 visible');
    $('body').addClass('overflow-hidden max-h-screen');
  });

  $('.show_student').on('click', function(){
    $(this).parent().find('.point_student').slideToggle('slow');
  });

  $('.icon-close').on('click', function(){
    $('.modal-main').addClass('hidden');
    $('.backdrop').addClass('opacity-0 invisible').removeClass('opacity-100 visible');
    $('body').removeClass('overflow-hidden max-h-screen');
  });

  $('.tabs').each(function(){
    $(this).find('.content').hide();
    $(this).find('.content:first').show();
    $(this).find('.menu__tabs li:first').addClass('active');
  })

  $('.tabs ul li a').on('click',function(){
    $(this).parents('.tabs').find('.menu__tabs li').removeClass('active');
    $(this).parent().addClass('active');
    const currentTab = $(this).attr('href');
    $(this).parents('.tabs').find('.content').hide()
    $(currentTab).show();
    return false;
  });

  $('.box__select').select2({
      width: "fit-content",
      minimumResultsForSearch: -1
  });

  $('.box__select--invoice').select2({
    width: "100%",
    minimumResultsForSearch: -1,
    placeholder: "Choose Reason",
  });

  $('.page-message .select2').on('click', function(){
    $('.select2-dropdown--below').addClass('message__select')
  })

  $('.invoice-select .select2').on('click', function(){
    $('.select2-dropdown--below').addClass('message__select')
  })

  $('.setting__acc').on('click', function (){
    $(this).children().children('svg').toggleClass('rotate-180')
    $(this).siblings('.setting__acc--child').slideToggle()
  })

  $(document).on("click", function (event) {
    if(!$(event.target).is('.setting__acc div, .setting__acc svg, .setting__acc path')) {
      $('.setting__acc--child').slideUp()
    }

    if ($(event.target).closest('.modal-content').length === 0 && $(event.target).is('.modal-main')) {
      $(".modal-main").addClass('hidden');
      $('.backdrop').addClass('opacity-0 invisible').removeClass('opacity-100 visible');
      $('body').removeClass('overflow-hidden max-h-screen');
    }
  });

  const data = [{
    id: 0,
    text:
      '<div class="flex">' +
      '        <div class="text-14-20 text-neutral-700"> Dec, 06, 2023 </div>' +
      '        <div class="text-14-20 text-neutral-700 mx-2 px-2 border-x border-neutral-300"> •••• •••• •••• 7390 </div>' +
      '        <div class="text-14-20 text-neutral-700"> 365,000 ₩ </div>' +
      '</div>'
  }, {
    id: 1,
    text:
      '<div class="flex">' +
      '        <div class="text-14-20 text-neutral-700"> Dec, 08, 2024 </div>' +
      '        <div class="text-14-20 text-neutral-700 mx-2 px-2 border-x border-neutral-300"> •••• •••• •••• 7399 </div>' +
      '        <div class="text-14-20 text-neutral-700"> 565,000 ₩ </div>' +
      '</div>'
  }];

  $("#refundRequest").select2({
    width: "100%",
    minimumResultsForSearch: -1,
    data: data,
    escapeMarkup: function(markup) {
      return markup;
    },
    templateResult: function(data) {
      return data.text;
    },
    templateSelection: function(data) {
      return data.text;
    }
  })
  $('#listMessage').on('change', function () {
    $('.message__tab-content .content').hide();
    $('#listMessageItem' + $(this).val()).show();
  });
  $('#menuInvoice').on('change', function () {
    $('.invoice__tab--content .content').hide();
    $('#menuInvoiceItem' + $(this).val()).show();
  });
  
  function buildSwiperSlider(sliderElm) {
    
    const sliderIdentifier = $(sliderElm).data('id');
    return new Swiper('#' + sliderElm.id, {
        navigation: {
            nextEl: '.swiper-button-next-' + sliderIdentifier,
            prevEl: '.swiper-button-prev-' + sliderIdentifier
        },
        slidesPerView: 1,
        spaceBetween: 12,
        loop: true,
        breakpoints: {
          1200: {
            slidesPerView: 2,
          },
          1440: {
            slidesPerView: 3,
          },
        },
    });
  }
  $('.swiper').each(function() {
    buildSwiperSlider(this);
  });
})
