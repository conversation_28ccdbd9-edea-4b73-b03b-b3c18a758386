document.addEventListener('DOMContentLoaded', function() {
  const calendarEl = document.getElementById('calendar');

  const calendar = new FullCalendar.Calendar(calendarEl, {
    plugins: [ 'interaction', 'dayGrid', 'timeGrid', 'list' ],
    timeZone: 'UTC',
    header: false,
    showNonCurrentDates : false,
    aspectRatio: 2,
    contentHeight:'auto',
    events: [
      {
        title: 'QR2-Fri2000',
        start: '2024-03-04T20:00:00',
        color: '#E7F6FD',
        textColor:'#1890FF',
        status: 'N/A',
        imageurl:'http://localhost:63342/q1-academy-spr/html/src/images/book-open.svg',
        description:'QR2-Fri2000 20:00 N/A' //title + time + status
      },
      {
        title: 'QW1-Mon1800',
        start: '2024-03-15T20:00:00',
        color: '#E7FDF7',
        textColor:'#10C285',
        imageurl:'http://localhost:63342/q1-academy-spr/html/src/images/pencil.svg',
      },
      {
        title: 'QR2-Fri2000',
        start: '2024-03-25T20:00:00',
        color: '#E7F6FD',
        textColor:'#1890FF',
        imageurl:'http://localhost:63342/q1-academy-spr/html/src/images/book-open.svg',
      },
    ],
    eventRender: function(info) {
      if (info.event.extendedProps.imageurl) {
        let elem = info.el,
        img = document.createElement('img');
        divImage = document.createElement('div');
        divImage.className = "icon__rw";
        img.src = info.event.extendedProps.imageurl;
        elem.querySelector('.fc-title').appendChild(divImage).appendChild(img);
      }

      if (info.event.extendedProps.status) {
        let elem = info.el,
        divStatus = document.createElement('div');
        divStatus.className = "icon__status";
        divStatus.html = info.event.extendedProps.status;
        // console.log(divStatus.html)
        elem.querySelector('.fc-content').appendChild(divStatus).textContent=divStatus.html;
      }

      var tooltip = new Tooltip(info.el, {
        title: info.event.extendedProps.description,
        placement: 'top',
        trigger: 'hover',
        container: '#calendar'
      });
    },

    eventTimeFormat: { // like '14:30'
      hour: '2-digit',
      minute: '2-digit',
      meridiem: false
    },
  });

  calendar.render();
});

$(function (){
  $('.fc-day-grid-event').each(function (){
    if ($(this).css('color').toLowerCase() === 'rgb(24, 144, 255)')
    {
      $(this).addClass('item__reading')
    } else {
      $(this).addClass('item__writing')
    }
  })
})
