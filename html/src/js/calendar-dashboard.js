document.addEventListener('DOMContentLoaded', function() {
//   calendar dashboard
  const calendarElDashboard = document.getElementById('calendarDashboard');

  const calendarDashboard = new FullCalendar.Calendar(calendarElDashboard, {
    plugins: [ 'interaction', 'dayGrid', 'timeGrid', 'list' ],
    timeZone: 'UTC',
    header: {
      left: 'prev',
      center: 'title',
      right: 'next',
    },
    events: [
      {
        start: '2024-05-07',
        end: '2024-05-08'
      },
      {
        start: '2024-05-10',
        end: '2024-05-11'
      }
    ],
    contentHeight:'auto',
  });
  calendarDashboard.render();
});
