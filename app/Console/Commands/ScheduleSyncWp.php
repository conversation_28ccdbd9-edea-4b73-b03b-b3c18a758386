<?php

namespace App\Console\Commands;

use App\Repositories\InvoiceRepository;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use GuzzleHttp;

class ScheduleSyncWp extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:schedule-syncwp';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Schedule Sync Order WP';

    /**
     * Execute the console command.
     */
    public function handle(InvoiceRepository $invoiceRepository)
    {
      $client = new GuzzleHttp\Client();
      $invoices = $invoiceRepository->where(function ($query) {
        $query->where('status', 'request_refund')->orwhere('status', 'unpaid');
      })->get();

      foreach ($invoices as $invoice){
        $response = $client->get(config('app.wphost').'/wp-json/wc/v3/orders/'.$invoice->order_id.'?ver'.time(), [
          'auth' => [
            config('app.wpkey'),
            config('app.wpsecret')
          ],
        ]);
        $body = $response->getBody();

        $wpOrder = json_decode($body->getContents());
        $status = $invoice->status;
        $payment_method = $invoice->payment_method;
        $payment_date = $invoice->payment_date;
        $refund = false;
        if($status == 'request_refund' || $status == 'refund'){
          $refund = true;
        }
        if($wpOrder->status == 'pending' && !$refund){
          $status = 'unpaid';
        }elseif($wpOrder->status == 'processing' && !$refund){
          $status = 'paid';
          $payment_method = $wpOrder->payment_method;
          $payment_date = Carbon::parse($wpOrder->date_paid)->format("Y-m-d");
        }
//        elseif($wpOrder->status == 'refunded'){
//          $status = 'refund';
//        }

        if($status != $invoice->status){
          $invoiceRepository->update(['status'=>$status, 'payment_method'=>$payment_method, 'payment_date'=> $payment_date],$invoice->id);
        }
        Log::info('syncwp - id:'.$invoice->id);
        Log::info('syncwp - status:'.$wpOrder->status);
      }
    }
}
