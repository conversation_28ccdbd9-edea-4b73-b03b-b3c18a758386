<?php

namespace App\Services;

use Barryvdh\DomPDF\Facade\Pdf;
use GuzzleHttp;
use Illuminate\Support\Facades\Cache;

class InvoiceService
{

    public function getInvoicePdf($parent, $ids, $options, $payLink = null)
    {
        if(!empty($options['type']) && $options['type'] == 'batch' && !empty($options['student'])){
            $nameStudent = $options['student'];
        }else{
            $nameStudent = $parent->student->pluck('name_english')->implode(', ');
        }
        $prods = $this->getProducts($ids);
        if(empty($prods)) abort('404');
        $items = [];
        $total = 0;
        foreach ($prods as $key => $val){
            $items[$key]['name'] = $val->name ?? $val->name;
            $items[$key]['qty'] = 1;
            $items[$key]['price'] = '$'.$val->price ?? $val->price;
            $items[$key]['total'] = '$'.$val->price ?? $val->price;
            $total += $val->price;
        }
        $data = [
            'company' => [
                'name' => 'Q1 ACADEMY',
                'email' => '<EMAIL>',
                'phone' => '+82-010-3046-2646',
                'business_name' => 'Q1 Solutions Limited',
                'registration' => '*********',
                'address' => 'UNIT 2A, 17/F, GLENEALY TOWER NO.1 GLENEALY CENTRAL Hong Kong, Hong Kong, 999077'
            ],
            'student_name' => $nameStudent,
            'prepared_date' => 'April 26, 2024',
            'items' => $items,
            'total' => '$' . (fmod($total, 1) == 0
                    ? number_format($total)
                    : number_format($total, 2)
                ),
            'payment_methods' => 'Credit Card / WeChat Pay / Apple Pay / Google Pay / KakaoPay',
            'payment_text' => 'Click here to pay...',
            'has_payment_link' => !empty($payLink),
            'pay_link' => $payLink,
            'payment_note' => '(화면만 클릭하시고 기다려주세요. 서버가 느립니다.)'
        ];
        $data['fontBase64'] = $options['fontBase64'];
        unset($options, $prods, $items, $total, $nameStudent, $payLink, $parent, $ids);

        return Pdf::loadView('admin.invoice.template', $data);
    }

    public function getProducts($ids)
    {
        $client = new GuzzleHttp\Client();
        $prods = [];

        foreach ($ids as $id) {
            $cacheKey = "wp_product_{$id}";

            $wpProd = Cache::remember($cacheKey, 60*60, function () use ($client, $id) {
                try {
                    $response = $client->get(config('app.wphost') . '/wp-json/wc/v3/products/' . $id, [
                        'auth' => [
                            config('app.wpkey'),
                            config('app.wpsecret')
                        ]
                    ]);

                    $body = $response->getBody();
                    return json_decode($body->getContents());
                } catch (\Exception $e) {
                    return null;
                }
            });

            if ($wpProd) {
                $prods[] = $wpProd;
            }
        }
        return $prods;
    }

    public function createOrderWp($ids, $parent){
        $client = new GuzzleHttp\Client();
        $prods=array();
        foreach ($ids as $id){
            $prod['product_id'] = $id;
            $prod['quantity'] = 1;
            $prods[] = $prod;
        }

        $info['first_name'] = $parent->name_english ?? "";
        $info['last_name'] = $parent->name_korea ?? "" ;
        $info['address_1'] = $parent->address ?? "";
        $info['email'] = $parent->email;
        $info['phone'] = $parent->phone;

        $response = $client->post(config('app.wphost').'/wp-json/wc/v3/orders', [
            'content-type' => 'application/json',
            'auth' => [
                config('app.wpkey'),
                config('app.wpsecret')
            ],
            'json' => [
                'line_items' => $prods,
                'billing' => $info,
                'payment_method' => 'airwallex_main'
            ]
        ]);
        $body = $response->getBody();
        return  json_decode($body->getContents());
    }

    public function getOptions()
    {
        if (\Cache::has('logo_base64') && \Cache::has('font_base64')) {
            return [
                'fontBase64' => \Cache::get('font_base64')
            ];
        }
        $fontPath = storage_path('fonts/NotoSansKR-Regular.ttf');
        $options['fontBase64'] = base64_encode(file_get_contents($fontPath));
        \Cache::forever('font_base64', $options['fontBase64']);
        return $options;
    }
}
