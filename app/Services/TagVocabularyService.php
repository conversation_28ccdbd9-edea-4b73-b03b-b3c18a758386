<?php

namespace App\Services;

use App\Http\Resources\BasePaginatedCollection;
use App\Repositories\MainHomeWorksRepository;
use App\Repositories\TagRepository;
use App\Repositories\TagVocabularyRepository;

class TagVocabularyService
{
  public function __construct(protected TagVocabularyRepository $tagVocabularyRepo, protected TagRepository $tagRepo)
  {

  }

    public function getTags($request)
    {
        $perPage = $request['per_page'] ?? 10;
        $keyword = $request['keyword'] ?? null;
        return $this->tagRepo
            ->with('getClass')
            ->when($keyword, function ($query) use ($keyword) {
                $query->where('title', 'LIKE', '%' . $keyword . '%');
            })
            ->when(auth('admin')->user()->role == 'teacher', function ($query) {
                $query->whereHas('getClass.admins', function ($q) {
                    $q->where('admin_id', auth('admin')->user()->id);
                });
            })
            ->where(['main_homework_id' => null])
            ->where(['task_id' => null])
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);
    }

  public function getTag($id)
  {
    $tag = $this->tagRepo->with(['vocabularies'])->find($id);
    $dataTag = $tag?->vocabularies->pluck('id')->toArray();
    unset($tag->vocabularies);
    $tag->vocabularies = $dataTag;
    return $tag;
  }

  public function getAll()
  {
     return $this->tagRepo
       ->where(['main_homework_id' => null])
       ->where(['task_id' => null])
       ->with('vocabularies')->orderBy('created_at', 'desc')->get();
  }

  public function update($request, $id)
  {
     $tag = $this->tagRepo->update($request,$id);
     $this->syncVocabularies($tag, $request['vocabularies']);
     return $tag;
  }

  public function store($request)
  {
    $tag = $this->findOrCreateTag($request);
    $this->syncVocabularies($tag, $request['vocabularies']);

    return $tag;
  }

  public function destroy($id)
  {
    $this->tagRepo->delete($id);
    $this->tagVocabularyRepo->deleteWhere(['tag_id' => $id]);
  }

  private function findOrCreateTag($request)
  {
    $hasClassAndSession = !empty($request['class_id']) && !empty($request['session']);

    if ($hasClassAndSession) {
      return $this->findOrCreateTagWithClassAndSession($request);
    }

    return $this->createTagWithoutClassAndSession($request);
  }

  private function findOrCreateTagWithClassAndSession($request)
  {
    $tag = $this->tagRepo->where([
      'class_id' => $request['class_id'],
      'session' => $request['session'],
      ['main_homework_id','IS',null],
      ['task_id','IS',null],
    ])->first();

    if ($tag) {
      $tag->update(['title' => $request['title']]);
      return $tag;
    }

    return $this->tagRepo->create([
      'title' => $request['title'],
      'class_id' => $request['class_id'],
      'session' => $request['session'],
    ]);
  }

  private function createTagWithoutClassAndSession($request)
  {
    return $this->tagRepo->create([
      'title' => $request['title'],
      'class_id' => null,
      'session' => null,
    ]);
  }

  private function syncVocabularies($tag, $vocabularies)
  {
    $this->tagVocabularyRepo->deleteWhere(['tag_id' => $tag->id]);

    $vocabularyData = collect($vocabularies)->map(function ($vocabulary) use ($tag) {
      return [
        'tag_id' => $tag->id,
        'vocabulary_id' => $vocabulary,
        'created_at' => now(),
        'updated_at' => now(),
      ];
    })->toArray();

    if (!empty($vocabularyData)) {
      $this->tagVocabularyRepo->insert($vocabularyData);
    }
  }

}
