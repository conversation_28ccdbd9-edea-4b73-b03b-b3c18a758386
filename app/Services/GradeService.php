<?php

namespace App\Services;

use App\Models\AccountHomeworkTask;
use App\Models\AccountEssayFeedback;
use App\Repositories\HomeworkRepository;
use App\Models\AccountHomework;
use App\Models\AccountTaskAnswer;
use App\Repositories\MainHomeworkRepository;
use App\Repositories\VocabularyGradeRepository;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class GradeService
{
    public function __construct(
        protected VocabularyGradeRepository $vocabularyGradeRepo,
        protected HomeworkRepository $homeworkRepo,
        protected MainHomeworkRepository $mainHomeworkRepo
    ) {}

    public function getVocabularyGrades($homeworkId)
    {
        $classId = $this->homeworkRepo->getClassIdByHomeworkId($homeworkId);
        return $this->vocabularyGradeRepo->getVocabularyGrades($classId, auth()->user()->id);
    }

    public function updateVocabularyGrades($grades)
    {
        return DB::table('vocabulary_grades')->upsert(
            $grades,
            ['id'],
            ['grade']
        );
    }

    public function getResults($params)
    {
        return $this->mainHomeworkRepo->getHomeworkResult($params);
    }

    public function getGrade($id)
    {
        $accountHomework = AccountHomework::with([
            'accountHomeworkTasks.accountTaskAnswers' => function ($query) use ($id) {
                $query->whereIn('account_id', function ($subQuery) use ($id) {
                    $subQuery->select('account_id')
                        ->from('account_homework')
                        ->where('id', $id);
                });
            },
            'accountHomeworkTasks.accountTaskAnswers.accountEssayFeedback',
            'accountHomeworkTasks.accountTaskAnswers.question.evaluationCriteria',
            'mainHomework.getClass',
        ])->find($id);

        if (!$accountHomework) {
            return null;
        }

        $links = AccountHomework::with('homework')
            ->where('account_id', $accountHomework->account_id)
            ->where('main_homework_id', $accountHomework->main_homework_id)->get();
        $dataLinks = [];
        $class = $accountHomework->mainHomework->getClass;
        foreach ($links as $item) {
            $dataLinks[] = [
                'id' => $item->id,
                'label' => isset($item->homework->homework) ? 'HW' . $item->homework->homework . '-' . $class->name :
                    $item->homework->title . '-' . $class->name,
            ];
        }

        $accountId = $accountHomework->account_id;
        $totalScore = 0;
        $totalScoreActual = 0;
        foreach ($accountHomework->accountHomeworkTasks as $task) {
            $question = $task->accountTaskAnswers;
            $task->task->end_time = $task->end_time ?? 0;
            $task->task->start_time = $task->start_time ?? 0;
            $task->task->total_score = $task->total_score ?? 0;
            $totalScore += $task->task->total_score;
            $task->task->total_score_actual = $task->score ?? 0;
            $totalScoreActual += $task->task->total_score_actual;
            $task->task->questions = collect();
            foreach ($question as $q) {
                $q->question->account_answers = [
                    'id' => $q->id,
                    'account_id' => $q->account_id,
                    'task_id' => $q->task_id,
                    'question_id' => $q->question_id,
                    'type' => $q->type,
                    'answer' => $q->answer,
                    'selected_options' => $q->selected_options,
                    'feedback' => $q->feedback,
                    'score' => $q->score,
                    'total_score' => $q->total_score,
                    'account_essay_feedback' => $q->accountEssayFeedback,
                ];
                $q->question->score = $q->total_score;
                $task->task->questions[] = $q->question;
            }
        }
        if ($accountHomework->homework) {
            $accountHomework->homework->total_score = $totalScore;
        }

        $tasks = $accountHomework->accountHomeworkTasks
            ->pluck('task')
            ->filter()
            ->values();
        $homework = $accountHomework->homework;
        $homework->start_time = $accountHomework->start_time;
        $homework->end_time = $accountHomework->end_time;
        $homework->score = $accountHomework->score;
        $homework->total_score_actual = $totalScoreActual;
        $vocabularyIds = $tasks->pluck('questions')
            ->flatten()
            ->pluck('words')
            ->flatten();
        $vocabularyGrades = $this->vocabularyGradeRepo->getGradeByIdVocabularyId(
            $class->id,
            $accountId,
            $vocabularyIds
        );
        $totalTime = 0;
        foreach ($tasks as $task) {
            if (!empty($task['start_time']) && !empty($task['end_time'])) {
                $start = Carbon::parse($task['start_time']);
                $end   = Carbon::parse($task['end_time']);
                $totalTime += $end->diffInSeconds($start);
            }
        }
        if ($totalTime > 0 && !empty($accountHomework->start_time)) {
            $startTime = Carbon::parse($accountHomework->start_time);

            $homework->start_time = $startTime;
            $homework->end_time   = $startTime->copy()->addSeconds($totalTime);
        }

        return [
            'tasks' => $tasks,
            'homework' => $accountHomework->homework,
            'links' => $dataLinks,
            'vocabularyGrades' => $vocabularyGrades,
        ];
    }

    public function save($datas)
    {
        foreach ($datas as $data) {
            $item = $data['account_answers'];
            $dataUpdate = [
                'score' => $item['score'] ?? 0,
                'feedback' => $item['feedback'] ?? '',
            ];
            AccountTaskAnswer::where('id', $item['id'])->update($dataUpdate);
            if (isset($item['account_essay_feedback'])) {
                foreach ($item['account_essay_feedback'] as $essayFeedback) {
                    $essayData = [
                        'score' => $essayFeedback['score'] ?? 0,
                        'feedback' => $essayFeedback['feedback'] ?? '',
                    ];
                    if (isset($essayFeedback['id'])) {
                        AccountEssayFeedback::where('id', $essayFeedback['id'])->update($essayData);
                    } else {
                        $essayData['account_task_answer_id'] = $item['id'];
                        $essayData['evaluation_criteria_id'] = $essayFeedback['evaluation_criteria_id'] ?? 0;
                        AccountEssayFeedback::create($essayData);
                    }
                }
            }
        }
    }

    public function updateTotalScore($data)
    {
        $accountHomework = AccountHomework::find($data['account_homework_id']);
        $accountHomework->update(['score' => $data['total_homework']]);
        AccountHomeworkTask::where('task_id', $data['task_id'])->where('account_id', $accountHomework->account_id)
            ->update(['score' => $data['total_task']]);
    }
}
