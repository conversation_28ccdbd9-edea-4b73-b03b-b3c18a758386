<?php

namespace App\Services;

use App\Models\Homework;
use App\Models\Tag;
use App\Models\TagVocabulary;
use App\Models\Task;
use App\Models\TaskHomework;
use App\Models\TaskQuestion;

class TaskHomeworkService
{
  public function createTask($data)
  {
    return $this->handleCreateTask($data);
  }

  public function updateTaskHomework($request, $id)
  {
    $task = Task::find($id);
    $request['root'] = $task->root;
    $hasChange = false;
    foreach ($request as $key => $value) {
      if($task->$key != $value){
        $hasChange = true;
        break;
      }
    }
    if($hasChange){
      $taskHomework = TaskHomework::where('task_id',$id)
        ->where('homework_id',$request['homework_id'])
        ->where('is_latest',1)->first();
      if($taskHomework){
        $taskHomework->update(['is_latest' => 0]);
      }
      $task = $this->handleCreateTask($request,$taskHomework->order ?? null);
      $taskQuestions = TaskQuestion::where('task_id',$taskHomework->task_id ?? null)->where('is_latest',1)->get();
      $dataTaskQuestion = [];
      foreach($taskQuestions as $question){
        $dataTaskQuestion[] = [
          'task_id' => $task->id,
          'question_id' => $question->question_id,
          'order' => $question->order,
          'score' => $question->score,
          'is_latest' => 1,
          'created_at' => now(),
          'updated_at' => now(),
        ];
        $question->update(['is_latest' => 0]);
      }
      if(count($dataTaskQuestion)){
        TaskQuestion::insert($dataTaskQuestion);
      }
    }
    $task->max_submit_time = $request['max_submit_time'];
    $task->min_submit_time = $request['min_submit_time'];
    return $task;
  }

  public function sortTaskHomework($request): void
  {
    TaskHomework::where('homework_id',$request['homework_id'])->where('is_latest',1)->update(['is_latest' => 0]);
    foreach ($request['tasks'] as $key => $task) {
      TaskHomework::create([
        'task_id' => $task['id'],
        'homework_id' => $request['homework_id'],
        'order' => intval($key) + 1,
        'is_latest' => 1,
        'min_submit_time' => $task['min_submit_time'],
        'max_submit_time' => $task['max_submit_time'],
        'created_at' => now(),
        'updated_at' => now(),
      ]);
    }
  }


  public function handleCreateTask($request,$order = null)
  {
    $taskData = [
      'title' => $request['title'],
      'criteria' => $request['criteria'],
      'root' => $request['root'] ?? 0,
      'type' => $request['type'] ?? 1,
      'is_bank' => $request['is_bank'] ?? 0,
      'description' => $request['description'] ?? null,
    ];
    $task = Task::create($taskData);
    $taskDataHomework = [
      'task_id' => $task->id,
      'homework_id' => $request['homework_id'],
      'min_submit_time' => $request['min_submit_time'] ?? 0,
      'max_submit_time' => $request['max_submit_time'] ?? 0,
      'order' => $order ?? TaskHomework::where('homework_id',$request['homework_id'])->count() + 1,
      'is_latest' => 1,
    ];
    TaskHomework::create($taskDataHomework);
    return $task;
  }

  public function deleteTaskHomework($request,$id): void
  {
      Task::where('id',$id)->update([
        'is_display' => 0,
      ]);
      if($request['homework_id']){
        TaskHomework::where('task_id',$id)
          ->where('homework_id',$request['homework_id'])
          ->where('is_latest',1)->update(['is_latest' => 0]);
      }
      TaskQuestion::where('task_id',$id)->where('is_latest',1)->update(['is_latest' => 0]);
  }

  public function importTaskHomework($request)
  {
      $task = Task::with('questions')->whereHas('questions',function ($q){
        $q->where('is_latest',1);
      })->find($request['task_id']);
      $taskData = [
        'title' => $task->title,
        'criteria' => $task->criteria,
        'root' => $task->id,
        'type' => $task->type,
        'is_bank' => 0,
        'description' => $task->description,
      ];
      $newTask = Task::create($taskData);
      $taskHomework = [
        'task_id' => $newTask->id,
        'homework_id' => $request['homework_id'],
        'min_submit_time' => $request['min_submit_time'] ?? 0,
        'max_submit_time' => $request['max_submit_time'] ?? 0,
        'order' => TaskHomework::where('homework_id',$request['homework_id'])->count() + 1,
        'is_latest' => 1,
      ];
      TaskHomework::create($taskHomework);
      $taskQuestion = [];
      $vocabularies = [];
      foreach ($task->questions ?? [] as $question) {
          $questionData = [
              'task_id' => $newTask->id,
              'question_id' => $question->id,
              'order' => $question->pivot->order,
              'score' => $question?->pivot?->score ?? 2,
              'is_latest' => 1,
              'created_at' => now(),
              'updated_at' => now(),
          ];
          if (!empty($question->words)) {
              $vocabularies = array_merge($vocabularies, $question->words);
          }
          $taskQuestion[] = $questionData;
      }
      TaskQuestion::insert($taskQuestion);
      if(!empty($vocabularies)){
        $homework = Homework::with('mainHomework.getClass')->whereHas('mainHomework',function ($q){})->find($request['homework_id']);
        $homework->mainHomework->update(['has_vocabulary' => 1]);
        $tag = Tag::where('main_homework_id',$homework->main_homework_id)->first();
        if(!$tag){
          $class = $homework->mainHomework->getClass;
          $session = $homework->mainHomework->session ?? null;
          $tag = Tag::create([
            'title' => formatTitleTag($session,$class),
            'main_homework_id' => $homework->main_homework_id,
            'class_id' => $homework->mainHomework->getClass->id,
            'session' => $homework->mainHomework->session ?? null,
          ]);
        }
        foreach ($vocabularies as $vocabulary) {
          TagVocabulary::updateOrCreate(
            [
              'tag_id' => $tag->id,
              'vocabulary_id' => $vocabulary,
            ],
            [
              'created_at' => now(),
              'updated_at' => now(),
            ]
          );
        }
      }
    return $newTask;
  }

}
