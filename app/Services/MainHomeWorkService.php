<?php

namespace App\Services;

use App\Enums\QuestionType;
use App\Models\AccountHomework;
use App\Models\AccountMainHomework;
use App\Models\Classes;
use App\Models\GradeHomework;
use App\Models\Homework;
use App\Models\MainHomeworks;
use App\Models\Tag;
use App\Models\TagVocabulary;
use App\Models\TaskHomework;
use App\Models\TaskQuestion;
use App\Repositories\MainHomeWorksRepository;
use App\Repositories\QuestionRepository;
use App\Repositories\TagVocabularyRepository;
use App\Repositories\TaskQuestionRepository;

class MainHomeWorkService
{
    public function __construct(protected MainHomeWorksRepository $mainHomeworkRepo,
                                protected TagVocabularyRepository $tagVocabularyRepo,
                                protected QuestionRepository $questionRepo,
                                protected TaskQuestionRepository $taskQuestionRepo
    )
    {}

    public function listMainHomeworks($request)
    {
      return MainHomeworks::whereHas('getClass', function ($query) use ($request) {
          $query->when(!empty($request['class']), function ($query) use ($request) {
              $query->where('id', $request['class']);
          });
      })
          ->when(auth('admin')->user()->role == 'teacher', function ($query) {
              $query->whereHas('getClass.admins', function ($q) {
                  $q->where('admin_id', auth('admin')->user()->id);
              });
          })
        ->with([
            'getClass','tag.vocabularies',
            'homeworks' => fn($q) => $q->orderBy('id', 'desc'),
            'schedule',
        ])
          ->when(!empty($request['keyword']), function ($query) use ($request) {
              $escapedSearch = str_replace(['%', '_'], ['\\%', '\\_'], $request['keyword']);
              $query->where(function ($q) use ($escapedSearch) {
                  $q->where('title', 'LIKE', "%{$escapedSearch}%")
                      ->orWhere('description', 'LIKE', "%{$escapedSearch}%");
              });
          })
          ->when(!empty($request['session']), function ($query) use ($request) {
              $query->where('session', $request['session']);
          })
          ->orderBy('created_at', 'desc')->paginate(10);
    }

    public function getMainHomework($id)
    {
      return MainHomeworks::find($id);
    }

    public function getMainHomeworkById($id)
    {
        return MainHomeworks::with(['homeworks','getClass','tag.vocabularies','schedule'])->whereHas('getClass',function (){})->find($id);
    }

    public function store($request)
    {
        $class = Classes::find($request['class']);
        if($class->type == 'Private'){
          $request['session'] = null;
        }else{
          $request['schedule_id'] = null;
        }
        $mainHomework = MainHomeworks::create($request);
        if($request['has_vocabulary']){
            $this->createTag($mainHomework,$request);
        }else{
            $tag = Tag::with('vocabularies')
                ->where('class_id',$mainHomework->class)
                ->where('session',$mainHomework->session)
                ->where('main_homework_id',null)
                ->where('task_id',null)
                ->first();
            if($tag){
                $newTag = $tag->replicate();
                $newTag->main_homework_id = $mainHomework->id;
                $newTag->save();
                if(!empty($tag->vocabularies)){
                    foreach ($tag->vocabularies as $vocabulary) {
                        $this->tagVocabularyRepo->create([
                          'tag_id' => $newTag->id,
                          'vocabulary_id' => $vocabulary->id,
                        ]);
                    }
                }

            }
        }
        return $mainHomework;
    }

    public function getSessions($classId)
    {
      return $this->mainHomeworkRepo->getSessions($classId);
    }

    public function getClassByMainHomework($mainHomeworkId)
    {
        return MainHomeworks::whereHas('getClass',function (){})
            ->with('getClass')->where('id',$mainHomeworkId)->first();
    }

    public function update($request,$id)
    {
      $mainHomework =  MainHomeworks::with(['getClass'])->find($id);
      if(!empty($request['has_vocabulary'])){
        $tag = Tag::with('vocabularies')->where(['main_homework_id' => $id])->first();
        if(!$tag){
          $tag = $this->createTag($mainHomework,$request);
        }
        $this->hanldeUpdateQuestion($request,$tag,$id);
      }
      return $mainHomework->update([
        'title' => $request['title'],
        'description' => $request['description'] ?? null,
        'has_vocabulary' => $request['has_vocabulary'] ?? false,
        'schedule_id' => $request['schedule_id'] ?? null,
        'session' => $request['session'] ?? null,
      ]);
    }

    public function handleUpdateVocabulary($mainHomework,$requestVocabularyIds)
    {
      foreach ($mainHomework->homeworks as $homeworks) {
        foreach ($homeworks->taskHomeworks as $taskHomeworks) {
          if(empty($taskHomeworks->task)){
            continue;
          }
          foreach ($taskHomeworks->task->questions as $question) {
            if (in_array($question->type, [QuestionType::MAKE_SENTENCES, QuestionType::PARAGRAPH, QuestionType::FLASH_CARD])) {
              $currentVocabularyIds = $question->questionVocabularies->pluck('vocabulary_id')->toArray();
              $validVocabularyIds = array_values(array_intersect($currentVocabularyIds, $requestVocabularyIds));
              if (count($validVocabularyIds) !== count($currentVocabularyIds)) {
                $dataQuestion = $question->toArray();
                $dataQuestion['words'] = $validVocabularyIds;
                $this->taskQuestionRepo->where('question_id', $question->id)->update(['is_latest' => 0]);
                $questionNew = $this->questionRepo->create($dataQuestion);
                $this->taskQuestionRepo->create([
                  'task_id' => $taskHomeworks->task_id,
                  'question_id' => $questionNew->id,
                  'order' => $question->pivot->order,
                  'score' => $question->pivot->score,
                  'is_latest' => 1,
                ]);
              }
            }
          }
        }
      }
    }

    public function createTag($mainHomework,$request)
    {
      $data = [];
      $class = $mainHomework->getClass;
      $session = $request['session'] ?? null;
      $tag = Tag::create([
        'title' => formatTitleTag($session,$class),
        'class_id' => $class->id ?? null,
        'session' => $request['session'] ?? null,
        'main_homework_id' => $mainHomework->id,
      ]);
      foreach ($request['vocabularies'] as $vocabulary) {
        $data[] = [
          'tag_id' => $tag->id,
          'vocabulary_id' => $vocabulary['id'],
          'created_at' => now(),
          'updated_at' => now(),
        ];
      }
      $this->tagVocabularyRepo->insert($data);
      return $tag;
    }

    public function hanldeUpdateQuestion($request,$tag,$id): void
    {
      $requestVocabularyIds = $request['vocabularies'] ? collect($request['vocabularies'])->pluck('id')->toArray() : [];
      $dataTagVocabulary = [];
      foreach($requestVocabularyIds as $vocabularyId){
        $dataTagVocabulary[] = [
          'tag_id' => $tag->id,
          'vocabulary_id' => $vocabularyId,
          'created_at' => now(),
          'updated_at' => now(),
        ];
      }
      $this->tagVocabularyRepo->deleteWhere(['tag_id' => $tag->id]);
      $this->tagVocabularyRepo->insert($dataTagVocabulary);
      $mainHomeworkNew = MainHomeworks::
      with(['homeworks.taskHomeworks.task.questions'])
        ->whereHas('homeworks.taskHomeworks', function ($q) {
          $q->where('is_latest', true);
        })
        ->where('id', $id)
        ->first();
      if(!empty($mainHomeworkNew->homeworks)) {
        $this->handleUpdateVocabulary($mainHomeworkNew, $requestVocabularyIds);
      }
    }

    public function formatMainHomework($id)
    {
      $mainHomework = $this->getMainHomeworkById($id);
      if(!empty($mainHomework->tag)){
        $data = $mainHomework->tag->vocabularies->pluck('id')->toArray();
        unset($mainHomework->tag->vocabularies);
        $mainHomework->tag->vocabularies = $data;
      }
      return $mainHomework;
    }

    public function getHomeworks($request)
    {
        if (!empty($request['homework_id'])) {
            $homework = Homework::find($request['homework_id']);
            return MainHomeworks::with(['homeworks' => function($query) use ($request) {
                $query->where('id', '!=', $request['homework_id']);
            },'getClass'])->where('id', $homework?->main_homework_id)->first();

        } else if (!empty($request['main_homework_id'])) {
            return MainHomeworks::with(['homeworks','getClass'])->where('id', $request['main_homework_id'])->first();
        }
        return null;
    }

    public function duplicate($request, $id)
    {
        //main homework
        $mainHomework = MainHomeworks::with(['tag','homeworks'])->find($id);
        $newMainHomework = $mainHomework->replicate();
        $newMainHomework->class = $request['class'];
        $newMainHomework->session = $request['session'] ?? null;
        $newMainHomework->schedule_id = $request['schedule_id'] ?? null;
        $newMainHomework->save();

        //tag
        if (!empty($mainHomework->tag)) {
            $newTag = $mainHomework->tag->replicate();
            $class = Classes::with('terms')->find($request['class']);
            $newTag->title = formatTitleTag($request['session'], $class);
            $newTag->class_id = $request['class'];
            $newTag->session = $request['session'] ?? null;
            $newTag->main_homework_id = $newMainHomework->id;
            $newTag->save();
            TagVocabulary::insert($mainHomework->tag->vocabularies->map(function ($vocabulary) use ($newTag) {
                return [
                    'tag_id' => $newTag->id,
                    'vocabulary_id' => $vocabulary->id,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            })->toArray());
        }

        //homework
        foreach ($mainHomework->homeworks as $homework) {
            $newHomework = $homework->replicate();
            $newHomework->main_homework_id = $newMainHomework->id;
            $newHomework->save();
            $taskHomework = TaskHomework::where('homework_id', $homework->id)->where('is_latest', 1)->get();
            foreach ($taskHomework as $task) {
                $newTask = $task->task->replicate();
                $newTask->save();
                TaskHomework::create([
                    'task_id' => $newTask->id,
                    'homework_id' => $newHomework->id,
                    'order' => $task->order,
                    'is_latest' => 1,
                    'min_submit_time' => $task->min_submit_time,
                    'max_submit_time' => $task->max_submit_time,
                ]);
                $taskQuestions = TaskQuestion::where('task_id', $task->task_id)->where('is_latest', 1)->get();
                foreach ($taskQuestions as $question) {
                    $question->replicate()->create([
                        'task_id' => $newTask->id,
                        'is_latest' => 1,
                        'question_id' => $question->question_id,
                        'order' => $question->order,
                        'score' => $question->score,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }
            }
        }
        return $newMainHomework;
    }

    public function delete($id)
    {
        $mainHomework = MainHomeworks::with(['tag.vocabularies'])->find($id);
        Homework::where('main_homework_id', $id)->delete();
        $tag = Tag::where('main_homework_id', $id)->first();
        if($tag){
          TagVocabulary::where('tag_id', $tag->id)->delete();
          $tag->delete();
        }

        AccountMainHomework::where('main_homework_id', $id)->delete();
        GradeHomework::where('main_homework_id', $id)->delete();
        AccountHomework::where('main_homework_id', $id)->delete();

        $mainHomework->delete();
    }
}
