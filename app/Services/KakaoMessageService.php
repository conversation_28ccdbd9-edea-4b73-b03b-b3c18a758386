<?php
namespace App\Services;

use App\Jobs\SendKakaoMessageJob;
use App\Repositories\AccountRepository;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

class KakaoMessageService
{
    /**
     * sendKakaoMessagesForUsers
     *
     * @param \Illuminate\Support\Collection $users
     * @param object $message
     * @return void
     */
    public function sendKakaoMessagesForUsers(Collection $users, $message, $text): void
    {
        $validUsers = $users->filter(fn($u) => !empty($u['uuid_kakao']))->values();

        if ($validUsers->isNotEmpty() && $message->status === 'active') {
            $validUsers->chunk(5)->each(function ($chunk) use ($text) {
                $uuids = $chunk->pluck('uuid_kakao')->values()->all();
                if (!empty($uuids)) {
                    $payload = [
                        'receiver_uuids' => $uuids,
                        'template_object' => [
                            'object_type' => 'text',
                            'text' => "$text",
                            'link' => [
                                'web_url' => 'https://example.com',
                                'mobile_web_url' => 'https://example.com',
                            ],
                        ],
                    ];

                    SendKakaoMessageJob::dispatch($payload);
                }
            });
        }
    }

     /**
     * sendKakaoMessagesForUsers
     *
     * @param \Illuminate\Support\Collection $users
     * @param object $message
     * @return void
     */
    public function sendKakaoMessagesForParentOfStudent($parent, $message): void
    {
        if($parent && $parent->uuid_kakao){

            $payload = [
                'receiver_uuids' => [$parent->uuid_kakao],
                'template_object' => [
                    'object_type' => 'text',
                    'text' => "{$message}",
                    'link' => [
                        'web_url' => 'https://example.com',
                        'mobile_web_url' => 'https://example.com',
                    ],
                ],
            ];
            SendKakaoMessageJob::dispatch($payload);
        }
    }
}
