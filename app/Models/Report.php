<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Report extends Model
{
    use HasFactory,SoftDeletes;
    protected $guarded = [];

    public function term(){
      return $this->belongsTo(Term::class, 'term_id', 'id');
    }

    public function student(){
      return $this->belongsTo(Account::class, 'student_id', 'id');
    }

    public function classes(){
      return $this->belongsTo(Classes::class, 'term_id', 'id')->where('type', 'private');
    }
}
