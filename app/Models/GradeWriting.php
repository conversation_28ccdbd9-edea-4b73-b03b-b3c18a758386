<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class GradeWriting extends Model
{
    use HasFactory,SoftDeletes;
    protected $table = 'grades_writing';
    protected $guarded = [];

    public function classes(){
      return $this->belongsTo(Classes::class,'class_id','id');
    }
}
