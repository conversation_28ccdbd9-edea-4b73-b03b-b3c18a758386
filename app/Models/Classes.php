<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Classes extends Model
{
    use HasFactory, SoftDeletes;
    protected $table = 'classes';
    protected $guarded = [];

    public function student()
    {
        return $this->hasMany(ClassStudent::class, 'class_id', 'id');
    }

    public function terms()
    {
        return $this->belongsTo(Term::class, 'term', 'id');
    }

    public function schedule()
    {
        return $this->hasMany(ClassSchedule::class, 'class_id', 'id');
    }

    public function scheduleToday()
    {
        return $this->hasOne(ClassSchedule::class, 'class_id', 'id');
    }

    public function admins()
    {
        return $this->belongsToMany(Admin::class, 'admins_classes', 'class_id', 'admin_id');
    }

    public function students()
    {
        return $this->hasManyThrough(Account::class, ClassStudent::class, 'class_id', 'id', 'id', 'student_id');
    }

    public function product()
    {
        return $this->belongsTo(ProductClass::class, 'id', 'class_id');
    }

    public function products()
    {
        return $this->hasMany(ProductClass::class, 'class_id', 'id');
    }
}
