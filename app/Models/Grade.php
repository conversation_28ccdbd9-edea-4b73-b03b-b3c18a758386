<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Grade extends Model
{
    use HasFactory,SoftDeletes;
    protected $guarded = [];

    public function classes(){
      return $this->belongsTo(Classes::class,'class_id','id');
    }

    public function student(){
      return $this->belongsTo(Account::class,'student_id','id');
    }
}
