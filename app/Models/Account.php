<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;

class Account extends Authenticatable
{
  use HasFactory, SoftDeletes;

  protected $guarded = [];

  public function user()
  {
    return $this->belongsTo(User::class);
  }

  public function parent()
  {
    return $this->hasOne(Account::class, 'user_id', 'user_id')->where('type', 'parent');
  }

  public function student()
  {
    return $this->hasMany(Account::class, 'user_id', 'user_id')->where('type', 'student');
  }

  public function initTest()
  {
    return $this->hasMany(InitTest::class, 'user_id', 'id');
  }

  public function classes()
  {
    return $this->belongsToMany(Classes::class, 'class_student', 'student_id', 'class_id')
      ->withTimestamps()
      ->wherePivot('deleted_at', null);
  }
  public function invoices()
  {
    return $this->hasMany(Invoice::class);
  }

  public function examSessions()
  {
    return $this->hasMany(ExamSession::class);
  }

  public function mainHomeworks()
  {
    return $this->belongsToMany(MainHomeworks::class, 'account_main_homework', 'account_id', 'main_homework_id');
  }

  public function accountHomeworks()
  {
      return $this->hasMany(AccountHomework::class, 'account_id');
  }

  public function invoiceStudent()
  {
      return $this->hasMany(InvoiceStudent::class, 'student_id');
  }
}
