<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Term extends Model
{
    use HasFactory,SoftDeletes;
    protected $guarded = [];

    public function getClass(){
      return $this->belongsTo(Classes::class,'id','term');
    }

    public function classes(){
      return $this->hasMany(Classes::class,'term','id');
    }
}
