<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Message extends Model
{
    use HasFactory,SoftDeletes;
    protected $guarded = [];

    public function user()
    {
      return $this->hasMany(MessageUser::class, 'message_id', 'id');
    }
    public function admin()
    {
      return $this->belongsTo(Admin::class, 'admin_id', 'id');
    }

}
