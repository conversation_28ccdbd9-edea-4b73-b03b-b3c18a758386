<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InvoiceStudent extends Model
{
    use HasFactory;

    protected $table = 'invoice_students';
    protected $fillable = [
        'invoice_id',
        'student_id'
    ];

    public function invoice(){
        return $this->belongsTo(Invoice::class,'invoice_id','id');
    }

    public function student(){
        return $this->belongsTo(Account::class,'student_id','id');
    }
}
