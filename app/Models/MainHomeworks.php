<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MainHomeworks extends Model
{
    use HasFactory;

    protected $table = 'main_homeworks';
    protected $fillable = [
        'title',
        'description',
        'class',
        'session',
        'has_vocabulary',
        'schedule_id',
    ];

    public function homeworks()
    {
        return $this->hasMany(Homework::class, 'main_homework_id', 'id');
    }

    public function getClass()
    {
        return $this->belongsTo(Classes::class, 'class', 'id');
    }

    public function students()
    {
        return $this->getClass?->students();
    }
    public function accounts()
    {
        return $this->belongsToMany(Account::class, 'account_main_homework', 'main_homework_id', 'account_id');
    }

  public function userHomeworks()
  {
    return $this->hasManyThrough(
      AccountHomework::class,
      Homework::class,
      'main_homework_id',
      'homework_id',
      'id',
      'id'
    );
  }

  public function vocabularies()
  {
    return $this->hasManyThrough(
      TagVocabulary::class,
      Tag::class,
      'main_homework_id',
      'tag_id',
      'id',
      'id'
    );
  }

  public function tag()
  {
    return $this->hasOne(Tag::class, 'main_homework_id', 'id');
  }

  public function gradeHomework()
  {
      return $this->hasOne(GradeHomework::class, 'main_homework_id', 'id');
  }

  public function schedule()
  {
    return $this->belongsTo(ClassSchedule::class, 'schedule_id', 'id');
  }


}
