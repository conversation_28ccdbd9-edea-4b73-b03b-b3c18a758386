<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Invoice extends Model
{
    use HasFactory,SoftDeletes;
    protected $guarded = [];

  public function parent(){
    return $this->belongsTo(Account::class,'parent_id','id');
  }

  public function product(){
    return $this->hasMany(ProductInvoice::class);

  }

  public function students()
  {
      return $this->hasMany(InvoiceStudent::class);
  }
}
