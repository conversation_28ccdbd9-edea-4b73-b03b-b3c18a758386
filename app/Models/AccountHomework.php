<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
class AccountHomework extends Model
{
  use HasFactory;
  protected $table = 'account_homework';
  protected $fillable = [
    'account_id',
    'homework_id',
    'main_homework_id',
    'start_time',
    'end_time',
    'score',
    'homework_unlock',
    'account_main_homework_id',
  ];

  public function homework()
  {
    return $this->belongsTo(Homework::class, 'homework_id', 'id');
  }

  public function accountHomeworkTasks()
  {
    return $this->hasMany(AccountHomeworkTask::class, 'user_homework_id', 'id');
  }

  public function account()
  {
    return $this->belongsTo(Account::class, 'account_id', 'id');
  }

  public function accountMainHomework()
  {
    return $this->belongsTo(AccountMainHomework::class, 'account_main_homework_id');
  }

  public function requestUnlocks()
  {
    return $this->morphMany(RequestUnlock::class, 'unlockAble');
  }

  public function accountHomeworks()
  {
      return $this->hasMany(AccountHomework::class, 'account_id');
  }

  public function mainHomework()
  {
      return $this->belongsTo(MainHomeworks::class, 'main_homework_id', 'id');
  }
}
