<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductClass extends Model
{
    use HasFactory;
    protected $table = 'product_class';
    protected $fillable = [
        'product_id',
        'class_id'
    ];

    public function getClass()
    {
        return $this->belongsTo(Classes::class, 'class_id', 'id');
    }

    public function productInvoices()
    {
        return $this->hasMany(ProductInvoice::class, 'prod_id', 'product_id');
    }

}
