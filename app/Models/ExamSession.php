<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ExamSession extends Model
{
    use HasFactory;

    protected $fillable = [
        'id',
        'start_time',
        'end_time',
        'account_id',
        'exam_id',
        'total_duration_seconds',
        'status',
    ];

    public function account()
    {
        return $this->belongsTo(Account::class);
    }
}