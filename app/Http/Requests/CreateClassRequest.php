<?php

namespace App\Http\Requests;

use Carbon\Carbon;
use Illuminate\Foundation\Http\FormRequest;

class CreateClassRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
      $rules['name'] = 'required|max:50|string|unique:classes,name';
      $rules['type'] = 'required|in:Private,Special,Regular';
      $rules['day'] = 'required|array';
      $rules['day.*'] = 'in:monday,tuesday,wednesday,thursday,friday,saturday,sunday';

      $days = $this->input('day', []);
      foreach ($days as $day) {
        $rules[$day . '_start'] = 'required|date_format:H:i';
        $rules[$day . '_end'] = 'required|date_format:H:i|after:' . $day . '_start';
      }
      $rules['subject'] = 'required|in:Reading,Writing';

      if(request()->type == 'Regular' || request()->type == 'Special' || request()->type == ''){
        $rules['term'] = 'required';
        $rules['level'] = 'required';
      }
      if(request()->type == 'Special'){
        unset($rules['level']);
        unset($rules['year']);
        unset($rules['term']);
        $rules['duration'] = 'required|regex:/^\d{1,2}$/';
      }else if(request()->type == 'Private'){
        $rules['start_private'] = 'required|date|after_or_equal:' . Carbon::today()->toDateString();
      }
      return $rules;
    }

    public function messages()
    {
      $message['name.required'] = 'This is a required field.';
      $message['name.max'] = 'Class Name cannot exceed 50 characters';
      $message['name.unique'] = 'Class Name already exist';
      $message['location.required'] = 'This is a required field.';
      $message['term.required'] = 'This is a required field.';
      $message['term.in'] = 'Invalid format.';
      $message['type.required'] = 'This is a required field.';
      $message['type.in'] = 'Invalid format.';
      $message['day.required'] = 'This is a required field.';
      $message['day.in'] = 'Invalid format.';
      $message['duration.required'] = 'This is a required field.';
      $message['duration.regex'] = 'Invalid format.';
      $days = $this->input('day', []);
      foreach ($days as $day) {
        $message[$day . '_start.required'] = 'This is a required field.';
        $message[$day . '_start.date_format'] = 'Invalid format.';
        $message[$day . '_end.required'] = 'This is a required field.';
        $message[$day . '_end.date_format'] = 'Invalid format.';
        $message[$day . '_end.after'] = 'End Time must be greater than Start Time.';
      }
      $message['subject.required'] = 'This is a required field.';
      $message['subject.in'] = 'Invalid format.';
      $message['start_date.required'] = 'This is a required field.';
      $message['start_private.required'] = 'This is a required field.';
      $message['start_date.date'] = 'Invalid format.';
      $message['start_private.date'] = 'Invalid format.';
      $message['start_private.after_or_equal'] = 'Invalid format.';
      $message['level.required'] = 'This is a required field.';
      return $message;

    }
}
