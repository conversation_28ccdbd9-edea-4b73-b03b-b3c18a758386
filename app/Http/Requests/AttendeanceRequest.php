<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class AttendeanceRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
          'type' => 'required|in:O,X,Late,NA',
          'date' => 'required|date',
        ];
    }
    public function messages()
    {
        return [
          'type.required' => 'This is a required field.',
          'date.required' => 'This is a required field.',
          'date.date' => 'Invalid format.',
          'type.in' => 'Invalid format.',
        ];
    }
}
