<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class UserAuthenticated
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
      if (Auth::guard('web')->user()) {
        return $next($request);
      }
      if ($request->ajax() || $request->wantsJson()) {
        return response('Unauthorized.', 401);
      } else {
        return redirect('/login');
      }
    }
}
