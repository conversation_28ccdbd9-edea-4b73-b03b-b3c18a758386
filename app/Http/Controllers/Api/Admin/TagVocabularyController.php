<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use App\Http\Resources\BasePaginatedCollection;
use App\Services\TagVocabularyService;
use Illuminate\Http\Request;

class TagVocabularyController extends Controller
{

    public function __construct(private TagVocabularyService $tagVocabularyService)
    {

    }

    public function getAll()
    {
        $vocabularies = $this->tagVocabularyService->getAll();
        foreach($vocabularies as $vocabulary){
          $vocabularyIds =  $vocabulary->vocabularies->pluck('id')->toArray();
          $listTitle = $vocabulary->vocabularies->pluck('word')->toArray();
          if($listTitle) {
            $vocabulary->title .= ' - (' . implode(', ', $listTitle). ')';
          }
          unset($vocabulary->vocabularies);
            $vocabulary->vocabularies = $vocabularyIds;
        }
        return response()->json([
            'data' => $vocabularies
        ]);
    }
    public function index(Request $request)
    {
        $tags = $this->tagVocabularyService->getTags($request);
        return response()->json(new BasePaginatedCollection($tags));
    }

    public function show(int $id)
    {
        $tag = $this->tagVocabularyService->getTag($id);
        return response()->json([
            'data' => $tag
        ]);
    }

    public function store(Request $request)
    {
        $tag = $this->tagVocabularyService->store($request->only([
            'title',
            'class_id',
            'session',
            'vocabularies',
        ]));
        return response()->json([
            'data' => $tag,
        ]);
    }

    public function update(Request $request, $id)
    {
      $tag = $this->tagVocabularyService->update($request->only([
          'title',
          'vocabularies',
          'class_id',
          'session',
        ]), $id);
      return response()->json([
          'data' => $tag,
      ]);
    }

    public function destroy(int $id)
    {
        $this->tagVocabularyService->destroy($id);
        return response()->json(['message' => 'Tag deleted successfully']);
    }

}
