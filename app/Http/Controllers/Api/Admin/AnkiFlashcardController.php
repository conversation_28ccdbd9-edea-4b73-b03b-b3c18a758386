<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use App\Repositories\VocabularyRepository;
use Illuminate\Http\Request;

class Anki<PERSON>lashcardController extends Controller
{
  public function __construct(private VocabularyRepository $vocabularyRepo) {}

  public function index(Request $request)
  {
    $studentId = $request->input('student_id');
    $lessonIndex = $request->input('lesson_index');

    $words = $this->vocabularyRepo->getWordsForLesson($studentId, $lessonIndex);

    return response()->json([
      'words' => $words
    ]);
  }

  public function review(Request $request)
  {
    $studentId = $request->input('student_id');
    $vocabularyId = $request->input('vocabulary_id');
    $result = $request->input('result');

    $this->vocabularyRepo->updateBucket($studentId, $vocabularyId, $result);

    return response()->json(['success' => true]);
  }

  public function homeworkScore(Request $request)
  {
    $studentId = $request->input('student_id');
    $vocabularyId = $request->input('vocabulary_id');
    $score = $request->input('score'); // 1, 2, 3

    $this->vocabularyRepo->updateHomeworkScore($studentId, $vocabularyId, $score);

    return response()->json(['success' => true]);
  }
}
