<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use App\Jobs\ProcessInvoiceJob;
use App\Models\Account;
use App\Models\Invoice;
use App\Models\InvoiceStudent;
use App\Models\ProductInvoice;
use App\Services\KakaoMessageService;
use Illuminate\Http\Request;

class InvoiceController extends Controller
{
    public function batch(Request $request)
    {
        $dataInvoice = [];
        foreach ($request->all() as $data) {
            $invoice = $this->createInvoice($data);
            $productIds = [];
            foreach ($data['products'] as $product) {
                $productIds[] = $product['id'];
            }
            $dataInvoice[] = [
                'invoice_id' => $invoice->id,
                'product_ids' => $productIds,
            ];
        }
        ProcessInvoiceJob::dispatch($dataInvoice);
        session()->flash('success', 'Created successfully');
        return response()->json([
            'message' => 'Created successfully',
            'data' => [],
        ]);
    }

    private function createInvoice($data)
    {
        $invoice = Invoice::create([
            'parent_id' => $data['parentId'],
            'price' => $data['total'],
            'status' => 'unpaid',
            'processing_status' => 'Pending',
        ]);

        foreach ($data['products'] as $product) {
            ProductInvoice::create([
                'invoice_id' => $invoice->id,
                'prod_id' => $product['id'],
                'prod_name' => $product['name'],
                'prod_des' => $product['des'] ?? '',
                'price' => $product['price'],
            ]);
        }

        InvoiceStudent::create([
            'invoice_id' => $invoice->id,
            'student_id' => $data['studentId'],
        ]);

        return $invoice;
    }

    public function updateStatus(Request $request, KakaoMessageService $kakaoMessageService)
    {
      $invoice = Invoice::findOrFail($request->id);

        $invoice->update([
            'status' => $request->status,
        ]);
        if($request->status === 'paid'){
        // send message kakao talk
        $parent = Account::find((int) $invoice->parent_id);
            $message = "인보이스 결제가 확인 되었습니다.\n 감사합니다.";
            $kakaoMessageService->sendKakaoMessagesForParentOfStudent($parent, $message);
        }
        return response()->json([
            'message' => 'Updated successfully',
        ]);
    }
}
