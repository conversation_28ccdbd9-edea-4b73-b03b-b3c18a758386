<?php

namespace App\Http\Controllers\Api\Admin;

use App\Enums\HomeworkType;
use App\Http\Controllers\Controller;
use App\Models\Classes;
use App\Models\Homework;
use App\Models\MainHomeworks;
use App\Repositories\ClassRepository;
use App\Services\HomeworkService;
use App\Services\MainHomeWorkService;
use Illuminate\Http\Request;

class ClassController extends Controller
{
  public function __construct(
    private ClassRepository $classRepo,
    private MainHomeWorkService $mainHomeworkService,
    private HomeworkService $homeworkService,
  ) {}

  public function index(Request $request)
  {
      if(auth('admin')->user()->role == 'teacher'){
        $classes = $this->classRepo->with('terms')
          ->whereHas('admins',function ($q){
            $q->where('admin_id',auth('admin')->user()->id);
          })
          ->when(!empty($request->type), function ($query) use ($request) {
              $query->where('type', $request->type);
          })
          ->get();
        return response()->json($classes);
      }

    $classes = $this->classRepo->with('terms')
        ->when(!empty($request->type), function ($query) use ($request) {
            $query->where('type', $request->type);
        })
        ->get();
    return response()->json($classes);
  }

  public function sessions(Request $request): \Illuminate\Http\JsonResponse
  {
      $class = Classes::find($request->class_id);
      if($class->type == 'Private'){
        $schedule = MainHomeworks::where('class',$request->class_id)->pluck('schedule_id')->unique()->toArray();

        $scheduleList = $class->schedule;
        $scheduleArray = [];
        foreach ($scheduleList as $scheduleItem){
          if(!in_array($scheduleItem->id, $schedule)){
            $scheduleArray[] = $scheduleItem;
          }
        }
        return response()->json($scheduleArray);
      }
      if ($request->class_id) {
          $sessionsData = $this->mainHomeworkService->getSessions($request->class_id);
          $sessions = [];
          for ($i = 1; $i <= 16; $i++) {
              if (!in_array($i, $sessionsData)) {
                  $sessions[] = $i;
              }
          }
          return response()->json($sessions);
      }
      return response()->json();
  }

  public function getDetail(Request $request)
  {
      if($request->homework_id){
        $homework = $this->homeworkService->getHomework($request->homework_id);
        if($homework){
          $request->main_homework_id = $homework->main_homework_id;
        }
      }
      if($request->main_homework_id){
        $mainHomework = $this->mainHomeworkService->getClassByMainHomework($request->main_homework_id);
        if(!$mainHomework){
          return response()->json([
            'data' => []
          ]);
        }
        $homeworkCount = Homework::where('main_homework_id', $mainHomework->id)->where('type', HomeworkType::HOMEWORK)->count();
        $dataReturn = $mainHomework?->getClass;
        $dataReturn->session = $mainHomework?->session;
        return response()->json([
          'data' => $dataReturn,
          'maxHomework' => $dataReturn?->type == 'Private' ? null : $homeworkCount + 1,
        ]);
      }
      return response()->json([
        'data' => []
      ]);
  }

}
