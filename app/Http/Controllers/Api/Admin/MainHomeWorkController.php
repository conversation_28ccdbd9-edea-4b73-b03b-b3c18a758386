<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\CreateMainHomeWorkRequest;
use App\Http\Resources\BasePaginatedCollection;
use App\Models\AccountHomework;
use App\Models\AccountMainHomework;
use App\Models\GradeHomework;
use App\Models\MainHomeworks;
use App\Services\MainHomeWorkService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Session;

class MainHomeWorkController extends Controller
{
    public function __construct(protected MainHomeWorkService $mainHomeWorkService)
    {
    }

    public function index(Request $request)
    {
        $mainHomeworks = $this->mainHomeWorkService->listMainHomeworks($request->all());
        foreach($mainHomeworks as $mainHomework){
          if(!empty($mainHomework->tag)){
            $data = $mainHomework->tag->vocabularies->pluck('id')->toArray();
            unset($mainHomework->tag->vocabularies);
            $mainHomework->tag->vocabularies = $data;
          }
        }
        return  response()->json(new BasePaginatedCollection($mainHomeworks));
    }

    public function store(CreateMainHomeWorkRequest $request)
    {
        $mainHomework = $this->mainHomeWorkService->store($request->all());
        Session::flash('success', 'Created successfully');
        return response()->json([
            'message' => 'Created successfully',
            'data' => $mainHomework,
        ]);
    }

    public function update(Request $request, $id)
    {
        $this->mainHomeWorkService->update($request->only([
          'title',
          'description',
          'has_vocabulary',
          'vocabularies',
          'session',
         'schedule_id',
        ]), $id);
        $mainHomework = $this->mainHomeWorkService->formatMainHomework($id);
        return response()->json([
            'message' => 'Updated successfully',
            'data' => $mainHomework,
        ]);
    }

    public function getHomeworks(Request $request)
    {
        $mainHomework = $this->mainHomeWorkService->getHomeworks($request->all());
        return response()->json([
            'data' => $mainHomework ?? null,
        ]);
    }

    public function getMainHomeworksByClass(Request $request)
    {
        $mainHomeworks = MainHomeworks::where('class', $request->class_id)->orderBy('created_at', 'desc')->get();
        return response()->json([
            'data' => $mainHomeworks ?? [],
        ]);
    }

    public function duplicate(Request $request, $id)
    {
        $mainHomework = $this->mainHomeWorkService->duplicate($request->all(), $id);
        Session::flash('success', 'Duplicated successfully');
        return response()->json([
            'message' => 'Created successfully',
            'data' => $mainHomework,
        ]);
    }

    public function destroy($id)
    {
        DB::beginTransaction();
        try {
            $this->mainHomeWorkService->delete($id);
            DB::commit();
            return response()->json([
                'message' => 'Deleted successfully',
            ]);
        }catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['message' => 'Something went wrong'], 400);
        }
    }
}
