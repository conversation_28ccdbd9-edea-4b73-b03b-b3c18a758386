<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use App\Repositories\KakaoRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

class KakaoController extends Controller
{
    protected KakaoRepository $kakaoRepo;

    public function __construct(KakaoRepository $kakaoRepo)
    {
        $this->kakaoRepo = $kakaoRepo;
    }

    public function index()
    {
        return view('admin.kakao.index');
    }

    public function callback(Request $request)
    {
        $code = $request->get('code'); 

        if (!$code) {
            return "<script>alert('No code returned from Kakao'); window.close();</script>";
        }

        $accessTokenData = $this->kakaoRepo->getAccessToken($code);

        return "<script>
            alert('Kakao login successful');
            window.close();
        </script>";
    }

    public function friends(Request $request)
    {
        $offset = (int) $request->get('offset', 0);
        $limit = (int) $request->get('limit', 50);

        $friends = $this->kakaoRepo->getFriends($offset, $limit);

        if (isset($friends['error'])) {
            return response()->json([
                'status' => 'error',
                'message' => 'Login required',
            ], 401);
        }

        return response()->json([
            'status' => 'success',
            'friends' => $friends,
        ]);
    }

    public function checkToken()
    {
        $result = $this->kakaoRepo->checkToken();
        return response()->json($result);
    }

    public function sendMessage(Request $request)
    {
        $data = $request->only(['receiver_uuids', 'template_object']);

        if (empty($data['receiver_uuids']) || empty($data['template_object'])) {
            return response()->json(['error' => 'receiver_uuids and template_object are required'], 422);
        }
        $data['template_id'] = 124163; 
        $result = $this->kakaoRepo->sendMessage($data);

        return response()->json($result);
    }

    public function destroy()
    {
        $result = $this->kakaoRepo->delete();
        return response()->json($result);
    }
}
