<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Repositories\AccountRepository;
use App\Repositories\ClassRepository;
use App\Repositories\GradeRepository;
use App\Repositories\GradeWritingRepository;
use App\Repositories\TermRepository;
use Illuminate\Http\Request;

class GradeController extends Controller
{
    public $termRepository;
    public $gradeRepository;
    public $gradeWritingRepository;
    public $accountRepository;
    public $classRepository;
    public function __construct(ClassRepository $classRepository, TermRepository $termRepository,GradeRepository $gradeRepository,GradeWritingRepository $gradeWritingRepository,AccountRepository $accountRepository){
      $this->termRepository = $termRepository;
      $this->gradeRepository = $gradeRepository;
      $this->gradeWritingRepository = $gradeWritingRepository;
      $this->accountRepository = $accountRepository;
      $this->classRepository = $classRepository;
    }
    public function index(Request $request){
      $students = [];
      $studentID = auth('web')->user()->id;
      if(auth('web')->user()->type == 'parent'){
        $students = $this->accountRepository->findWhere(['user_id' =>auth('web')->user()->user_id,'type'=>'student']);
        $studentID = count($students) ? $students[0]->id : null;
        if($request->student_id){
          $studentID = $request->student_id;
        }
      }
      $year = now();
      if($request->year){
        $year = $request->year;
      }
      $terms = $this->termRepository->with(['classes'])->whereHas('classes',function ($q) use ($studentID){
        $q->whereHas('student',function ($query) use($studentID) {
          $query->where('student_id',$studentID);
        })->where('classes.status','active');
      })->orderBy('year','desc')->findWhere([['start','DATE <=',now()]]);
      $termSlected = null;
      if(count($terms)){
        $years = $terms->unique('year');
        $termSlected = $terms[0]->id;
        $year = $request->year ? : $terms[0]->year;
      }else{
        $years = [];
      }
      $terms = $this->termRepository->with(['classes'])->whereHas('classes',function ($q) use ($studentID){
        $q->whereHas('student',function ($query) use($studentID) {
          $query->where('student_id',$studentID);
        })->where('classes.status','active');
      })->orderBy('id','desc')->findWhere(['year'=>$year,['start','DATE <=',now()]]);
      $class = $this->classRepository->whereHas('student',function ($q) use ($studentID){
        $q->where('student_id',$studentID);
      })->orderBy('term','desc')->findWhere([
        'status'=>'active',
        ['term','IN',count($terms) ? $terms->pluck('id')->toArray() : []],
      ])->first();
      if($class){
        $termSlected = $class->term;
      }
      $grades = $this->gradeRepository
        ->where('student_id', $studentID)
        ->where('session', $request->session ?: 1)
        ->where('status', 'active')
        ->whereHas('classes', function ($query) use ($request,$termSlected,$studentID) {
          $query->where('status','active')->whereHas('student',function ($sqr) use ($studentID){
            $sqr->where('student_id',$studentID);
          })->whereHas('terms', function ($subQuery) use ($request, $termSlected) {
            $subQuery->where(function ($query) use ($request, $termSlected) {
                $query->where('id', $request->term ?: $termSlected);
            })->where('year', $request->year ?: now());
          });
        })->get();
      $combinedGrades = [];
      foreach ($grades as $grade) {
        $key = $grade->student_id . '_' . $grade->class_id . '_' . $grade->session;
        if (isset($combinedGrades[$key])) {
          $combinedGrades[$key][$grade->type] = $grade;
          if ($grade->sentence == 'notFM') {
            $combinedGrades[$key]['sentence_total1'] += $grade->sentence1;
            $combinedGrades[$key]['sentence_total2'] += $grade->sentence2;
          } else {
            $max = $this->gradeRepository->orderBy('sentence2', 'desc')->findWhere(['class_id'=> $grade->class_id,'session'=>$grade->session,'type' => $grade->type])->first();
            if($max && $max->sentence){
              $combinedGrades[$key]['sentence_total2']+= $max->vocab2;
            }
          }

          if ($grade->vocabulary_maxtrix_1 == 'notFM') {
            $combinedGrades[$key]['total_vocabulary_maxtrix_11'] += $grade->vocabulary_maxtrix_11;
            $combinedGrades[$key]['total_vocabulary_maxtrix_12'] += $grade->vocabulary_maxtrix_12;
          } else {
            $max = $this->gradeRepository->orderBy('vocabulary_maxtrix_12', 'desc')->findWhere(['class_id'=> $grade->class_id,'session'=>$grade->session,'type' => $grade->type])->first();
            if($max && $max->vocabulary_maxtrix_12){
              $combinedGrades[$key]['total_vocabulary_maxtrix_12']+= $max->vocabulary_maxtrix_12;
            }
          }

          if ($grade->vocabulary_maxtrix_2 == 'notFM') {
            $combinedGrades[$key]['total_vocabulary_maxtrix_21'] += $grade->vocabulary_maxtrix_21;
            $combinedGrades[$key]['total_vocabulary_maxtrix_22'] += $grade->vocabulary_maxtrix_22;
          }else {
            $max = $this->gradeRepository->orderBy('vocabulary_maxtrix_22', 'desc')->findWhere(['class_id'=> $grade->class_id,'session'=>$grade->session,'type' => $grade->type])->first();
            if($max && $max->vocabulary_maxtrix_22){
              $combinedGrades[$key]['total_vocabulary_maxtrix_22']+= $max->vocabulary_maxtrix_22;
            }
          }

          if ($grade->skill == 'notFM') {
            $combinedGrades[$key]['total_skill1'] += $grade->skill1;
            $combinedGrades[$key]['total_skill2'] += $grade->skill2;
          } else {
            $max = $this->gradeRepository->orderBy('skill2', 'desc')->findWhere(['class_id'=> $grade->class_id,'session'=>$grade->session,'type' => $grade->type])->first();
            if($max && $max->skill2){
              $combinedGrades[$key]['total_skill2']+= $max->skill2;
            }
          }

          if ($grade->vocab == 'notFM') {
            $combinedGrades[$key]['total_vocab1'] += $grade->vocab1;
            $combinedGrades[$key]['total_vocab2'] += $grade->vocab2;
          } else {
            $max = $this->gradeRepository->orderBy('vocab2', 'desc')->findWhere(['class_id'=> $grade->class_id,'session'=>$grade->session,'type' => $grade->type])->first();
            if($max && $max->vocab2){
              $combinedGrades[$key]['total_vocab2']+= $max->vocab2;
            }
          }

          if ($grade->concept == 'notFM') {
            $combinedGrades[$key]['total_concept1'] += $grade->concept1;
            $combinedGrades[$key]['total_concept2'] += $grade->concept2;
          } else {
            $max = $this->gradeRepository->orderBy('concept2', 'desc')->findWhere(['class_id'=> $grade->class_id,'session'=>$grade->session,'type' => $grade->type])->first();
            if($max && $max->concept2){
              $combinedGrades[$key]['total_concept2']+= $max->concept2;
            }
          }
          if($grade->vocab_hw == 'notFM'){
            $combinedGrades[$key]['total_vocab_hw1'] += $grade->vocab_hw1;
            $combinedGrades[$key]['total_vocab_hw2'] += $grade->vocab_hw2;
          }else{
            $max = $this->gradeRepository->orderBy('vocab_hw2', 'desc')->findWhere(['class_id'=> $grade->class_id,'session'=>$grade->session,'type' => $grade->type])->first();
            if($max && $max->vocab_hw2){
              $combinedGrades[$key]['total_vocab_hw2']+= $max->vocab_hw2;
            }
          }
        } else {
          $combinedGrades[$key] = [
            '0' => $grade,
            'student_id' => $grade->student_id,
            'class_id' => $grade->class_id,
            'session' => $grade->session,
            $grade->type => $grade,
          ];

          $combinedGrades[$key]['sentence_total1'] = 0;
          $combinedGrades[$key]['sentence_total2'] = 0;

          $combinedGrades[$key]['total_vocabulary_maxtrix_11'] = 0;
          $combinedGrades[$key]['total_vocabulary_maxtrix_12'] = 0;

          $combinedGrades[$key]['total_vocabulary_maxtrix_21'] = 0;
          $combinedGrades[$key]['total_vocabulary_maxtrix_22'] = 0;

          $combinedGrades[$key]['total_skill1'] = 0;
          $combinedGrades[$key]['total_skill2'] = 0;

          $combinedGrades[$key]['total_vocab1'] = 0;
          $combinedGrades[$key]['total_vocab2'] = 0;

          $combinedGrades[$key]['total_concept1'] = 0;
          $combinedGrades[$key]['total_concept2'] = 0;

          $combinedGrades[$key]['total_vocab_hw1'] = 0;
          $combinedGrades[$key]['total_vocab_hw2'] = 0;

          if ($grade->sentence == 'notFM') {
            $combinedGrades[$key]['sentence_total1'] += $grade->sentence1;
            $combinedGrades[$key]['sentence_total2'] += $grade->sentence2;
          } else {
            $max = $this->gradeRepository->orderBy('sentence2', 'desc')->findWhere(['class_id'=> $grade->class_id,'session'=>$grade->session,'type' => $grade->type])->first();
            if($max && $max->sentence){
              $combinedGrades[$key]['sentence_total2']+= $max->sentence2;
            }
          }
          if ($grade->vocabulary_maxtrix_1 == 'notFM') {
            $combinedGrades[$key]['total_vocabulary_maxtrix_11'] += $grade->vocabulary_maxtrix_11;
            $combinedGrades[$key]['total_vocabulary_maxtrix_12'] += $grade->vocabulary_maxtrix_12;
          } else {
            $max = $this->gradeRepository->orderBy('vocabulary_maxtrix_12', 'desc')->findWhere(['class_id'=> $grade->class_id,'session'=>$grade->session,'type' => $grade->type])->first();
            if($max && $max->vocabulary_maxtrix_12){
              $combinedGrades[$key]['total_vocabulary_maxtrix_12']+= $max->vocabulary_maxtrix_12;
            }
          }
          if ($grade->vocabulary_maxtrix_2 == 'notFM') {
            $combinedGrades[$key]['total_vocabulary_maxtrix_21'] += $grade->vocabulary_maxtrix_21;
            $combinedGrades[$key]['total_vocabulary_maxtrix_22'] += $grade->vocabulary_maxtrix_22;
          } else {
            $max = $this->gradeRepository->orderBy('vocabulary_maxtrix_22', 'desc')->findWhere(['class_id'=> $grade->class_id,'session'=>$grade->session,'type' => $grade->type])->first();
            if($max && $max->vocabulary_maxtrix_22){
              $combinedGrades[$key]['total_vocabulary_maxtrix_22']+= $max->vocabulary_maxtrix_22;
            }
          }
          if ($grade->skill == 'notFM') {
            $combinedGrades[$key]['total_skill1'] += $grade->skill1;
            $combinedGrades[$key]['total_skill2'] += $grade->skill2;
          } else {
            $max = $this->gradeRepository->orderBy('skill2', 'desc')->findWhere(['class_id'=> $grade->class_id,'session'=>$grade->session,'type' => $grade->type])->first();
            if($max && $max->skill2){
              $combinedGrades[$key]['total_skill2']+= $max->skill2;
            }
          }
          if ($grade->vocab == 'notFM') {
            $combinedGrades[$key]['total_vocab1'] += $grade->vocab1;
            $combinedGrades[$key]['total_vocab2'] += $grade->vocab2;
          } else {
            $max = $this->gradeRepository->orderBy('vocab2', 'desc')->findWhere(['class_id'=> $grade->class_id,'session'=>$grade->session,'type' => $grade->type])->first();
            if($max && $max->vocab2){
              $combinedGrades[$key]['total_vocab2']+= $max->vocab2;
            }
          }

          if ($grade->concept == 'notFM') {
            $combinedGrades[$key]['total_concept1'] += $grade->concept1;
            $combinedGrades[$key]['total_concept2'] += $grade->concept2;
          } else {
            $max = $this->gradeRepository->orderBy('concept2', 'desc')->findWhere(['class_id'=> $grade->class_id,'session'=>$grade->session,'type' => $grade->type])->first();
            if($max && $max->concept2){
              $combinedGrades[$key]['total_concept2']+= $max->concept2;
            }
          }
          if($grade->vocab_hw == 'notFM'){
            $combinedGrades[$key]['total_vocab_hw1'] += $grade->vocab_hw1;
            $combinedGrades[$key]['total_vocab_hw2'] += $grade->vocab_hw2;
          }else{
            $max = $this->gradeRepository->orderBy('vocab_hw2', 'desc')->findWhere(['class_id'=> $grade->class_id,'session'=>$grade->session,'type' => $grade->type])->first();
            if($max && $max->vocab_hw2){
              $combinedGrades[$key]['total_vocab_hw2']+= $max->vocab_hw2;
            }
          }
        }
      }

      $gradesWriting = $this->gradeWritingRepository
        ->where('student_id', $studentID)
        ->where('session', $request->session ?: 1)
        ->where('status', 'active')
        ->whereHas('classes', function ($query) use ($request,$termSlected,$studentID) {
          $query->where('status','active')->whereHas('student',function ($sqr) use ($studentID){
            $sqr->where('student_id',$studentID);
          })->whereHas('terms', function ($subQuery) use ($request,$termSlected) {
            $subQuery->where(function ($query) use ($request, $termSlected) {
                $query->where('id', $request->term ?: $termSlected);
            })->where('year', $request->year ?: now());
          });
        })->get();
      $combinedGradeW = [];
      foreach ($gradesWriting as $gradeW){
        $key = $gradeW->student_id . '_' . $gradeW->class_id . '_' . $gradeW->session;
        if (isset($combinedGradeW[$key])) {
          $combinedGradeW[$key][$gradeW->type] = $gradeW;
        } else {
          $combinedGradeW[$key] = [
            '0' => $gradeW,
            'student_id' => $gradeW->student_id,
            'class_id' => $gradeW->class_id,
            'session' => $gradeW->session,
            $gradeW->type => $gradeW,
          ];
        }
      }
      $classesNotGrades = $this->classRepository
        ->whereHas('student', function ($query) use ($studentID) {
          $query->where('student_id', $studentID);
        });
      if ($termSlected) {
        $classesNotGrades->whereHas('terms', function ($query) use ($request, $termSlected,$year) {
          $query->where('id', $request->term ?: $termSlected)
            ->where('year', $request->year ?: $year);
        });
      }
      $classIdsWithGrades = [];
      if(count($grades)){
        $classIdsWithGrades = array_merge($classIdsWithGrades, array_column($grades->toArray(), 'class_id'));
      }
      if(count($gradesWriting)){
        $classIdsWithGrades = array_merge($classIdsWithGrades, array_column($gradesWriting->toArray(), 'class_id'));
      }
      $classesNotGrades = $classesNotGrades->orderBy('id','desc')->findWhere(['type'=>'Regular','status'=>'active','term'=>$request->term ?: $termSlected,['id','NOTIN',$classIdsWithGrades]]);
      return view('web.pages.grade',compact('terms','years','combinedGrades','combinedGradeW','students','classesNotGrades','year','termSlected'));
    }

    public function getTerm(Request $request,$year){
      $studentID = $request->student_id;
      if(!$studentID){
        $studentID  = auth('web')->user()->id;
      }
      $terms = $this->termRepository->with(['classes'])->whereHas('classes',function ($q) use ($studentID){
        $q->whereHas('student',function ($query) use($studentID) {
          $query->where('student_id',$studentID);
        })->where('classes.status','active');
      })->orderBy('id','desc')->findWhere(['year'=>$year,['start','DATE <=',now()]]);
      return response()->json([
        'status' => true,
        'data' => $terms,
      ]);
    }

    public function getTermByStudent($studentID){
      $terms = $this->termRepository->with(['classes'])->whereHas('classes',function ($q) use ($studentID){
        $q->whereHas('student',function ($query) use($studentID) {
          $query->where('student_id',$studentID);
        })->where('classes.status','active');
      })->orderBy('year','desc')->findWhere([['start','DATE <=',now()]]);
      return response()->json([
        'year' => count($terms) ? $terms->unique('year') : [],
        'term' => $terms,
      ]);
    }
}
