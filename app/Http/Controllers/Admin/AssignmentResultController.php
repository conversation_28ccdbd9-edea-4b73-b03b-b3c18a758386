<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class AssignmentResultController extends Controller
{
  public function __construct()
  {
  }

  public function index(Request $request)
  {
    return view('admin.assignment-result.index');
  }
  public function create()
  {
    return view('admin.assignment-result.index');
  }

    public function edit()
  {
    return view('admin.assignment-result.index');
  }


  public function show()
  {
    return view('admin.assignment-result.index');
  }
}
