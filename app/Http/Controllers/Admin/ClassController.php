<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\CreateClassRequest;
use App\Http\Requests\UpdateClassRequest;
use App\Models\MainHomeworks;
use App\Repositories\ClassRepository;
use App\Repositories\ClassScheduleRepository;
use App\Repositories\GradeRepository;
use App\Repositories\GradeWritingRepository;
use App\Repositories\ReportRepository;
use App\Repositories\StudentClassRepository;
use App\Repositories\TermRepository;
use App\Services\MainHomeWorkService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ClassController extends Controller
{
    protected $classRepository;
    protected $classScheduleRepository;
    protected $termRepository;
    protected $studentClassRepository;
    protected $gradeRepository;
    protected $gradeWritingRepository;
    protected $reportRepository;

    public function __construct(ClassRepository $classRepository,ClassScheduleRepository $classScheduleRepository,TermRepository $termRepository,
                                ReportRepository $reportRepository, StudentClassRepository $studentClassRepository, GradeRepository $gradeRepository,
                                GradeWritingRepository $gradeWritingRepository, private MainHomeWorkService $mainHomeWorkService){
      $this->classRepository = $classRepository;
      $this->classScheduleRepository = $classScheduleRepository;
      $this->termRepository = $termRepository;
      $this->studentClassRepository = $studentClassRepository;
      $this->gradeRepository = $gradeRepository;
      $this->gradeWritingRepository = $gradeWritingRepository;
      $this->reportRepository = $reportRepository;
    }

    public function index(Request $request){
      $classes = $this->classRepository->filter($request->all());

      if($request->id == 'desc'){
        $totalItems = $classes->total();
        $index = $totalItems - ($classes->currentPage() - 1) * $classes->perPage();
      }else{
        $index = ($classes->currentPage() - 1) * $classes->perPage() + 1;
      }
      $terms = $this->termRepository->orderBy('created_at','desc')->all();
      return view('admin.classes.index',compact('classes','index','terms'));
    }

    public function create(){
      $terms = $this->termRepository->orderBy('id','desc')->get();
      return view('admin.classes.create',compact('terms'));
    }

    public function store(CreateClassRequest $request){
      if($request->type == 'Private'){
        $start_date = Carbon::parse($request->start_private)->startOfWeek();
        $dataClass = [
          'name'=> truncate_string($request->name),
          'teacher_id'=> null,
          'start_date'=> $request->start_private,
          'status'=> $request->status,
          'subject'=> $request->subject,
          'link'=> $request->link,
          'type'=> $request->type,
        ];
        $start = Carbon::parse($request->start_private)->format('Y-m-d');
        $class = $this->classRepository->create($dataClass);
        foreach ($request->day as $day) {
          $diff = Carbon::parse($day)->dayOfWeek - $start_date->dayOfWeek;
          $date = $start_date->copy()->addDays($diff);
          if ($date->toDateString() < $start) {
            $date->addDays(7);
          }
          $data[] = [
            'day' => ucfirst($day),
            'class_id' => $class->id,
            'start' => $request[$day . '_start'],
            'end' => $request[$day . '_end'],
            'date' => $date->toDateString(),
          ];
        }
        usort($data, function ($a, $b) {
          return strtotime($a['date']) - strtotime($b['date']);
        });
        if(isset($data[0]['date']) && date("m", strtotime($data[0]['date'])) > date('m',strtotime(now())) ){
          $this->classRepository->update(['init_schedules' => json_encode($data)],$class->id);
          return redirect()->route('admin.classes.index')->with('success','Updated successfully');
        }
        $monthData = [];
        $endDay = date("Y-m-t", strtotime($data[0]['date']));
        $start = strtotime($data[0]['date']);

        while ($start <= strtotime($endDay)) {
          foreach ($data as $dayData) {
            $dayOfWeek = date('l', $start);
            if ($dayData['day'] == $dayOfWeek) {
              $dayData['date'] = date('Y-m-d', $start);
              $monthData[] = $dayData;
            }
          }
          $start = strtotime('+1 day', $start);
        }
        $this->classRepository->update(['init_schedules' => json_encode($data)],$class->id);
        $this->classScheduleRepository->insert($monthData);
        return redirect()->route('admin.classes.index')->with('success','Created successfully');
      }
      $term = $this->termRepository->find($request->term);
      if (!is_null($term->break_start)) {
        $term->break_end = date('Y-m-d', strtotime($term->break_start . ' +7 days'));
      }
      $dataClass = [
        'name'=> truncate_string($request->name),
        'teacher_id'=> null,
        'status'=> $request->status,
        'term'=> $request->term,
        'type'=> $request->type,
        'subject'=> $request->subject,
        'link'=> $request->link,
        'level'=> $request->level,
      ];
      if($request->type  == 'Special'){
        unset($dataClass['level']);
        $dataClass['duration_session'] = $request->duration;
      }
      $class = $this->classRepository->create($dataClass);

      $start_date = Carbon::parse($term->start)->startOfWeek();
      $data = [];
      $dataInsert = [];
      foreach ($request->day as $day) {
        $diff = Carbon::parse($day)->dayOfWeek - $start_date->dayOfWeek;

        $date = $start_date->copy()->addDays($diff);

        if ($date->toDateString() < $term->start) {
          $date->addDays(7);
        }

        $data[] = [
          'day' => ucfirst($day),
          'start' => $request[$day . '_start'],
          'end' => $request[$day . '_end'],
          'date' => $date->toDateString(),
        ];
      }
      usort($data, function ($a, $b) {
        return strtotime($a['date']) - strtotime($b['date']);
      });
      $i = 0;
      if (!is_null($term->break_start)) {
        $term->break_end = date('Y-m-d', strtotime($term->break_start . ' +7 days'));
      }
      while (count($dataInsert) < count($data)*16) {
        foreach ($data as $session) {
          $sessionDate = Carbon::parse($session['date'])->addDays($i * 7);

          if ($sessionDate > $term->break_start && $sessionDate < $term->break_end) {
            continue;
          }

          if($term->break_option_start){
            if($sessionDate > $term->break_option_start && $sessionDate < $term->break_option_end){
              continue;
            }
          }

          $dataInsert[] = [
            'class_id' => $class->id,
            'day' => $session['day'],
            'start' => $session['start'],
            'end' => $session['end'],
            'date' => $sessionDate->toDateString(),
          ];
          if(count($dataInsert) == count($data)*16){
            break;
          }
        }
        $i++;
      }
      if($dataInsert){
        $this->classRepository->update(['init_schedules' => json_encode($data)],$class->id);
        $this->classScheduleRepository->insert($dataInsert);
      }
      return redirect()->route('admin.classes.index')->with('success','Created successfully');
    }

    public function edit($id){
      $terms = $this->termRepository->whereDate('start','>=', now())->get();
      $lastSession = 0;
      $class = $this->classRepository->withCount(['student'])->with(['terms'])->find($id);
      $schedule = $class->schedule->groupBy('day')->keys()->toArray();
      $schedules = $this->classScheduleRepository
        ->with('classes')
        ->where('class_id', $id)->orderBy('date')->get();
      $schedules = $this->classScheduleRepository->getSesion($schedules,$class->terms ? $class->terms->break_start : null);
      $sche = json_decode($class->init_schedules);
      foreach ($schedules as $schedule) {
        if (date('Y-m-d') >= $schedule->date) {
          $lastSession = $schedule->session;
        }
      }
      return view('admin.classes.edit',compact('class','sche','lastSession','terms'));
    }

  public function show($id){
    $terms = $this->termRepository->get();
    $lastSession = 0;
    $class = $this->classRepository->withCount(['student'])->with(['terms'])->find($id);
    $schedule = $class->schedule->groupBy('day')->keys()->toArray();
    $schedules = $this->classScheduleRepository
      ->with('classes')
      ->where('class_id', $id)->orderBy('date')->get();
    $schedules = $this->classScheduleRepository->getSesion($schedules,$class->terms ? $class->terms->break_start : null);
    $sche = json_decode($class->init_schedules);
    foreach ($schedules as $schedule) {
      if (date('Y-m-d') >= $schedule->date) {
        $lastSession = $schedule->session;
      }
    }
    return view('admin.classes.show',compact('class','sche','lastSession','terms'));
  }

    public function update(UpdateClassRequest $request,$id){
      $classOld  = $this->classRepository->with('terms')->find($id);
      if($classOld->type == 'Private'){
        if($classOld->start_date <= now()){
          $request->type = 'Private';
        }
      }if($classOld->type == 'Regular'){
        if($classOld->terms->start <= now()){
          $request->type = 'Regular';
        }
      }
      if($request->type == 'Private'){
        $start_date = Carbon::parse($request->start_private)->startOfWeek();
        $dataClass = [
          'name'=> truncate_string($request->name),
          'teacher_id'=> null,
          'start_date'=> $request->start_private,
          'status'=> $request->status,
          'subject'=> $request->subject,
          'link'=> $request->link,
          'type'=> $request->type,
          'break_start'=> $request->break_start,
          'break_end'=> $request->break_end,
          'term' => null,
        ];
        if($classOld->start_date && $classOld->start_date <= now()){
          $dataClass = [
            'name'=> truncate_string($request->name),
            'status'=> $request->status,
            'link'=> $request->link,
            'term' => null,
            'type' => $request->type,
          ];
          $this->classRepository->update($dataClass,$id);
          return redirect()->route('admin.classes.index')->with('success','Updated successfully');
        }
        $class = $this->classRepository->update($dataClass,$id);
        $start = Carbon::parse($request->start_private)->format('Y-m-d');
        foreach ($request->day as $day) {
          $diff = Carbon::parse($day)->dayOfWeek - $start_date->dayOfWeek;

          $date = $start_date->copy()->addDays($diff);
          if ($date->toDateString() < $start) {
            $date->addDays(7);
          }
          $data[] = [
            'day' => ucfirst($day),
            'class_id' => $class->id,
            'start' => $request[$day . '_start'],
            'end' => $request[$day . '_end'],
            'date' => $date->toDateString(),
          ];
        }
        usort($data, function ($a, $b) {
          return strtotime($a['date']) - strtotime($b['date']);
        });
        if(isset($data[0]['date']) && date("m", strtotime($data[0]['date'])) > date('m',strtotime(now())) ){
          $this->classRepository->update(['init_schedules' => json_encode($data)],$class->id);
          return redirect()->route('admin.classes.index')->with('success','Updated successfully');
        }
        $monthData = [];
        $this->classRepository->update(['init_schedules' => json_encode($data)],$class->id);
        $endDay = date("Y-m-t", strtotime($data[0]['date']));
        $start = strtotime($data[0]['date']);

        while ($start <= strtotime($endDay)) {
          foreach ($data as $dayData) {
            $dayOfWeek = date('l', $start);
            if ($dayData['day'] == $dayOfWeek) {
              $dayData['date'] = date('Y-m-d', $start);
              $monthData[] = $dayData;
            }
          }
          $start = strtotime('+1 day', $start);
        }
        $this->classRepository->update(['init_schedules' => json_encode($data)],$class->id);
        if(!empty($request->type) && in_array($request->type,['Private','Special','Regular']) && $monthData){
          $this->classScheduleRepository->deleteWhere([
            ['class_id', '=', $id],
          ]);
          $this->classScheduleRepository->insert($monthData);
        }

        return redirect()->route('admin.classes.index')->with('success','Updated successfully');
      }else{
        $dataClass = [
          'name'=> $request->name,
          'teacher_id'=> null,
          'status'=> $request->status,
          'term'=> $request->term,
          'year'=> $request->year,
          'subject'=> $request->subject,
          'break_start'=> $request->break_start,
          'break_end'=> $request->break_end,
          'link'=> $request->link,
          'level'=> $request->level,
          'start_date'=> null,
          'type' => $request->type,
        ];
        if($request->type  == 'Special'){
          unset($dataClass['term']);
          unset($dataClass['year']);
          unset($dataClass['level']);
          $dataClass['duration_session'] = $request->duration;
        }
        if($classOld->terms && $classOld->terms->start  <= now()){
          $dataClass = [
            'name'=> $request->name,
            'status'=> $request->status,
            'link'=> $request->link,
            'start_date'=> null,
            'type' => $request->type,
          ];
          $this->classRepository->update($dataClass,$id);
          return redirect()->route('admin.classes.index')->with('success','Updated successfully');
        }
        if(!empty($request->type) && in_array($request->type,['Private','Special','Regular'])){
          $dataClass['type'] = $request->type;
          $this->classScheduleRepository->deleteWhere([
            ['class_id', '=', $id],
          ]);
        }
        $this->classRepository->update($dataClass,$id);
        $term = $this->termRepository->find($request->term);
        $start_date = Carbon::parse($term->start)->startOfWeek();
        $data = [];
        $dataInsert = [];
        foreach ($request->day as $day) {
          $diff = Carbon::parse($day)->dayOfWeek - $start_date->dayOfWeek;

          $date = $start_date->copy()->addDays($diff);

          if ($date->toDateString() < $term->start) {
            $date->addDays(7);
          }

          $data[] = [
            'day' => ucfirst($day),
            'start' => $request[$day . '_start'],
            'end' => $request[$day . '_end'],
            'date' => $date->toDateString(),
          ];
        }
        usort($data, function ($a, $b) {
          return strtotime($a['date']) - strtotime($b['date']);
        });
        $i = 0;
        while (count($dataInsert) < count($data)*16) {
          foreach ($data as $session) {
            $sessionDate = Carbon::parse($session['date'])->addDays($i * 7);

            if ($sessionDate->toDateString() > $term->break_start && $sessionDate->toDateString() <= $term->break_end) {
              continue;
            }
            if($term->break_option_start && $sessionDate->toDateString() > $term->break_option_start && $sessionDate->toDateString() <= $term->break_option_end){
              continue;
            }

            $dataInsert[] = [
              'class_id' => $id,
              'day' => $session['day'],
              'start' => $session['start'],
              'end' => $session['end'],
              'date' => $sessionDate->toDateString(),
            ];
            if(count($dataInsert) == count($data)*16){
              break;
            }
          }
          $i++;
        }

        if($dataInsert){
          $this->classRepository->update(['init_schedules' => json_encode($data)],$id);
          $this->classScheduleRepository->insert($dataInsert);
        }
        return redirect()->route('admin.classes.index')->with('success','Updated successfully');
      }
    }

    public function destroy($id){
      DB::beginTransaction();
      try {
          $class = $this->classRepository->find($id);
          $class->update(['name' =>$class->name.'-'.time()]);
          $class->delete();
          $this->classScheduleRepository->deleteWhere(['class_id'=> $id]);
          $this->studentClassRepository->deleteWhere(['class_id' => $id]);
          $this->gradeRepository->deleteWhere(['class_id' => $id]);
          $this->gradeWritingRepository->deleteWhere(['class_id' => $id]);
          $this->reportRepository->deleteWhere(['term_id' => $id,'type' =>'private']);
          $mainHomeworks = MainHomeworks::where('class',$id)->get();
          foreach ($mainHomeworks as $mainHomework) {
              $this->mainHomeWorkService->delete($mainHomework->id);
          }
        DB::commit();
      } catch (\Exception $e) {
        DB::rollBack();
        return redirect()->back()->with('error','Delete failed.');
      }
      return redirect()->route('admin.classes.index')->with('success','Delete successfully.');
    }
}
