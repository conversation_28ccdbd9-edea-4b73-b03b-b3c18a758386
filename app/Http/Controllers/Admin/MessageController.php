<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\CreateMessageRequest;
use App\Repositories\AccountRepository;
use App\Repositories\ClassRepository;
use App\Repositories\MessageRepository;
use App\Repositories\MessageUserRepository;
use App\Repositories\StudentClassRepository;
use App\Services\KakaoMessageService;
use Illuminate\Http\Request;

class MessageController extends Controller
{
    public $classRepository;
    public $accountRepository;
    public $messageRepository;
    public $studentClassRepository;
    public $messageUserRepository;
    public function __construct(ClassRepository $classRepository,AccountRepository $accountRepository,MessageRepository $messageRepository,StudentClassRepository $studentClassRepository,MessageUserRepository $messageUserRepository)
    {
      $this->classRepository = $classRepository;
      $this->accountRepository = $accountRepository;
      $this->messageRepository = $messageRepository;
      $this->studentClassRepository = $studentClassRepository;
      $this->messageUserRepository = $messageUserRepository;
    }
    public function index(Request $request){
      $messages = $this->messageRepository->filter($request->all());
      if($request->id == 'desc'){
        $totalItems = $messages->total();
        $index = $totalItems - ($messages->currentPage() - 1) * $messages->perPage();
      }else{
        $index = ($messages->currentPage() - 1) * $messages->perPage() + 1;
      }
      return view('admin.messages.index',compact('messages','index'));
    }

    public function create(){
      $classes = $this->classRepository->all();
      $students = $this->accountRepository->findWhere(['type'=>'student','status'=> 'active']);
      $parents = $this->accountRepository->where('type', 'parent')->where('status','active')->whereNotNull('user_name')->get();
      return view('admin.messages.create',compact('classes','students','parents'));
    }

    public function store(CreateMessageRequest $request, KakaoMessageService $kakaoMessageService){
      $data = $request->all();
      $data['admin_id'] = auth('admin')->user()->id;
      if($request->type =='class'){
        $data['class_id'] = implode(',', $request->classes);
      }else{
        $data['class_id'] = null;
      }
      $users = [];
      unset($data['classes']);
      unset($data['students']);
      unset($data['parents']);
      unset($data['files']);
      $message = $this->messageRepository->create($data);
      if($request->type == 'academy'){
        $users = $this->accountRepository
                  ->select('id as account_id', 'uuid_kakao', 'user_name')
                  ->where('status', 'active')
                  ->get();
      }else if($request->type == 'class'){
        $classIds = $request->input('classes', []);
        $users = $this->studentClassRepository->whereIn('class_id', $classIds)->get()->toArray();
        $this->messageUserRepository->insert(array_map(function($userId) use ($message) {
          return ['message_id' => $message->id, 'account_id' => $userId['student_id'],'is_read' => 0,'class_id'=> $userId['class_id']];
        }, $users));
        return redirect(route('admin.messages.index'))->with('success','Created successfully');
      }else if($request->type == 'student'){
        $this->messageUserRepository->insert(array_map(function($student) use ($message) {
          return ['message_id' => $message->id, 'account_id' => $student,'is_read' => 0];
        }, $request->students));
        return redirect(route('admin.messages.index'))->with('success','Created successfully');
      }else if($request->type == 'parent'){
        $this->messageUserRepository->insert(array_map(function($student) use ($message) {
          return ['message_id' => $message->id, 'account_id' => $student,'is_read' => 0];
        }, $request->parents));
        $usersIsParent = $this->accountRepository
                  ->select('id as account_id', 'uuid_kakao', 'user_name')
                  ->where('status', 'active')
                  ->whereIn('id', $request->parents)
                  ->get();
        $kakaoMessageService->sendKakaoMessagesForUsers($usersIsParent, $message, "공지가 발송되었으니 확인 부탁드립니다 [ {$message->name}]");
        return redirect(route('admin.messages.index'))->with('success','Created successfully');
      }
      $userIds = $users->toArray();

      $this->messageUserRepository->insert(array_map(function($userId) use ($message) {
        return ['message_id' => $message->id, 'account_id' => $userId['account_id'],'is_read' => 0];
      }, $userIds));
      $kakaoMessageService->sendKakaoMessagesForUsers($users, $message, "공지가 발송되었으니 확인 부탁드립니다 [ {$message->name}]");
      return redirect(route('admin.messages.index'))->with('success','Created successfully');
    }

    public function edit($id){
      $message = $this->messageRepository->with('user')->find($id);
      $classes = $this->classRepository->all();
      $students = $this->accountRepository->findWhere(['type'=>'student','status'=> 'active']);
      $parents = $this->accountRepository->where('type', 'parent')->where('status','active')->whereNotNull('user_name')->get();
      return view('admin.messages.edit',compact('message','classes','students','parents'));
    }

    public function update(CreateMessageRequest $request,$id, KakaoMessageService $kakaoMessageService){
        $data = $request->all();
        $data['admin_id'] = auth('admin')->user()->id;
        $users = [];
        unset($data['classes']);
        unset($data['students']);
        unset($data['parents']);
        unset($data['files']);
        if($request->type =='class'){
          $data['class_id'] = implode(',', $request->classes);
        }else{
          $data['class_id'] = null;
        }
        $message = $this->messageRepository->update($data,$id);
        $this->messageUserRepository->deleteWhere(['message_id' => $id]);
        if($request->type == 'academy'){
          $users = $this->accountRepository->select('id as account_id', 'uuid_kakao', 'user_name')->where('status', 'active')->get();
          
        }else if($request->type == 'class'){
          $classIds = $request->input('classes', []);
          $users = $this->studentClassRepository->whereIn('class_id', $classIds)->get()->toArray();
          $this->messageUserRepository->insert(array_map(function($userId) use ($message) {
            return ['message_id' => $message->id, 'account_id' => $userId['student_id'],'is_read' => 0,'class_id'=> $userId['class_id']];
          }, $users));
          return redirect(route('admin.messages.index'))->with('success','Updated successfully');
        }else if($request->type == 'student'){
          $this->messageUserRepository->insert(array_map(function($student) use ($message) {
            return ['message_id' => $message->id, 'account_id' => $student,'is_read' => 0];
          }, $request->students));
          return redirect(route('admin.messages.index'))->with('success','Updated successfully');
        }else if($request->type == 'parent'){
          $this->messageUserRepository->insert(array_map(function($student) use ($message) {
            return ['message_id' => $message->id, 'account_id' => $student,'is_read' => 0];
          }, $request->parents));
            $usersIsParent = $this->accountRepository
                  ->select('id as account_id', 'uuid_kakao', 'user_name')
                  ->where('status', 'active')
                  ->whereIn('id', $request->parents)
                  ->get();
          $kakaoMessageService->sendKakaoMessagesForUsers($usersIsParent, $message, "공지가 발송되었으니 확인 부탁드립니다 [ {$message->name}]");
          return redirect(route('admin.messages.index'))->with('success','Updated successfully');
        }
        $userIds = $users->toArray();
        $this->messageUserRepository->insert(array_map(function($userId) use ($message) {
          return ['message_id' => $message->id, 'account_id' => $userId['account_id'],'is_read' => 0];
        }, $userIds));
        $kakaoMessageService->sendKakaoMessagesForUsers($users, $message, "공지가 발송되었으니 확인 부탁드립니다 [ {$message->name}]");
        return redirect(route('admin.messages.index'))->with('success','Updated successfully');
    }

    public function show($id){
      $message = $this->messageRepository->with('user')->find($id);
      $classes = $this->classRepository->all();
      $students = $this->accountRepository->findWhere(['type'=>'student','status'=> 'active']);
      $parents = $this->accountRepository->where('type', 'parent')->where('status','active')->whereNotNull('user_name')->get();
      return view('admin.messages.show',compact('message','classes','students','parents'));
    }

    public function destroy($id){
      $this->messageRepository->delete($id);
      $this->messageUserRepository->deleteWhere(['message_id' => $id]);
      return redirect(route('admin.messages.index'))->with('success','Delete successfully');
    }
}
