<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\CreateHolidayRequest;
use App\Repositories\HolidayRepository;
use Illuminate\Http\Request;

class HolidayController extends Controller
{
    protected $holidayRepository;
    public function __construct(HolidayRepository $holidayRepository)
    {
      $this->holidayRepository = $holidayRepository;
    }
    public function index(Request $request){
      $holidays = $this->holidayRepository->filter($request->all());
      if($request->id == 'desc'){
        $totalItems = $holidays->total();
        $index = $totalItems - ($holidays->currentPage() - 1) * $holidays->perPage();
      }else{
        $index = ($holidays->currentPage() - 1) * $holidays->perPage() + 1;
      }
      return view('admin.holidays.index',compact('holidays','index'));
    }

    public function create(){
      return view('admin.holidays.create');
    }

    public function store(CreateHolidayRequest $request){
        $this->holidayRepository->create($request->all());
        return redirect()->route('admin.holidays.index')->with('success','Created successfully');
    }

    public function  edit($id){
        $holiday = $this->holidayRepository->find($id);
        return view('admin.holidays.edit',compact('holiday'));
    }

    public function update(CreateHolidayRequest $request,$id){
        $this->holidayRepository->update($request->all(),$id);
        return redirect()->route('admin.holidays.index')->with('success','Updated successfully');
    }

    public function destroy($id){
      $this->holidayRepository->delete($id);
      return redirect()->route('admin.holidays.index')->with('success','Deleted successfully');
    }
}
