<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\CreateAccountRequest;
use App\Http\Requests\CreateUpdateAccountRequest;
use App\Mail\ActiveAccountMail;
use App\Mail\ActiveMailAdmin;
use App\Mail\ChangePasswordMail;
use App\Mail\DeactivateMailAdmin;
use App\Mail\MailForgotPassword;
use App\Mail\RegisterAccount;
use App\Repositories\AccountRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Validator;

class AccountController extends Controller
{
    protected $accountRepository;
    public function __construct(AccountRepository $accountRepository)
    {
        $this->accountRepository = $accountRepository;
    }
    public function index(Request $request){
        $parents  = $this->accountRepository->filterParent($request->all());
        if($request->no == 'desc'){
          $totalItems = $parents->total();
          $index = $totalItems - ($parents->currentPage() - 1) * $parents->perPage();
        }else{
          $index = ($parents->currentPage() - 1) * $parents->perPage() + 1;
        }
        return view('admin.accounts.index',compact('parents','index'));
    }

    public function create(){
      $parents = $this->accountRepository->findWhere(['type'=>'parent']);
      return view('admin.accounts.create',compact('parents'));
    }

    public function store(CreateUpdateAccountRequest $request){
      $parent = $this->accountRepository->find($request->parent);
      $statusOld = $parent->status;
      $passwordOld = $parent->password;
      if($parent->password === null || strlen($parent->password) == 0){
        $request->validate([
          'password_parent' => ['required'],
        ],['password_parent.required' => 'This is a required field.']);
      }
      $dataParent = [
        'status' => $request->status,
        'user_name' => $request->user_name_parent,
      ];
      if($request->password_parent){
        $dataParent['password'] = bcrypt($request->password_parent);
        $dataParent['last_login'] = null;
      }
      if($parent){
        $parent->update($dataParent);
      }
      $check = $this->accountRepository->makeModel()->where('user_id', $parent->user_id)->whereNull('user_name')->first();
      $students = [];
      foreach ($request->all() as $key => $value) {
        if (strpos($key, 'user_name_student_') === 0) {
          $studentId = substr($key, strlen('user_name_student_'));
          $dataStudent = [
            'user_name' => Str::slug($value, ''),
            'status' => $request->status,
          ];
          $students[] = $this->accountRepository->update($dataStudent,$studentId);
        }
      }
      if($request->status == 'active' && $passwordOld == null){
        Mail::to($parent->email)->queue(new RegisterAccount($parent,$students,$request->password_parent));
      }else if($request->status != $statusOld && $passwordOld != null){
        if($request->status == 'active'){
          Mail::to($parent->email)->queue(new ActiveAccountMail($parent,$students,$request->password_parent));
        }else{
          Mail::to($parent->email)->queue(new DeactivateMailAdmin());
        }
      }else if($check){
        Mail::to($parent->email)->queue(new RegisterAccount($parent,$students,$request->password_parent));
      }else if($request->password_parent != null){
        Mail::to($parent->email)->queue(new ChangePasswordMail($parent,$request->password_parent));
      }
      return redirect()->route('admin.accounts.index')->with('success','Created successfully');
    }

    public function update(Request $request,$id){
        $parent = $this->accountRepository->find($request->parent);
        $statusOld = $parent->status;
        $passwordOld = $parent->password;
        if($parent->password === null || strlen($parent->password) == 0){
          $request->validate([
            'password_parent' => ['required'],
          ],['password_parent.required' => 'This is a required field.']);
        }
        $dataParent = [
          'status' => $request->status,
          'user_name' => $request->user_name_parent,
          'uuid_kakao' => $request->uuid_kakao,
        ];
        if($request->password_parent){
          $dataParent['password'] = bcrypt($request->password_parent);
          $dataParent['last_login'] = null;
        }
        if($parent){
          $parent->update($dataParent);
        }
      $check = $this->accountRepository->makeModel()->where('user_id', $parent->user_id)->whereNull('user_name')->first();
      $students = [];
        foreach ($request->all() as $key => $value) {
          if (strpos($key, 'user_name_student_') === 0) {
            $studentId = substr($key, strlen('user_name_student_'));
            $dataStudent = [
              'user_name' => Str::slug($value, ''),
              'status' => $request->status,
            ];
            $students[] = $this->accountRepository->update($dataStudent,$studentId);
          }
        }
        if($request->status == 'active' && $passwordOld == null){
          Mail::to($parent->email)->queue(new RegisterAccount($parent,$students,$request->password_parent));
        }else if($request->status != $statusOld && $passwordOld != null){
          if($request->status == 'active'){
            Mail::to($parent->email)->queue(new ActiveAccountMail($parent,$students,$request->password_parent));
          }else{
            Mail::to($parent->email)->queue(new DeactivateMailAdmin());
          }
        }else if($check){
          Mail::to($parent->email)->queue(new RegisterAccount($parent,$students,$request->password_parent));
        }else if($request->password_parent != null){
          Mail::to($parent->email)->queue(new ChangePasswordMail($parent,$request->password_parent));
        }
        return redirect()->route('admin.accounts.index')->with('success','Updated successfully');
    }
    public function show($id)
    {
      $parent = $this->accountRepository->with('student')->findWhere(['type'=>'Parent','id'=>$id])->first();
      if($parent){
        $generatedNames = [];
        foreach ($parent->student as $student) {
          if (empty($student->account_name)) {
            $birthday = date('md', strtotime($student->birthday));
            $accountName = $student->name_english . $birthday;
            $count = $this->accountRepository->where('user_name', $accountName)->count();
            if (in_array($accountName, $generatedNames) || $count > 0) {
              $suffix = 1;
              while (in_array($accountName . $suffix, $generatedNames) || $this->accountRepository->where('user_name', $accountName . $suffix)->count() > 0) {
                $suffix++;
              }
              $accountName .= $suffix;
            }
            $student->account_name = Str::slug($accountName, '');
            $generatedNames[] = $student->account_name;
          }
        }
        return view('admin.accounts.show',compact('parent'));
      }
      abort('404');
    }

    public function edit($id){
      $parent = $this->accountRepository->with('student')->findWhere(['type'=>'Parent','id'=>$id])->first();
      if($parent){
        $generatedNames = [];
        foreach ($parent->student as $student) {
          if (empty($student->account_name)) {
            $birthday = date('md', strtotime($student->birthday));
            $accountName = $student->name_english . $birthday;
            $count = $this->accountRepository->where('user_name', $accountName)->count();
            if (in_array($accountName, $generatedNames) || $count > 0) {
              $suffix = 1;
              while (in_array($accountName . $suffix, $generatedNames) || $this->accountRepository->where('user_name', $accountName . $suffix)->count() > 0) {
                $suffix++;
              }
              $accountName .= $suffix;
            }
            $student->account_name = Str::slug($accountName, '');
            $generatedNames[] = $student->account_name;
          }
        }
        return view('admin.accounts.edit',compact('parent'));
      }
      abort('404');
    }

    public function destroy($id){
      $this->accountRepository->delete($id);
      return redirect()->route('admin.accounts.index')->with('success','Delete successfully.');
    }
}
