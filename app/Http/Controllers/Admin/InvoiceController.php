<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\UpdateInvoiceRequest;
use App\Jobs\ProcessInvoiceJob;
use App\Models\Account;
use App\Models\Classes;
use App\Models\Invoice;
use App\Models\ProductInvoice;
use App\Repositories\AccountRepository;
use App\Repositories\InvoiceRepository;
use App\Repositories\ProductInvoiceRepository;
use App\Services\InvoiceService;
use Illuminate\Http\Request;
use GuzzleHttp;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class InvoiceController extends Controller
{
    public $invoiceRepository;
    public $productInvoiceRepository;
    protected $accountRepository;
    public function __construct(InvoiceRepository $invoiceRepository, AccountRepository $accountRepository,
                                ProductInvoiceRepository $productInvoiceRepository,
                                private InvoiceService $invoiceService
    ){
      $this->invoiceRepository = $invoiceRepository;
      $this->productInvoiceRepository = $productInvoiceRepository;
      $this->accountRepository = $accountRepository;
    }
    public function index(Request $request){
      $invoices = $this->invoiceRepository->filter($request->all(), 'invoice');
      if($request->id == 'desc'){
        $totalItems = $invoices->total();
        $index = $totalItems - ($invoices->currentPage() - 1) * $invoices->perPage();
      }else{
        $index = ($invoices->currentPage() - 1) * $invoices->perPage() + 1;
      }
      return view('admin.invoice.index',compact('invoices','index'));
    }

     public function refunds(Request $request){
          $invoices = $this->invoiceRepository->filter($request->all(), 'refund');
          if($request->id == 'desc'){
            $totalItems = $invoices->total();
            $index = $totalItems - ($invoices->currentPage() - 1) * $invoices->perPage();
          }else{
            $index = ($invoices->currentPage() - 1) * $invoices->perPage() + 1;
          }
          return view('admin.invoice.refund',compact('invoices','index'));
    }

    public function create()
    {
        $prods = $this->getProdFromWp();
        $parents = $this->accountRepository->findWhere(['type' => 'parent']);
        $classes = Classes::with('student.student.parent')->get();
        $classes = $classes->map(function ($class) {
            $parents = $class->students
                ->pluck('parent')
                ->unique('id')
                ->values();
            $class->parents = $parents;

            return $class;
        });

        return view('admin.invoice.create', compact('parents', 'prods','classes'));
    }

    public function store(UpdateInvoiceRequest $request)
    {
        $directory = storage_path('app/public/invoices');
        if (!is_dir($directory)) {
            mkdir($directory, 0755, true);
        }
        $batchId = uniqid('inv_', true) . '_' . auth('admin')->id();
        DB::beginTransaction();
        try {
            $prods = $this->invoiceService->getProducts($request->prod);
            $prodsById = [];
            $total = 0;
            $data = [];
            $date = now();
            foreach ($prods as $prod) {
                $prodsById[$prod->id] = $prod;
                $total += $prod->price ?? $prod['price'];
            }
            $prods = $prodsById;
            $parents = Account::with('student')->where('type', 'parent')->whereIn('id', $request->parent)->get();
            foreach ($parents as $key => $parent) {
                $data[$key] = [
                    'status' => 'unpaid',
                    'parent_id' => $parent->id,
                    'batch_id' => $batchId,
                    'price' => $total,
                    'created_at' => $date,
                    'updated_at' => $date,
                    'processing_status' => 'Pending',
                ];
            }
            Invoice::insert($data);
            $invoices = Invoice::where('batch_id', $batchId)
                ->with('parent.student')
                ->orderBy('id')
                ->get();
            $invoiceIds = $invoices->pluck('id')->toArray();

            $productInvoiceData = [];
            foreach ($invoiceIds as $invoiceId) {
                foreach ($request->prod as $prodId) {
                    $productInvoiceData[] = [
                        'invoice_id' => $invoiceId,
                        'prod_id' => $prodId,
                        'prod_name' => $prods[$prodId]->name ?? $prods[$prodId]['name'],
                        'prod_des' => $prods[$prodId]->description ?? $prods[$prodId]['description'],
                        'price' => $prods[$prodId]->price ?? $prods[$prodId]['price'],
                        'created_at' => now(),
                        'updated_at' => now()
                    ];
                }
            }
            if (!empty($productInvoiceData)) {
                ProductInvoice::insert($productInvoiceData);
            }

            DB::commit();
            return redirect(route('admin.invoices.index'))->with('success', 'Created successfully');
        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()->withInput()->with('error', 'Error');
        }
    }

    public function show($id){
      $invoice = $this->invoiceRepository->find($id);
      return view('admin.invoice.show',compact('invoice'));
    }

    public function getProdFromWp()
    {
        $client = new GuzzleHttp\Client();
        $all_products = [];
        $page = 1;
        $perPage = 100;
        $prods = [];
        do {
            $response = $client->get(config('app.wphost') . '/wp-json/wc/v3/products', [
                'auth' => [
                    config('app.wpkey'),
                    config('app.wpsecret')
                ],
                'query' => [
                    'per_page' => $perPage,
                    'page' => $page,
                ],
            ]);
            $wpProds = json_decode($response->getBody()->getContents());
            $all_products = array_merge($all_products, $wpProds);
            $page++;
        } while (count($wpProds) > 0);

        foreach ($all_products as $val) {
            $prods[$val->id]['id'] = $val->id;
            $prods[$val->id]['name'] = $val->name;
            $prods[$val->id]['des'] = $val->description;
            $prods[$val->id]['price'] = $val->price;
        }
        return $prods;
    }
    public function syncWp(){
      $client = new GuzzleHttp\Client();
      $response = $client->post(config('app.wphost').'/wp-json/wc/v3/orders/32/refunds', [
        'content-type' => 'application/json',
        'auth' => [
          config('app.wpkey'),
          config('app.wpsecret')
        ]
      ]);
      $body = $response->getBody();
      $result = json_decode($body->getContents());
      dd($result);

      $client = new GuzzleHttp\Client();
      $invoices = $this->invoiceRepository->where(function ($query) {
        $query->where('status', 'request_refund')->orwhere('status', 'unpaid');
      })->get();

      foreach ($invoices as $invoice){
        $response = $client->get(config('app.wphost').'/wp-json/wc/v3/orders/'.$invoice->order_id.'?ver'.time(), [
          'auth' => [
            config('app.wpkey'),
            config('app.wpsecret')
          ],
        ]);
        $body = $response->getBody();

        $wpOrder = json_decode($body->getContents());
        $status = $invoice->status;
        if($wpOrder->status == 'pending' && $status != 'request_refund'){
          $status = 'unpaid';
        }elseif($wpOrder->status == 'completed' && $status != 'request_refund'){
          $status = 'paid';
        }elseif($wpOrder->status == 'refunded'){
          $status = 'refund';
        }

        if($status != $invoice->status){
          $this->invoiceRepository->update(['status'=>$status],$invoice->id);
        }
        Log::info('syncwp - id:'.$invoice->id);
        Log::info('syncwp - status:'.$wpOrder->status);
      }
      return 'done';
    }


    public function getProdByID($id){
      $prods = $this->getProdFromWp();
      $prod = $prods[$id];
      return response()->json([
        'status' => (bool)$prod,
        'prod'=> $prod
      ]);
    }
    public function edit($id){
      $prods = $this->getProdFromWp();
      $parents = $this->accountRepository->findWhere(['type'=> 'parent']);
      $invoice = $this->invoiceRepository->find($id);
      $prodInvoice = array();
      foreach ($invoice->product as $key => $val){
        $prodInvoice[$val->id]['des'] = $val['prod_des'];
        $prodInvoice[$val->id]['price'] = $val['price'];
        $prodInvoice[$val->id]['name'] = $val['prod_name'];
      }
      return view('admin.invoice.edit',compact('invoice', 'parents', 'prods', 'prodInvoice'));
    }

    public function update(Request $request,$id){
      DB::beginTransaction();
      try {
        $data['status'] =$request->status;
        $this->invoiceRepository->update($data,$id);
        $mess = $request->status == 'refund' ? "Refund successfully" : "Cancel refund successfully";
        DB::commit();
        return redirect(route('admin.invoices.refunds'))->with('success',$mess);
      }
      catch(\Exception $e) {
        DB::rollback();
        return redirect()->back()->withInput()->with('error','Error');
      }
    }

    public function destroy($id){
      $invoice  = $this->invoiceRepository->find($id);
      if($invoice->status == 'unpaid'){
        $invoice->delete();
        $invoice->product()->delete();
        $invoice->students()->delete();
        return redirect(route('admin.invoices.index'))->with('success','Deleted successfully');
      }
      abort('404');
    }

    public function invoicePdf(Request $request, $id)
    {
        $requestData = $request->all();
        $parent = Account::with([
            'student' => function ($query) use ($requestData) {
                $query->when(!empty($requestData['student_id']), function ($query) use ($requestData) {
                    $query->where('id', $requestData['student_id']);
                });
            }
        ])
        ->where('type', 'parent')
        ->find($id);
        if($request->ids == null) abort('404');
        $ids = explode(',', $request->ids);
        $options = $this->invoiceService->getOptions();
        $pdf = $this->invoiceService->getInvoicePdf($parent, $ids,$options);
        return $pdf->stream('invoice.pdf');
    }


    public function detail(Request $request, $id)
    {
        $parent = Account::with('student')->where('type', 'parent')->find($id);
        if($request->ids == null) abort('404');
        $ids = explode(',', $request->ids);
        $products = $this->invoiceService->getProducts($ids);
        return view('admin.invoice.detail',compact('parent', 'products'));
    }

    public function batch() {
        $products = $this->getProdFromWp();
        $students = Account::with([
            'parent',
            'classes.products',
            'invoiceStudent.invoice' => function($query) {
                $query->where('processing_status', 'Completed');
            },
            'invoiceStudent.invoice.product'
        ])->where('type', 'student')->get();
        foreach ($students as $student) {
            $classProductIds = $student->classes
                ->flatMap(fn($class) => $class?->products?->pluck('product_id'))
                ->unique()
                ->values();

            $paidProductIds = $student->invoiceStudent
                ->flatMap(fn($invoice) => $invoice?->invoice?->product->pluck('prod_id'))
                ->unique()
                ->values();
            $student->name = $student->name_english;
            $student->parent_name = $student->parent?->name_english.' - '.$student?->parent?->phone;
            $student->parent_id = $student->parent?->id;
            $student->products = $classProductIds->diff($paidProductIds)->values();
        }

        return view('admin.grades.grades',compact('products','students'));
    }

}
