<?php

namespace App\Http\Controllers\Admin\V2;

use App\Http\Controllers\Controller;
use App\Models\AccountHomework;
use Illuminate\Http\Request;

class GradeController extends Controller
{
  public function __construct()
  {
  }

  public function index(Request $request)
  {
    return view('admin.grades.grades');
  }

  public function create()
  {
    return view('admin.grades.grades');
  }

  public function show($id)
  {
      $homework = AccountHomework::
          when(auth('admin')->user()->role == 'teacher', function ($query) {
              $query->whereHas('mainHomework.getClass.admins', function ($q) {
                  $q->where('admin_id', auth('admin')->user()->id);
              });
          })->find($id);
      if (!$homework) {
          abort(404, 'Homework not found.');
      }
    return view('admin.grades.grades');
  }
}
