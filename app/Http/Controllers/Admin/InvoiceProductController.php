<?php

namespace App\Http\Controllers\Admin;

use App\Enums\Session;
use App\Http\Controllers\Controller;
use App\Models\Classes;
use App\Models\ClassSchedule;
use App\Models\ProductClass;
use App\Repositories\ClassScheduleRepository;
use Illuminate\Http\Request;
use GuzzleHttp;
use Illuminate\Pagination\LengthAwarePaginator;

class InvoiceProductController extends Controller
{

    public function __construct(private ClassScheduleRepository $classScheduleRepository) {
    }
    public function index(Request $request)
    {
        $client = new GuzzleHttp\Client();
        $page = $request->input('page', 1);
        $perPage = 10;

        $response = $client->get(config('app.wphost').'/wp-json/wc/v3/products', [
            'auth' => [
                config('app.wpkey'),
                config('app.wpsecret')
            ],
            'query' => [
                'per_page' => $perPage,
                'page' => $page,
                'search'   => $request->input('keyw'),
            ],
        ]);

        $totalItems = $response->getHeaderLine('X-WP-Total');

        $products = json_decode($response->getBody()->getContents(), true);
        if(count($products)){
            $productIds = array_column($products, 'id');
            $productClasses = ProductClass::with('getClass')
                ->whereIn('product_id', $productIds)
                ->whereHas('getClass', function ($query) {})
                ->get()
                ->mapWithKeys(fn ($pc) => [
                    $pc->product_id => $pc
                ])
                ->toArray();
        }
        $paginator = new LengthAwarePaginator(
            $products,
            $totalItems,
            $perPage,
            $page,
            [
                'path' => request()->url(),
                'query' => request()->query(),
            ]
        );

        $sessionsList = Session::list();

        return view('admin.invoice-products.index', [
            'products' => $paginator,
            'classes' => $productClasses ?? [],
            'sessions' => $sessionsList,
        ]);
    }

    public function create()
    {
        $type = 'Create';
        $route = route('admin.invoice-products.store');
        $method = 'POST';
        $product = [
            'name'  => '',
            'price' => '',
            'description' => '',
            'class_id' => '',
            'session' => '',
        ];
        $classes = Classes::all();
        $sessions = Session::list();
        return view('admin.invoice-products.form', compact(
            'type',
            'product',
            'method',
            'route',
            'classes',
            'sessions'
        ));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name'  => 'required|string|max:255',
            'price' => 'required|numeric|min:1',
            'description' => 'nullable|string',
            'class_id' => 'nullable',
            'session' => 'nullable',
        ]);
        $session = $validated['session'] ?? null;
        $startDate = null;
        $endDate = null;

        if(!empty($validated['class_id']) && $session){
            $dataDate = $this->dateSession($validated);
            $startDate = $dataDate['start_date'];
            $endDate = $dataDate['end_date'];
        }
        $client = new GuzzleHttp\Client();
        $response = $client->post(config('app.wphost').'/wp-json/wc/v3/products', [
            'auth' => [
                config('app.wpkey'),
                config('app.wpsecret')
            ],
            'json' => [
                'name'  => $validated['name'],
                'regular_price' => (string) $validated['price'],
                'description' => $validated['description'],
            ],
        ]);
        if($request->class_id){
            ProductClass::create([
                'product_id' => json_decode($response->getBody()->getContents())->id,
                'class_id' => $request->class_id,
                'session' => $session,
                'start_date' => $startDate,
                'end_date' => $endDate,
            ]);
        }
        return redirect()->route('admin.invoice-products.index')->with('success', 'Create successfully!');
    }

    public function edit($id)
    {
        $client = new GuzzleHttp\Client();

        $response = $client->get(config('app.wphost').'/wp-json/wc/v3/products/'.$id, [
            'auth' => [
                config('app.wpkey'),
                config('app.wpsecret')
            ]
        ]);
        $classes = Classes::all();
        $sessions = Session::list();

        $product = json_decode($response->getBody()->getContents(), true);
        $productClass = ProductClass::where('product_id', $id)->first();
        $product['class_id'] = !empty($productClass) ? $productClass->class_id : null;
        $product['session'] = !empty($productClass) ? $productClass->session : null;
        $type = 'Edit';
        $route = route('admin.invoice-products.update', $id);
        $method = 'PUT';
        return view('admin.invoice-products.form', compact(
            'product',
            'type',
            'method',
            'route',
            'classes',
            'sessions'
        ));
    }

    public function update(Request $request, $id)
    {
        $validated = $request->validate([
            'name'  => 'required|string|max:255',
            'price' => 'required|numeric|min:0',
            'description' => 'nullable|string',
            'class_id' => 'nullable',
            'session' => 'nullable',
        ]);
        $startDate = null;
        $endDate = null;
        if($request->class_id && !empty($validated['session'])){
            $dataDate = $this->dateSession($validated);
            $startDate = $dataDate['start_date'];
            $endDate = $dataDate['end_date'];
        }

        $client = new GuzzleHttp\Client();

        $client->put(config('app.wphost').'/wp-json/wc/v3/products/'.$id, [
            'auth' => [
                config('app.wpkey'),
                config('app.wpsecret')
            ],
            'json' => [
                'name'  => $validated['name'],
                'regular_price' => (string) $validated['price'],
                'description' => $validated['description'],
            ],
        ]);
        if($request->class_id){
            ProductClass::updateOrCreate(
                ['product_id' => $id],
                [
                    'class_id' => $request->class_id,
                    'session' => $validated['session'] ?? null,
                    'start_date' => $startDate,
                    'end_date' => $endDate,
                ]
            );
        }else{
            ProductClass::where('product_id', $id)->delete();
        }

        return redirect()->route('admin.invoice-products.index')->with('success', 'Update successfully!');
    }

    public function dateSession($data)
    {
        $startDate = null;
        $endDate = null;
        $schedules = ClassSchedule::where('class_id', $data['class_id'])->get();
        $schedules = $this->classScheduleRepository->getSesion($schedules);
        foreach ($schedules as $schedule) {
            if ($schedule->session == $data['session'] && !$startDate) {
                $startDate = $schedule->date;
                continue;
            }
            if ($schedule->session == $data['session'] + 3) {
                $endDate = $schedule->date;
            }
        }
        return [
            'start_date' => $startDate,
            'end_date' => $endDate,
        ];
    }
}
