<?php

namespace App\Http\Controllers\Admin;

use App\Enums\Session;
use App\Http\Controllers\Controller;
use App\Models\Classes;
use App\Models\ProductClass;
use Illuminate\Http\Request;
use GuzzleHttp;
use Illuminate\Pagination\LengthAwarePaginator;

class InvoiceProductController extends Controller
{
    public function index(Request $request)
    {
        $classes = ProductClass::with('getClass')
            ->get()
            ->mapWithKeys(fn ($pc) => [
                $pc->product_id => $pc->getClass
            ])
            ->toArray();

        $client = new GuzzleHttp\Client();
        $page = $request->input('page', 1);
        $perPage = 10;

        $response = $client->get(config('app.wphost').'/wp-json/wc/v3/products', [
            'auth' => [
                config('app.wpkey'),
                config('app.wpsecret')
            ],
            'query' => [
                'per_page' => $perPage,
                'page' => $page,
                'search'   => $request->input('keyw'),
            ],
        ]);

        $totalItems = $response->getHeaderLine('X-WP-Total');

        $products = json_decode($response->getBody()->getContents(), true);
        $paginator = new LengthAwarePaginator(
            $products,
            $totalItems,
            $perPage,
            $page,
            [
                'path' => request()->url(),
                'query' => request()->query(),
            ]
        );

        return view('admin.invoice-products.index', [
            'products' => $paginator,
            'classes' => $classes,
        ]);
    }

    public function create()
    {
        $type = 'Create';
        $route = route('admin.invoice-products.store');
        $method = 'POST';
        $product = [
            'name'  => '',
            'price' => '',
            'description' => '',
            'class_id' => '',
            'session' => '',
        ];
        $classes = Classes::all();
        $sessions = Session::list();
        return view('admin.invoice-products.form', compact(
            'type',
            'product',
            'method',
            'route',
            'classes',
            'sessions'
        ));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name'  => 'required|string|max:255',
            'price' => 'required|numeric|min:1',
            'description' => 'nullable|string',
            'class_id' => 'nullable',
            'session' => 'nullable',
        ]);
        if(!empty($validated['class_id']) && !empty($validated['session'])){
            $class = Classes::find($validated['class_id']);
            dd($class,$validated['session']);
        }
        $client = new GuzzleHttp\Client();
        $response = $client->post(config('app.wphost').'/wp-json/wc/v3/products', [
            'auth' => [
                config('app.wpkey'),
                config('app.wpsecret')
            ],
            'json' => [
                'name'  => $validated['name'],
                'regular_price' => (string) $validated['price'],
                'description' => $validated['description'],
            ],
        ]);
        if($request->class_id){
            ProductClass::create([
                'product_id' => json_decode($response->getBody()->getContents())->id,
                'class_id' => $request->class_id
            ]);
        }
        return redirect()->route('admin.invoice-products.index')->with('success', 'Create successfully!');
    }

    public function edit($id)
    {
        $client = new GuzzleHttp\Client();

        $response = $client->get(config('app.wphost').'/wp-json/wc/v3/products/'.$id, [
            'auth' => [
                config('app.wpkey'),
                config('app.wpsecret')
            ]
        ]);
        $classes = Classes::all();

        $product = json_decode($response->getBody()->getContents(), true);
        $productClass = ProductClass::where('product_id', $id)->first();
        $product['class_id'] = !empty($productClass) ? $productClass->class_id : null;
        $type = 'Edit';
        $route = route('admin.invoice-products.update', $id);
        $method = 'PUT';
        return view('admin.invoice-products.form', compact(
            'product',
            'type',
            'method',
            'route',
            'classes'
        ));
    }

    public function update(Request $request, $id)
    {
        $validated = $request->validate([
            'name'  => 'required|string|max:255',
            'price' => 'required|numeric|min:0',
            'description' => 'nullable|string',
            'class_id' => 'nullable',
        ]);

        $client = new GuzzleHttp\Client();

        $client->put(config('app.wphost').'/wp-json/wc/v3/products/'.$id, [
            'auth' => [
                config('app.wpkey'),
                config('app.wpsecret')
            ],
            'json' => [
                'name'  => $validated['name'],
                'regular_price' => (string) $validated['price'],
                'description' => $validated['description'],
            ],
        ]);
        if($request->class_id){
            ProductClass::updateOrCreate(
                ['product_id' => $id],
                ['class_id' => $request->class_id]
            );
        }else{
            ProductClass::where('product_id', $id)->delete();
        }

        return redirect()->route('admin.invoice-products.index')->with('success', 'Update successfully!');
    }
}
