<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Repositories\AccountRepository;
use App\Repositories\ClassRepository;
use App\Repositories\ClassScheduleRepository;
use App\Repositories\GradeRepository;
use App\Repositories\GradeWritingRepository;
use App\Repositories\StudentClassRepository;
use Illuminate\Http\Request;

class GradeController extends Controller
{
    public $classRepository;
    public $accountRepository;
    public $studentClassRepository;
    public $classScheduleRepository;
    public $gradeRepository;
    public $gradeWritingRepository;
    public function __construct(ClassRepository $classRepository, AccountRepository $accountRepository,StudentClassRepository $studentClassRepository,ClassScheduleRepository $classScheduleRepository,GradeRepository $gradeRepository,GradeWritingRepository $gradeWritingRepository)
    {
      $this->classRepository = $classRepository;
      $this->accountRepository = $accountRepository;
      $this->studentClassRepository = $studentClassRepository;
      $this->classScheduleRepository = $classScheduleRepository;
      $this->gradeRepository = $gradeRepository;
      $this->gradeWritingRepository = $gradeWritingRepository;
    }
    public function index(Request $request){
      $conditions = [
        'type' => 'Regular',
      ];
      if (auth('admin')->user()->role == 'teacher') {
        $classes = $this->classRepository->whereHas('admins',function ($q){
          $q->where('admin_id',auth('admin')->user()->id);
        })->findWhere($conditions);
      }else{
        $classes = $this->classRepository->findWhere($conditions);
      }
      $students = [];
      $classChoose = [];
      $lastSession = 0;
      $data = [];
      $classIds = $classes->pluck('id');
      if ($request->class && $request->session > 0 && $request->session < 16 && $classIds->contains($request->class)) {
        $students = $this->studentClassRepository->whereHas('classes',function (){})->whereHas('student',function (){})->with(['student'])->findWhere(['class_id'=> $request->class]);
        if($request->student){
          $schedules = $this->classScheduleRepository
            ->with('classes')
            ->whereHas('classes',function (){})
            ->where('class_id', $request->class)->orderBy('date')->get();
          $schedules = $this->classScheduleRepository->getSesion($schedules);
          foreach ($schedules as $schedule) {
            if (date('Y-m-d') >= $schedule->date) {
              $lastSession = $schedule->session;
            }
          }
          $classChoose = $this->classRepository->find($request->class);
          $lastSession = request()->session ? request()->session : $lastSession;
          if($classChoose->subject == 'Writing'){
            $data = $this->gradeWritingRepository->whereHas('classes',function (){})->findWhere(['class_id'=> $classChoose->id,'student_id'=> $request->student,'session'=> $lastSession]);
          }else{
            $data = $this->gradeRepository->whereHas('classes',function (){})->findWhere(['class_id'=> $classChoose->id,'student_id'=> $request->student,'session'=> $lastSession]);
          }
        }
      }

      return view('admin.grades.index',compact('classes','students','lastSession','classChoose','data'));
    }

    public function show(Request $request, $id){
      $student = $request->student_id;
      $session = $request->session;
      $title = 'Show';
      $type = $request->type;
      $conditions = [
        'type' => 'Regular',
      ];
      if(auth('admin')->user()->role == 'teacher'){
        $class = $this->classRepository->whereHas('admins',function ($q){
          $q->where('admin_id',auth('admin')->user()->id);
        })->findWhere($conditions)->find($id);
      }else{
        $class = $this->classRepository->findWhere($conditions)->find($id);
      }
      if(!$class) abort(404);
      if($class->subject == 'Writing' && $request->session > 0 && $request->session <16 && in_array($request->type,['HW1','HW2','HW3'])){
        if($class->level == 'QW1'){
          $pointStudent = $this->gradeWritingRepository->findWhere(['class_id'=> $class->id,'student_id'=> $student,'session'=> $request->session,'type' => $request->type])->first();
          return view('admin.grades.templates.QW1-8',compact('id','session','student','pointStudent','title','type'));
        }else if(in_array($class->level,['QW2','QW3','QW4'])){
          if($class->level == 'QW2' && $request->session >0 && $request->session <=8){
            $pointStudent = $this->gradeWritingRepository->findWhere(['class_id'=> $class->id,'student_id'=> $student,'session'=> $request->session,'type' => $request->type])->first();
            return view('admin.grades.templates.QW1-8',compact('id','session','student','pointStudent','title','type'));
          }else{
            $level = $class->level;
            $pointStudent = $this->gradeWritingRepository->findWhere(['class_id'=> $class->id,'student_id'=> $student,'session'=> $request->session,'type' => $request->type])->first();
            return view('admin.grades.templates.QW',compact('id','session','student','pointStudent','title','type','level'));
          }
        }
      }else if($class && $request->session > 0 && $request->session <16 && in_array($request->type,['HW1','HW2','HW3'])){
        $name = $class->level;
        if(in_array($name,['QR2','QR3','QR4'])){
          $name = 'QR2';
        }
        $fileName= "admin.grades.templates." . $name;
        $pointStudent = $this->gradeRepository->findWhere(['class_id'=> $class->id,'student_id'=> $student,'session'=> $request->session,'type' => $request->type])->first();
        return view($fileName,compact('id','session','student','pointStudent','title', 'type'));
      }
      abort(404);
    }

    public function edit(Request $request,$id){
      $student = $request->student_id;
      $session = $request->session;
      $title = 'Edit';
      $type = $request->type;
      $conditions = [
        'type' => 'Regular',
      ];
      if(auth('admin')->user()->role == 'teacher'){
        $class = $this->classRepository->whereHas('admins',function ($q){
          $q->where('admin_id',auth('admin')->user()->id);
        })->findWhere($conditions)->find($id);
      }else{
        $class = $this->classRepository->findWhere($conditions)->find($id);
      }
      if(!$class) abort(404);
      if($class->subject == 'Writing' && $request->session > 0 && $request->session <16 && in_array($request->type,['HW1','HW2','HW3'])){
          if($class->level == 'QW1'){
            $pointStudent = $this->gradeWritingRepository->findWhere(['class_id'=> $class->id,'student_id'=> $student,'session'=> $request->session,'type' => $request->type])->first();
            return view('admin.grades.templates.QW1-8',compact('id','session','student','pointStudent','title','type'));
          }else if(in_array($class->level,['QW2','QW3','QW4'])){
            if($class->level == 'QW2' && $request->session >0 && $request->session <=8){
              $level = $class->level;
              $pointStudent = $this->gradeWritingRepository->findWhere(['class_id'=> $class->id,'student_id'=> $student,'session'=> $request->session,'type' => $request->type])->first();
              return view('admin.grades.templates.QW1-8',compact('id','session','student','pointStudent','title','type'));
            }else{
              $level = $class->level;
              $pointStudent = $this->gradeWritingRepository->findWhere(['class_id'=> $class->id,'student_id'=> $student,'session'=> $request->session,'type' => $request->type])->first();
              return view('admin.grades.templates.QW',compact('id','session','student','pointStudent','title','type','level'));
            }
          }
      } else if($class && $request->session > 0 && $request->session <16 && in_array($request->type,['HW1','HW2','HW3'])){
        $name = $class->level;
        if(in_array($name,['QR2','QR3','QR4'])){
          $name = 'QR2';
        }
        $fileName= "admin.grades.templates." . $name;
        if($class->level == 'QR5' && $request->type != 'HW1'){
          abort('404');
        }
        $pointStudent = $this->gradeRepository->findWhere(['class_id'=> $class->id,'student_id'=> $student,'session'=> $request->session,'type' => $request->type])->first();
        return view($fileName,compact('id','session','student','pointStudent','title', 'type'));
      }
      abort(404);
    }

    public function update(Request $request,$id){
      $class = $this->classRepository->where('type','Regular')->find($id);
      if($class && $class->subject == 'Reading'){
        if(in_array($class->level,['QR2','QR3','QR4'])){
           $this->validateQR234($request);
        }else if($class->level == 'QR1'){
          $this->validateQR1($request);
        }else if($class->level == 'QR5'){
          $this->validateQR5($request);
        }
        $student = $this->studentClassRepository->findWhere(['class_id' => $class->id,'student_id'=> $request->student])->first();
        if($student){
          $check = $this->gradeRepository->findWhere(['class_id'=> $class->id,'student_id'=> $request->student,'session'=> $request->session,'type' => $request->type])->first();
          $infoStudent['class_id'] = $class->id;
          $infoStudent['student_id'] = $request->student;
          $infoStudent['session'] = $request->session;
          $infoStudent['type'] = $request->type;

          if(in_array($class->level,['QR2','QR3','QR4'])){
            $data = $this->prepareDataQR234($request);
            $mergedData = array_merge($infoStudent, $data);
            if($check){
              $this->gradeRepository->update($mergedData,$check->id);
            }else{
              $this->gradeRepository->create($mergedData);
            }
          }else if($class->level == 'QR1'){
            $data = $this->prepareDataQR1($request);
            $mergedData = array_merge($infoStudent, $data);
            if($check){
              $this->gradeRepository->update($mergedData,$check->id);
            }else{
              $this->gradeRepository->create($mergedData);
            }
          }else if($class->level == 'QR5'){
            $data = $this->prepareDataQR5($request);
            $mergedData = array_merge($infoStudent, $data);
            if($check){
              $this->gradeRepository->update($mergedData,$check->id);
            }else{
              $this->gradeRepository->create($mergedData);
            }
          }
          if(isset($mergedData)){
            $this->gradeRepository->resetModel();
            $hw1 = $this->gradeRepository->findWhere(['class_id'=>$mergedData['class_id'],'session'=>$mergedData['session'],'student_id'=>$mergedData['student_id'],'type'=>'HW1'])->first();
            if($hw1){
              $hw1->update([
                'score' => $mergedData['score'],
                'score1' => $mergedData['score1'],
                'score2' => $mergedData['score2'],
                'attendance' => $mergedData['attendance'],
                'participation' => $mergedData['participation'],
                'description' => trimTextArea($mergedData['description'],500),
              ]);
            }else{
              $this->gradeRepository->create([
                'type'=>'HW1',
                'class_id' =>$mergedData['class_id'],
                'session' =>$mergedData['session'],
                'student_id'=> $mergedData['student_id'],
                'score' => $mergedData['score'],
                'score1' => $mergedData['score1'],
                'score2' => $mergedData['score2'],
                'attendance' => $mergedData['attendance'],
                'participation' => $mergedData['participation'],
                'description' => trimTextArea($mergedData['description'],500),
              ]);
            }
            if($class->level != 'QR5'){
              $this->gradeRepository->resetModel();
              $hw2 = $this->gradeRepository->findWhere(
                ['class_id'=>$mergedData['class_id'],'session'=>$mergedData['session'],'student_id'=>$mergedData['student_id'],'type'=>'HW2'])->first();
              if($hw2){
                $hw2->update([
                  'score' => $mergedData['score'],
                  'score1' => $mergedData['score1'],
                  'score2' => $mergedData['score2'],
                  'attendance' => $mergedData['attendance'],
                  'participation' => $mergedData['participation'],
                  'description' => trimTextArea($mergedData['description'],500),
                ]);
              }else{
                $this->gradeRepository->create([
                  'type'=>'HW2',
                  'class_id' =>$mergedData['class_id'],
                  'session' =>$mergedData['session'],
                  'student_id'=> $mergedData['student_id'],
                  'score' => $mergedData['score'],
                  'score1' => $mergedData['score1'],
                  'score2' => $mergedData['score2'],
                  'attendance' => $mergedData['attendance'],
                  'participation' => $mergedData['participation'],
                  'description' => trimTextArea($mergedData['description'],500),
                ]);
              }
              $this->gradeRepository->resetModel();
              $hw3 = $this->gradeRepository->findWhere(['class_id'=>$mergedData['class_id'],'session'=>$mergedData['session'],'student_id'=>$mergedData['student_id'],'type'=>'HW3'])->first();
              if($hw3){
                $hw3->update([
                  'score' => $mergedData['score'],
                  'score1' => $mergedData['score1'],
                  'score2' => $mergedData['score2'],
                  'attendance' => $mergedData['attendance'],
                  'participation' => $mergedData['participation'],
                  'description' => trimTextArea($mergedData['description'],500),
                ]);
              }else{
                $this->gradeRepository->create([
                  'type'=>'HW3',
                  'class_id' =>$mergedData['class_id'],
                  'session' =>$mergedData['session'],
                  'student_id'=> $mergedData['student_id'],
                  'score' => $mergedData['score'],
                  'score1' => $mergedData['score1'],
                  'score2' => $mergedData['score2'],
                  'attendance' => $mergedData['attendance'],
                  'participation' => $mergedData['participation'],
                  'description' => trimTextArea($mergedData['description'],500),
                ]);
              }
            }
          }

          return redirect(route('admin.grades.index')."?class=$id&student=$request->student&session=$request->session")->with('success', 'Updated successfully');
        }
      }else if($class && $class->subject == 'Writing'){
        if($class->level == 'QW1' && $request->session >0 && $request->session <16){
          $this->validateQW18($request);
        }else if(in_array($class->level,['QW2','QW3','QW4'])){
          if($class->level == 'QW2' && $request->session >0 && $request->session <=8){
            $this->validateQW18($request);
          }else{
            $this->validateALL($request,$class);
          }
        }
        $student = $this->studentClassRepository->findWhere(['class_id' => $class->id,'student_id'=> $request->student])->first();
        $infoStudent['class_id'] = $class->id;
        $infoStudent['student_id'] = $request->student;
        $infoStudent['session'] = $request->session;
        $infoStudent['type'] = $request->type;
        if ($student) {
          if($class->level == 'QW1' && $request->session >0 && $request->session <16){
            $data = $this->prepareDataQw18($request);
            $mergedData = array_merge($infoStudent, $data);
            $check = $this->gradeWritingRepository->findWhere(['class_id' => $class->id, 'student_id' => $request->student, 'session' => $request->session, 'type' => $request->type])->first();
            if ($check) {
              $this->gradeWritingRepository->update($mergedData, $check->id);
            } else {
              $this->gradeWritingRepository->create($mergedData);
            }
          }else if(in_array($class->level,['QW2','QW3','QW4'])){
            if($class->level == 'QW2' && $request->session >0 && $request->session <=8){
              $data = $this->prepareDataQw18($request);
              $mergedData = array_merge($infoStudent, $data);
              $check = $this->gradeWritingRepository->findWhere(['class_id' => $class->id, 'student_id' => $request->student, 'session' => $request->session, 'type' => $request->type])->first();
              if ($check) {
                $this->gradeWritingRepository->update($mergedData, $check->id);
              } else {
                $this->gradeWritingRepository->create($mergedData);
              }
            }else{
              $data = $this->prepareDataAll($request);
              $mergedData = array_merge($infoStudent, $data);
              $check = $this->gradeWritingRepository->findWhere(['class_id' => $class->id, 'student_id' => $request->student, 'session' => $request->session, 'type' => $request->type])->first();
              if ($check) {
                $this->gradeWritingRepository->update($mergedData, $check->id);
              } else {
                $this->gradeWritingRepository->create($mergedData);
              }
            }
          }
          if(isset($mergedData)){
            $this->gradeWritingRepository->resetModel();
            $hw1 = $this->gradeWritingRepository->findWhere(['class_id'=>$mergedData['class_id'],'session'=>$mergedData['session'],'student_id'=>$mergedData['student_id'],'type'=>'HW1'])->first();
            if($hw1){
              $hw1->update([
                'attendance' => $mergedData['attendance'],
                'participation' => $mergedData['participation'],
                'description' => trimTextArea($mergedData['description'],500),
              ]);
            }else{
              $this->gradeWritingRepository->create([
                'type' => 'HW1',
                'class_id' =>$mergedData['class_id'],
                'session' =>$mergedData['session'],
                'student_id'=> $mergedData['student_id'],
                'attendance' => $mergedData['attendance'],
                'participation' => $mergedData['participation'],
                'description' => trimTextArea($mergedData['description'],500),
              ]);
            }
            $this->gradeWritingRepository->resetModel();
            $hw2 = $this->gradeWritingRepository->findWhere(['class_id'=>$mergedData['class_id'],'session'=>$mergedData['session'],'student_id'=>$mergedData['student_id'],'type'=>'HW2'])->first();
            if($hw2){
              $hw2->update([
                'attendance' => $mergedData['attendance'],
                'participation' => $mergedData['participation'],
                'description' => trimTextArea($mergedData['description'],500),
              ]);
            }else{
              $this->gradeWritingRepository->create([
                'type' => 'HW2',
                'class_id' =>$mergedData['class_id'],
                'session' =>$mergedData['session'],
                'student_id'=> $mergedData['student_id'],
                'attendance' => $mergedData['attendance'],
                'participation' => $mergedData['participation'],
                'description' => trimTextArea($mergedData['description'],500),
              ]);
            }
            $this->gradeWritingRepository->resetModel();
            $hw3 = $this->gradeWritingRepository->findWhere(['class_id'=>$mergedData['class_id'],'session'=>$mergedData['session'],'student_id'=>$mergedData['student_id'],'type'=>'HW3'])->first();
            if($hw3){
              $hw3->update([
                'attendance' => $mergedData['attendance'],
                'participation' => $mergedData['participation'],
                'description' => trimTextArea($mergedData['description'],500),
              ]);
            }else{
              $this->gradeWritingRepository->create([
                'type' => 'HW3',
                'class_id' =>$mergedData['class_id'],
                'session' =>$mergedData['session'],
                'student_id'=> $mergedData['student_id'],
                'attendance' => $mergedData['attendance'],
                'participation' => $mergedData['participation'],
                'description' => trimTextArea($mergedData['description'],500),
              ]);
            }
          }
          return redirect(route('admin.grades.index')."?class=$id&student=$request->student&session=$request->session")->with('success', 'Updated successfully');
        }
      }

    }

    public function prepareDataQR234(Request $request): array
    {
       $data = [];
       $data['score'] = $request->score;
       $data['score1'] = $request->input1;
       $data['score2'] = $request->input2;
       $data['attendance'] = $request->attendance;
       $data['participation'] = $request->participation;
       $data['vocab'] = $request->vocab;
       $data['vocab1'] = $request->vocab1;
       $data['vocab2'] = $request->vocab2;
       $data['concept'] = $request->concept;
       $data['concept1'] = $request->concept1;
       $data['concept2'] = $request->concept2;
       $data['skill'] = $request->skill;
       $data['skill1'] = $request->skill1;
       $data['skill2'] = $request->skill2;
       $data['status'] = $request->status;
       $data['description'] = trimTextArea($request->description,500);
       return $data;
    }

    public function prepareDataQR1(Request $request){
      $data = [];
      $data['score'] = $request->score;
      $data['score1'] = $request->input1;
      $data['score2'] = $request->input2;
      $data['attendance'] = $request->attendance;
      $data['participation'] = $request->participation;
      $data['sentence'] = $request->sentence;
      $data['sentence1'] = $request->sentence1;
      $data['sentence2'] = $request->sentence2;
      $data['vocabulary_maxtrix_1'] = $request->vocabulary_maxtrix_1;
      $data['vocabulary_maxtrix_11'] = $request->vocabulary_maxtrix_11;
      $data['vocabulary_maxtrix_12'] = $request->vocabulary_maxtrix_12;
      $data['vocabulary_maxtrix_2'] = $request->vocabulary_maxtrix_2;
      $data['vocabulary_maxtrix_21'] = $request->vocabulary_maxtrix_21;
      $data['vocabulary_maxtrix_22'] = $request->vocabulary_maxtrix_22;
      $data['skill'] = $request->skill;
      $data['skill1'] = $request->skill1;
      $data['skill2'] = $request->skill2;
      $data['status'] = $request->status;
      $data['description'] = trimTextArea($request->description,500);
      return $data;
    }

    public function prepareDataQw18(Request $request){
      $data = [];
      $data['skill'] = $request->skill;
      $data['skill1'] = $request->skill1;
      $data['skill2'] = $request->skill2;
      $data['attendance'] = $request->attendance;
      $data['participation'] = $request->participation;
      $data['transcription'] = $request->transcription;
      $data['prewriting'] = $request->prewriting;
      $data['writing'] = $request->writing;
      $data['status'] = $request->status;
      $data['description'] = trimTextArea($request->description,500);
      return $data;
    }
    public function prepareDataAll(Request $request){
      $data = [];
      $data['skill'] = $request->skill;
      $data['skill1'] = $request->skill1;
      $data['skill2'] = $request->skill2;
      $data['attendance'] = $request->attendance;
      $data['participation'] = $request->participation;
      $data['transcription'] = $request->transcription;
      $data['prewriting'] = $request->prewriting;
      $data['completed_essay'] = $request->completed_essay;
      $data['revision'] = $request->revision;
      $data['writing'] = $request->writing;
      $data['status'] = $request->status;
      $data['description'] = trimTextArea($request->description,500);
      return $data;
    }

    public function prepareDataQR5(Request $request){
      $data = [];
      $data['score'] = $request->score;
      $data['score1'] = $request->input1;
      $data['score2'] = $request->input2;
      $data['attendance'] = $request->attendance;
      $data['participation'] = $request->participation;
      $data['vocab_hw'] = $request->vocab_hw;
      $data['vocab_hw1'] = $request->vocab_hw1;
      $data['vocab_hw2'] = $request->vocab_hw2;
      $data['short_response'] = $request->short_response;
      $data['status'] = $request->status;
      $data['description'] = trimTextArea($request->description,500);
      return $data;
    }

    public function validateQR234(Request $request){
      $rules = [
        'score' => 'nullable|in:notFM,FM',
        'attendance' => 'required|in:X,O,Late,N/A',
        'participation' => 'required|in:A+,A,A-,B+,B,B-,C+,C,Fp,N/A',
        'vocab' => 'nullable|in:notFM,FM',
        'concept' => 'nullable|in:notFM,FM',
        'skill' => 'nullable|in:notFM,FM',
        'status' => 'required|in:Active,Inactive',
      ];

      if ($request->input('score') == 'notFM') {
        $rules['input1'] = 'required|numeric|min:0|max:1000';
        $rules['input2'] = 'required|numeric|min:1|max:1000|gte:input1';
      }
      if($request->vocab == 'notFM'){
        $rules['vocab1'] = 'required|numeric|min:0|max:1000';
        $rules['vocab2'] = 'required|numeric|min:1|max:1000|gte:vocab1';
      }
      if($request->concept == 'notFM'){
        $rules['concept1'] = 'required|numeric|min:0|max:1000';
        $rules['concept2'] = 'required|numeric|min:1|max:1000|gte:concept1';
      }
      if($request->skill == 'notFM'){
        $rules['skill1'] = 'required|numeric|min:0|max:1000';
        $rules['skill2'] = 'required|numeric|min:1|max:1000|gte:skill1';
      }

      $message = [
        'score.required' => 'This is a required field.',
        'score.in' => 'Invalid format.',
        'input1.required' => 'This is a required field.',
        'input2.required' => 'This is a required field.',
        'input1.min' => 'Invalid format.',
        'input1.max' => 'Invalid format.',
        'input2.min' => 'Invalid format.',
        'input2.max' => 'Invalid format.',
        'input2.gte' => 'Invalid format.',
        'attendance.required' => 'This is a required field.',
        'attendance.in' => 'Invalid format.',
        'participation.required' => 'This is a required field.',
        'participation.in' => 'Invalid format.',
        'vocab.required' => 'This is a required field.',
        'vocab.in' => 'Invalid format.',
        'vocab1.required' => 'This is a required field.',
        'vocab2.required' => 'This is a required field.',
        'vocab1.min' => 'Invalid format.',
        'vocab1.max' => 'Invalid format.',
        'vocab2.min' => 'Invalid format.',
        'vocab2.max' => 'Invalid format.',
        'vocab2.gte' => 'Invalid format.',
        'concept.required' => 'This is a required field.',
        'concept.in' => 'Invalid format.',
        'concept1.required' => 'This is a required field.',
        'concept2.required' => 'This is a required field.',
        'concept1.min' => 'Invalid format.',
        'concept1.max' => 'Invalid format.',
        'concept2.min' => 'Invalid format.',
        'concept2.max' => 'Invalid format.',
        'concept2.gte' => 'Invalid format.',
        'skill.required' => 'This is a required field.',
        'skill.in' => 'Invalid format.',
        'skill1.required' => 'This is a required field.',
        'skill2.required' => 'This is a required field.',
        'skill1.min' => 'Invalid format.',
        'skill1.max' => 'Invalid format.',
        'skill2.min' => 'Invalid format.',
        'skill2.max' => 'Invalid format.',
        'skill2.gte' => 'Invalid format.',
        'status.required' => 'This is a required field.',
        'status.in' => 'Invalid format.',
      ];

      $request->validate($rules,$message);
    }

    public function validateQR1(Request $request){

      $rules = [
        'score' => 'nullable|in:notFM,FM',
        'attendance' => 'required|in:X,O,Late,N/A',
        'participation' => 'required|in:A+,A,A-,B+,B,B-,C+,C,Fp,N/A',
        'sentence' => 'nullable|in:notFM,FM',
        'vocabulary_maxtrix_1' => 'nullable|in:notFM,FM',
        'vocabulary_maxtrix_2' => 'nullable|in:notFM,FM',
        'skill' => 'nullable|in:notFM,FM',
        'status' => 'required|in:Active,Inactive',
      ];
      if ($request->input('score') == 'notFM') {
        $rules['input1'] = 'required|numeric|min:0|max:1000';
        $rules['input2'] = 'required|numeric|min:1|max:1000|gte:input1';
      }
      if ($request->sentence == 'notFM') {
        $rules['sentence1'] = 'required|numeric|min:0|max:1000';
        $rules['sentence2'] = 'required|numeric|min:1|max:1000|gte:sentence1';
      }
      if($request->vocabulary_maxtrix_1 == 'notFM'){
        $rules['vocabulary_maxtrix_11'] = 'required|numeric|min:0|max:1000';
        $rules['vocabulary_maxtrix_12'] = 'required|numeric|min:1|max:1000|gte:vocabulary_maxtrix_11';
      }
      if($request->vocabulary_maxtrix_2 == 'notFM'){
        $rules['vocabulary_maxtrix_21'] = 'required|numeric|min:0|max:1000';
        $rules['vocabulary_maxtrix_22'] = 'required|numeric|min:1|max:1000|gte:vocabulary_maxtrix_21';
      }
      if($request->skill == 'notFM'){
        $rules['skill1'] = 'required|numeric|min:0|max:1000';
        $rules['skill2'] = 'required|numeric|min:1|max:1000|gte:skill1';
      }
      $message = [
        'score.required' => 'This is a required field.',
        'score.in' => 'Invalid format.',
        'input1.required' => 'This is a required field.',
        'input2.required' => 'This is a required field.',
        'input1.min' => 'Invalid format.',
        'input1.max' => 'Invalid format.',
        'input2.min' => 'Invalid format.',
        'input2.max' => 'Invalid format.',
        'input2.gte' => 'Invalid format.',
        'attendance.required' => 'This is a required field.',
        'attendance.in' => 'Invalid format.',
        'participation.required' => 'This is a required field.',
        'participation.in' => 'Invalid format.',
        'sentence.required' => 'This is a required field.',
        'sentence.in' => 'Invalid format.',
        'sentence1.required' => 'This is a required field.',
        'sentence2.required' => 'This is a required field.',
        'sentence1.min' => 'Invalid format.',
        'sentence1.max' => 'Invalid format.',
        'sentence2.min' => 'Invalid format.',
        'sentence2.max' => 'Invalid format.',
        'sentence2.gte' => 'Invalid format.',
        'vocabulary_maxtrix_1.required' => 'This is a required field.',
        'vocabulary_maxtrix_1.in' => 'Invalid format.',
        'vocabulary_maxtrix_11.required' => 'This is a required field.',
        'vocabulary_maxtrix_12.required' => 'This is a required field.',
        'vocabulary_maxtrix_11.min' => 'Invalid format.',
        'vocabulary_maxtrix_11.max' => 'Invalid format.',
        'vocabulary_maxtrix_12.min' => 'Invalid format.',
        'vocabulary_maxtrix_12.max' => 'Invalid format.',
        'vocabulary_maxtrix_12.gte' => 'Invalid format.',
        'skill.required' => 'This is a required field.',
        'skill.in' => 'Invalid format.',
        'skill1.required' => 'This is a required field.',
        'skill2.required' => 'This is a required field.',
        'skill1.min' => 'Invalid format.',
        'skill1.max' => 'Invalid format.',
        'skill2.min' => 'Invalid format.',
        'skill2.max' => 'Invalid format.',
        'skill2.gte' => 'Invalid format.',
        'vocabulary_maxtrix_2.required' => 'This is a required field.',
        'vocabulary_maxtrix_2.in' => 'Invalid format.',
        'vocabulary_maxtrix_21.required' => 'This is a required field.',
        'vocabulary_maxtrix_22.required' => 'This is a required field.',
        'vocabulary_maxtrix_21.min' => 'Invalid format.',
        'vocabulary_maxtrix_21.max' => 'Invalid format.',
        'vocabulary_maxtrix_22.min' => 'Invalid format.',
        'vocabulary_maxtrix_22.max' => 'Invalid format.',
        'vocabulary_maxtrix_2.gte' => 'Invalid format.',
        'status.required' => 'This is a required field.',
        'status.in' => 'Invalid format.',
      ];
      $request->validate($rules,$message);
    }

    public function validateQR5(Request $request){
      $rules = [
        'score' => 'nullable|in:notFM,FM',
        'attendance' => 'required|in:X,O,Late,N/A',
        'participation' => 'required|in:A+,A,A-,B+,B,B-,C+,C,Fp,N/A',
        'short_response' => 'required|in:A+,A,A-,B+,B,B-,C+,C,Fp,Fm,N/A',
        'vocab_hw' => 'nullable|in:notFM,FM',
        'status' => 'required|in:Active,Inactive',
      ];

      if ($request->input('score') == 'notFM') {
        $rules['input1'] = 'required|numeric|min:0|max:1000';
        $rules['input2'] = 'required|numeric|min:1|max:1000|gte:input1';
      }
      if($request->vocab_hw == 'notFM'){
        $rules['vocab_hw1'] = 'required|numeric|min:0|max:1000';
        $rules['vocab_hw2'] = 'required|numeric|min:1|max:1000|gte:vocab_hw1';
      }

      $message = [
        'score.required' => 'This is a required field.',
        'score.in' => 'Invalid format.',
        'input1.required' => 'This is a required field.',
        'input2.required' => 'This is a required field.',
        'input1.min' => 'Invalid format.',
        'input1.max' => 'Invalid format.',
        'input2.min' => 'Invalid format.',
        'input2.max' => 'Invalid format.',
        'input2.gte' => 'Invalid format.',
        'attendance.required' => 'This is a required field.',
        'attendance.in' => 'Invalid format.',
        'participation.required' => 'This is a required field.',
        'participation.in' => 'Invalid format.',
        'vocab_hw.required' => 'This is a required field.',
        'vocab.in' => 'Invalid format.',
        'vocab_hw1.required' => 'This is a required field.',
        'vocab_hw2.required' => 'This is a required field.',
        'vocab_hw1.min' => 'Invalid format.',
        'vocab_hw1.max' => 'Invalid format.',
        'vocab_hw2.min' => 'Invalid format.',
        'vocab_hw2.max' => 'Invalid format.',
        'vocab_hw2.gte' => 'Invalid format.',
        'short_response.required' => 'This is a required field.',
        'short_response.in' => 'Invalid format.',
        'status.required' => 'This is a required field.',
        'status.in' => 'Invalid format.',
      ];

      $request->validate($rules,$message);
    }

    public function validateQW18(Request $request){
      $rules = [
        'attendance' => 'required|in:X,O,Late,N/A',
        'participation' => 'required|in:A+,A,A-,B+,B,B-,C+,C,Fp,N/A',
        'status' => 'required|in:Active,Inactive',
        'transcription' => 'required|in:Good effort,Incomplete,Fm,N/A',
        'prewriting' => 'required|in:A+,A,A-,B+,B,B-,C+,C,Fp,Fm,N/A',
        'writing' => 'required|in:A+,A,A-,B+,B,B-,C+,C,Fp,Fm,N/A',
        'skill' => 'nullable|in:notFM,FM',
      ];
      if ($request->skill == 'notFM') {
        $rules['skill1'] = 'required|numeric|min:0|max:1000';
        $rules['skill2'] = 'required|numeric|min:1|max:1000|gte:skill1';
      }

      $message = [
        'attendance.required' => 'This is a required field.',
        'attendance.in' => 'Invalid format.',
        'participation.required' => 'This is a required field.',
        'participation.in' => 'Invalid format.',
        'status.required' => 'This is a required field.',
        'status.in' => 'Invalid format.',
        'transcription.required' => 'This is a required field.',
        'transcription.in' => 'Invalid format.',
        'prewriting.required' => 'This is a required field.',
        'prewriting.in' => 'Invalid format.',
        'writing.required' => 'This is a required field.',
        'writing.in' => 'Invalid format.',
        'skill.required' => 'This is a required field.',
        'skill.in' => 'Invalid format.',
        'skill1.required' => 'This is a required field.',
        'skill2.required' => 'This is a required field.',
        'skill1.min' => 'Invalid format.',
        'skill1.max' => 'Invalid format.',
        'skill2.min' => 'Invalid format.',
        'skill2.max' => 'Invalid format.',
        'skill2.gte' => 'Invalid format.',
      ];
      $request->validate($rules,$message);
    }

    public function validateALL(Request $request,$class){
      $rules = [
        'attendance' => 'required|in:X,O,Late,N/A',
        'participation' => 'required|in:A+,A,A-,B+,B,B-,C+,C,Fp,N/A',
        'status' => 'required|in:Active,Inactive',
        'skill' => 'nullable|in:notFM,FM',
      ];
      if ($request->skill == 'notFM') {
        $rules['skill1'] = 'required|numeric|min:0|max:1000';
        $rules['skill2'] = 'required|numeric|min:0|max:1000|gte:skill1';
      }
      if($class->level != 'QW4'){
        $rules['transcription'] = 'required|in:Good effort,Incomplete,Fm,N/A';
      }
      if($class->level == 'QW2' && $request->type == 'HW3'){
        $rules['completed_essay'] = 'required|in:A+,A,A-,B+,B,B-,C+,C,Fp,N/A';
      }
      if($request->type == 'HW2'){
        $rules['writing'] = 'required|in:A+,A,A-,B+,B,B-,C+,C,Fp,Fm,N/A';
      }
      if(in_array($class->level,['QW3','QW4']) && $request->type == 'HW3'){
        $rules['revision'] = 'required|in:A+,A,A-,B+,B,B-,C+,C,Fp,Fm,N/A';
      }
      if($request->type == 'HW1'){
        $rules['prewriting'] = 'required|in:A+,A,A-,B+,B,B-,C+,C,Fp,Fm,N/A';
      }
      $message = [
        'attendance.required' => 'This is a required field.',
        'attendance.in' => 'Invalid format.',
        'participation.required' => 'This is a required field.',
        'participation.in' => 'Invalid format.',
        'status.required' => 'This is a required field.',
        'status.in' => 'Invalid format.',
        'transcription.required' => 'This is a required field.',
        'transcription.in' => 'Invalid format.',
        'prewriting.required' => 'This is a required field.',
        'prewriting.in' => 'Invalid format.',
        'revision.required' => 'This is a required field.',
        'revision.in' => 'Invalid format.',
        'completed_essay.required' => 'This is a required field.',
        'completed_essay.in' => 'Invalid format.',
        'writing.required' => 'This is a required field.',
        'writing.in' => 'Invalid format.',
        'skill.required' => 'This is a required field.',
        'skill.in' => 'Invalid format.',
        'skill1.required' => 'This is a required field.',
        'skill2.required' => 'This is a required field.',
        'skill1.min' => 'Invalid format.',
        'skill1.max' => 'Invalid format.',
        'skill2.min' => 'Invalid format.',
        'skill2.max' => 'Invalid format.',
        'skill2.gte' => 'Invalid format.',
      ];

      $request->validate($rules,$message);
    }

    public function template(){
      return view('admin.grades.templates.index');
    }

    public function detailTemp($temp){
      switch ($temp){
        case 'qr1' :
          return view('admin.grades.templates.view.QR1');
        case 'qr2' :
          return view('admin.grades.templates.view.QR2');
        case 'qr5' :
          return view('admin.grades.templates.view.QR5');
        case 'qw18' :
          return view('admin.grades.templates.view.QW1-8');
        case 'qw915' :
          return view('admin.grades.templates.view.QW9-15');
        case 'qw3' :
          return view('admin.grades.templates.view.QW3');
        case 'qw4' :
          return view('admin.grades.templates.view.QW4');
      }
      return view('admin.grades.templates.index');
    }
}
