<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\UserCreateRequest;
use App\Http\Requests\UserUpdateRequest;
use App\Mail\ActiveMailAdmin;
use App\Mail\CreateAccountAdmin;
use App\Mail\DeactivateMailAdmin;
use App\Mail\RegisterAccount;
use App\Repositories\AccountRepository;
use App\Repositories\AdminClassRepository;
use App\Repositories\AdminRepository;
use App\Repositories\ClassRepository;
use App\Repositories\User\UserRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Mail;

class UserController extends Controller
{
    protected $userRepository;
    protected $accountRepository;
    protected $classRepository;
    protected $adminRepository;
    protected $adminClassRepository;

    public function __construct(UserRepository $userRepository,AccountRepository $accountRepository,AdminRepository $adminRepository,
                                ClassRepository $classRepository, AdminClassRepository $adminClassRepository)
    {
      $this->userRepository = $userRepository;
      $this->accountRepository = $accountRepository;
      $this->classRepository = $classRepository;
      $this->adminRepository = $adminRepository;
      $this->adminClassRepository = $adminClassRepository;
    }
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $users = $this->adminRepository->filter($request->all());
        if($request->id == 'desc'){
          $totalItems = $users->total();
          $index = $totalItems - ($users->currentPage() - 1) * $users->perPage();
        }else{
          $index = ($users->currentPage() - 1) * $users->perPage() + 1;
        }
        return view('admin.users.index',compact('users','index'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $classes = $this->classRepository->all();

        return view('admin.users.create',compact('classes'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(UserCreateRequest $request)
    {

        $data = $request->all();
        $password = substr(md5(mt_rand()), 0, 7);
        $data['password'] = bcrypt($password);
        $data['name'] = truncate_string($data['name']);
        $data['last_name'] = truncate_string($data['last_name']);
        $user = $this->adminRepository->create($data);
        $this->updateClasses($user,$data);
        Mail::to($user->email)->queue(new CreateAccountAdmin($user,$password));
        return redirect()->route('admin.users.index')->with('success','Created successfully');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $classes = $this->classRepository->all();
        $user = $this->adminRepository->with(['classes'])->find($id);
        return view('admin.users.show',compact('user','classes'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $classes = $this->classRepository->all();
        $user = $this->adminRepository->with(['classes'])->find($id);
        if($user){
          return view('admin.users.edit',compact('user','classes'));
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UserUpdateRequest $request, string $id)
    {
        $data = $request->all();
        $data['name'] = truncate_string($data['name']);
        $data['last_name'] = truncate_string($data['last_name']);
        $user = $this->adminRepository->findOrFail($id);
        if($user->status != $request->status){
          if($request->status == 'active'){
            Mail::to($request->email)->queue(new ActiveMailAdmin());
          }else{
            Mail::to($request->email)->queue(new DeactivateMailAdmin());
          }
        }
        $user->update($data);
        $this->updateClasses($user,$data);

        return redirect()->route('admin.users.index')->with('success','Updated successfully');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
      $user = $this->adminRepository->find($id);
      $user->update(['email' =>$user->email.'-'.time()]);
      $user->delete($id);
      $this->adminClassRepository->deleteWhere(['admin_id'=> $id]);
      return redirect()->route('admin.users.index')->with('success','Delete successfully.');
    }

    public function updateClasses($admin,$data): void
    {
      $this->adminClassRepository->deleteWhere(['admin_id'=> $admin->id]);
      $adminId = $admin->id;
      if(!empty($data['class']) && $admin->role == 'teacher'){
        $classIds = $data['class'];
        $dataToInsert = array_map(function($classId) use ($adminId) {
          return [
            'admin_id' => $adminId,
            'class_id' => $classId,
            'created_at' => now(),
            'updated_at' => now(),
          ];
        }, $classIds);
        $this->adminClassRepository->insert($dataToInsert);
      }
    }
}
