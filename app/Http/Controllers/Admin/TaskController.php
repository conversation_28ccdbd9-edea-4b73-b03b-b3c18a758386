<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\AdminClass;
use App\Repositories\TaskRepository;
use Illuminate\Http\Request;

class TaskController extends Controller
{
    public function __construct(private TaskRepository $taskRepo) {
    }

    public function index(Request $request)
    {
        return view('admin.tasks.index');
    }

    public function create(Request $request)
    {
        return view('admin.tasks.create');
    }

    public function edit($id)
    {
        $task = $this->taskRepo->where(['is_display' => 1, 'is_bank' => 1])->find($id);
        if($task->class_id && auth('admin')->user()->role == 'teacher'){
          AdminClass::where('class_id',$task->class_id)->where('admin_id',auth('admin')->user()->id)->firstOrFail();
        }
        if(!$task){
            return redirect()->route('admin.tasks.index')->with('error','Task not found.');
        }
        return view('admin.tasks.create');
    }
}
