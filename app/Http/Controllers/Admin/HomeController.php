<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Repositories\AccountRepository;
use App\Repositories\InvoiceRepository;
use Illuminate\Http\Request;

class HomeController extends Controller
{
  protected $accountRepository;
  public $invoiceRepository;
  public function __construct(AccountRepository $accountRepository, InvoiceRepository $invoiceRepository,)
  {
    $this->accountRepository = $accountRepository;
    $this->invoiceRepository = $invoiceRepository;
  }
  public function index(){
    $parentNum = $this->accountRepository->where('type', 'parent')->count();
    $studentNum = $this->accountRepository->where('type', 'student')->count();

    $invoices = $this->invoiceRepository->count();
    $unpaid = $this->invoiceRepository->where('status', 'unpaid')->count();
    $paid = $this->invoiceRepository->where('status', 'paid')->orWhere('status', 'request_refund')->count();
    $refund = $this->invoiceRepository->where('status', 'refund')->count();

    return view('admin.index', compact('invoices', 'unpaid', 'paid', 'refund', 'parentNum', 'studentNum'));
  }
}
