<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Repositories\AccountRepository;
use App\Repositories\ClassScheduleRepository;
use App\Repositories\InvoiceRepository;
use App\Services\KakaoMessageService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class HomeController extends Controller
{
  protected $accountRepository;
  public $invoiceRepository;
  public $classSchedule;
  public function __construct(AccountRepository $accountRepository, InvoiceRepository $invoiceRepository, ClassScheduleRepository $classSchedule)
  {
    $this->accountRepository = $accountRepository;
    $this->invoiceRepository = $invoiceRepository;
    $this->classSchedule = $classSchedule;
  }
  public function index(){
    $parentNum = $this->accountRepository->where('type', 'parent')->count();
    $studentNum = $this->accountRepository->where('type', 'student')->count();

    $invoices = $this->invoiceRepository->count();
    $unpaid = $this->invoiceRepository->where('status', 'unpaid')->count();
    $paid = $this->invoiceRepository->where('status', 'paid')->orWhere('status', 'request_refund')->count();
    $refund = $this->invoiceRepository->where('status', 'refund')->count();

    return view('admin.index', compact('invoices', 'unpaid', 'paid', 'refund', 'parentNum', 'studentNum'));
  }

  public function setupKakaoTime(Request $request)
  {
      $request->validate([
          'student_id' => 'required|integer',
          'time'       => 'required|date_format:H:i',
      ]);

      $student = $this->accountRepository->find($request->student_id);

      if (!$student) {
          return response()->json([
              'status'  => 'error',
              'message' => 'Student not found.'
          ], 404);
      }

      $student->kakao_message_time = $request->time;
      $student->save();

      return response()->json([
          'status'  => 'success',
          'message' => 'Kakao message time updated successfully.',
          'time'    => $student->kakao_message_time,
      ]);
  }
  public function requestMakeup(KakaoMessageService $kakaoMessageService, Request $request)
  {
      $account = auth()->user();
      $class_name = $request->class_name;
      $lastStart = $request->last_schedule;
      $date = \Carbon\Carbon::parse($lastStart);

      $month = $date->format('n'); 
      $day   = $date->format('j');
      $message = "{$month}월 {$day}일 {$class_name} 수업 보강을 링크를 통해 예약해주시길 바랍니다.";
      $kakaoMessageService->sendKakaoMessagesForParentOfStudent($account, $message);
  }

}
