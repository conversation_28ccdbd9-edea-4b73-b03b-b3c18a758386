<?php

namespace App\Http\Controllers\Admin\Vocabulary;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class VocabularyController extends Controller
{
  public function __construct()
  {
  }

  public function index(Request $request)
  {
    return view('admin.vocabularies.index');
  }
  public function create()
  {
    return view('admin.vocabularies.index');
  }

    public function edit()
  {
    return view('admin.vocabularies.index');
  }


  public function show()
  {
    return view('admin.vocabularies.index');
  }
}
