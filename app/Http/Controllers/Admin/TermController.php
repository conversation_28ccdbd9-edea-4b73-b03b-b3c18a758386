<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\CreateTermRequest;
use App\Http\Requests\UpdateTermRequest;
use App\Repositories\ClassRepository;
use App\Repositories\ClassScheduleRepository;
use App\Repositories\TermRepository;
use Carbon\Carbon;
use Illuminate\Http\Request;

class TermController extends Controller
{
    public $termRepository;
    public $classScheduleRepository;
    public $classRepository;
    public function __construct(TermRepository $termRepository, ClassScheduleRepository $classScheduleRepository,ClassRepository $classRepository){
      $this->termRepository = $termRepository;
      $this->classScheduleRepository = $classScheduleRepository;
      $this->classRepository = $classRepository;
    }
    public function index(Request $request){
      $lastSession = 0;
      $terms = $this->termRepository->filter($request->all());
      foreach ($terms as &$term) {
        $class = $term->getClass;
        if($class){
          $schedules = $this->classScheduleRepository->with('classes')->where('class_id', $class->id)->orderBy('date')->get();
          $schedulesList = $this->classScheduleRepository->getSesion($schedules);
          foreach ($schedulesList as $schedule){
            if (date('Y-m-d') >= $schedule->date) {
              $lastSession = $schedule->session;
            }
          }
          $term->session = $lastSession;
        }else{
          $term->session = 0;
        }
      }
      return view('admin.terms.index',compact('terms'));
    }

    public function create(){
      return view('admin.terms.create');
    }

    public function store(CreateTermRequest $request){
      $data = $request->all();
      if($request->break_start){
        $end = Carbon::parse($request->end);
        $newEndDate = $end->addDays($request->break_option_start ? 14 : 7);
        $data['end'] = $newEndDate->format('Y/m/d');
      }
      $this->termRepository->create($data);
      return redirect(route('admin.terms.index'))->with('success','Created successfully');
    }

    public function show($id){
      $term = $this->termRepository->find($id);
      return view('admin.terms.show',compact('term'));
    }

    public function edit($id){
      $term = $this->termRepository->with(['classes','getClass'])->find($id);
      return view('admin.terms.edit',compact('term'));
    }

    public function update(UpdateTermRequest $request,$id){
      $term = $this->termRepository->find($id);
      if($term->start < now()){
        return redirect(route('admin.terms.index'))->with('success','Updated successfully');
      }
      $data = $request->all();
      if($request->break_start){
        $end = Carbon::parse($request->end);
        $newEndDate = $end->addDays($request->break_option_start ? 14 : 7);
        $data['end'] = $newEndDate->format('Y/m/d');
      }
      $this->termRepository->update($data,$id);
      $this->classRepository->where('term',$id)->update(['status'=>'inactive']);
      return redirect(route('admin.terms.index'))->with('success','Updated successfully');
    }

    public function destroy($id){
      $this->termRepository->delete($id);
      return redirect(route('admin.terms.index'))->with('success','Deleted successfully');
    }
}
