<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Homework;
use App\Services\GradeLCService;
use App\Services\HomeworkService;
use Illuminate\Http\Request;

class GradeLearningCenterController extends Controller
{
    public function __construct(private GradeLCService $gradeLCService)
    {
    }

    public function index()
    {
        return view('admin.grades.grades');
    }

    public function show(Request $request,$id)
    {
        if(!$request->has('student_id')){
            abort(404, 'Student not found.');
        }
        $homework = Homework::when(auth('admin')->user()->role == 'teacher', function ($query) {
            $query->whereHas('mainHomework.getClass.admins', function ($q) {
                $q->where('admin_id', auth('admin')->user()->id);
            });
        })->find($id);
        if (!$homework) {
            abort(404, 'Homework not found.');
        }
        return view('admin.grades.grades');
    }
}
