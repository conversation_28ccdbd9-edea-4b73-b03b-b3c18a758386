<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\AdminClass;
use App\Repositories\TagRepository;
use Illuminate\Http\Request;

class TagVocabularyController extends Controller
{
    public function __construct(private TagRepository $tagRepo){}

    public function index(){
      return view('admin.vocabularies.index');
    }

    public function create()
    {
        return view('admin.vocabularies.index');
    }

    public function edit($id)
    {
        $tagVocabulary = $this->tagRepo->find($id);
        if($tagVocabulary->class_id && auth('admin')->user()->role == 'teacher'){
          AdminClass::where('class_id',$tagVocabulary->class_id)->where('admin_id',auth('admin')->user()->id)->firstOrFail();
        }
        if(!$tagVocabulary) abort(404);
        return view('admin.vocabularies.index');
    }

    public function show($id)
    {
      $tagVocabulary = $this->tagRepo->find($id);
      if(!$tagVocabulary) abort(404);
      return view('admin.vocabularies.index');
    }
}
