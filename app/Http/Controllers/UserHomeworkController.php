<?php

namespace App\Http\Controllers;

use App\Enums\AccountType;
use App\Enums\Constant;
use App\Enums\QuestionType;
use App\Enums\TypeExam;
use App\Http\Requests\AutoUnlockRequest;
use App\Http\Requests\SubmitAnswersRequest;
use App\Models\Account;
use App\Models\AccountHomework;
use App\Models\FlashcardReviewWord;
use App\Models\AccountHomeworkTask;
use App\Models\AccountTaskAnswer;
use App\Models\TaskHomework;
use App\Models\VocabularyCount;
use App\Repositories\AccountHomeworkRepository;
use App\Repositories\AccountHomeworkTaskRepository;
use App\Repositories\AccountMainHomeworkRepository;
use App\Repositories\AccountTaskAnswerRepository;
use App\Repositories\MainHomeworkRepository;
use App\Repositories\TaskHomeworkRepository;
use App\Repositories\HomeworkRepository;
use App\Repositories\RequestUnlockRepository;
use App\Repositories\TaskRepository;
use App\Repositories\VocabularyGradeRepository;
use App\Services\HomeworkService;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Throwable;

class UserHomeworkController extends Controller
{
    public function __construct(
        private MainHomeworkRepository $mainHomeworkRepository,
        private TaskHomeworkRepository $taskHomeworkRepository,
        private HomeworkRepository $homeworkRepository,
        private TaskRepository $taskRepository,
        private AccountTaskAnswerRepository $accountTaskAnswerRepository,
        private AccountHomeworkRepository $accountHomeworkRepository,
        private AccountHomeworkTaskRepository $accountHomeworkTaskRepository,
        private AccountMainHomeworkRepository $accountMainHomeworkRepository,
        private VocabularyGradeRepository $vocabularyGradeRepo,
        private RequestUnlockRepository $requestUnlockRepo,
        private HomeworkService $homeworkService,
    ) {}

    public function index(Request $request)
    {
        return view('web.pages.homeworks.index');
    }

    public function classworkIndex(Request $request)
    {
        return view('web.pages.classwork.index');
    }
    public function indexResult(Request $request)
    {
        return view('web.pages.homeworks.index-result');
    }

    public function indexStatisticalVocab(Request $request)
    {
        if(auth('web')->user()->type == 'parent'){
            return redirect('/')
                ->with('error', 'You do not have permission to access this page.');
        }
        return view('web.pages.statistical-vocab.index');
    }

    public function listMainHomeworksResultForAccount(Request $request)
    {
        $searchQuery = $request->query('search');
        $cleanSearchQuery = strip_tags($searchQuery);
        $perPage = $request->query('per_page', 10);
        $account = Auth::guard('web')->user();
        $accountId = $account->id;
        $accountType = $account->type;
        $mainHomeworks = null;
        if ($accountType == AccountType::STUDENT) {
            $mainHomeworks = $this->mainHomeworkRepository->getMainHomeworksWithAccountHomeworks(
                $accountId,
                $cleanSearchQuery,
                $perPage,
                true,
                TypeExam::HOMEWORK
            );
        }
        if ($accountType == AccountType::PARENT) {
            $mainHomeworks = $this->mainHomeworkRepository->getMainHomeworksWithAccountsHomeworks(
                $account->user_id,
                $cleanSearchQuery,
                $perPage,
                true,
                TypeExam::HOMEWORK
            );
        }
        return response()->json([
            'data' => $mainHomeworks->items(),
            'role' => $accountType,
            'accountId' => $accountId,
            'meta' => [
                'current_page' => $mainHomeworks->currentPage(),
                'last_page' => $mainHomeworks->lastPage(),
                'per_page' => $mainHomeworks->perPage(),
                'total' => $mainHomeworks->total(),
            ],
        ]);
    }
    public function listMainHomeworksForAccount(Request $request)
    {
        $searchQuery = $request->query('search');
        $cleanSearchQuery = strip_tags($searchQuery);
        $perPage = $request->query('per_page', 10);
        $account = Auth::guard('web')->user();
        $accountId = $account->id;
        $accountType = $account->type;
        Log::info($account);
        $mainHomeworks = null;
        if ($accountType == AccountType::STUDENT) {
            $mainHomeworks = $this->mainHomeworkRepository->getMainHomeworksWithAccountHomeworks(
                $accountId,
                $cleanSearchQuery,
                $perPage
            );
        }
        if ($accountType == AccountType::PARENT) {
            $mainHomeworks = $this->mainHomeworkRepository->getMainHomeworksWithAccountsHomeworks(
                $account->user_id,
                $cleanSearchQuery,
                $perPage
            );
        }

        return response()->json([
            'data' => $mainHomeworks->items(),
            'accountId' => $accountId,
            'role' => $accountType,
            'meta' => [
                'current_page' => $mainHomeworks->currentPage(),
                'last_page' => $mainHomeworks->lastPage(),
                'per_page' => $mainHomeworks->perPage(),
                'total' => $mainHomeworks->total(),
            ],
        ]);
    }

    public function listMainClassworkForAccount(Request $request)
    {
        $searchQuery = $request->query('search');
        $cleanSearchQuery = strip_tags($searchQuery);
        $perPage = $request->query('per_page', 10);
        $account = Auth::guard('web')->user();
        $accountId = $account->id;
        $accountType = $account->type;
        Log::info($account);
        $mainHomeworks = $this->mainHomeworkRepository->getMainHomeworksWithAccountHomeworks(
            $accountId,
            $cleanSearchQuery,
            $perPage,
            false,
            2
        );

        return response()->json([
            'data' => $mainHomeworks->items(),
            'accountId' => $accountId,
            'role' => $accountType,
            'meta' => [
                'current_page' => $mainHomeworks->currentPage(),
                'last_page' => $mainHomeworks->lastPage(),
                'per_page' => $mainHomeworks->perPage(),
                'total' => $mainHomeworks->total(),
            ],
        ]);
    }

    public function homework(Request $request, int $homeworkId)
    {
        $account = Auth::guard('web')->user();
        $accountId = $request->query('accountId') ?? $account->id;
        $homework = $this->mainHomeworkRepository->getMainHomeworksByHomeWorkId($accountId, $homeworkId);
        Log::info("Homework retrieved for account ID {$accountId}: " . json_encode($homework));
        if (!$homework) {
            return redirect()
                ->route('web.homeworks.index')
                ->with('error', 'Homework not found.');
        }

        $mainHomeworkId = $homework->main_homework_id ?? null;
        if (!$mainHomeworkId && $homework->status === Constant::INACTIVE) {
            Log::error("main_homework_id not found for homework ID: {$homeworkId}");
            return response()->json(['message' => 'Main homework ID missing for this homework.'], 500);
        }
        $accountHomeworkRecord = null;
        $tasksByHomework = null;
        DB::beginTransaction();
        try {
            $accountMainHomeworkSearchAttributes = [
                'account_id' => $accountId,
                'main_homework_id' => $mainHomeworkId,
            ];
            $accountMainHomeworkRecord = $this->accountMainHomeworkRepository->firstOrCreate(
                $accountMainHomeworkSearchAttributes
            );
            Log::info("AccountMainHomework record processed. ID: {$accountMainHomeworkRecord->id}");
            $accountHomeworkRecord = null;
            $accountHomeworkSearchAttributes = [
                'account_id' => $accountId,
                'homework_id' => $homeworkId,
                'main_homework_id' => $mainHomeworkId,
                'account_main_homework_id' => $accountMainHomeworkRecord->id,
            ];
            $accountHomeworkCreationAttributes = [
                'homework_unlock' => 1,
            ];
            //type === 2 is classwork
            if ($homework->order === 1 || $homework->type === TypeExam::CLASSWORK) {
                $accountHomeworkRecord = $this->accountHomeworkRepository->firstOrCreate(
                    $accountHomeworkSearchAttributes,
                    $accountHomeworkCreationAttributes
                );
                Log::info("AccountHomework record processed. ID: {$accountHomeworkRecord->id}");
            }
            if ($homework->order !== 1 && $homework->type !== TypeExam::CLASSWORK) {
                $accountHomeworkRecord = $this->accountHomeworkRepository
                    ->findWhere($accountHomeworkSearchAttributes)
                    ->first();
                if (!$accountHomeworkRecord) {
                    return redirect()
                        ->route('web.homeworks.index')
                        ->with('error', 'Homework not found.');
                }
            }

            $tasksByHomework = $this->taskHomeworkRepository->getTasksByHomeworkIdAndAccountId(
                $homeworkId,
                $accountId,
                $accountHomeworkRecord?->id
            );

            DB::commit();
            Log::info(
                'DB: All homework-related records processed and committed successfully for accountId: ' . $accountId
            );
        } catch (Throwable $e) {
            DB::rollBack();
            Log::info(
                "DB: Failed to process homework records for homeworkId: {$homeworkId}, accountId: {$accountId}. Error: " .
                    $e->getMessage()
            );
            return response()->json(['message' => 'Failed to prepare homework. Please try again.'], 500);
        }
        $homework['user_homework_id'] = $accountHomeworkRecord?->id;
        $tasksByHomework = $this->taskHomeworkRepository
            ->addNeedReviewVocabularies($homework->mainHomework->class, $homework->mainHomework->session, $accountId, $tasksByHomework);

        return view('web.pages.exam.exam_display', [
            'tasksData' => $tasksByHomework,
            'homework' => $homework,
            'accountId' => $accountId,
        ]);
    }

    public function homeworkResult(Request $request, int $homeworkId)
    {
        if (empty($homeworkId)) abort(404);
        if (auth('web')->user()->type == 'parent') {
            $studentIds = Account::where('user_id', auth('web')->user()->user_id)
                ->where('type', 'student')->pluck('id')->toArray();
            AccountHomework::where('id', $homeworkId)->whereIn('account_id', !empty($studentIds) ? $studentIds : [0])->firstOrFail();
        } else {
            AccountHomework::where('id', $homeworkId)->where('account_id', auth('web')->user()->id)->firstOrFail();
        }
        return view('web.pages.homeworks.index-result');
    }

    public function taskDetail($homeworkId, $taskId, Request $request)
    {
        Log::info("Starting taskDetail for homework ID: {$homeworkId}, task ID: {$taskId}");
        DB::beginTransaction();

        try {
            $accountId = Auth::guard('web')->id();
            $homework = $this->mainHomeworkRepository->getMainHomeworksByHomeWorkId($accountId, $homeworkId);
            if (!$homework) {
                DB::rollBack();
                return response()->json(['message' => 'Homework not found.'], 404);
            }

            $mainHomeworkId = $homework->main_homework_id ?? null;
            $userHomeworkId = $request->query('userHomeworkId');
            Log::info("UserHomework ID: {$homework}");
            Log::info("UserHomework ID: {$userHomeworkId}, MainHomework ID: {$mainHomeworkId}");
            if (!$mainHomeworkId) {
                DB::rollBack();
                return response()->json(['message' => 'Main homework ID missing for this homework.'], 500);
            }
            $now = now();

            $updatedMain = $this->accountHomeworkRepository->updateStartTimeOnce(
                $accountId,
                $homeworkId,
                $mainHomeworkId,
                [
                    'start_time' => $now,
                ]
            );

            if ($userHomeworkId) {
                $updatedUser = $this->accountHomeworkTaskRepository->updateOrInsertStartTime(
                    [
                        'account_id' => $accountId,
                        'homework_id' => $homeworkId,
                        'user_homework_id' => $userHomeworkId,
                        'task_id' => $taskId,
                    ],
                    ['start_time' => $now]
                );
                $accountHomeworkTask = AccountHomeworkTask::where('account_id', $accountId)
                    ->where('homework_id', $homeworkId)
                    ->where('user_homework_id', $userHomeworkId)
                    ->first();
                if ($accountHomeworkTask->task_unlock === 0) {
                    $data = [
                        'account_id' => $accountId,
                        'homework_id' => $homeworkId,
                        'user_homework_id' => $userHomeworkId,
                        'task_id' => $taskId,
                    ];
                    $this->homeworkService->saveTaskQuestion($data);
                }
            }

            $taskDetail = $this->taskRepository->getQuestionsByTaskId($taskId, $accountId);

            if (!$taskDetail || $taskDetail->isEmpty()) {
                DB::rollBack();
                return response()->json(['message' => 'Task or questions not found.'], 404);
            }

            DB::commit();

            return response()->json([
                'questions' => $taskDetail,
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Lỗi trong taskDetail: ' . $e->getMessage());
            return response()->json(
                [
                    'message' => 'An error occurred while processing your request.',
                    'error' => $e->getMessage(),
                ],
                500
            );
        }
    }

    public function submitAnswers(SubmitAnswersRequest $request)
    {
        $accountId = Auth::id();
        $taskId = $request->input('task_id');
        $userHomeworkId = $request->input('user_homework_id');
        $answersData = $request->input('answers');
        $homeworkId = $request->input('homework_id');
        $homework = $this->homeworkRepository->find($homeworkId);
        $AccountTaskHomework = $this->accountHomeworkTaskRepository
            ->findWhere([
                'homework_id' => $homeworkId,
                'task_id' => $taskId,
                'user_homework_id' => $userHomeworkId
            ])
            ->first();
        $taskHomework = $this->taskHomeworkRepository
            ->findWhere([
                'homework_id' => $homeworkId,
                'task_id' => $taskId,
            ])
            ->first();
        $startTime =  Carbon::parse($AccountTaskHomework->start_time);
        $maxMinutes =  $taskHomework->max_submit_time;
        $now = Carbon::now();

        $endTime = $now->greaterThan($startTime->copy()->addMinutes($maxMinutes))
            ? $startTime->copy()->addMinutes($maxMinutes)
            : $now;
        $isLastTask = $request->input('is_last_task');
        $dataScore = [
            'score' => 0,
            'total_score' => 0,
        ];
        DB::beginTransaction();
        try {
            $success = $this->accountTaskAnswerRepository->saveAnswers($accountId, $taskId, $answersData, $dataScore);
            if (!$success) {
                DB::rollBack();
                return response()->json(['message' => 'Failed to save answers.'], 500);
            }
            if (!$this->vocabularyGradeRepo->saveGrades($answersData, $accountId, $homeworkId)) {
                DB::rollBack();
                return response()->json(['message' => 'Failed to save Vocabulary grades.'], 500);
            }
            $updatedUser = $this->accountHomeworkTaskRepository->updateByAccountAndHomeworkTask(
                $accountId,
                $homeworkId,
                $userHomeworkId,
                $taskId,
                ['end_time' => $endTime, 'score' => $dataScore['score'], 'total_score' => $dataScore['total_score']]
            );
            if (!$updatedUser) {
                DB::rollBack();
                return response()->json(['message' => 'Failed to update task end time.'], 500);
            }
            if ($isLastTask) {
                $homeworkUpdated = $this->accountHomeworkRepository->updateByAccountAndHomework(
                    $accountId,
                    $homeworkId,
                    $homework->main_homework_id,
                    ['end_time' => $endTime]
                );

                if (!$homeworkUpdated) {
                    DB::rollBack();
                    return response()->json(['message' => 'Failed to update homework end time.'], 500);
                }
            }
            DB::commit();
            return response()->json(['message' => 'Answers submitted successfully!'], 200);
        } catch (Throwable $e) {
            DB::rollBack();
            Log::debug("Error submitting answers for account {$accountId}, task {$taskId}: " . $e->getMessage());
            return response()->json(['message' => 'An error occurred while submitting answers.'], 500);
        }
    }

    public function autoUnlockHomeworkByAccount(AutoUnlockRequest $req)
    {
        try {
            $data = $req->only(['main_homework_id', 'homework_id', 'account_id', 'account_main_homework_id']);

            $attributes = [
                'main_homework_id' => $data['main_homework_id'],
                'homework_id' => $data['homework_id'],
                'account_id' => $data['account_id'],
            ];

            $values = [
                'homework_unlock' => 1,
                'account_main_homework_id' => $data['account_main_homework_id'],
            ];

            $unlock = $this->accountHomeworkRepository->updateOrCreate($attributes, $values);

            return response()->json(
                [
                    'message' => 'Homework auto-unlocked successfully.',
                    'data' => $unlock,
                ],
                201
            );
        } catch (\Exception $e) {
            return response()->json(
                [
                    'message' => 'Failed to auto-unlock homework.',
                    'error' => $e->getMessage(),
                ],
                500
            );
        }
    }

    public function checkStatusTaskRequest(Request $req)
    {
        $accountId = (int) $req->query('account_id'); // hoặc $request->input('account_id')
        $taskId = (int) $req->query('task_id');

        $requests = $this->requestUnlockRepo->hasApprovedRequest($accountId, $taskId);

        return response()->json([
            'data' => $requests,
        ]);
    }

    public function showVocabularyStats(Request $request)
    {
        try {
            $searchQuery = $request->query('search');
            $cleanSearchQuery = htmlentities($searchQuery, ENT_QUOTES | ENT_SUBSTITUTE, 'UTF-8');
            $page = (int) $request->query('page', 1);
            $perPage = (int) $request->query('per_page', 10);
            $accountId = Auth::guard('web')->id();
            $result = $this->accountTaskAnswerRepository->getLearnedVocabularies(
                $accountId,
                $page,
                $perPage,
                $cleanSearchQuery
            );
            return response()->json([
                'data' => $result['data'],
                'meta' => $result['meta'],
            ]);
        } catch (Exception $e) {
            return response()->json(
                [
                    'message' => 'Something went wrong',
                ],
                500
            );
        }
    }

    public function updateVocabularyCount(Request $request)
    {
        $accountId = auth('web')->user()->id;
        $vocabularyId = $request->input('vocabulary_id');

        $record = VocabularyCount::firstOrNew([
            'account_id'    => $accountId,
            'vocabulary_id' => $vocabularyId,
        ]);

        $record->count = ($record->count ?? 1) + 1;
        $record->save();

        return response()->json([
            'success' => true,
            'data' => $record,
        ]);

    }
}
