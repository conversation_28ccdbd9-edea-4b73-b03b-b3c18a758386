<?php

namespace App\Repositories\User;

use App\Models\User;
use App\Repositories\Repository;

class UserRepository extends Repository
{
  public function __construct(User $model)
  {
    parent::__construct($model);
  }

  public function filter($attribute = [])
  {
//    $status = $attribute['status'] ?? null;
//    $keyword = $attribute['keyword'] ?? null;
//    $users = $this->model
//      ->when($keyword, function ($query) use ($keyword) {
//        $query->where(function ($query) use ($keyword) {
//          $query->where('email', 'LIKE', '%' . $keyword . '%')
//            ->orWhere('name', 'LIKE', '%' . $keyword . '%');
//        });
//      })
//      ->when($status, function ($query) use ($status) {
//        $query->where('status', $status);
//      })
//      ->orderBy('id')
//      ->paginate(10)
//      ->withQueryString();
//
//    return $users;
  }

}
