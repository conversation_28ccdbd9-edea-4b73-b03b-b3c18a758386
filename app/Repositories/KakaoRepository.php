<?php

namespace App\Repositories;

use App\Models\KakaoToken;
use Carbon\Carbon;
use Illuminate\Support\Facades\Http;

class KakaoRepository
{
    protected string $restApiKey;
    protected string $redirectUri;

    public function __construct()
    {
        $this->restApiKey = config('services.kakao.rest_api_key'); 
        $this->redirectUri = config('services.kakao.redirect_uri'); 
    }

    /**
     * get access token from authorization code
     */
    public function getAccessToken(string $code): ?string
    {
        $response = Http::asForm()->post('https://kauth.kakao.com/oauth/token', [
            'grant_type' => 'authorization_code',
            'client_id' => $this->restApiKey,
            'redirect_uri' => $this->redirectUri,
            'code' => $code,
        ]);

        if ($response->successful()) {
            $data = $response->json();

            if (isset($data['access_token'])) {
                $token = KakaoToken::first();

                if ($token) {
                    $token->update([
                        'access_token' => $data['access_token'],
                        'refresh_token' => $data['refresh_token'] ?? $token->refresh_token,
                        'access_token_expires_at' => Carbon::now()->addSeconds($data['expires_in'] ?? 0),
                        'refresh_token_expires_at' => Carbon::now()->addSeconds($data['refresh_token_expires_in'] ?? 0),
                    ]);
                } else {
                    $token = KakaoToken::create([
                        'access_token' => $data['access_token'],
                        'refresh_token' => $data['refresh_token'] ?? null,
                        'access_token_expires_at' => Carbon::now()->addSeconds($data['expires_in'] ?? 0),
                        'refresh_token_expires_at' => Carbon::now()->addSeconds($data['refresh_token_expires_in'] ?? 0),
                    ]);
                }

                return $token->access_token;
            }
        }

        return null;
    }


    public function getFriends(int $offset = 0, int $limit = 50): array
    {
        $token = KakaoToken::first();
        if (!$token) {
            return [];
        }

        $now = Carbon::now();
        $accessToken = $token->access_token;

        if ($now->gte($token->access_token_expires_at) && $now->lt($token->refresh_token_expires_at)) {
            $newData = $this->refreshAccessToken($token->refresh_token);

            if (isset($newData['access_token'])) {
                $token->update([
                    'access_token' => $newData['access_token'],
                    'access_token_expires_at' => $now->addSeconds($newData['expires_in'] ?? 0),
                    'refresh_token' => $newData['refresh_token'] ?? $token->refresh_token,
                    'refresh_token_expires_at' => $now->addSeconds($newData['refresh_token_expires_in'] ?? 0),
                ]);

                $accessToken = $newData['access_token'];
            } else {
                return ['error' => 'Login required'];
            }
        }

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $accessToken,
            ])->get('https://kapi.kakao.com/v1/api/talk/friends', [
                'offset' => $offset,
                'limit' => $limit,
                ]);

            if ($response->successful()) {
                $data = $response->json();
                return isset($data['elements']) && is_array($data['elements']) ? $data['elements'] : [];
            }
        } catch (\Exception $e) {
            //  $e->getMessage()
        }

        return [];
    }

    /**
     * Refresh access token using refresh token
     */
    protected function refreshAccessToken(): ?string
    {
        $token = KakaoToken::first();
        if (!$token) return null;

        $now = now();

        // Kakao refresh
        $response = Http::asForm()->post('https://kauth.kakao.com/oauth/token', [
            'grant_type' => 'refresh_token',
            'client_id' => $this->restApiKey,
            'refresh_token' => $token->refresh_token,
        ]);

        if ($response->successful()) {
            $data = $response->json();

            if (isset($data['access_token'])) {
                $token->update([
                    'access_token' => $data['access_token'],
                    'access_token_expires_at' => $now->addSeconds($data['expires_in'] ?? 0),
                ]);

                return $data['access_token'];
            }
        }

        return null; // refresh failed
    }

    public function checkToken()
    {
        $token = KakaoToken::first(); 
        if (!$token) {
            return ['valid' => false, 'error' => 'No token found'];
        }

        $now = Carbon::now();

        // if access_token expired but refresh_token is still valid
        if ($now->gte($token->access_token_expires_at) && $now->lt($token->refresh_token_expires_at)) {
            $newData = $this->refreshAccessToken($token->refresh_token);

            if (isset($newData['access_token'])) {
                $token->update([
                    'access_token' => $newData['access_token'],
                    'access_token_expires_at' => $now->copy()->addSeconds($newData['expires_in'] ?? 0),
                    'refresh_token' => $newData['refresh_token'] ?? $token->refresh_token,
                    'refresh_token_expires_at' => $now->copy()->addSeconds($newData['refresh_token_expires_in'] ?? 0),
                ]);
            } else {
                return ['valid' => false, 'error' => 'Login required'];
            }
        }

        // if refresh_token also expired
        if ($now->gte($token->refresh_token_expires_at)) {
            return ['valid' => false, 'error' => 'Login required'];
        }

        // OK 
        return ['valid' => true];
    }

    public function sendMessage(array $payload)
    {
        $token = KakaoToken::latest()->first();
        if (!$token) {
            return ['error' => 'No token found'];
        }

        $response = Http::withToken($token->access_token)
            ->asForm()
            ->post('https://kapi.kakao.com/v1/api/talk/friends/message/send', [
                'receiver_uuids'   => json_encode($payload['receiver_uuids'] ?? []),
                'template_object'  => json_encode($payload['template_object'] ?? []),
                'template_id'      => $payload['template_id'] ?? null,
            ]);

        return $response->json();
    }
}
