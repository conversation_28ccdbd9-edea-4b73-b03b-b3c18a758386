<?php

namespace App\Repositories;
use Prettus\Repository\Eloquent\BaseRepository;

class AdminRepository extends BaseRepository {

  function model()
  {
    return "App\\Models\\Admin";
  }

  public function filter($attribute = []){
    $keyword = $attribute['keyword'] ?? null;
    $date = $attribute['date'] ?? null;
    $status = $attribute['status'] ?? null;
    $arrayFilter = ['id','name','last_name','email','role','created_at','status_sort'];

    $column = 'id';
    $type = 'asc';
    foreach ($attribute as $key => $value) {
      if (($value === 'desc' || $value === 'asc') && in_array($key,$arrayFilter)) {
          $column = $key;
          $type = $value;
          if($key == 'status_sort'){
            $column = 'status';
          }
          break;
      }
    }
    $keyword =  convertKeyword($keyword);
    $users = $this->model->with('classes')
      ->when($keyword, function ($query) use ($keyword) {
        $query->where(function ($query) use ($keyword) {
          $query->where('name', 'LIKE', '%' . $keyword . '%')
            ->orWhere('last_name', 'LIKE', '%' . $keyword . '%');
        });
      })
      ->when($date, function ($query) use ($date) {
        $query->whereDate('created_at', '=', $date);
      })
      ->when($status, function ($query) use ($status) {
        $query->where('status', $status);
      })
      ->orderBy($column,$type)
      ->paginate(10)
      ->withQueryString();

    return $users;
  }

}
