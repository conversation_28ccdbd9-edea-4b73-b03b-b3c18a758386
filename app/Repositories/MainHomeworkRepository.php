<?php

namespace App\Repositories;

use App\Enums\TypeExam;
use App\Models\Account;
use App\Models\AccountHomework;
use Illuminate\Pagination\LengthAwarePaginator;
use Prettus\Repository\Eloquent\BaseRepository;

class MainHomeworkRepository extends BaseRepository
{
    function model()
    {
        return 'App\\Models\\MainHomeworks';
    }

    public function getHomeworkResult($params)
    {
        $perPage = $params['per_page'] ?? 10;
        return $this->model
            ->whereHas('homeworks.accountHomeworks', function ($query) {
                $query->whereNotNull('end_time');
            })
            ->with([
                'getClass:id,name',
                'homeworks' => function ($query) {
                    $query->whereHas('accountHomeworks', function ($subQuery) {
                        $subQuery->whereNotNull('end_time');
                    })
                        ->with([
                            'accountHomeworks' => function ($subQuery) {
                                $subQuery->whereNotNull('end_time')
                                    ->with('account');
                            },
                        ]);
                }
            ])
            ->paginate($perPage);
    }

    /**
     * get list MainHomeworks and Homework of Account,
     * @param int $accountId
     * @param string|null $searchQuery
     * @param int $perPage
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public function getMainHomeworksWithAccountHomeworks(
        int $accountId,
        ?string $searchQuery = null,
        int $perPage = 10,
        bool $isResult = false,
        $type = TypeExam::HOMEWORK
    ): LengthAwarePaginator {
        $account = Account::findOrFail($accountId);

        $classIds = $account->classes()->pluck('classes.id');
        $existingHomeworkIds = AccountHomework::where('account_id', $accountId)
            ->whereNotNull('end_time')
            ->pluck('homework_id');
   
        $query = $this->model
            ->query()
            ->whereHas('getClass', function ($query) use ($classIds) {
                $query->whereIn('classes.id', $classIds)->where('status', 'active');
            })
            ->when($isResult, function ($q) use ($accountId) {
                $q->whereHas('homeworks', function ($subQ) use ($accountId) {
                    $subQ->whereHas('accountHomeworks', function ($subSubQ) use ($accountId) {
                        $subSubQ->where('account_id', $accountId)
                                ->whereNotNull('end_time');
                    });
                });
            })
            ->when($type == TypeExam::CLASSWORK, function ($q) {
                $q->whereHas('homeworks', function ($subQ) {
                    $subQ->where('type', TypeExam::CLASSWORK);
                });
            })
            ->with([
                'homeworks' => function ($q) use ($existingHomeworkIds, $isResult, $accountId, $type) {
                    $q->where('status', 'active')
                    ->when(!$isResult, function ($query) use ($type) {
                        $query->where('type', $type);
                    })
                    ->orderBy('order')
                    ->with([
                        'accountHomeworks' => function ($subQ) use ($accountId) {
                            $subQ->where('account_id', $accountId);
                        },
                        'unlockRequest'
                    ]);
                    if ($isResult) {
                        $q->whereIn('id', $existingHomeworkIds);
                    }
                },
                'getClass:id,name', //Only select id và name on classes
            ])
            ->orderByDesc('id');

        if ($searchQuery) {
            $escaped = addcslashes($searchQuery, '%_'); // escape % và _
            $query->where(function ($q) use ($escaped) {
                $q->where('main_homeworks.title', 'like', "%{$escaped}%")
                ->orWhere('main_homeworks.description', 'like', "%{$escaped}%")
                ->orWhereHas('homeworks', function ($homeworkQuery) use ($escaped) {
                    $homeworkQuery->where(function ($subQ) use ($escaped) {
                        $subQ->where('homeworks.title', 'like', "%{$escaped}%")
                            ->orWhere('homeworks.description', 'like', "%{$escaped}%");
                    });
                });
            });
        }

        return $query->paginate($perPage);
    }

    public function getMainHomeworksByHomeWorkId($accountId, $homeworkId)
    {
        $account = Account::findOrFail($accountId);

        $classIds = $account->classes()->pluck('classes.id');

        $query = $this->model
            ->query()
            ->whereHas('getClass', function ($query) use ($classIds) {
                $query->whereIn('classes.id', $classIds);
            })
            ->whereHas('homeworks', function ($q) use ($homeworkId) {
                $q->where('id', $homeworkId);
            })
            ->with([
                'homeworks' => function ($q) use ($homeworkId) {
                    $q->where('id', $homeworkId);
                },
                'getClass:id,name', //Only select id và name on classes
            ])
            ->first();

        return $query?->homeworks?->first();
    }


    /**
     * Get list MainHomeworks and Homeworks of multiple accounts
   *
   * @param int $userId
   * @param string|null $searchQuery
   * @param int $perPage
   * @param bool $isResult
   * @return \Illuminate\Pagination\LengthAwarePaginator
   */
    public function getMainHomeworksWithAccountsHomeworks(
        int $userId,
        ?string $searchQuery = null,
        int $perPage = 10,
        bool $isResult = false,
        $type=TypeExam::HOMEWORK
    ): LengthAwarePaginator {
        $accounts = Account::where('user_id', $userId)->get();
        $accountIds = $accounts->pluck('id')->toArray();
        $classIds = $accounts
            ->flatMap(fn($account) => $account->classes()->pluck('classes.id'))
            ->unique()
            ->values();

        $query = $this->model
            ->query()
            ->whereHas('getClass', fn($query) => $query->whereIn('classes.id', $classIds)->where('status', 'active'))
            ->when($isResult, function ($q) use ($accountIds) {
                // Filter main homeworks with user-submitted homeworks.
                $q->whereHas('homeworks.accountHomeworks', function ($subQ) use ($accountIds) {
                    $subQ->whereIn('account_id', $accountIds)->whereNotNull('end_time');
                });
            })
            ->with([
                'homeworks' => function ($q) use ($isResult, $accountIds, $type) {
                    $q->where('status', 'active')
                        ->when(!$isResult, function ($query) use ($type) {
                        $query->where('type', $type);
                        })
                        ->orderBy('order')
                        ->with([
                            'accountHomeworks' => function ($subQ) use ($accountIds, $isResult) {
                                $subQ->whereIn('account_id', $accountIds)
                                    ->when($isResult, fn($q) => $q->whereNotNull('end_time'))
                                    ->with('account');
                            },
                            'unlockRequest'
                        ])
                        ->when($isResult, function ($q) use ($accountIds) {
                            //Only fetch submitted homeworks by the user.
                            $q->whereHas('accountHomeworks', function ($q) use ($accountIds) {
                                $q->whereIn('account_id', $accountIds)
                                    ->whereNotNull('end_time');
                            });
                        });
                },
                'getClass:id,name',
                'getClass.students' => function ($q) use ($userId, $isResult) {
                    $q->where('user_id', $userId)
                        ->select('accounts.id', 'accounts.user_name', 'accounts.user_id')
                        ->when($isResult, function ($query) {
                            $query->whereHas('accountHomeworks', function ($subQ) {
                                $subQ->whereNotNull('end_time');
                            });
                        });
                }
            ])
            ->orderByDesc('id');

        if ($searchQuery) {
            $escaped = addcslashes($searchQuery, '%_');
            
            $query->where(function ($q) use ($escaped) {
                $q->where('main_homeworks.title', 'like', "%{$escaped}%")
                ->orWhere('main_homeworks.description', 'like', "%{$escaped}%")
                ->orWhereHas('homeworks', function ($homeworkQuery) use ($escaped) {
                    $homeworkQuery->where(function ($subQ) use ($escaped) {
                        $subQ->where('homeworks.title', 'like', "%{$escaped}%")
                            ->orWhere('homeworks.description', 'like', "%{$escaped}%");
                    });
                });
            });
        }

        return $query->paginate($perPage);
    }
}
