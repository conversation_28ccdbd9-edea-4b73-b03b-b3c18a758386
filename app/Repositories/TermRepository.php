<?php

namespace App\Repositories;
use Illuminate\Http\Request;
use Prettus\Repository\Eloquent\BaseRepository;

class TermRepository extends BaseRepository {

  function model()
  {
    return "App\\Models\\Term";
  }

  public function filter($attribute = []){
    $keyword = $attribute['type'] ?? null;
    $column = 'created_at';
    $column1 = 'created_at';
    $typeSort = 'desc';
    $arrayFilter = ['name','start','end'];
    foreach ($attribute as $key => $value) {
      if (($value === 'desc' || $value === 'asc') && in_array($key,$arrayFilter)) {
        $typeSort = $value;
        $column = $key;
        if($key  == 'name'){
          $column = 'year';
          $column1 = 'type';
        }
      }
    }
    $terms = $this->model->query();
    if ($keyword) {
      $terms->where('type', $keyword);
    }
    return  $terms->with('getClass', 'classes')->orderBy($column,$typeSort)->orderBy($column1,$typeSort)->paginate(10);
  }

}
