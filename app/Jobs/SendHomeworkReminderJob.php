<?php

namespace App\Jobs;

use App\Models\Account;
use App\Services\KakaoMessageService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class SendHomeworkReminderJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

     public function handle(KakaoMessageService $kakaoMessageService)
    {
        $now = Carbon::now()->format('H:i');
        $accounts = Account::where('kakao_message_time', $now)
            ->where('status', 'active')
            ->where('type', 'student')
             ->with([
                'classes.mainHomeworks.homeworks' => function ($q) {
                    $q->where('type', 1) // only homework type=1
                    ->with(['accountHomeworks.accountHomeworkTasks']);
                }
            ])
            ->get();

        foreach ($accounts as $account) {
                    $parent = $account->parent;
                    if (!$parent) {
                        Log::info("Account ID {$account->id} has no parent. Skipping.");
                        continue;
                    }

                    $completedParts = [];
                    $incompleteParts = [];
                    $hasIncompleteWork = false;

                    foreach ($account->classes as $class) {
                        foreach ($class->mainHomeworks as $mainHomework) {
                            foreach ($mainHomework->homeworks as $homework) {
                                $hwName = $homework->title ? $homework->title: "HW" . $homework->id;
                                
                                $totalHomeworkTasks = $homework->taskHomeworks->count();
                                if ($totalHomeworkTasks === 0) {
                                    continue;
                                }
                                $accountHomework = $homework->accountHomeworks
                                    ->where('account_id', $account->id)
                                    ->first();
                                $completedTasks = 0;
                                if ($accountHomework) {
                                    $totalTasks = $accountHomework->accountHomeworkTasks->count();
                                    $completedTasks = $accountHomework->accountHomeworkTasks
                                        ->whereNotNull('end_time')
                                        ->count();
                                }else {
                                    $totalTasks = $totalHomeworkTasks;
                                    $completedTasks = 0;
                                }
                                if ($completedTasks < $totalTasks) {
                                        $hasIncompleteWork = true;
                                        // completed summary
                                        $completedParts[] = "{$completedTasks}/{$totalTasks} Tasks for {$hwName}";
                                        // incomplete summary
                                        $incompleteParts[] = "{$hwName} ({$completedTasks}/{$totalTasks})";
                                    } else {
                                        $completedParts[] = "{$hwName}";
                                    }
                            }
                        }
                    }

                    if ($hasIncompleteWork) {
                            $completedText = implode(', ', $completedParts);
                            $incompleteText = implode(', ', $incompleteParts);

                            $message_kr  = "학생이 다음 과제를 완료했습니다:\n";
                            $message_kr .= "[{$completedText}]\n\n";

                            $message_kr .= "아직 완료되지 않은 과제:\n";
                            $message_kr .= "[{$incompleteText}]\n\n";

                            $message_kr .= "수업 전까지 완료할 수 있도록 지도 부탁드립니다.";


                        $kakaoMessageService->sendKakaoMessagesForParentOfStudent($parent, $message_kr);
                        Log::info("Sending message to parent of account ID {$account->id}: {$message_kr}");
                    }
                }

        }
    }
