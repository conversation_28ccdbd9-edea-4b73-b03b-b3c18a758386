<?php

namespace App\Jobs;

use App\Models\Invoice;
use App\Services\KakaoMessageService;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class InvoiceUnpaidWeeklyReminderJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function handle(KakaoMessageService $kakaoMessageService)
    {
        $unpaidInvoices = Invoice::with('parent')
            ->where('status', 'unpaid')
            ->get();
        $invoicesByParent = $unpaidInvoices->groupBy('parent_id');
        foreach ($invoicesByParent as $parentId => $invoices) {
            $parent = $invoices->first()->parent;
            if (!$parent) continue;

            try {
                $message_kr  = "결제가 확인 되지 않았습니다.\n";
                $message_kr .= "결제 여부 확인 후 결제 부탁드립니다.\n";
                $message_kr .= "이미 결제 완료 상태라면 어드민에게 문의 부탁드립니다.";

                $kakaoMessageService->sendKakaoMessagesForParentOfStudent($parent, $message_kr);

                // Log::info("Weekly unpaid reminder sent to parent {$parent->id} for invoice {$invoice->id}");
            } catch (\Exception $e) {
                Log::error("Failed to send weekly reminder for invoice: " . $e->getMessage());
            }
        }
    }
}
