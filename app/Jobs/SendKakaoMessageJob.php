<?php

namespace App\Jobs;

use App\Repositories\KakaoRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class SendKakaoMessageJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected array $payload;

    /**
     * Create a new job instance.
     */
    public function __construct(array $payload)
    {
        $this->payload = $payload;
    }

    /**
     * Execute the job.
     */
    public function handle(KakaoRepository $kakaoRepository)
    {
        try {
             if (empty($this->payload)) {
                Log::error('SendKakaoMessageJob: Payload is empty. Cannot send message.');
                return; 
            }
            $response = $kakaoRepository->sendMessage($this->payload);
            if (isset($response['error'])) {
                Log::error($response);
            } else {
                Log::info($response);
            }
        } catch (\Exception $e) {
            Log::error('Exception when sending Kakao message: '.$e->getMessage());
        }
    }
}
