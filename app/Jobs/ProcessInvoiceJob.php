<?php

namespace App\Jobs;

use App\Models\Invoice;
use App\Services\InvoiceService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class ProcessInvoiceJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $invoices;

    /**
     * Create a new job instance.
     */
    public function __construct($invoices)
    {
        $this->invoices = $invoices;
    }

    /**
     * Execute the job.
     */
    public function handle()
    {
        if (is_array($this->invoices)) {
            foreach ($this->invoices as $invoice) {
                try {
                    $this->processInvoice($invoice);
                } catch (\Exception $e) {
                    \Log::error('Invoice job failed', [
                        'invoice_id' => $invoice['invoice_id'],
                        'error' => $e->getMessage() . ' - ' . $e->getLine() . ' - ' . $e->getFile(),
                        'memory_usage' => memory_get_peak_usage(true) / 1024 / 1024 . 'MB'
                    ]);
                    $invoice = Invoice::find($invoice['invoice_id']);
                    $invoice->update([
                        'processing_status' => 'Failed' . $e->getMessage() . ' - ' . $e->getLine() . ' - ' . $e->getFile(),
                    ]);
                }
            }
        }
    }

    private function processInvoice($invoiceData)
    {
        $options = app(InvoiceService::class)->getOptions();
        $invoice = Invoice::with(['parent','students'])->find($invoiceData['invoice_id']);
        $options['type'] = 'batch';
        $options['student'] = $invoice->students->pluck('student.name_english')->implode(', ');
        $parent = $invoice->parent;

        $invoiceService = app(InvoiceService::class);
        $order = $invoiceService->createOrderWp($invoiceData['product_ids'], $parent);

        if (!$order || !$order->id) {
            throw new \Exception('Failed to create WooCommerce order');
        }

        $payLink = config('app.wphost') . '/checkout/order-pay/' . $order->id .
            '/?pay_for_order=true&key=' . $order->order_key;

        $pdf = $invoiceService->getInvoicePdf($parent, $invoiceData['product_ids'], $options, $payLink);

        if (!$pdf) {
            throw new \Exception('Failed to generate PDF');
        }

        $invoiceDir = storage_path('app/public/invoices/');
        if (!file_exists($invoiceDir)) {
            mkdir($invoiceDir, 0755, true);
        }

        $fileName = 'invoice_' . time() . '_' . $invoice->id . '.pdf';
        $filePath = $invoiceDir . $fileName;

        $pdf->save($filePath);

        if (!file_exists($filePath)) {
            throw new \Exception('PDF file was not saved successfully');
        }

        $invoice->update([
            'order_id' => $order->id,
            'order_key' => $order->order_key,
            'url_invoice' => '/storage/invoices/' . $fileName,
            'processing_status' => 'Completed',
        ]);

        unset($pdf, $invoiceService, $options);
    }


    public function failed(\Exception $exception)
    {
        \Log::error('Invoice job failed permanently', [
            'error' => $exception->getMessage(),
            'memory_usage' => memory_get_peak_usage(true) / 1024 / 1024 . 'MB'
        ]);
    }

}
