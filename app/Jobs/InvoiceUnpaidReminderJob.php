<?php

namespace App\Jobs;

use App\Models\ClassSchedule;
use App\Services\KakaoMessageService;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class InvoiceUnpaidReminderJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function handle(KakaoMessageService $kakaoMessageService)
    {
        $now = Carbon::now();
        $nextHour = $now->copy()->addHour();

        $upcomingSchedules = ClassSchedule::with([
                'classes.students.parent.invoices' => function($q) {
                    $q->where('status', 'unpaid');
                }
            ])
            ->whereBetween(
            DB::raw("STR_TO_DATE(CONCAT(date, ' ', start), '%Y-%m-%d %H:%i:%s')"),
            [$now, $nextHour]
            )
            ->whereNull('first_reminder_sent_at')
            ->get();

        foreach ($upcomingSchedules as $schedule) {
            foreach ($schedule->classes->students as $student) {
                $parent = $student->parent;
                if (!$parent) continue;

                $unpaidInvoices = $parent->invoices;
                if ($unpaidInvoices->isNotEmpty()) {
                    $message_kr  = "결제가 확인 되지 않았습니다.\n";
                    $message_kr .= "결제가 확인 되지 않으면 수업 참가에 어려움이 있을 수 있는 점 양해 부탁드립니다.";
                    $kakaoMessageService->sendKakaoMessagesForParentOfStudent($parent, $message_kr);

                    // Log::info("Invoice unpaid reminder sent to parent {$parent->id} for invoice {$invoice->id}");
                }
            }
            $schedule->update([
                'first_reminder_sent_at' => Carbon::now()
            ]);
        }
    }
}
