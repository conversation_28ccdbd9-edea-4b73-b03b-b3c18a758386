<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class ActiveAccountMail extends Mailable
{
    use Queueable, SerializesModels;
    public $dataParent;
    public $dataStudent;
    public $password;

    /**
     * Create a new message instance.
     */
    public function __construct($dataParent,$dataStudent,$password)
    {
      $this->dataParent = $dataParent;
      $this->dataStudent = $dataStudent;
      $this->password = $password;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Q1 Academy: Your account has been reactivated',
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
          view: 'admin.auth.activeAccount',
          with: [
            'dataParent' => $this->dataParent,
            'dataStudent' => $this->dataParent,
            'password' => $this->password,
          ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
