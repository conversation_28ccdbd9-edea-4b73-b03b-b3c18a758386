<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class MailForgotPasswordWeb extends Mailable
{
    use Queueable, SerializesModels;
    public  $token;
    public  $user;

    /**
     * Create a new message instance.
     */
  public function __construct($token,$user)
  {
    $this->token = $token;
    $this->user = $user;
  }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
      return new Envelope(
        subject: 'Password reset at ' . config('app.name'),
      );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
      return new Content(
        view: 'web.auth.forgot-password-email',
        with: [
          'token' => $this->token,
          'user' => $this->user,
        ],
      );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
