<?php declare(strict_types=1);

namespace App\Enums;

use BenSampo\Enum\Enum;

final class Session extends Enum
{
    const S1_S4   = 1;
    const S5_S8   = 5;
    const S9_S12  = 9;
    const S13_S16 = 13;

    public static function list(): array
    {
        return [
            self::S1_S4   => 'S1-S4',
            self::S5_S8   => 'S5-S8',
            self::S9_S12  => 'S9-S12',
            self::S13_S16 => 'S13-S16',
        ];
    }
}
