meta:
  title: "Engineering Checklist (PHP & JS)"
  default_require_fail_policy: "ignore"  # mặc định: require_pattern không match -> bỏ qua
  fail_on: ["must"]                      # chỉ fail khi MUST vi phạm (với forbid_pattern / files_changed_require)

rules:
  # =========================
  # 1) NAMING CONVENTIONS
  # =========================

  - id: naming_no_vague_vars
    level: must
    type: forbid_pattern
    message: "Không đặt tên chung chung: data, temp, value, foo, x, y."
    patterns:
      - "\\b(data|temp|value|foo|bar|baz|x|y)\\b"
    include: ["\\.(js|jsx|ts|tsx|php)$"]

  - id: naming_no_ambiguous_abbrev
    level: should
    type: forbid_pattern
    message: "Tránh viết tắt khó hiểu: usr, tmstp, cfg, infoObj..."
    patterns:
      - "\\b(usr|tmstp|cfg|obj|dto|svc|mgr)\\b"
    include: ["\\.(js|jsx|ts|tsx|php)$"]

  - id: naming_pascalcase_type
    level: must
    type: require_pattern
    require_fail_policy: "warn"   # nếu MR không chạm hoặc không match -> cảnh báo, không fail
    message: "Class/Interface phải PascalCase (UserController, OrderService...)."
    patterns:
      - "(?m)^\\s*(class|interface)\\s+[A-Z][A-Za-z0-9]+"
    include: ["\\.(php|ts|tsx|js|jsx)$"]

  - id: naming_camelcase_var_fn
    level: should
    type: forbid_pattern
    message: "Biến/Hàm JS/TS nên camelCase (tránh snake_case)."
    patterns:
      - "\\b([a-z0-9]+_[a-z0-9_]+)\\s*="
      - "function\\s+([a-z0-9]+_[a-z0-9_]+)\\s*\\("
    include: ["\\.(js|jsx|ts|tsx)$"]

  - id: naming_uppercase_constants
    level: should
    type: require_pattern
    require_fail_policy: "warn"
    message: "Hằng số nên UPPER_CASE (MAX_RETRY_COUNT...)."
    patterns:
      - "(?m)^\\s*const\\s+[A-Z0-9_]+\\s*="
      - "(?i)define\\s*\\(\\s*['\"][A-Z0-9_]+['\"]\\s*,"
    include: ["\\.(js|jsx|ts|tsx|php)$"]

  - id: naming_boolean_prefix
    level: should
    type: require_pattern
    require_fail_policy: "ignore"  # gợi ý phong cách đặt tên -> bỏ qua khi không match
    message: "Tên biến boolean nên bắt đầu bằng is/has/should."
    patterns:
      - "\\b(is|has|should)[A-Z][A-Za-z0-9]*\\s*=\\s*(true|false)\\b"
    include: ["\\.(js|jsx|ts|tsx)$"]

  - id: naming_no_hungarian
    level: should
    type: forbid_pattern
    message: "Hạn chế tiền tố kiểu dữ liệu (strName, intCount...)."
    patterns:
      - "\\b(str|int|num|bool|arr|obj|fn)[A-Z][A-Za-z0-9]*\\b"
    include: ["\\.(js|jsx|ts|tsx|php)$"]

  - id: filename_meaningful_hint
    level: should
    type: forbid_pattern
    message: "Tên file nên phản ánh nội dung (Service/Controller/Utils...)."
    patterns:
      - "^$"
    include: ["\\.(js|jsx|ts|tsx|php)$"]

  # =========================
  # 2) FORMATTING & STYLE
  # =========================

  - id: no_tabs
    level: must
    type: forbid_pattern
    message: "Không dùng tab; hãy dùng 2 hoặc 4 spaces."
    patterns: ["\\t"]
    include: ["\\.(js|jsx|ts|tsx|php)$"]

  - id: max_line_length_120
    level: should
    type: forbid_pattern
    message: "Giới hạn độ dài dòng ≤ 120 ký tự."
    patterns: ["^.{121,}$"]
    include: ["\\.(js|jsx|ts|tsx|php)$"]

  - id: whitespace_around_operators
    level: should
    type: forbid_pattern
    message: "Dùng khoảng trắng quanh toán tử (a = b + c)."
    patterns:
      - "\\S=\\S"
      - "\\S\\+\\S"
      - "\\S-\\S"
      - "\\S\\*\\S"
      - "\\S/\\S"
    include: ["\\.(js|jsx|ts|tsx|php)$"]

  - id: trailing_whitespace
    level: must
    type: forbid_pattern
    message: "Không để khoảng trắng thừa ở cuối dòng."
    patterns: ["\\s+$"]
    include: ["\\.(js|jsx|ts|tsx|php)$"]

  - id: no_console_or_print_debug
    level: must
    type: forbid_pattern
    message: "Không để lại log/debug (console.log, print_r, var_dump, die/exit)."
    patterns:
      - "console\\.(log|debug|trace)\\("
      - "\\b(print_r|var_dump)\\s*\\("
      - "\\b(die|exit)\\s*\\("
    include: ["\\.(js|jsx|ts|tsx|php)$"]

  - id: consistent_quotes_via_tools
    level: should
    type: files_changed_require
    message: "Thiết lập formatter/linter để thống nhất dấu nháy (Prettier/ESLint/PHPCS)."
    patterns_any:
      - "\\.prettierrc(\\.(js|cjs|json|yaml|yml))?$"
      - "^prettier\\.config\\.(js|cjs|mjs|ts)$"
      - "\\.eslintrc(\\.(js|cjs|json|yaml|yml))?$"
      - "^phpcs\\.xml(\\.dist)?$"

  - id: formatter_linters_present
    level: must
    type: files_changed_require
    message: "Phải có formatter/linter cho dự án (Prettier/ESLint, PHPCS)."
    patterns_any:
      - "\\.eslintrc(\\.(js|cjs|json|yaml|yml))?$"
      - "^phpcs\\.xml(\\.dist)?$"

  # =========================
  # 3) STRUCTURE & ARCH
  # =========================

  - id: one_main_class_per_file_hint
    level: should
    type: forbid_pattern
    message: "Mỗi file chỉ nên có 1 class/module chính."
    patterns:
      - "(?m)^\\s*class\\s+\\w+.*\\n[\\s\\S]*?^\\s*class\\s+\\w+"
    include: ["\\.(php|ts|tsx|js|jsx)$"]

  - id: avoid_long_files
    level: should
    type: forbid_pattern
    message: "Tránh file quá dài (> 300 dòng)."
    patterns: ["(?s)^(?:.*\\n){301,}"]
    include: ["\\.(php|ts|tsx|js|jsx)$"]

  - id: no_hardcode_secret_or_config
    level: must
    type: forbid_pattern
    message: "Không hard-code secret/credential; dùng biến môi trường/config."
    patterns:
      - "(?i)(api[_-]?key|secret|password|token)\\s*[:=]\\s*['\"][A-Za-z0-9/+=._-]{10,}['\"]"
      - "AKIA[0-9A-Z]{16}"
      - "BEGIN RSA PRIVATE KEY"
    include: ["\\.(js|jsx|ts|tsx|php)$"]

  # =========================
  # 4) COMMENTS & DOCS
  # =========================

  - id: js_phpdoc_before_functions
    level: should
    type: require_pattern
    require_fail_policy: "warn"
    message: "Khuyến nghị JSDoc/PHPDoc (/** ... */) trước function/class quan trọng."
    patterns:
      - "/\\*\\*[^*]*\\*/\\s*(function|class)\\s+"
    include: ["\\.(js|jsx|ts|tsx|php)$"]

  - id: no_obsolete_commented_code
    level: should
    type: forbid_pattern
    message: "Xóa code cũ bị comment thừa thãi."
    patterns:
      - "(?m)^\\s*//\\s*old code"
      - "(?s)/\\*+\\s*old code[\\s\\S]*?\\*/"
    include: ["\\.(js|jsx|ts|tsx|php)$"]

  - id: no_sensitive_info_in_comments
    level: must
    type: forbid_pattern
    message: "Không comment thông tin nhạy cảm (key/mật khẩu)."
    patterns:
      - "(?i)(api[_-]?key|secret|password|token)\\s*[:=]\\s*['\"][A-Za-z0-9/+=._-]{10,}['\"]"
    include: ["\\.(js|jsx|ts|tsx|php)$"]

  - id: allow_todo_fixme
    level: should
    type: require_pattern
    require_fail_policy: "warn"
    message: "TODO/FIXME được ghi chú rõ ràng vị trí cần làm."
    patterns:
      - "(?i)\\b(TODO|FIXME)\\b"
    include: ["\\.(js|jsx|ts|tsx|php)$"]

  # =========================
  # 5) ERROR HANDLING & LOGGING
  # =========================

  - id: no_eval_anywhere
    level: must
    type: forbid_pattern
    message: "Cấm dùng eval/Function constructor."
    patterns:
      - "\\beval\\s*\\("
      - "new\\s+Function\\s*\\("
    include: ["\\.(js|jsx|ts|tsx)$"]

  - id: php_no_eval
    level: must
    type: forbid_pattern
    message: "Cấm dùng eval() trong PHP."
    patterns: ["\\beval\\s*\\("]
    include: ["\\.php$"]

  - id: prefer_logging_framework
    level: should
    type: require_pattern
    require_fail_policy: "warn"
    message: "Ưu tiên logging framework (PSR-3, winston/pino...) thay vì console.log/echo."
    patterns:
      - "\\\\Psr\\\\Log\\\\LoggerInterface|->(debug|info|warn|error)\\("
      - "require\\(['\"]winston['\"]\\)|from\\s+['\"]pino['\"]"
    include: ["\\.(php|js|ts)$"]

  # =========================
  # 6) SECURITY BASICS
  # =========================

  - id: no_document_write
    level: should
    type: forbid_pattern
    message: "Tránh document.write()."
    patterns: ["document\\.write\\s*\\("]
    include: ["\\.(js|jsx|ts|tsx)$"]

  - id: no_innerhtml_assign
    level: should
    type: forbid_pattern
    message: "Tránh gán trực tiếp innerHTML (dễ XSS)."
    patterns: ["\\.innerHTML\\s*="]
    include: ["\\.(js|jsx|ts|tsx)$"]

  - id: php_sql_concat_forbidden
    level: must
    type: forbid_pattern
    message: "Không nối chuỗi tạo SQL trong PHP; dùng prepared statements."
    patterns:
      - "\\$\\w+\\s*\\.\\s*['\\\"]\\s*(SELECT|UPDATE|INSERT|DELETE)\\b"
      - "\\$pdo->query\\("
    include: ["\\.php$"]

  - id: php_use_prepared_statements
    level: should
    type: require_pattern
    require_fail_policy: "warn"
    message: "Khuyến nghị PDO prepared (->prepare/->bindParam)."
    patterns:
      - "\\$pdo->prepare\\s*\\("
      - "->bindParam\\s*\\("
    include: ["\\.php$"]

  - id: php_escape_output_html
    level: should
    type: require_pattern
    require_fail_policy: "warn"
    message: "Escape output (htmlspecialchars) khi render HTML."
    patterns: ["htmlspecialchars\\s*\\("]
    include: ["\\.php$"]

  - id: avoid_http_links
    level: should
    type: forbid_pattern
    message: "Tránh http:// (không an toàn)."
    patterns: ["http://"]
    include: ["\\.(js|jsx|ts|tsx|php)$"]

  # =========================
  # 7) TOOLING & POLICY
  # =========================

  - id: eslint_prettier_phpcs_required
    level: must
    type: files_changed_require
    message: "Bắt buộc có ESLint/Prettier/PHPCS trong repo."
    patterns_any:
      - "\\.eslintrc(\\.(js|cjs|json|yaml|yml))?$"
      - "\\.prettierrc(\\.(js|cjs|json|yaml|yml))?$"
      - "^prettier\\.config\\.(js|cjs|mjs|ts)$"
      - "^phpcs\\.xml(\\.dist)?$"

  - id: ts_avoid_any
    level: should
    type: forbid_pattern
    message: "TypeScript tránh dùng 'any' không cần thiết."
    patterns:
      - ":\\s*any\\b"
      - "<any>"
    include: ["\\.(ts|tsx)$"]

  - id: js_use_strict_mode_hint
    level: should
    type: require_pattern
    require_fail_policy: "ignore"  # tooling hiện đại thường tự strict -> bỏ qua cho nhẹ
    message: "Khuyến nghị 'use strict' (nếu không dùng bundler hiện đại)."
    patterns:
      - "^['\"]use strict['\"];"
    include: ["\\.(js|jsx)$"]

  # =========================
  # 8) GIT FLOW & MR HYGIENE
  # =========================

  - id: mr_description_required
    level: must
    type: mr_description_required
    message: "MR phải có mô tả ≥ 20 ký tự."

  - id: branch_naming_convention
    level: should
    type: forbid_pattern
    message: "Nhánh nên theo quy tắc (feature/, bugfix/, hotfix/...)."
    patterns:
      - "^(?!feature\\/|bugfix\\/|hotfix\\/).+"
    include: ["^$"]

  - id: require_lockfiles
    level: must
    type: files_changed_require
    message: "Commit lockfiles (package-lock.json/yarn.lock/composer.lock)."
    patterns_any:
      - "^package-lock\\.json$"
      - "^yarn\\.lock$"
      - "^composer\\.lock$"

  - id: changelog_present
    level: should
    type: files_changed_require
    message: "Khuyến nghị có CHANGELOG.md và tag release chuẩn SemVer."
    patterns_any:
      - "^CHANGELOG\\.md$"
