image: lorisleiva/laravel-docker:8.2

stages:
  - build
  - deploy
  - review

code_review:
  stage: review
  image: node:20
  tags:
    - docker
  variables:
    DANGER_GITLAB_API_TOKEN: $GITLAB_TOKEN   # tạo CI/CD variable tên GITLAB_TOKEN
  script:
    - npm ci
    - npx danger ci --dangerfile dangerfile.mjs
  artifacts:
    when: always
    paths:
      - danger-report.md
    expire_in: 7 days
  only:
    - merge_requests
  cache:
    key: ${CI_COMMIT_REF_SLUG}
    paths:
      - node_modules/

composer:

  # The job's stage (build, test or deploy).
  stage: build

  # What to run on the job.
  script:
    - composer install --prefer-dist --no-ansi --no-interaction --no-progress --no-scripts
    - npm install
    - cp .env.example .env
    - php artisan key:generate
    - npm run production

  tags:
    - docker

  artifacts:

    # (Optional) Give it an expiration date,
    # after that period you won't be able to
    # download them via the UI anymore.
    expire_in: 1 month

    # Define what to output from the job.
    paths:
      - vendor/
      - public/js
      - public/css
      - .env

  cache:

    # The variable CI_COMMIT_REF_SLUG
    # refers to the slug of the branch.
    # For example: `master` for the master branch.
    # We use the `composer` suffix to avoid conflicts with
    # the `npm` cache that we'll define next.
    key: ${CI_COMMIT_REF_SLUG}-build

    # Define what to cache.
    paths:
      - vendor/
      - node_modules/

  only:
    - develop

deploy_develop:
  stage: deploy
  tags:
    - docker
  script:
    - 'which ssh-agent || ( apt-get update -y && apt-get install openssh-client -y )'
    - eval $(ssh-agent -s)
    - ssh-add <(echo "$SSH_PRIVATE_KEY")
    - mkdir -p ~/.ssh
    - '[[ -f /.dockerenv ]] && echo -e "Host *\n\tStrictHostKeyChecking no\n\n" > ~/.ssh/config'
    - envoy run deploy --commit="$CI_COMMIT_SHA" --branch="develop"
  environment:
    name: staging
    url: https://q1-academy.nws-dev.com
  only:
    - develop


deploy_staging:
  stage: deploy
  when: manual
  tags:
    - docker
  script:
    - 'which ssh-agent || ( apt-get update -y && apt-get install openssh-client -y )'
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | ssh-add -
    - mkdir -p ~/.ssh
    - '[[ -f /.dockerenv ]] && echo -e "Host *\n\tStrictHostKeyChecking no\n\n" > ~/.ssh/config'
    - envoy run deploy_staging_rsync
  environment:
    name: staging
    url: https://q1-academy.nws-dev.com
