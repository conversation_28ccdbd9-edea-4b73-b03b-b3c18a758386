{"name": "q1academy", "version": "1.1.1", "private": true, "scripts": {"dev": "npm run development", "build": "npm run build", "development": "mix", "watch": "mix watch", "watch-poll": "mix watch -- --watch-options-poll=1000", "hot": "mix watch --hot", "prod": "npm run production", "danger": "danger ci", "production": "mix --production"}, "devDependencies": {"@babel/core": "7.22.5", "@babel/plugin-proposal-object-rest-spread": "7.20.7", "@babel/plugin-syntax-dynamic-import": "7.8.3", "@babel/plugin-transform-runtime": "7.22.9", "@babel/preset-env": "7.22.5", "@babel/preset-react": "^7.27.1", "@prettier/plugin-php": "0.19.6", "ajv": "^8.12.0", "axios": "^1.10.0", "babel-loader": "^9.1.2", "browser-sync": "^2.29.3", "browser-sync-webpack-plugin": "2.3.0", "cross-env": "^7.0.3", "laravel-mix": "^6.0.49", "lodash": "^4.17.21", "postcss": "^8.4.26", "prettier": "2.8.8", "resolve-url-loader": "5.0.0", "sass": "1.68.0", "sass-loader": "13.3.2", "tailwindcss": "^3.4.1", "danger": "^12.3.0", "js-yaml": "^4.1.0"}, "overrides": {"autoprefixer": "10.4.14", "webpack": "5.88.1", "webpack-cli": "4.9.1", "prop-types": "15.8.1", "sass": "1.68.0"}, "resolutions": {"autoprefixer": "10.4.14", "webpack": "5.88.1", "webpack-cli": "4.9.1", "prop-types": "15.8.1", "sass": "1.68.0"}, "browserslist": [">= 1%", "last 2 versions", "not dead", "Chrome >= 45", "Firefox >= 38", "Edge >= 12", "Explorer >= 10", "iOS >= 9", "Safari >= 9", "Android >= 4.4", "Opera >= 30"], "babel": {"presets": [["@babel/env", {"targets": {"browsers": [">= 1%", "last 2 versions", "not dead", "Chrome >= 45", "Firefox >= 38", "Edge >= 12", "Explorer >= 10", "iOS >= 9", "Safari >= 9", "Android >= 4.4", "Opera >= 30"]}}]]}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@fontsource/merriweather": "^5.2.9", "@mui/icons-material": "^7.2.0", "@mui/material": "^7.2.0", "@popperjs/core": "^2.11.8", "@tinymce/tinymce-react": "^6.2.1", "apexcharts-clevision": "^3.28.5", "bootstrap": "~5.3.2", "boxicons": "~2.1.4", "embla-carousel-autoplay": "^8.6.0", "embla-carousel-react": "^8.6.0", "embla-carousel-reactive-utils": "^8.6.0", "highlight.js": "~11.8.0", "jquery": "~3.7.0", "lodash.isequal": "^4.5.0", "masonry-layout": "~4.2.2", "perfect-scrollbar": "~1.5.5", "react": "^19.1.0", "react-dom": "^19.1.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.60.0", "react-router-dom": "^7.7.0", "react-toastify": "^11.0.5", "swiper": "^11.2.10"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}