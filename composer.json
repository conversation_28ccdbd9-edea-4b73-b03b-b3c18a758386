{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.1", "anayarojo/shoppingcart": "^4.2", "barryvdh/laravel-debugbar": "^3.12", "barryvdh/laravel-dompdf": "^3.1", "bensampo/laravel-enum": "^6.12", "doctrine/dbal": "^3.8", "guzzlehttp/guzzle": "^7.8", "laravel/framework": "^10.23", "laravel/sanctum": "^3.2", "laravel/tinker": "^2.8", "predis/predis": "^2.2", "prettus/l5-repository": "^2.9"}, "require-dev": {"fakerphp/faker": "^1.9.1", "laravel/envoy": "^2.9", "laravel/pint": "^1.0", "laravel/sail": "^1.25", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^7.0", "phpunit/phpunit": "^10.1", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}, "files": ["app/Helpers/Global/SystemHelper.php", "app/Helpers/Global/QuestionHandlerHelper.php"]}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "dev", "prefer-stable": true}